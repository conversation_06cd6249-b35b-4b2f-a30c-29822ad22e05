package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import vn.agis.crm.base.domain.imports.ImportJobMode;
import vn.agis.crm.base.domain.imports.ImportJobSource;
import vn.agis.crm.base.domain.imports.ImportJobStatus;

import java.util.Date;

@Entity
@Table(name = "import_jobs")
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportJob extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "file_name", nullable = false, length = 255)
    private String fileName;

    @Column(name = "file_checksum", length = 255)
    private String fileChecksum;

    @Enumerated(EnumType.STRING)
    @Column(name = "source", nullable = false, length = 50)
    private ImportJobSource source;

    @Column(name = "source_link", length = 500)
    private String sourceLink;

    @Enumerated(EnumType.STRING)
    @Column(name = "mode", nullable = false, length = 20)
    private ImportJobMode mode = ImportJobMode.RUN;

    @Column(name = "total_rows")
    private Integer totalRows = 0;

    @Column(name = "valid_rows")
    private Integer validRows = 0;

    @Column(name = "error_rows")
    private Integer errorRows = 0;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false, length = 20)
    private ImportJobStatus status = ImportJobStatus.PENDING;

    @Lob
    @Column(name = "options", columnDefinition = "LONGTEXT")
    private String options;

    @Column(name = "updated_at")
    @Temporal(TemporalType.TIMESTAMP)
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;

    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = new Date();
        }
        if (updatedAt == null) {
            updatedAt = new Date();
        }
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = new Date();
    }
}
