package vn.agis.crm.base.utils;

import org.apache.commons.lang3.StringUtils;
public class SqlUtils {
    public static final String BACK_SLASH = "\\";
    public static final String PERCENT = "%";
    public static final String UNDERLINED = "_";

    public static final String PERCENT_REPLACE = BACK_SLASH + PERCENT;
    public static final String UNDERLINE_REPLACE = BACK_SLASH + UNDERLINED;

    public static String optimizeSearchLike(String nameSearch) {
        String replacePercent = StringUtils.replace(nameSearch, PERCENT, PERCENT_REPLACE);
        return StringUtils.replace(replacePercent, UNDERLINED, UNDERLINE_REPLACE);
    }
}
