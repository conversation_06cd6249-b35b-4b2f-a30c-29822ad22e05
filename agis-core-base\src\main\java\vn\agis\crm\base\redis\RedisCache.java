package vn.agis.crm.base.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.*;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.utils.GenericSerializer;
import vn.agis.crm.base.utils.ObjectUtils;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.TimeUnit;

public class RedisCache implements ObjectCache {
    private static final Logger logger = LoggerFactory.getLogger(RedisCache.class);
    private RedisTemplate redisTemplate;
    private GenericSerializer serializer;
    private ValueOperations<String, String> valueOperations;
    private String prefix = null;
    private boolean enableTransactionSupport = false;

    public RedisCache(RedisTemplate redisTemplate) throws NoSuchFieldException, IllegalAccessException {
        this.redisTemplate = redisTemplate;
        Field f = redisTemplate.getClass().getDeclaredField("enableTransactionSupport");
        f.setAccessible(true);
        enableTransactionSupport = (boolean) f.get(redisTemplate);
        this.serializer = new GenericSerializer();
        this.valueOperations = redisTemplate.opsForValue();
    }

    @Override
    public Object get(String key, Class<?> type) throws IOException {
        String value = valueOperations.get(cacheKey(type, key));
        if (!ObjectUtils.empty(value)) {
            return serializer.deSerialize(type, value);
        }
        return null;
    }

    @Override
    public Collection<Object> getAll(Collection<String> keySet, Class<?> type) throws IOException {
        if (keySet == null || keySet.isEmpty()) return null;
        List<String> cacheKeys = new ArrayList<>();
        for (String key: keySet)
            cacheKeys.add(cacheKey(type, key));
        List<String> values = valueOperations.multiGet(cacheKeys);
        if (values == null || values.isEmpty()) return null;
        List<Object> objectResults = new ArrayList<>();
        for (String value: values)
            objectResults.add(serializer.deSerialize(type, value));
        return objectResults;
    }

    @Override
    public void put(String key, Object item, Class<?> type) throws Exception {
        String value = serializer.serialize(item);
        valueOperations.set(cacheKey(type, key), value);
    }

    @Override
    public void put(String key, Object item) throws Exception {
        String value = serializer.serialize(item);
        valueOperations.set(cacheKey(item.getClass(), key), value);
    }

    @Override
    public void put(String key, Object item, int ttl, Class<?> type) throws Exception {
        put(key, item, ttl, TimeUnit.SECONDS, type);
    }

    @Override
    public void put(String key, Object item, long timeout, TimeUnit timeUnit, Class<?> type) throws Exception {
        if (timeout == 0) {
            put(key, item, type);
            return;
        }
        String value = serializer.serialize(item);
        valueOperations.set(cacheKey(type, key), value, timeout, timeUnit);
    }

    @Override
    public void putAll(Map<String, Object> m, Class<?> type) {

    }

    @Override
    public void remove(String key, Class<?> type) {
        redisTemplate.delete(cacheKey(type, key));
    }

    public void removeAll(Collection<String> keySet, Class<?> type) {
        if (keySet == null || keySet.isEmpty()) return;
        Set<String> cacheKeys = new HashSet<>();
        for (String key: keySet)
            cacheKeys.add(cacheKey(type, key));
        redisTemplate.delete(cacheKeys);
    }

    @Override
    public Long increment(String key, Long interval) {
        Long increment = valueOperations.increment(cacheKey(Long.class, key), interval);
        return increment;
    }

    public boolean expire(String keys, long timeout, TimeUnit timeUnit) {
        boolean check = redisTemplate.expire(cacheKey(keys), timeout, timeUnit);;
        return check;
    }

    @Override
    public boolean expire(String keys, long timeout, TimeUnit timeUnit, Class<?> typeClass) {
        boolean check = redisTemplate.expire(cacheKey(typeClass, keys), timeout, timeUnit);
        return check;
    }


    private String cacheKey(Class<?> type, String key) {
        return String.format("%s-%s:%s", getPrefix(), type.getSimpleName(), key);
    }

    private String cacheKey(String key) {
        return String.format("%s-%s", getPrefix(), key);
    }

    private String getPrefix() {
        if (prefix == null) {
            if (!SpringContext.getServiceName().isBlank())
                prefix = SpringContext.getServiceName();
            else prefix = "";
        }
        return prefix;
    }

    private Set<String> getAllOriginKey(String pattern) {
        if (!(redisTemplate.getKeySerializer() instanceof StringRedisSerializer)) {
            logger.error("Not support because Current Key Serialize is " + redisTemplate.getKeySerializer().getClass().getSimpleName() +
                    " , must be StringRedisSerializer");
            return null;
        }
        Set<String> stringSet = redisTemplate.keys(pattern);
        return stringSet;
    }

    public Set<String> getAllKey(Class<?> type) {
        if (!(redisTemplate.getKeySerializer() instanceof StringRedisSerializer)) {
            logger.error("Not support because Current Key Serialize is " + redisTemplate.getKeySerializer().getClass().getSimpleName() +
                    " , must be StringRedisSerializer");
            return null;
        }
        Set<String> originKeys = redisTemplate.keys("*" + type.getSimpleName() + ":*");
        if (originKeys == null || originKeys.isEmpty()) return null;
        Set<String> returnKeys = new HashSet<>();
        for (String originKey : originKeys)
            returnKeys.add(originKey.split(":")[1]);
        return returnKeys;
    }

    public List<Object> getAllForType(Class<?> type) throws IOException {
        if (!(redisTemplate.getKeySerializer() instanceof StringRedisSerializer)) {
            logger.error("Not support because Current Key Serialize is " + redisTemplate.getKeySerializer().getClass().getSimpleName() +
                    " , must be StringRedisSerializer");
            return null;
        }
        Set<String> keys = getAllOriginKey("*" + type.getSimpleName() + ":*");
        if (keys != null && keys.size() > 0) {
            List<String> values = valueOperations.multiGet(keys);
            if (values == null || values.isEmpty()) return null;
            List<Object> valueReturn = new ArrayList<>();
            for (String value: values) {
                if (!ObjectUtils.empty(value)) {
                    valueReturn.add(serializer.deSerialize(type, value));
                }
            }
            return valueReturn;
        }
        return null;
    }

    public Long getTtl(String rawKey) {
        Long ttl = redisTemplate.getExpire(rawKey);
        return ttl;
    }

    public Long getTtl(String key, Class<?> type) {
        Long ttl = redisTemplate.getExpire(cacheKey(type, key));
        return ttl;
    }

    public Collection<Object> scan (Class<?> type, Long count) throws IOException {
        String pattern = "*" + cacheKey(type, "*");
        RedisConnection redisConnection = redisTemplate.getConnectionFactory().getConnection();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).count(count).build();
        Set<String> resultKey = new HashSet<>();
        Cursor<byte[]> cursor = redisConnection.scan(options);
        while (cursor.hasNext()) {
            String[] rawKey = new String(cursor.next()).split(":");
            resultKey.add(rawKey[rawKey.length - 1]);
        }
        RedisConnectionUtils.releaseConnection(redisConnection, this.redisTemplate.getConnectionFactory());
        return getAll(resultKey, type);
    }

    public List<String> scanKey (String pattern, Long count) {
        RedisConnection redisConnection = redisTemplate.getConnectionFactory().getConnection();
        ScanOptions options = ScanOptions.scanOptions().match(pattern).count(count).build();
        List<String> resultKey = new ArrayList<>();
        Cursor<byte[]> cursor = redisConnection.scan(options);
        while (cursor.hasNext()) {
            String fullKey = new String(cursor.next());
            resultKey.add(fullKey);
        }
        RedisConnectionUtils.releaseConnection(redisConnection, this.redisTemplate.getConnectionFactory());
        return resultKey;
    }

    public List<String> smember(Class<?> type) {
        List<String> resultKey = new ArrayList<>();
        RedisConnection redisConnection = redisTemplate.getConnectionFactory().getConnection();
        Set<byte[]> members = redisConnection.sMembers(type.getSimpleName().getBytes());
        if (members == null || members.isEmpty()) return null;
        for (byte[] e : members) {
            resultKey.add(new String(e));
        }
        RedisConnectionUtils.releaseConnection(redisConnection, this.redisTemplate.getConnectionFactory());
        return resultKey;
    }
}
