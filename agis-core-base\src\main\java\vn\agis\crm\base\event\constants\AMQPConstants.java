package vn.agis.crm.base.event.constants;

import org.springframework.amqp.core.ExchangeTypes;
import vn.agis.crm.base.constants.Constants;

import java.util.Map;

import static vn.agis.crm.base.event.constants.AMQPConstants.Queue.QUEUE_KEY_ADAPTER_SMS;

public interface AMQPConstants {
    /**
     * Khai bao cac X-change dung trong RabbitMQ
     **/
    interface Xchange {
        String CRM_DIRECT_EXCHANGE = "crm.direct";
        String CRM_DELAY_EXCHANGE = "crm.delay";
        Map<String, String> XCHANGE_TYPE_MAP = Map.of(CRM_DIRECT_EXCHANGE, ExchangeTypes.DIRECT, CRM_DELAY_EXCHANGE, ExchangeType.X_DELAY_MESSAGE);
    }

    /**
     * Khai bao cac Queue dung trong RabbitMQ
     **/
    interface Queue {
        /**
         * type
         **/
        String API_TYPE = "api";
        String DATA_TYPE = "data";

        /**
         * PREFIX QUEUE NAME
         * A queue is constructed by  QUEUE_<TYPE>_PREFIX +  SERVICE_NAME
         */
        String QUEUE_PREFIX = "";
        String QUEUE_API_PREFIX = QUEUE_PREFIX + API_TYPE + ".";
        String QUEUE_DATA_PREFIX = QUEUE_PREFIX + DATA_TYPE + ".";

        /**
         * AMQP Exchange to routing message between core backend service
         */

        String QUEUE_KEY_DEVICE_MANAGEMENT = QUEUE_API_PREFIX + Constants.CRMService.BE_DEVICE_MGMT;
        String QUEUE_KEY_CORE_MANAGEMENT = QUEUE_API_PREFIX + Constants.CRMService.BE_CORE_MGMT;
        String QUEUE_KEY_SUB_ADAPTER = QUEUE_API_PREFIX + Constants.CRMService.ADAPTER_SUBSCRIPTION_ADAPTER;
        String QUEUE_KEY_REPORT = QUEUE_API_PREFIX + Constants.CRMService.BE_REPORT;
        String QUEUE_KEY_LOGGING = QUEUE_API_PREFIX + Constants.CRMService.BE_LOGGING;
        String QUEUE_KEY_RULE_MGMT = QUEUE_API_PREFIX + Constants.CRMService.BE_ALERT_RULE_MGMT;
        String QUEUE_KEY_ADAPTER_SIM_MGMT = QUEUE_API_PREFIX + Constants.CRMService.ADAPTER_SIM_MGMT;
        String QUEUE_KEY_ADAPTER_SMS = QUEUE_API_PREFIX + Constants.CRMService.ADAPTER_SMS;
        String QUEUE_KEY_ADAPTER_SMS_RETRY = QUEUE_KEY_ADAPTER_SMS + "-retry";
        String QUEUE_KEY_BLOCK_USER = QUEUE_DATA_PREFIX + Constants.CRMService.BLOCK_LIST_USER;
        String QUEUE_KEY_IOT_ADAPTER = QUEUE_API_PREFIX + Constants.CRMService.BE_IOT_ADAPTER;
        String QUEUE_KEY_BOS_ADAPTER = QUEUE_API_PREFIX + Constants.CRMService.BE_BOS_ADAPTER;

    }

    /**
     * Khai bao cac Routing key dung trong RabbitMQ
     **/
    interface RoutingKey {
        /**
         * PREFIX ROUTING KEY
         * A routing key is constructed by ROUTING_KEY_<TYPE>_PREFIX + SERVICE_NAME + ROUTING_KEY_SUFFIX
         */
        String ROUTING_KEY_API_PREFIX = Queue.API_TYPE + ".";
        String ROUTING_KEY_DEVICE_MANAGEMENT = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_DEVICE_MGMT;
        String ROUTING_KEY_CORE_MANAGEMENT = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_CORE_MGMT;
        String ROUTING_KEY_SUB_ADAPTER = ROUTING_KEY_API_PREFIX + Constants.CRMService.ADAPTER_SUBSCRIPTION_ADAPTER;
        String ROUTING_KEY_REPORT = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_REPORT;
        String ROUTING_KEY_LOGGING = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_LOGGING;
        String ROUTING_KEY_RULE_MGMT = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_ALERT_RULE_MGMT;
        String ROUTING_KEY_ADAPTER_SIM_MGMT = ROUTING_KEY_API_PREFIX + Constants.CRMService.ADAPTER_SIM_MGMT;
        String ROUTING_KEY_ADAPTER_SMS = ROUTING_KEY_API_PREFIX + Constants.CRMService.ADAPTER_SMS;
        String ROUTING_KEY_ADAPTER_SMS_RETRY = QUEUE_KEY_ADAPTER_SMS + "-retry";
        String ROUTING_KEY_IOT_ADAPTER = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_IOT_ADAPTER;
        String ROUTING_KEY_BOS_ADAPTER = ROUTING_KEY_API_PREFIX + Constants.CRMService.BE_BOS_ADAPTER;
    }

    interface ExchangeType {
        String TOPIC = "topic";
        String DIRECT = "direct";
        String X_DELAY_MESSAGE = "x-delayed-message";
        String FANOUT = "fanout";
        String HEADER = "headers";
    }
}
