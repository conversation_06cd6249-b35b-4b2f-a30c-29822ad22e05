package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Answer;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Question;

@Data
@NoArgsConstructor
@Table(name = "query_virtual_assistant")
@Entity
public class QueryVirtualAssistant extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "chat_id")
    private Long chatId;

    @Lob
    @Column(name = "question")
    private String question;

    @Lob
    @Column(name = "answer")
    private String answer;

    @Transient
    private Question questionInfo;

    @Transient
    private Answer answerInfo;
}
