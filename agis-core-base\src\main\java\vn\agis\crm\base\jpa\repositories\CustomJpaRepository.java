package vn.agis.crm.base.jpa.repositories;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.NoRepositoryBean;
import vn.agis.crm.base.jpa.entity.AbstractEntity;

import java.io.Serializable;

@NoRepositoryBean
public interface CustomJpaRepository<T extends AbstractEntity, ID extends Serializable> extends JpaRepository<T,ID>, JpaSpecificationExecutor<T> {
}
