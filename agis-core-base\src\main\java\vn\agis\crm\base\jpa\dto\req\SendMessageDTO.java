package vn.agis.crm.base.jpa.dto.req;

import lombok.*;

import java.util.Objects;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SendMessageDTO {
    private String phoneNumber;
    private String content;
    private int retryCount = 0;
    private String errDesc = null;
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SendMessageDTO that = (SendMessageDTO) o;
        return Objects.equals(phoneNumber, that.phoneNumber) && Objects.equals(content, that.content);
    }

    @Override
    public int hashCode() {
        return Objects.hash(phoneNumber, content);
    }
}