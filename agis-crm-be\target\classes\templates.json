[{"id": "find_customer_by_phone", "description": "<PERSON><PERSON><PERSON> k<PERSON> hàng theo số điện thoại", "sql": "SELECT * FROM customers WHERE phone = :phone", "parameters": [{"name": "phone", "type": "string", "pattern": "(?i)(?:sdt|số\\s?(?:điệ<PERSON> tho<PERSON>|máy|liên hệ|phone)|phone|mobile)?\\s*(\\+?84|0)(\\d{8,9})"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có số phone 0908089883", "<PERSON><PERSON><PERSON><PERSON> hàng có số 0987654321 là ai?", "<PERSON><PERSON><PERSON> <PERSON> hàng có số điện thoại 0912 345 678", "Thông tin khách hàng với số liên hệ +84981234567", "<PERSON> đang dùng số ************?", "<PERSON><PERSON><PERSON><PERSON> hàng nào có SĐT ************?", "<PERSON>ra c<PERSON><PERSON> kh<PERSON>ch hàng theo số mobile 0941234567", "<PERSON> biết thông tin khách với số máy 0977777777", "<PERSON>ườ<PERSON> nào đăng ký với số ************?", "<PERSON><PERSON><PERSON> dữ liệu kh<PERSON>ch hàng theo số điện thoại 0909090909"]}, {"id": "find_customer_by_cccd", "description": "<PERSON><PERSON><PERSON> k<PERSON>ch hàng theo số CCCD/CMND", "sql": "SELECT * FROM customers WHERE cccd = :cccd", "parameters": [{"name": "cccd", "type": "string", "pattern": "(?i)(?:cccd|căn\\s*cước|cmnd|chứng\\s*minh\\s*nhân\\s*dân|số\\s*giấy\\s*tờ)\\D*(\\d{9,12})"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có số CCCD 0808308398", "<PERSON><PERSON><PERSON><PERSON> hàng có CMND 123456789 là ai?", "Ai đang dùng số căn cước 012345678901?", "<PERSON>ra c<PERSON>u kh<PERSON>ch hàng có chứng minh nhân dân 987654321", "<PERSON><PERSON><PERSON><PERSON> hàng nào có số giấy tờ 079123456789?", "Thông tin khách hàng với số CMND: 123456789", "<PERSON><PERSON><PERSON> dữ liệu kh<PERSON>ch hàng theo CCCD 001234567890", "Cho tôi biết khách hàng nào đăng ký bằng căn cước công dân 123456789012"]}, {"id": "find_customer_by_email", "description": "<PERSON><PERSON><PERSON> k<PERSON> hàng theo email", "sql": "SELECT * FROM customers WHERE email = :email", "parameters": [{"name": "email", "type": "string", "pattern": "\\b[\\w._%+-]+@[\\w.-]+\\.[A-Za-z]{2,6}\\b"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có địa chỉ email <EMAIL>", "<PERSON><PERSON><PERSON><PERSON> hàng có email <EMAIL> là ai", "Th<PERSON>ng tin khách hàng với email: <EMAIL>", "<PERSON> là chủ sở hữu củ<NAME_EMAIL>", "<PERSON> tôi biết kh<PERSON>ch có email <EMAIL>", "<PERSON><PERSON><PERSON><PERSON> nào đăng ký bằ<NAME_EMAIL>"]}, {"id": "find_customer_by_name", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo tên", "sql": "SELECT * FROM customers WHERE name LIKE :name", "parameters": [{"name": "name", "type": "string", "pattern": "(?i)(?:(?:tên|họ tên|fullname|full name|khách tên)\\s+)([\\p{L} ]+)"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có tên <PERSON> A", "<PERSON><PERSON><PERSON><PERSON> hàng có họ tên Trần Thị B là ai", "<PERSON> là khách có tên <PERSON>", "<PERSON>h<PERSON><PERSON> tin khách hàng với full name <PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> nào đăng ký dưới tên <PERSON>", "Cho tôi biết khách hàng họ tên Hoàng <PERSON>ăn E"]}, {"id": "find_customer_by_address_or_addresses", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo địa chỉ (có thể một hoặc nhiều địa chỉ, nếu nhiều địa chỉ thì cách nhau bằng dấu /)", "sql": "SELECT * FROM customers WHERE address LIKE ANY(:addresses)", "parameters": [{"name": "addresses", "type": "list<string>", "pattern": "(?i)(?:(?:địa chỉ(?: nhà)?|nhà ở|nơi ở|chỗ ở|địa chỉ liên hệ|ở khu vực)[:]?\\s*)([\\p{L}0-9 ,./-]+)"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà: 180 <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> 22, <PERSON><PERSON><PERSON>, HCM", "Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà ở khu vực Bình Thạnh", "<PERSON><PERSON><PERSON><PERSON> hàng nào có địa chỉ ở Quận 1?", "<PERSON>ra cứu thông tin khách có địa chỉ tại Vũng Tàu", "<PERSON><PERSON><PERSON><PERSON> hàng sống ở khu vực Bình Dương là ai?", "Ai đăng ký với địa chỉ Hưng <PERSON>ên", "<PERSON> tôi biết thông tin khách hàng có địa chỉ liên hệ <PERSON> An", "<PERSON><PERSON><PERSON><PERSON> nào ở khu vực HCM", "Thông tin khách hàng có nơi ở Hà Nội", "Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà ở khu vực Bình Thạnh/Bình Tân/Quận 1/Quận 2/Quận 3/<PERSON><PERSON><PERSON> Tàu/Bình Dương/HCM/HN/Hưng Yên/Long An"]}, {"id": "find_customer_by_relative", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo tên ngư<PERSON>i thân", "sql": "SELECT c.* FROM customer_relatives cr INNER JOIN customers c ON cr.customer_id = c.id WHERE cr.full_name LIKE CONCAT('%', :relativeName, '%')", "parameters": [{"name": "relativeName", "type": "string", "pattern": "(?i)(?:(?:người thân|vợ|chồng|con trai|con gái|con|anh|chị|em|bố|mẹ|cha|ông|bà)\\s*(?:tên)?\\s*)([\\p{L} ]+)"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có người thân tên <PERSON>ễn <PERSON>n B", "<PERSON><PERSON><PERSON><PERSON> hàng nào có vợ tên Trần Thị Mai?", "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng có chồng tên <PERSON>", "Ai là khách hàng có con trai tên <PERSON>?", "<PERSON> biết khách hàng có con gái tên <PERSON>", "<PERSON><PERSON><PERSON><PERSON> hàng nào có anh tên <PERSON>", "<PERSON><PERSON><PERSON> k<PERSON>ch có em gái tên Tr<PERSON>n <PERSON>hu <PERSON>rang", "<PERSON> có bố tên <PERSON>", "<PERSON><PERSON><PERSON><PERSON> hàng nào có mẹ tên Trần <PERSON>h<PERSON>a", "Cho tôi thông tin khách hàng có cha tên <PERSON>", "Thông tin khách có bà tên <PERSON>", "<PERSON><PERSON><PERSON><PERSON> nào có ông tên Trần <PERSON>"]}, {"id": "find_customer_by_product", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo loại sản phẩm sở hữu", "sql": "SELECT c.* FROM customers c INNER JOIN customer_properties cp ON c.id = cp.customer_id INNER JOIN projects p ON cp.project_id = p.id WHERE p.name LIKE CONCAT('%', :product, '%')", "parameters": [{"name": "product", "type": "string", "pattern": "(?i)(<PERSON><PERSON><PERSON><PERSON> thự|Penthouse|Sky ?Villa|Duplex|Nhà phố|Shophouse|căn hộ(?: Studio)?|Studio|1PN\\+?|2PN\\+?|3PN|4PN)"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm Biệt thự", "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm căn hộ 2PN", "<PERSON><PERSON><PERSON><PERSON> hàng nào hiện có căn hộ Studio?", "<PERSON><PERSON><PERSON> k<PERSON>ch hàng đã mua Shophouse", "Ai đang sở hữu Sky Villa?", "<PERSON><PERSON> s<PERSON>ch khách hàng có căn hộ 3PN", "<PERSON>h<PERSON>ch nào đã mua nhà phố?", "<PERSON><PERSON>m thông tin khách hàng đang sở hữu sản phẩm Duplex", "<PERSON><PERSON><PERSON><PERSON> hàng nào đang sở hữu căn hộ Penthouse?"]}, {"id": "find_customer_by_birthday", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo ng<PERSON> sinh", "sql": "SELECT * FROM customers WHERE birth_date = STR_TO_DATE(:birthday, '%d/%m/%Y')", "parameters": [{"name": "birthday", "type": "date", "pattern": "\\b\\d{1,2}[/-]\\d{1,2}[/-]\\d{4}\\b"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có ngày sinh vào 09/09/1985", "Cho tôi thông tin chi tiết của khách hàng NAM có ngày sinh vào 09/09/1985", "<PERSON> sinh ngày 9/9/1985?", "<PERSON><PERSON><PERSON><PERSON> hàng có sinh nhật 09-09-1985", "<PERSON><PERSON><PERSON> sinh ng<PERSON>y 9-9-1985", "Th<PERSON>ng tin khách hàng sinh vào ngày 09/09/1985", "<PERSON><PERSON> s<PERSON>ch khách hàng có ngày sinh 09/09/1985"]}, {"id": "find_customer_by_business", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo lĩnh vực kinh doanh", "sql": "SELECT * FROM customers WHERE business_field LIKE CONCAT('%', :field, '%')", "parameters": [{"name": "field", "type": "string", "pattern": "(?i)(?:(?:lĩnh vực|ngành nghề|ngành|làm trong lĩnh vực|hoạt động trong lĩnh vực)\\s)([\\p{L} ]+)"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng đang kinh doanh trong lĩnh vực xuất nhập khẩu", "Cho tôi thông tin chi tiết của khách hàng NỮ đang kinh doanh trong lĩnh vực xuất nhập khẩu", "<PERSON><PERSON><PERSON><PERSON> hàng nào làm trong ngành xây dựng?", "<PERSON><PERSON> sách khách hàng hoạt động trong lĩnh vực bất động sản", "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng đang kinh doanh ngành gi<PERSON><PERSON> dục", "Thông tin khách hàng làm trong lĩnh vực công nghệ thông tin", "Ai đang hoạt động trong ngành tài chính?", "<PERSON>ra c<PERSON><PERSON> kh<PERSON>ch hàng thu<PERSON><PERSON> lĩnh vự<PERSON> thư<PERSON><PERSON> mại điện tử", "<PERSON><PERSON><PERSON><PERSON> hàng NAM kinh doanh trong ngành vận tải", "<PERSON> biết khách hàng làm trong lĩnh vự<PERSON> du lịch"]}, {"id": "find_customer_by_hobby", "description": "<PERSON><PERSON><PERSON> kh<PERSON>ch hàng theo sở thích (hỗ trợ exact và fuzzy match trong cột interest dạng JSON array)", "sql": "SELECT * FROM customers WHERE JSON_CONTAINS(interests, JSON_QUOTE(:hobby)) OR JSON_SEARCH(interests, 'one', CONCAT('%', :hobby, '%')) IS NOT NULL", "parameters": [{"name": "hobby", "type": "string", "pattern": "(?<=sở thích\\s)[\\p{L} ]+"}], "examples": ["Cho tôi thông tin chi tiết của khách hàng có sở thích chơi golf", "Cho tôi thông tin chi tiết của khách hàng NAM có sở thích chơi golf", "Cho tôi thông tin chi tiết của khách hàng có sở thích Luxury Properties", "Cho tôi thông tin chi tiết của khách hàng có sở thích liên quan đến Real Estate"]}]