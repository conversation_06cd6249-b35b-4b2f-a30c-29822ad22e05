package vn.agis.crm.base.jpa.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.entity.AbstractEntity;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.jpa.services.CrudService;

import java.io.Serializable;

public abstract class CrudController<T extends AbstractEntity, ID extends Serializable> {

    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(CrudController.class);

    protected CrudService<T, ID> service;
    protected EventBus eventBus;

    public CrudController(CrudService service, EventBus eventBus) {
        this.service = service;
        this.eventBus = eventBus;
    }

    public Event process(Event event) {
        return service.process(event);
    }

}
