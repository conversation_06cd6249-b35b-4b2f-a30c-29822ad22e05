// Test examples and usage scenarios for CustomerBirthdayNotificationScheduler
// This demonstrates how to test and validate the birthday notification functionality

package vn.agis.crm.scheduler;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.repository.ConfigRepository;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.repository.NotificationRepository;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Transactional
public class CustomerBirthdayNotificationSchedulerTest {

    @Autowired
    private CustomerBirthdayNotificationScheduler scheduler;
    
    @Autowired
    private ConfigRepository configRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private NotificationRepository notificationRepository;

    @Test
    public void testBirthdayNotificationWith3DaysAdvance() {
        // Setup: Enable 3-day advance notifications
        Config config = createBirthdayConfig("3");
        configRepository.save(config);

        // Setup: Create test employee (staff)
        Employee staff = createTestEmployee("Staff User", "<EMAIL>", 2);
        employeeRepository.save(staff);

        // Setup: Create customer with birthday in 3 days
        Customers customer = createCustomerWithBirthdayInDays(3);
        customer.setCurrentStaffId(staff.getId());
        customerRepository.save(customer);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(staff.getId());
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getType().equals(2); // CustomerBirthday
        assert notification.getTitle().equals("Nhắc nhở sinh nhật khách hàng");
        assert notification.getContent().contains("sau 3 ngày");
        assert notification.getContent().contains(customer.getFullName());
        assert notification.getTargetCustomerId().equals(customer.getId());

        System.out.println("✅ 3-day advance birthday notification test passed");
    }

    @Test
    public void testSameDayBirthdayNotification() {
        // Setup: Enable same-day notifications
        Config config = createBirthdayConfig("0");
        configRepository.save(config);

        // Setup: Create test employee (manager)
        Employee manager = createTestEmployee("Manager User", "<EMAIL>", 1);
        employeeRepository.save(manager);

        // Setup: Create customer with birthday today
        Customers customer = createCustomerWithBirthdayInDays(0);
        customer.setCurrentManagerId(manager.getId());
        customerRepository.save(customer);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(manager.getId());
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getContent().contains("hôm nay");
        assert !notification.getContent().contains("sau");

        System.out.println("✅ Same-day birthday notification test passed");
    }

    @Test
    public void testEmployeePrioritySystem() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("1");
        configRepository.save(config);

        // Setup: Create employees
        Employee staff = createTestEmployee("Staff User", "<EMAIL>", 2);
        Employee manager = createTestEmployee("Manager User", "<EMAIL>", 1);
        Employee admin1 = createTestEmployee("Admin User 1", "<EMAIL>", 1);
        Employee admin2 = createTestEmployee("Admin User 2", "<EMAIL>", 1);
        employeeRepository.saveAll(List.of(staff, manager, admin1, admin2));

        // Test Case 1: Customer with both staff and manager (should prioritize staff only)
        Customers customer1 = createCustomerWithBirthdayInDays(1);
        customer1.setCurrentStaffId(staff.getId());
        customer1.setCurrentManagerId(manager.getId());
        customerRepository.save(customer1);

        // Test Case 2: Customer with only manager (should use manager only)
        Customers customer2 = createCustomerWithBirthdayInDays(1);
        customer2.setCurrentManagerId(manager.getId());
        customerRepository.save(customer2);

        // Test Case 3: Customer with no assignments (should use ALL admin employees)
        Customers customer3 = createCustomerWithBirthdayInDays(1);
        customerRepository.save(customer3);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Priority system worked correctly
        List<Notifications> staffNotifications = notificationRepository.findByTargetEmployeeId(staff.getId());
        List<Notifications> managerNotifications = notificationRepository.findByTargetEmployeeId(manager.getId());
        List<Notifications> admin1Notifications = notificationRepository.findByTargetEmployeeId(admin1.getId());
        List<Notifications> admin2Notifications = notificationRepository.findByTargetEmployeeId(admin2.getId());

        assert staffNotifications.size() == 1; // Customer1 -> Staff only (priority 1)
        assert managerNotifications.size() == 1; // Customer2 -> Manager only (priority 2)
        assert admin1Notifications.size() == 1; // Customer3 -> Admin1 (priority 3)
        assert admin2Notifications.size() == 1; // Customer3 -> Admin2 (priority 3)

        // Verify that customer3 notifications went to both admins
        assert admin1Notifications.get(0).getTargetCustomerId().equals(customer3.getId());
        assert admin2Notifications.get(0).getTargetCustomerId().equals(customer3.getId());

        System.out.println("✅ Employee priority system test passed - All admins receive notifications");
    }

    @Test
    public void testDisabledConfiguration() {
        // Setup: Disable notifications (no config)
        // Don't create any config

        // Setup: Create customer with birthday
        Customers customer = createCustomerWithBirthdayInDays(1);
        customerRepository.save(customer);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: No notifications were created
        List<Notifications> allNotifications = notificationRepository.findAll();
        assert allNotifications.size() == 0;

        System.out.println("✅ Disabled configuration test passed");
    }

    @Test
    public void testInvalidConfiguration() {
        // Setup: Invalid configuration values
        Config invalidConfig1 = createBirthdayConfig("invalid");
        Config invalidConfig2 = createBirthdayConfig("-1");
        Config invalidConfig3 = createBirthdayConfig("999");

        // Test each invalid config
        for (Config config : List.of(invalidConfig1, invalidConfig2, invalidConfig3)) {
            configRepository.deleteAll();
            configRepository.save(config);

            // Setup: Create customer with birthday
            Customers customer = createCustomerWithBirthdayInDays(1);
            customerRepository.save(customer);

            // Execute: Run the scheduler job
            scheduler.processCustomerBirthdayNotifications();

            // Verify: No notifications were created due to invalid config
            List<Notifications> notifications = notificationRepository.findAll();
            assert notifications.size() == 0;

            // Cleanup
            customerRepository.deleteAll();
            notificationRepository.deleteAll();
        }

        System.out.println("✅ Invalid configuration test passed");
    }

    @Test
    public void testMultipleCustomersWithSameBirthday() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("2");
        configRepository.save(config);

        // Setup: Create employees
        Employee staff1 = createTestEmployee("Staff 1", "<EMAIL>", 2);
        Employee staff2 = createTestEmployee("Staff 2", "<EMAIL>", 2);
        employeeRepository.saveAll(List.of(staff1, staff2));

        // Setup: Create multiple customers with same birthday
        Customers customer1 = createCustomerWithBirthdayInDays(2);
        customer1.setFullName("Customer A");
        customer1.setCurrentStaffId(staff1.getId());

        Customers customer2 = createCustomerWithBirthdayInDays(2);
        customer2.setFullName("Customer B");
        customer2.setCurrentStaffId(staff2.getId());

        Customers customer3 = createCustomerWithBirthdayInDays(2);
        customer3.setFullName("Customer C");
        customer3.setCurrentStaffId(staff1.getId());

        customerRepository.saveAll(List.of(customer1, customer2, customer3));

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: All customers got notifications
        List<Notifications> staff1Notifications = notificationRepository.findByTargetEmployeeId(staff1.getId());
        List<Notifications> staff2Notifications = notificationRepository.findByTargetEmployeeId(staff2.getId());

        assert staff1Notifications.size() == 2; // Customer A and C
        assert staff2Notifications.size() == 1; // Customer B

        // Verify content is customer-specific
        for (Notifications notification : staff1Notifications) {
            assert notification.getContent().contains("Customer A") || 
                   notification.getContent().contains("Customer C");
        }

        System.out.println("✅ Multiple customers with same birthday test passed");
    }

    @Test
    public void testInactiveEmployeeHandling() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("1");
        configRepository.save(config);

        // Setup: Create inactive staff and active admin
        Employee inactiveStaff = createTestEmployee("Inactive Staff", "<EMAIL>", 2);
        inactiveStaff.setStatus(Employee.Status.inactive);

        Employee activeAdmin = createTestEmployee("Active Admin", "<EMAIL>", 1);

        employeeRepository.saveAll(List.of(inactiveStaff, activeAdmin));

        // Setup: Create customer assigned to inactive staff
        Customers customer = createCustomerWithBirthdayInDays(1);
        customer.setCurrentStaffId(inactiveStaff.getId());
        customerRepository.save(customer);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Notification went to admin (fallback) instead of inactive staff
        List<Notifications> staffNotifications = notificationRepository.findByTargetEmployeeId(inactiveStaff.getId());
        List<Notifications> adminNotifications = notificationRepository.findByTargetEmployeeId(activeAdmin.getId());

        assert staffNotifications.size() == 0; // No notification to inactive staff
        assert adminNotifications.size() == 1; // Notification to admin fallback

        System.out.println("✅ Inactive employee handling test passed");
    }

    @Test
    public void testErrorHandlingAndContinuation() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("1");
        configRepository.save(config);

        // Setup: Create valid employee
        Employee validEmployee = createTestEmployee("Valid Employee", "<EMAIL>", 2);
        employeeRepository.save(validEmployee);

        // Setup: Create customers - one with valid assignment, one without
        Customers validCustomer = createCustomerWithBirthdayInDays(1);
        validCustomer.setFullName("Valid Customer");
        validCustomer.setCurrentStaffId(validEmployee.getId());

        Customers orphanCustomer = createCustomerWithBirthdayInDays(1);
        orphanCustomer.setFullName("Orphan Customer");
        // No employee assignments and no admin fallback

        customerRepository.saveAll(List.of(validCustomer, orphanCustomer));

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Valid customer got notification despite orphan customer error
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(validEmployee.getId());
        assert notifications.size() == 1;
        assert notifications.get(0).getContent().contains("Valid Customer");

        System.out.println("✅ Error handling and continuation test passed");
    }

    @Test
    public void testMultipleAdminNotifications() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("2");
        configRepository.save(config);

        // Setup: Create multiple admin employees
        Employee admin1 = createTestEmployee("Admin User 1", "<EMAIL>", 1);
        Employee admin2 = createTestEmployee("Admin User 2", "<EMAIL>", 1);
        Employee admin3 = createTestEmployee("Admin User 3", "<EMAIL>", 1);
        Employee nonAdminEmployee = createTestEmployee("Regular Employee", "<EMAIL>", 2);
        employeeRepository.saveAll(List.of(admin1, admin2, admin3, nonAdminEmployee));

        // Setup: Create multiple customers with no assignments (will use admin fallback)
        Customers customer1 = createCustomerWithBirthdayInDays(2);
        customer1.setFullName("Unassigned Customer 1");

        Customers customer2 = createCustomerWithBirthdayInDays(2);
        customer2.setFullName("Unassigned Customer 2");

        customerRepository.saveAll(List.of(customer1, customer2));

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: All admin employees received notifications for both customers
        List<Notifications> admin1Notifications = notificationRepository.findByTargetEmployeeId(admin1.getId());
        List<Notifications> admin2Notifications = notificationRepository.findByTargetEmployeeId(admin2.getId());
        List<Notifications> admin3Notifications = notificationRepository.findByTargetEmployeeId(admin3.getId());
        List<Notifications> regularNotifications = notificationRepository.findByTargetEmployeeId(nonAdminEmployee.getId());

        // Each admin should receive 2 notifications (one for each customer)
        assert admin1Notifications.size() == 2;
        assert admin2Notifications.size() == 2;
        assert admin3Notifications.size() == 2;
        assert regularNotifications.size() == 0; // Non-admin should not receive notifications

        // Verify notification content is customer-specific
        for (Notifications notification : admin1Notifications) {
            assert notification.getContent().contains("Unassigned Customer 1") ||
                   notification.getContent().contains("Unassigned Customer 2");
            assert notification.getType().equals(2); // CustomerBirthday
            assert notification.getTitle().equals("Nhắc nhở sinh nhật khách hàng");
        }

        // Verify all admins got the same customers
        List<Long> admin1CustomerIds = admin1Notifications.stream()
            .map(Notifications::getTargetCustomerId)
            .sorted()
            .toList();
        List<Long> admin2CustomerIds = admin2Notifications.stream()
            .map(Notifications::getTargetCustomerId)
            .sorted()
            .toList();
        List<Long> admin3CustomerIds = admin3Notifications.stream()
            .map(Notifications::getTargetCustomerId)
            .sorted()
            .toList();

        assert admin1CustomerIds.equals(admin2CustomerIds);
        assert admin2CustomerIds.equals(admin3CustomerIds);
        assert admin1CustomerIds.contains(customer1.getId());
        assert admin1CustomerIds.contains(customer2.getId());

        System.out.println("✅ Multiple admin notifications test passed - All admins receive all unassigned customer notifications");
    }

    @Test
    public void testPartialAdminNotificationFailure() {
        // Setup: Enable notifications
        Config config = createBirthdayConfig("1");
        configRepository.save(config);

        // Setup: Create admin employees (one will be made inactive to simulate failure)
        Employee activeAdmin = createTestEmployee("Active Admin", "<EMAIL>", 1);
        Employee inactiveAdmin = createTestEmployee("Inactive Admin", "<EMAIL>", 1);
        inactiveAdmin.setStatus(Employee.Status.inactive); // This will cause notification to fail

        employeeRepository.saveAll(List.of(activeAdmin, inactiveAdmin));

        // Setup: Create customer with no assignments
        Customers customer = createCustomerWithBirthdayInDays(1);
        customer.setFullName("Test Customer");
        customerRepository.save(customer);

        // Execute: Run the scheduler job
        scheduler.processCustomerBirthdayNotifications();

        // Verify: Only active admin received notification
        List<Notifications> activeAdminNotifications = notificationRepository.findByTargetEmployeeId(activeAdmin.getId());
        List<Notifications> inactiveAdminNotifications = notificationRepository.findByTargetEmployeeId(inactiveAdmin.getId());

        assert activeAdminNotifications.size() == 1; // Active admin got notification
        assert inactiveAdminNotifications.size() == 0; // Inactive admin filtered out

        // Verify notification content
        Notifications notification = activeAdminNotifications.get(0);
        assert notification.getContent().contains("Test Customer");
        assert notification.getTargetCustomerId().equals(customer.getId());

        System.out.println("✅ Partial admin notification failure test passed - Only active admins receive notifications");
    }

    // Helper methods for test setup

    private Config createBirthdayConfig(String value) {
        Config config = new Config();
        config.setConfigKey("NOTIFICATION_BIRTHDAY_DAYS_BEFORE");
        config.setConfigValue(value);
        config.setConfigType(1);
        config.setDescription("Test birthday notification configuration");
        config.setCreatedAt(new Date());
        config.setCreatedBy(1L);
        return config;
    }

    private Employee createTestEmployee(String name, String email, Integer roleId) {
        Employee employee = new Employee();
        employee.setEmployeeCode("EMP_" + System.currentTimeMillis());
        employee.setFullName(name);
        employee.setEmail(email);
        employee.setPhone("0900000000");
        employee.setPassword("password");
        employee.setRoleId(roleId);
        employee.setStatus(Employee.Status.active);
        employee.setCreatedAt(new Date());
        employee.setCreatedBy(1L);
        return employee;
    }

    private Customers createCustomerWithBirthdayInDays(int daysFromNow) {
        Customers customer = new Customers();
        customer.setFullName("Test Customer " + System.currentTimeMillis());
        customer.setPhone("0900000000");
        customer.setEmail("<EMAIL>");
        customer.setSourceType("Data");
        
        // Calculate birthday date
        LocalDate targetDate = LocalDate.now(ZoneId.of("Asia/Ho_Chi_Minh")).plusDays(daysFromNow);
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MONTH, targetDate.getMonthValue() - 1); // Calendar months are 0-based
        cal.set(Calendar.DAY_OF_MONTH, targetDate.getDayOfMonth());
        cal.set(Calendar.YEAR, 1990); // Set a birth year
        
        customer.setBirthDate(cal.getTime());
        customer.setCreatedAt(new Date());
        customer.setCreatedBy(1L);
        
        return customer;
    }
}

// Example manual testing and configuration scenarios
class CustomerBirthdaySchedulerManualTest {
    
    public static void main(String[] args) {
        System.out.println("🎂 Customer Birthday Notification Scheduler - Manual Test Scenarios");
        
        // Scenario 1: Enable 3-day advance notifications
        System.out.println("\n📋 Scenario 1: Enable 3-day advance notifications");
        System.out.println("SQL: INSERT INTO configs (config_key, config_value, config_type, description) VALUES ('NOTIFICATION_BIRTHDAY_DAYS_BEFORE', '3', 1, 'Send birthday notifications 3 days in advance');");
        System.out.println("Expected: Customers with birthdays in 3 days will trigger notifications");
        
        // Scenario 2: Same-day notifications
        System.out.println("\n📋 Scenario 2: Same-day notifications");
        System.out.println("SQL: UPDATE configs SET config_value = '0' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';");
        System.out.println("Expected: Only customers with birthdays today will trigger notifications");
        
        // Scenario 3: Weekly advance notifications
        System.out.println("\n📋 Scenario 3: Weekly advance notifications");
        System.out.println("SQL: UPDATE configs SET config_value = '7' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';");
        System.out.println("Expected: Customers with birthdays in 1 week will trigger notifications");
        
        // Scenario 4: Disable notifications
        System.out.println("\n📋 Scenario 4: Disable notifications");
        System.out.println("SQL: DELETE FROM configs WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';");
        System.out.println("Expected: No birthday notifications will be sent");
        
        // Scenario 5: Test employee priority
        System.out.println("\n📋 Scenario 5: Test employee priority");
        System.out.println("1. Create customer with current_staff_id = 123");
        System.out.println("2. Create customer with current_manager_id = 456 (no staff)");
        System.out.println("3. Create customer with no assignments");
        System.out.println("Expected: Staff gets notification, Manager gets notification, Admin gets notification");
        
        // Scenario 6: Test timezone behavior
        System.out.println("\n📋 Scenario 6: Test timezone behavior");
        System.out.println("1. Set server timezone to different zone (e.g., UTC)");
        System.out.println("2. Verify job still runs at 8:00 AM Vietnam time");
        System.out.println("3. Verify birthday calculations use Vietnam timezone");
        System.out.println("Expected: Consistent behavior regardless of server timezone");
        
        System.out.println("\n🔍 Monitoring Commands:");
        System.out.println("-- Check recent notifications");
        System.out.println("SELECT * FROM notifications WHERE type = 2 AND created_at >= CURDATE() ORDER BY created_at DESC;");
        
        System.out.println("\n-- Check customers with birthdays today");
        System.out.println("SELECT * FROM customers WHERE DAY(birth_date) = DAY(CURDATE()) AND MONTH(birth_date) = MONTH(CURDATE()) AND deleted_at IS NULL;");
        
        System.out.println("\n-- Check configuration");
        System.out.println("SELECT * FROM configs WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';");
        
        System.out.println("\n-- Check notification statistics");
        System.out.println("SELECT DATE(created_at) as date, COUNT(*) as count FROM notifications WHERE type = 2 GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 7;");
    }
}
