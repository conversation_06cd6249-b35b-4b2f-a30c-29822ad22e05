package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.jpa.dto.DryRunResultDto;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;

import java.util.*;
import java.util.Comparator;
import java.util.stream.Collectors;

/**
 * Enhanced utility class for calculating import statistics and generating reports
 * Supports new row-level error summary format
 */
public class ImportStatisticsCalculator {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportStatisticsCalculator.class);
    
    /**
     * Calculate comprehensive statistics from validation results
     */
    public static DryRunResultDto calculateStatistics(Long jobId, String fileName,
                                                     List<ValidationResultDto> validationResults,
                                                     Date startTime, Date endTime) {
        DryRunResultDto result = new DryRunResultDto();
        
        // Basic job info
        result.setJobId(jobId);
        result.setFileName(fileName);
        result.setStartedAt(startTime);
        result.setFinishedAt(endTime);
        result.setStatus("SUCCESS");
        
        if (startTime != null && endTime != null) {
            result.setProcessingTimeMs(endTime.getTime() - startTime.getTime());
        }
        
        // Calculate row statistics
        calculateRowStatistics(result, validationResults);
        
        // Calculate estimation
        calculateEstimation(result, validationResults);
        
        // Generate enhanced error summary with row-level details
        generateEnhancedErrorSummary(result, validationResults);
        
        // Generate warnings
        generateWarnings(result, validationResults);
        
        return result;
    }
    
    /**
     * Calculate row-level statistics
     */
    private static void calculateRowStatistics(DryRunResultDto result, List<ValidationResultDto> validationResults) {
        int totalRows = validationResults.size();
        int validRows = 0;
        int errorRows = 0;
        int warningRows = 0;
        int duplicateRows = 0;
        
        for (ValidationResultDto validation : validationResults) {
            if (validation.isValid()) {
                validRows++;
            } else {
                errorRows++;
            }
            
            if (validation.hasWarnings()) {
                warningRows++;
            }
            
            // Check for duplicate errors
            if (validation.getErrors() != null) {
                boolean hasDuplicate = validation.getErrors().stream()
                    .anyMatch(error -> error.getErrorType().contains("DUPLICATE"));
                if (hasDuplicate) {
                    duplicateRows++;
                }
            }
        }
        
        result.setTotalRows(totalRows);
        result.setValidRows(validRows);
        result.setErrorRows(errorRows);
        result.setWarningRows(warningRows);
        result.setDuplicateRows(duplicateRows);
    }
    
    /**
     * Calculate import estimation
     */
    private static void calculateEstimation(DryRunResultDto result, List<ValidationResultDto> validationResults) {
        DryRunResultDto.EstimationDto estimation = new DryRunResultDto.EstimationDto();
        
        int customersToCreate = 0;
        int customersToUpdate = 0;
        int relativesToCreate = 0;
        int propertiesToCreate = 0;
        int offersToCreate = 0;
        int assignmentsToCreate = 0;
        
        for (ValidationResultDto validation : validationResults) {
            if (validation.isValid()) {
                // Count customers (each valid row = 1 customer)
                boolean isDuplicate = validation.getErrors() != null && 
                    validation.getErrors().stream()
                        .anyMatch(error -> ImportErrorType.DUPLICATE_IN_SYSTEM.getCode().equals(error.getErrorType()));
                
                if (isDuplicate) {
                    customersToUpdate++;
                } else {
                    customersToCreate++;
                }
                
                // Estimate related entities based on data presence
                Map<String, String> rowData = validation.getOriginalRowData();
                if (rowData != null) {
                    // Count relatives
                    if (hasValue(rowData, "HỌ VÀ TÊN NGƯỜI THÂN")) {
                        relativesToCreate++;
                    }
                    
                    // Count properties
                    if (hasValue(rowData, "TÊN DỰ ÁN") && hasValue(rowData, "MÃ CĂN")) {
                        propertiesToCreate++;
                    }
                    
                    // Count offers
                    if (hasValue(rowData, "DỰ ÁN ĐANG CHÀO (SƠ CẤP)")) {
                        offersToCreate++;
                    }
                    
                    // Count assignments
                    if (hasValue(rowData, "MÃ SỐ NHÂN VIÊN")) {
                        assignmentsToCreate++;
                    }
                }
            }
        }
        
        estimation.setCustomersToCreate(customersToCreate);
        estimation.setCustomersToUpdate(customersToUpdate);
        estimation.setRelativesToCreate(relativesToCreate);
        estimation.setPropertiesToCreate(propertiesToCreate);
        estimation.setOffersToCreate(offersToCreate);
        estimation.setAssignmentsToCreate(assignmentsToCreate);
        
        // Calculate estimated processing time
        long avgTimePerRow = ImportConfigurationManager.getAvgProcessingTimePerRowMs();
        long totalValidRows = customersToCreate + customersToUpdate;
        long estimatedTimeMs = totalValidRows * avgTimePerRow;
        
        estimation.setEstimatedProcessingTimeMs(estimatedTimeMs);
        estimation.setEstimatedProcessingTime(formatDuration(estimatedTimeMs));
        
        result.setEstimation(estimation);
    }
    
    /**
     * Generate enhanced error summary with individual row-level error details
     */
    private static void generateEnhancedErrorSummary(DryRunResultDto result, List<ValidationResultDto> validationResults) {
        List<DryRunResultDto.ErrorSummaryDto> errorSummary = new ArrayList<>();

        for (ValidationResultDto validation : validationResults) {
            if (validation.getErrors() != null) {
                for (ImportErrorDto error : validation.getErrors()) {
                    // Create individual error entry for each error
                    DryRunResultDto.ErrorSummaryDto errorEntry = new DryRunResultDto.ErrorSummaryDto(
                        error.getRowNumber(),
                        error.getColumnName(),
                        error.getErrorType(),
                        error.getErrorDescription(),
                        error.getSeverity()
                    );

                    errorSummary.add(errorEntry);
                }
            }

            // Also include warnings in error summary
            if (validation.getWarnings() != null) {
                for (ImportErrorDto warning : validation.getWarnings()) {
                    DryRunResultDto.ErrorSummaryDto warningEntry = new DryRunResultDto.ErrorSummaryDto(
                        warning.getRowNumber(),
                        warning.getColumnName(),
                        warning.getErrorType(),
                        warning.getErrorDescription(),
                        warning.getSeverity()
                    );

                    errorSummary.add(warningEntry);
                }
            }
        }

        // Sort by row number for better readability
        errorSummary.sort(Comparator.comparing(DryRunResultDto.ErrorSummaryDto::getRow));

        result.setErrorSummary(errorSummary);
    }
    
    /**
     * Generate general warnings
     */
    private static void generateWarnings(DryRunResultDto result, List<ValidationResultDto> validationResults) {
        List<String> warnings = new ArrayList<>();
        
        // Check for high error rate
        if (result.getTotalRows() > 0) {
            double errorRate = (double) result.getErrorRows() / result.getTotalRows();
            if (errorRate > 0.5) {
                warnings.add("Tỷ lệ lỗi cao được phát hiện: " + String.format("%.1f%%", errorRate * 100) +
                           " dòng có lỗi. Vui lòng kiểm tra lại dữ liệu của bạn.");
            }
        }

        // Check for duplicates
        if (result.getDuplicateRows() > 0) {
            warnings.add(result.getDuplicateRows() + " số điện thoại trùng lặp được phát hiện. " +
                        "Các số này sẽ được xử lý theo chiến lược cập nhật của bạn.");
        }
        
        // Check for missing required data
        long missingRequiredFields = result.getErrorSummary().stream()
            .filter(error -> ImportErrorType.MISSING_REQUIRED_FIELD.getCode().equals(error.getErrType()))
            .count();

        if (missingRequiredFields > 0) {
            warnings.add(missingRequiredFields + " dòng thiếu thông tin bắt buộc (tên hoặc số điện thoại). " +
                        "Các dòng này sẽ bị bỏ qua khi import.");
        }

        // Check for format issues
        long formatIssues = result.getErrorSummary().stream()
            .filter(error -> error.getErrType() != null && error.getErrType().contains("INVALID_FORMAT"))
            .count();

        if (formatIssues > 0) {
            warnings.add(formatIssues + " lỗi định dạng dữ liệu được phát hiện. " +
                        "Vui lòng kiểm tra định dạng ngày, số điện thoại và email.");
        }
        
        result.setWarnings(warnings);
    }
    
    /**
     * Check if a field has a non-empty value
     */
    private static boolean hasValue(Map<String, String> rowData, String fieldName) {
        String value = rowData.get(fieldName);
        return value != null && !value.trim().isEmpty();
    }
    
    /**
     * Format duration in milliseconds to human-readable format
     */
    private static String formatDuration(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + " ms";
        } else if (milliseconds < 60000) {
            return String.format("%.1f seconds", milliseconds / 1000.0);
        } else if (milliseconds < 3600000) {
            return String.format("%.1f minutes", milliseconds / 60000.0);
        } else {
            return String.format("%.1f hours", milliseconds / 3600000.0);
        }
    }
    
    /**
     * Generate summary report text
     */
    public static String generateSummaryReport(DryRunResultDto result) {
        StringBuilder report = new StringBuilder();
        
        report.append("=== DRY-RUN VALIDATION SUMMARY ===\n");
        report.append("File: ").append(result.getFileName()).append("\n");
        report.append("Total Rows: ").append(result.getTotalRows()).append("\n");
        report.append("Valid Rows: ").append(result.getValidRows()).append("\n");
        report.append("Error Rows: ").append(result.getErrorRows()).append("\n");
        report.append("Warning Rows: ").append(result.getWarningRows()).append("\n");
        
        if (result.getEstimation() != null) {
            DryRunResultDto.EstimationDto est = result.getEstimation();
            report.append("\n=== IMPORT ESTIMATION ===\n");
            report.append("Customers to Create: ").append(est.getCustomersToCreate()).append("\n");
            report.append("Customers to Update: ").append(est.getCustomersToUpdate()).append("\n");
            report.append("Estimated Processing Time: ").append(est.getEstimatedProcessingTime()).append("\n");
        }
        
        if (result.getErrorSummary() != null && !result.getErrorSummary().isEmpty()) {
            report.append("\n=== SAMPLE ERRORS ===\n");

            // Group errors by type for summary
            Map<String, Long> errorCounts = result.getErrorSummary().stream()
                .collect(Collectors.groupingBy(
                    error -> error.getErrType() != null ? error.getErrType() : "UNKNOWN",
                    Collectors.counting()
                ));

            errorCounts.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(5)
                .forEach(entry -> report.append(entry.getKey())
                    .append(": ").append(entry.getValue()).append(" occurrences\n"));
        }
        
        return report.toString();
    }
}
