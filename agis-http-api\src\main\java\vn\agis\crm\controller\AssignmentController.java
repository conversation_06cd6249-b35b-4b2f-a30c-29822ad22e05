package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.agis.crm.base.jpa.dto.req.ManualAssignmentRequest;
import vn.agis.crm.service.AssignmentApiService;

import java.util.Map;

@RestController
@RequestMapping("/assignments")
public class AssignmentController {

    private final AssignmentApiService assignmentApiService;

    @Autowired
    public AssignmentController(AssignmentApiService assignmentApiService) {
        this.assignmentApiService = assignmentApiService;
    }

    @PostMapping("/manual")
    @Operation(description = "Assign manager/staff to customers manually.")
    public ResponseEntity<Map<String, Object>> manualAssign(@RequestBody ManualAssignmentRequest request) {
        Map<String, Object> result = assignmentApiService.manualAssign(request);
        return ResponseEntity.ok(result);
    }
}

