package vn.agis.crm.base.utils;

import org.springframework.data.domain.Sort;

import java.math.BigInteger;
import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import static java.lang.Math.max;

/**
 * Created by tiemnd on 12/14/19.
 */
public class StringUtils {

    public static String repeat(String str, String separator, int count) {
        StringBuilder sb = new StringBuilder((str.length() + separator.length()) * max(count, 0));

        for (int n = 0; n < count; n++) {
            if (n > 0) sb.append(separator);
            sb.append(str);
        }
        return sb.toString();
    }

    /**
     * return string in snake case
     *
     * @param input a String, for ex: userName
     * @return string in snake case, for ex: user_name
     */
    public static String toSnakeCase(String input) {
        if (input == null) {
            return "";
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (Character.isUpperCase(c)) {
                if (i == 0) {
                    result.append(Character.toLowerCase(c));
                } else {
                    result.append('_').append(Character.toLowerCase(c));
                }
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * returns url query with placeholders to use when call RestTemplate
     *
     * @param map a Map, for ex: {"limit":"5","offset":"0"}
     * @return url query, for ex: limit={limit}&offset={offset}
     */
    public static String queryStringFromMap(Map<String, String> map) {
        if (map.size() > 0) {
            StringBuilder sb = new StringBuilder("");
            for (String key : map.keySet()) {
                sb.append(String.format("&%s={%s}", key, key));
            }
            //remove first "&" char
            return sb.substring(1);
        }
        return "";

    }

    public static String convertDate(String date, String fromFormat, String toFormat) {
        String result = null;
        try {

            // Declare date
            java.text.DateFormat dateFormat = new SimpleDateFormat(fromFormat);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(toFormat);

            // Convert date time
            Date dateDf = dateFormat.parse(date);
            result = simpleDateFormat.format(dateDf);

        } catch (ParseException e) {
            result = null;
            e.printStackTrace();
        }

        return result;
    }

    public static String convertDateWithTimeZone(String date, String fromFormat, TimeZone fromTimeZone, String toFormat, TimeZone toTimeZone) {
        String result = null;
        try {

            // Declare date
            DateFormat dateFormat = new SimpleDateFormat(fromFormat);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(toFormat);

            // Set time zone
            dateFormat.setTimeZone(fromTimeZone);
            simpleDateFormat.setTimeZone(toTimeZone);

            // Convert date time
            Date dateDf = dateFormat.parse(date);
            result = simpleDateFormat.format(dateDf);

        } catch (ParseException e) {
            result = null;
            e.printStackTrace();
        }

        return result;
    }

    public static Long convertDatetimeToTimestamp(String dateTime, String fromFormat) {
        Long result = null;
        try {
            SimpleDateFormat dateFormat = new SimpleDateFormat(fromFormat);
            Date date = dateFormat.parse(dateTime);
            Timestamp timestamp = new Timestamp(date.getTime());
            result = timestamp.getTime();
        } catch (ParseException e) {
            result = null;
            e.printStackTrace();
        }
        return result;
    }

    public static String convertDateToString(int interval) {
        Calendar cal = Calendar.getInstance();
        if (interval > 0) {
            cal.add(Calendar.MINUTE, -interval);
        }
        return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cal.getTime());
    }

    public static long convertDateToLong(String date, String format) throws ParseException {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(format);
        // Convert date time
        Date dateDf = simpleDateFormat.parse(date);
        return dateDf.getTime();
    }

    public static boolean checkExpireTime(long created, long expireTime) {
        if ((System.currentTimeMillis() - expireTime * 1000) < created) return true;
        return false;
    }

    //TODO add comments
    private static int comparePart(String s1, String s2) {
        char c1 = s1.charAt(s1.length() - 1);
        char c2 = s2.charAt(s2.length() - 1);
        String tmp1 = s1.substring(0, s1.length() - 1);
        String tmp2 = s2.substring(0, s2.length() - 1);

        Integer i1 = Character.isAlphabetic(c1) ? Integer.valueOf(tmp1) : Integer.valueOf(s1);
        Integer i2 = Character.isAlphabetic(c2) ? Integer.valueOf(tmp2) : Integer.valueOf(s2);

        int res = i1.compareTo(i2);
        if (res == 0) {
            if (Character.isAlphabetic(c1) && Character.isAlphabetic(c2)) {
                return res = Character.valueOf(c1).compareTo(Character.valueOf(c2));
            }
            if (Character.isAlphabetic(c1) && !Character.isAlphabetic(c2)) {
                return -1;
            }
            if (!Character.isAlphabetic(c1) && Character.isAlphabetic(c2)) {
                return 1;
            }
        }
        return res;
    }

    public static String setToString(Set<Long> list) {
        String tmp = list.toString();
        tmp = tmp.replaceAll("\\[", "(").replace("]", ")");
        return tmp;
    }

    public static String setStringToString(Set<String> list) {
        String tmp = list.toString();
        tmp = tmp.replaceAll("\\[", "").replace("]", "");
        return tmp;
    }

    public static String setLongToString(Set<Long> list) {
        String tmp = list.toString();
        tmp = tmp.replaceAll("\\[", "(").replace("]", ")");
        return tmp;
    }

    public static String setBigIntegerToString(Set<BigInteger> list) {
        String tmp = list.toString();
        tmp = tmp.replaceAll("\\[", "(").replace("]", ")");
        return tmp;
    }

    public static String listBigIntegerToString(List<BigInteger> list) {
        String tmp = list.toString();
        tmp = tmp.replaceAll("\\[", "(").replace("]", ")");
        return tmp;
    }

    public static Set<Long> convertListBigIntegerToSetLong(List<BigInteger> list) {
        Set<Long> ids = new HashSet<>();
        if (list.size() < 1) return ids;
        for (BigInteger i : list) {
            if (i != null) {
                ids.add(i.longValue());
            }
        }

        return ids;
    }

    @Deprecated
    public static Sort toSort(String input) {
        List<Sort.Order> orders = new ArrayList<>();
        String[] sorts = input.split(";");
        for (String s : sorts) {
            String tmp[] = s.split(":");
            if (tmp[1].contains("ASC")) {
                orders.add(new Sort.Order(Sort.Direction.ASC, tmp[0].trim()));
            } else {
                orders.add(new Sort.Order(Sort.Direction.DESC, tmp[0].trim()));
            }
        }
        Sort sort = Sort.by(orders);
        return sort;
    }


    public static String camelToSnake(String str) {

        String result = "";
        char c = str.charAt(0);
        result = result + Character.toLowerCase(c);
        for (int i = 1; i < str.length(); i++) {
            char ch = str.charAt(i);
            if (Character.isUpperCase(ch)) {
                result = result + '_';
                result
                        = result
                        + Character.toLowerCase(ch);
            } else {
                result = result + ch;
            }
        }
        return result;
    }
    public static boolean isEmpty(String s) {
        return s == null || s.trim().length() == 0;
    }
}
