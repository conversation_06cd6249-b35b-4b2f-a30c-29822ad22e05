package vn.agis.crm.base.utils;


import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import vn.agis.crm.base.core.filters.UserPrincipal;

import java.util.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigInteger;
import java.security.MessageDigest;

/**
 * Created by huyvv
 * Date: 03/06/2021
 * Time: 9:38 AM
 * for all issues, contact me: <EMAIL>
 **/
public class SecurityUtils {
    public static final String PATTERN_KEY = "KeyEncryptUser_";

    private SecurityUtils() {

    }

    public static String Encrypt(String key, String data) {
        try {
            if (key == null || key.isEmpty()) return data;
            Cipher cipher = Cipher.getInstance("TripleDES");
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(key.getBytes(), 0, key.length());
            String keyMd5 = new BigInteger(1, md5.digest()).toString(16).substring(0, 24);
            SecretKeySpec keySpec = new SecretKeySpec(keyMd5.getBytes(), "TripleDES");
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            byte[] stringBytes = data.getBytes();
            byte[] raw = cipher.doFinal(stringBytes);
            String result = Base64.getEncoder().encodeToString(raw);
            result = result.replaceAll("[\\r\\n\\s\\t]", "");
            return result;
        } catch (Exception ex) {
            return data;
        }
    }

    public static String Decrypt(String key, String data) {
        try {
            if (key == null || key.isEmpty()) return data;
            Cipher cipher = Cipher.getInstance("TripleDES");
            MessageDigest md5 = MessageDigest.getInstance("MD5");
            md5.update(key.getBytes(), 0, key.length());
            String keyMd5 = new
                    BigInteger(1, md5.digest()).toString(16).substring(0, 24);
            SecretKeySpec keySpec = new
                    SecretKeySpec(keyMd5.getBytes(), "TripleDES");
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            byte[] raw = Base64.getDecoder().decode(data);
            byte[] stringBytes = cipher.doFinal(raw);
            return new String(stringBytes);
        } catch (Exception ex) {
            return data;
        }
    }

    public static Long getUserId() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        Long userId = null;

        if (authentication != null) {
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                userId = ((UserPrincipal) authentication.getPrincipal()).getUserId();
            }
        }
        return userId;
    }

    public static String getUserName() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        String userName = null;

        if (authentication != null) {
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                userName = ((UserPrincipal) authentication.getPrincipal()).getUsername();
            }
        }
        return userName;
    }

    public static Integer getUserType() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        Integer userType = null;

        if (authentication != null) {
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                userType = ((UserPrincipal) authentication.getPrincipal()).getUserType();
            }
        }
        return userType;
    }
//
//    public static String getProvinceCode() {
//        SecurityContext securityContext = SecurityContextHolder.getContext();
//        Authentication authentication = securityContext.getAuthentication();
//        String provinceCode = null;
//
//        if (authentication != null) {
//            if (authentication.getPrincipal() instanceof UserPrincipal) {
//                provinceCode = ((UserPrincipal) authentication.getPrincipal()).getProvinceCode();
//            }
//        }
//        return provinceCode;
//    }

    public static Long getCurrentUserId() {
        return getUserId();
    }

    public static UserPrincipal getUserPrincipal() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();

        if (authentication != null) {
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                return (UserPrincipal) authentication.getPrincipal();
            }
        }
        return null;
    }

    public static String getIpAddress() {
        String ipAddress = null;
        Object details =
                SecurityContextHolder.getContext().getAuthentication().getDetails();
        if (details instanceof WebAuthenticationDetails) ipAddress = ((WebAuthenticationDetails) details).getRemoteAddress();
        return ipAddress;
    }

//    public static String getFullName() {
//        SecurityContext securityContext = SecurityContextHolder.getContext();
//        Authentication authentication = securityContext.getAuthentication();
//        String fullName = null;
//
//        if (authentication != null) {
//            if (authentication.getPrincipal() instanceof UserPrincipal) {
//                fullName = ((UserPrincipal) authentication.getPrincipal()).getFullName();
//            }
//        }
//        return fullName;
//    }

    public static String getEmail() {
        SecurityContext securityContext = SecurityContextHolder.getContext();
        Authentication authentication = securityContext.getAuthentication();
        String email = null;

        if (authentication != null) {
            if (authentication.getPrincipal() instanceof UserPrincipal) {
                email = ((UserPrincipal) authentication.getPrincipal()).getEmail();
            }
        }
        return email;
    }
}
