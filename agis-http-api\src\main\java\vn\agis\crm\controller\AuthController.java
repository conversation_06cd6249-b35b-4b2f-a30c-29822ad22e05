package vn.agis.crm.controller;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.agis.crm.base.constants.Constants.CRMService;
import vn.agis.crm.base.jpa.dto.LoginInfo;
import vn.agis.crm.base.jpa.dto.SSOLoginDTO;
import vn.agis.crm.base.jpa.dto.resp.AuthResponseWithTokenAndErrorCodeDTO;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.service.AuthService;

import java.util.Map;

/**
 * Created by huyvv
 * Date: 21/03/2020
 * Time: 8:39 AM
 * for all issues, contact me: <EMAIL>
 **/
@RestController
@RequestMapping("/auth")
public class AuthController {
    @SuppressWarnings("unused")
    private final Logger logger = LoggerFactory.getLogger(AuthController.class);

    private AuthService authService;

    @Autowired
    public AuthController(AuthService authService) {
        this.authService = authService;
    }

    @PostMapping(path = "/token")
    public ResponseEntity<JWTToken> token(@RequestBody LoginInfo loginInfo) {
        AuthResponseWithTokenAndErrorCodeDTO token = this.authService.token(loginInfo);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(CRMService.AUTH_HEADER_STRING, "Bearer " + token.getAccessToken());
        JWTToken jwtToken = new JWTToken(token.getAccessToken(), token.getErrorCode(), token.getNbf(), token.getExp());
        return new ResponseEntity<JWTToken>(jwtToken, httpHeaders, HttpStatus.OK);
    }

    @PostMapping(path = "/login")
    public ResponseEntity<JWTToken> login(@RequestBody LoginInfo loginInfo) {
        AuthResponseWithTokenAndErrorCodeDTO token = this.authService.login(loginInfo);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(CRMService.AUTH_HEADER_STRING, "Bearer " + token.getAccessToken());
        JWTToken jwtToken = new JWTToken(token.getAccessToken(), token.getErrorCode(), token.getNbf(), token.getExp());
        return new ResponseEntity<JWTToken>(jwtToken, httpHeaders, HttpStatus.OK);
    }

    // sso
    @PostMapping(path = "/sso/token")
    public ResponseEntity<JWTToken> ssoToken(@RequestBody SSOLoginDTO ssoLoginDTO) {
        AuthResponseWithTokenAndErrorCodeDTO token = this.authService.verifySsoToken(ssoLoginDTO);
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(CRMService.AUTH_HEADER_STRING, "Bearer " + token.getAccessToken());
        JWTToken jwtToken = new JWTToken(token.getAccessToken(), token.getErrorCode(), token.getNbf(), token.getExp());
        return new ResponseEntity<JWTToken>(jwtToken, httpHeaders, HttpStatus.OK);
    }


    @PostMapping(path = "/genAuthCode")
    public ResponseEntity<String> genAuthCode(@RequestBody Map<String,Object> params, HttpServletRequest request) {
        String authCode = this.authService.genAuthCode(params, request.getHeader(HttpHeaders.AUTHORIZATION));
        if(authCode != null){
            return new ResponseEntity<String>(ObjectMapperUtil.toJsonString(authCode), HttpStatus.OK);
        } else {
            return new ResponseEntity<String>(ObjectMapperUtil.toJsonString(authCode), HttpStatus.NOT_FOUND);
        }
    }

    /**
     * Object to return as body in JWT Authentication.
     */
    static class JWTToken {

        private String idToken;

        private String errorCode;

        @Getter
        @JsonProperty("nbf")
        private Long nbf;

        @Getter
        @JsonProperty("exp")
        private Long exp;

        JWTToken(String idToken, String errorCode, Long nbf, Long exp) {
            this.idToken = idToken;
            this.errorCode = errorCode;
            this.nbf = nbf;
            this.exp = exp;
        }

        @JsonProperty("id_token")
        String getIdToken() {
            return idToken;
        }

        @JsonProperty("error_code")
        String getErrorCode() {
            return errorCode;
        }

        void setIdToken(String idToken) {
            this.idToken = idToken;
        }

        void setErrorCode(String errorCode) {
            this.errorCode = errorCode;
        }

         void setNbf(Long nbf) {
            this.nbf = nbf;
        }

         void setExp(Long exp) {
            this.exp = exp;
        }

    }
}
