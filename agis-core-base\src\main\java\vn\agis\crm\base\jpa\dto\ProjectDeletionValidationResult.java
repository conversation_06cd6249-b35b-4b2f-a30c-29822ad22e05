package vn.agis.crm.base.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result of project deletion validation containing all dependency information
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDeletionValidationResult {
    
    /**
     * Whether the project can be safely deleted
     */
    private boolean canDelete;
    
    /**
     * List of dependency errors preventing deletion
     */
    private List<ProjectDependencyError> dependencies;
    
    /**
     * Overall validation message
     */
    private String message;
    
    /**
     * Project ID that was validated
     */
    private Long projectId;
    
    /**
     * Project name for reference
     */
    private String projectName;
    
    /**
     * Creates a successful validation result (no dependencies found)
     */
    public static ProjectDeletionValidationResult success(Long projectId, String projectName) {
        ProjectDeletionValidationResult result = new ProjectDeletionValidationResult();
        result.setCanDelete(true);
        result.setProjectId(projectId);
        result.setProjectName(projectName);
        result.setDependencies(new ArrayList<>());
        result.setMessage("Dự án có thể xóa an toàn.");
        return result;
    }
    
    /**
     * Creates a failed validation result with dependency errors
     */
    public static ProjectDeletionValidationResult failure(Long projectId, String projectName, 
                                                         List<ProjectDependencyError> dependencies) {
        ProjectDeletionValidationResult result = new ProjectDeletionValidationResult();
        result.setCanDelete(false);
        result.setProjectId(projectId);
        result.setProjectName(projectName);
        result.setDependencies(dependencies);
        result.setMessage(createFailureMessage(dependencies));
        return result;
    }
    
    /**
     * Creates an overall failure message based on dependency errors
     */
    private static String createFailureMessage(List<ProjectDependencyError> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return "Không thể xóa dự án do có phụ thuộc không xác định.";
        }

        if (dependencies.size() == 1) {
            return dependencies.get(0).getMessage();
        }

        StringBuilder message = new StringBuilder("Dự án đang được sử dụng, không thể xóa.");

        return message.toString();
    }
    
    /**
     * Adds a dependency error to the result
     */
    public void addDependencyError(ProjectDependencyError error) {
        if (this.dependencies == null) {
            this.dependencies = new ArrayList<>();
        }
        this.dependencies.add(error);
        this.canDelete = false;
        this.message = createFailureMessage(this.dependencies);
    }
    
    /**
     * Checks if there are any dependency errors
     */
    public boolean hasDependencies() {
        return dependencies != null && !dependencies.isEmpty();
    }
    
    /**
     * Gets the total count of all dependencies
     */
    public long getTotalDependencyCount() {
        if (dependencies == null) {
            return 0;
        }
        return dependencies.stream().mapToLong(ProjectDependencyError::getCount).sum();
    }
}
