# Lead Uncared Warning Notification Scheduler

## Overview

A comprehensive scheduled job system for automatically sending lead uncared warning notifications to employees in the AGIS CRM system. The scheduler runs daily at 8:00 AM (Vietnam timezone) and creates notifications for leads that have been assigned but haven't had any interactions within the configured warning period.

## ✅ **Implementation Details**

### **Scheduler Configuration**
```java
@Component
@Scheduled(cron = "0 0 8 * * *", zone = "Asia/Ho_Chi_Minh")
public class LeadUncaredWarningNotificationScheduler {
    // Daily execution at 8:00 AM Vietnam time
}
```

### **System Configuration**
- **Config Key**: `"NOTIFICATION_LEAD_UNCARED_WARNING_DAYS"`
- **Config Value**: Number of days after assignment to send warning (e.g., "3" for 3 days)
- **Config Type**: 1 (single value)
- **Validation**: Must be integer between 1-365

### **Lead Detection Criteria**
The scheduler identifies uncared leads using these criteria:

1. **Active Assignment**: `assigned_to IS NULL` (currently active)
2. **Assignment Age**: `assigned_from <= (current_date - warning_days)`
3. **Lead Source**: `source_type = 'Leads'` (only lead customers)
4. **Not Deleted**: `deleted_at IS NULL` (active customers only)
5. **No Interactions**: No primary or secondary interactions after `assigned_from`

## ✅ **Core Functionality**

### **1. Configuration Validation**
```java
private Integer getWarningDaysFromConfig() {
    Config config = configRepository.findOneByConfigKeyIgnoreCase("NOTIFICATION_LEAD_UNCARED_WARNING_DAYS");
    // Validates existence, format, and range (1-365 days)
}
```

### **2. Uncared Lead Detection**
```java
private List<CustomerAssignments> findUncaredLeadAssignments(int warningDays) {
    Date cutoffDate = Date.from(LocalDate.now().minusDays(warningDays).atStartOfDay().toInstant());
    // Uses CustomerAssignmentRepository.findActiveAssignmentsBeforeDate()
}
```

### **3. Interaction Checking**
```java
private boolean hasInteractionsAfterAssignment(Customers customer, Date assignedFrom) {
    // Check primary interactions through customer_offers -> interactions_primary
    // Check secondary interactions through customer_properties -> interactions_secondary
    // Returns true if any interaction.happened_at > assignedFrom
}
```

### **4. Warning Notification Creation**
```java
Notifications notification = notificationService.createNotification(
    employee.getId(),
    3, // LeadUncaredWarning type
    "Cảnh báo lead chưa được chăm sóc",
    content,
    customer.getId(),
    null // System-generated
);
```

## ✅ **Notification Content Templates**

### **Standard Warning Message**
```
"Lead [Tên khách hàng] (SĐT: [phone]) đã được phân công cho bạn từ ngày [dd/MM/yyyy] nhưng chưa có tương tác nào. Hãy liên hệ khách hàng sớm nhất có thể."
```

### **Example Content**
```
"Lead Nguyễn Văn A (SĐT: 0901234567) đã được phân công cho bạn từ ngày 20/12/2024 nhưng chưa có tương tác nào. Hãy liên hệ khách hàng sớm nhất có thể."
```

## ✅ **Database Integration**

### **Repository Dependencies**
- **CustomerAssignmentRepository**: Find active assignments before cutoff date
- **CustomerRepository**: Get customer details and validate source type
- **CustomerOfferRepository**: Get customer offers for primary interaction checking
- **CustomerPropertyRepository**: Get customer properties for secondary interaction checking
- **InteractionsPrimaryRepository**: Check primary interactions by customer offer ID
- **InteractionsSecondaryRepository**: Check secondary interactions by customer property ID
- **EmployeeRepository**: Validate employee status for notifications
- **ConfigRepository**: Read warning days configuration

### **New Repository Method**
```java
// Added to CustomerAssignmentRepository
@Query("SELECT ca FROM CustomerAssignments ca WHERE ca.assignedTo IS NULL AND ca.assignedFrom <= :cutoffDate ORDER BY ca.assignedFrom ASC")
List<CustomerAssignments> findActiveAssignmentsBeforeDate(@Param("cutoffDate") Date cutoffDate);
```

## ✅ **Interaction Detection Logic**

### **Primary Interactions (Customer Offers)**
```java
// Check interactions through customer_offers -> interactions_primary
List<CustomerOffers> customerOffers = customerOfferRepository.findByCustomerId(customer.getId());
for (CustomerOffers offer : customerOffers) {
    List<InteractionsPrimary> primaryInteractions = interactionsPrimaryRepository.findByCustomerOfferId(offer.getId());
    // Check if any interaction.happenedAt > assignedFrom
}
```

### **Secondary Interactions (Customer Properties)**
```java
// Check interactions through customer_properties -> interactions_secondary
List<CustomerProperties> customerProperties = customerPropertyRepository.findByCustomerId(customer.getId());
for (CustomerProperties property : customerProperties) {
    List<InteractionsSecondary> secondaryInteractions = interactionsSecondaryRepository.findByCustomerPropertyId(property.getId());
    // Check if any interaction.happenedAt > assignedFrom
}
```

## ✅ **Error Handling & Reliability**

### **Transaction Management**
```java
@Transactional(propagation = Propagation.REQUIRES_NEW)
public boolean processUncaredLeadNotification(CustomerAssignments assignment, int warningDays) {
    // Each notification processed in separate transaction
    // Prevents rollback of entire batch on individual failures
}
```

### **Comprehensive Error Handling**
- **Configuration Errors**: Invalid/missing config gracefully handled
- **Assignment Processing Errors**: Individual failures don't stop batch processing
- **Employee Validation Errors**: Inactive employees skipped with logging
- **Interaction Check Errors**: Assumes no interactions on error (safe approach)
- **Notification Creation Errors**: Logged but don't crash job

### **Logging Strategy**
```java
// Job-level logging
logger.info("🚨 Starting lead uncared warning notification job");
logger.info("Found {} uncared lead assignments", uncaredAssignments.size());
logger.info("🚨 Job completed. Processed: {}, Success: {}, Errors: {}", total, success, errors);

// Individual processing logging
logger.debug("Customer {} is an uncared lead", customer.getId());
logger.debug("Created uncared lead notification {} for employee {} about customer {}", ...);
logger.warn("Employee {} for assignment {} is not active, skipping notification", ...);
```

## ✅ **Performance Optimizations**

### **Efficient Database Queries**
- **Single Configuration Query**: One-time config lookup per job run
- **Optimized Assignment Search**: Uses indexed queries with date filtering
- **Batch Customer Processing**: Processes assignments in order of assignment date
- **Lazy Interaction Loading**: Only checks interactions for potential uncared leads

### **Memory Management**
- **Streaming Processing**: Processes assignments one by one
- **Transaction Boundaries**: Separate transactions prevent memory buildup
- **Efficient Filtering**: Early filtering reduces unnecessary processing

## ✅ **Configuration Examples**

### **Enable 3-Day Warning**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_UNCARED_WARNING_DAYS', '3', 1, 'Số ngày cảnh báo lead chưa được chăm sóc', NOW(), 1);
```

### **Same-Day Warnings**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_UNCARED_WARNING_DAYS', '1', 1, 'Cảnh báo lead chưa được chăm sóc sau 1 ngày', NOW(), 1);
```

### **Weekly Warnings**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_UNCARED_WARNING_DAYS', '7', 1, 'Cảnh báo lead chưa được chăm sóc sau 1 tuần', NOW(), 1);
```

### **Disable Warnings**
```sql
-- Option 1: Delete the config
DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- Option 2: Set empty value
UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- Option 3: Set invalid value
UPDATE configs SET config_value = 'DISABLED' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';
```

## ✅ **Integration Features**

### **Spring Boot Integration**
- Uses existing `@EnableScheduling` from CustomerBirthdayNotificationScheduler
- Scheduler automatically discovered and registered
- Shares timezone configuration with other schedulers

### **Timezone Support**
- **Vietnam Timezone**: All date calculations use `Asia/Ho_Chi_Minh`
- **Consistent Timing**: Job runs at 8:00 AM local time
- **Date Formatting**: Assignment dates formatted according to Vietnamese conventions (dd/MM/yyyy)

### **Existing Service Integration**
- **NotificationService**: Uses existing `createNotification()` method
- **ConfigRepository**: Leverages existing configuration system
- **All Repositories**: Uses existing repository methods and patterns

## ✅ **Monitoring & Observability**

### **Job Execution Metrics**
```java
// Logged metrics per job run:
- Total assignments processed
- Successful notifications created
- Error count
- Job start/completion times
- Configuration values used
```

### **Health Indicators**
```java
// Warning conditions logged:
- No uncared assignments found
- Configuration disabled/missing
- Inactive employees assigned to leads
- Individual notification creation failures
```

### **Debug Information**
```java
// Debug-level logging includes:
- Cutoff date calculations
- Lead filtering logic
- Interaction checking details
- Individual assignment processing
```

## ✅ **Production Considerations**

### **Scalability**
- **Batch Processing**: Processes all uncared assignments in single job run
- **Future Enhancement**: Consider pagination for large assignment volumes
- **Database Load**: Optimized queries minimize database impact
- **Memory Usage**: Streaming approach prevents memory issues

### **Reliability**
- **Idempotent**: Safe to run multiple times (notifications have unique constraints)
- **Fault Tolerant**: Individual failures don't affect other assignments
- **Recoverable**: Failed assignments can be reprocessed manually if needed

### **Maintenance**
- **Configuration Changes**: No code changes needed to adjust warning timing
- **Assignment Changes**: Automatically adapts to new assignments
- **Interaction Changes**: Automatically detects new interactions

## ✅ **Testing Recommendations**

### **Unit Tests**
1. Configuration parsing and validation
2. Date calculation logic (timezone handling)
3. Lead filtering logic (source type, deletion status)
4. Interaction detection logic
5. Notification content generation
6. Error handling scenarios

### **Integration Tests**
1. End-to-end job execution with test data
2. Database transaction behavior
3. NotificationService integration
4. Configuration change effects
5. Multiple assignment scenarios

### **Manual Testing**
1. Test with various configuration values (1, 3, 7, 30 days)
2. Test with leads having different interaction patterns
3. Test with inactive employees
4. Test timezone behavior across different server locations

## ✅ **SQL Monitoring Queries**

### **Check Uncared Leads (Manual Query)**
```sql
-- Find leads that would trigger warnings with current configuration
SELECT
    c.id,
    c.full_name,
    c.phone,
    c.source_type,
    ca.assigned_from,
    e.full_name as employee_name,
    DATEDIFF(CURDATE(), DATE(ca.assigned_from)) as days_since_assignment
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN employees e ON ca.employee_id = e.id
WHERE c.source_type = 'Leads'
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
  AND ca.assigned_from <= DATE_SUB(CURDATE(), INTERVAL 3 DAY)  -- Adjust based on config
ORDER BY ca.assigned_from ASC;
```

### **Check Recent Warning Notifications**
```sql
-- Check lead uncared warning notifications from last 7 days
SELECT
    n.id,
    n.target_employee_id,
    n.target_customer_id,
    n.title,
    n.content,
    n.is_read,
    n.created_at,
    c.full_name as customer_name,
    c.phone as customer_phone,
    e.full_name as employee_name
FROM notifications n
LEFT JOIN customers c ON n.target_customer_id = c.id
LEFT JOIN employees e ON n.target_employee_id = e.id
WHERE n.type = 3  -- LeadUncaredWarning type
  AND n.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY n.created_at DESC;
```

### **Check Warning Statistics**
```sql
-- Warning notification statistics by date
SELECT
    DATE(created_at) as notification_date,
    COUNT(*) as warning_count,
    COUNT(DISTINCT target_customer_id) as unique_leads,
    COUNT(DISTINCT target_employee_id) as unique_employees
FROM notifications
WHERE type = 3  -- LeadUncaredWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY notification_date DESC;
```

This implementation provides a robust, configurable, and maintainable solution for automatic lead uncared warning notifications while following AGIS CRM architectural patterns and coding standards.
