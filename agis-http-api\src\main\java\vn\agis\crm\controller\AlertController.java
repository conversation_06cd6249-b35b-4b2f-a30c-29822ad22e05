package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.ChangeStatusAlertReqDTO;
import vn.agis.crm.base.jpa.dto.req.CreateAlertReq;
import vn.agis.crm.base.jpa.dto.req.SearchAlertReqDTO;
import vn.agis.crm.base.jpa.dto.req.SearchHistoryAlertRequest;
import vn.agis.crm.base.jpa.dto.req.UpdateAlertReq;
import vn.agis.crm.base.jpa.dto.resp.AlertFullNameResponse;
import vn.agis.crm.base.jpa.dto.resp.AlertHistoryResponseDTO;
import vn.agis.crm.base.jpa.dto.resp.DetailAlertResponse;
import vn.agis.crm.base.jpa.entity.Alert;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.AlertService;

@RestController
@RequestMapping("/alerts")
public class AlertController extends CrudController<Alert, Long>{
    private final Logger log = LoggerFactory.getLogger(AlertController.class);

    AlertService alertService;

    @Autowired
    public AlertController(AlertService service) {
        super(service);
        this.alertService = service;
        this.baseUrl = "/alerts";
    }

    @GetMapping("/search")
    @CrossOrigin
    @Operation(description = "Danh sách Alert")
    @PreAuthorize("hasAnyAuthority('searchAlert')")
    public ResponseEntity<Page<AlertFullNameResponse>> searchAPN(
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "deviceTypeId", required = false, defaultValue = "-1") Integer deviceTypeId,
            @RequestParam(name = "modelCode", required = false, defaultValue = " ") String modelCode,
            @RequestParam(name = "typeCode", required = false, defaultValue = " ") String typeCode,
            @RequestParam(name = "eventType", required = false, defaultValue = "-1") Integer eventType,
            @RequestParam(name = "status", required = false, defaultValue = "-1") Integer status,
            @RequestParam(name = "severity", required = false, defaultValue = "-1") Integer severity,
            @RequestParam(name = "username", required = false, defaultValue = " ") String username,
            @RequestParam(name = "fromDate", required = false, defaultValue = " ") Long fromDate,
            @RequestParam(name = "toDate", required = false, defaultValue = " ") Long toDate,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "alertName,desc") String sortBy) {
        log.info("--- Execute searchAlert method: Start --");
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        SearchAlertReqDTO searchDTO = new SearchAlertReqDTO(name, deviceTypeId,modelCode, typeCode, eventType, status, severity, username, fromDate, toDate, page, size, sortBy);
        Page<AlertFullNameResponse> alerts = alertService.searchAlert(searchDTO, listRequest.getPageable());
        log.info("--- Execute searchAlert method: End --");
        return ResponseEntity.ok().body(alerts);
    }
    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('getAlert')")
    public ResponseEntity<DetailAlertResponse> detailAlert(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        DetailAlertResponse result = new DetailAlertResponse();
        result = alertService.getAlertDetail(id);
        return ResponseEntity.ok().body(result);
    }
    @PostMapping()
    @PreAuthorize("hasAnyAuthority('createAlert')")
    public ResponseEntity<Alert> createAlert(@RequestBody CreateAlertReq entity, HttpServletRequest httpServletRequest) {
        Alert ratingPlan = alertService.createAlert(entity);
        return ResponseEntity.ok().body(ratingPlan);
    }


    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('updateAlert')")
    public ResponseEntity<Alert> updateAlert(@PathVariable Long id, @RequestBody UpdateAlertReq entity, HttpServletRequest httpServletRequest) {
        entity.setId(id);
        Alert ratingPlan = alertService.update( entity);
        return ResponseEntity.ok().body(ratingPlan);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('deleteAlert')")
    public ResponseEntity<ResponseBase> deleteAlert(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = alertService.deleteAlert(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @PostMapping("/status")
    @PreAuthorize("hasAnyAuthority('changeStatusAlert')")
    public ResponseEntity<ResponseBase> changeStatusAlert(@RequestBody ChangeStatusAlertReqDTO entity, HttpServletRequest httpServletRequest) {
        ResponseBase ratingPlan = alertService.changeStatusAlert(entity);
        return ResponseEntity.ok().body(ratingPlan);
    }

    @GetMapping("/check-exist")
    public Integer changeExistName(@RequestParam String name) {
        return alertService.checkExist(name);
    }

    @GetMapping("/history")
    public Page<AlertHistoryResponseDTO> searchHistory(
        @RequestParam(name = "eventType", required = false, defaultValue = "-1") Integer eventType,
        @RequestParam(name = "fromDate", required = false, defaultValue = " ") Long fromDate,
        @RequestParam(name = "toDate", required = false, defaultValue = " ") Long toDate,
        @RequestParam(name = "deviceId", required = false, defaultValue = "-1") Long deviceId,
        @RequestParam(name = "userId", required = false, defaultValue = "-1") Long userId,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @RequestParam(name = "sort", required = false, defaultValue = "raisedDate,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        SearchHistoryAlertRequest request = new SearchHistoryAlertRequest(deviceId,userId,eventType, fromDate, toDate, page, size,
            sortBy);
        return alertService.searchHistory(request, listRequest.getPageable());
    }

}
