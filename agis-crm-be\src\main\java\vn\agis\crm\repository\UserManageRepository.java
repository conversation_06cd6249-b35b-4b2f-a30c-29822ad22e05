package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import vn.agis.crm.base.jpa.entity.UserManage;

import java.util.List;

public interface UserManageRepository extends JpaRepository<UserManage, Long> {
    List<UserManage> findDistinctByUserManageId(Long id);
    @Modifying
    void deleteByUserId(Long userId);
    @Modifying
    void deleteByUserIdIn(List<Long> userIds);
    UserManage getUserManageByUserId(Long userId);

    @Query("SELECT u.userId FROM UserManage u WHERE u.userManageId = :userManageId")
    List<Long> getUserIdByUserManageId(Long userManageId);
}
