package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class ImportResultDto {
    private Long jobId;
    private String fileName;
    private String status; // SUCCESS, FAILED, CANCELLED
    private Date startedAt;
    private Date finishedAt;
    private Long totalProcessingTimeMs;
    private String totalProcessingTime; // Human readable format
    
    // Import statistics
    private Integer totalRows;
    private Integer processedRows;
    private Integer successfulRows;
    private Integer failedRows;
    private Integer skippedRows;
    private Double successRate; // Percentage of successful rows
    
    // Database operations summary
    private DatabaseOperationsDto databaseOperations;
    
    // Error and warning summary
    private Integer totalErrors;
    private Integer totalWarnings;
    private List<ErrorSummaryDto> errorSummary;
    private List<String> warnings;
    
    // Performance metrics
    private Double averageRowProcessingTimeMs;
    private Integer batchesProcessed;
    private Long peakMemoryUsageMB;
    
    // Cancellation information (if applicable)
    private Boolean wasCancelled;
    private String cancellationReason;
    private Date cancelledAt;
    
    // File processing details
    private String fileFormat;
    private Long fileSizeBytes;
    private String fileChecksum;
    
    // Validation summary (from dry-run)
    private ValidationSummaryDto validationSummary;
    
    @Data
    public static class DatabaseOperationsDto {
        private Integer customersCreated;
        private Integer customersUpdated;
        private Integer customersSkipped;
        private Integer relativesCreated;
        private Integer propertiesCreated;
        private Integer offersCreated;
        private Integer interactionsCreated;
        private Integer assignmentsCreated;
        
        // Transaction statistics
        private Integer transactionsCommitted;
        private Integer transactionsRolledBack;
        private List<String> rollbackReasons;
        
        public Integer getTotalCustomerOperations() {
            return (customersCreated != null ? customersCreated : 0) + 
                   (customersUpdated != null ? customersUpdated : 0);
        }
        
        public Integer getTotalRecordsCreated() {
            return (customersCreated != null ? customersCreated : 0) +
                   (relativesCreated != null ? relativesCreated : 0) +
                   (propertiesCreated != null ? propertiesCreated : 0) +
                   (offersCreated != null ? offersCreated : 0) +
                   (interactionsCreated != null ? interactionsCreated : 0) +
                   (assignmentsCreated != null ? assignmentsCreated : 0);
        }
    }
    
    @Data
    public static class ErrorSummaryDto {
        private String errorType;
        private String errorDescription;
        private Integer count;
        private List<Integer> affectedRows;
        private String severity; // ERROR, WARNING, INFO
        private List<String> sampleErrors; // First few error messages for reference
    }
    
    @Data
    public static class ValidationSummaryDto {
        private Integer validRowsFromDryRun;
        private Integer errorRowsFromDryRun;
        private Integer warningRowsFromDryRun;
        private Boolean dryRunCompleted;
        private Date dryRunCompletedAt;
        
        // Comparison between dry-run and actual execution
        private Integer actualValidRows;
        private Integer actualErrorRows;
        private String consistencyStatus; // "CONSISTENT", "INCONSISTENT", "UNKNOWN"
        private List<String> consistencyNotes;
    }
    
    public ImportResultDto() {}
    
    public ImportResultDto(Long jobId, String fileName, String status) {
        this.jobId = jobId;
        this.fileName = fileName;
        this.status = status;
        this.finishedAt = new Date();
        this.wasCancelled = false;
        this.totalErrors = 0;
        this.totalWarnings = 0;
    }
    
    /**
     * Calculate success rate based on processed rows
     */
    public void calculateSuccessRate() {
        if (processedRows != null && processedRows > 0 && successfulRows != null) {
            this.successRate = (successfulRows.doubleValue() / processedRows.doubleValue()) * 100.0;
        } else {
            this.successRate = 0.0;
        }
    }
    
    /**
     * Calculate total processing time and format it
     */
    public void calculateProcessingTime() {
        if (startedAt != null && finishedAt != null) {
            this.totalProcessingTimeMs = finishedAt.getTime() - startedAt.getTime();
            this.totalProcessingTime = formatDuration(totalProcessingTimeMs);
            
            if (processedRows != null && processedRows > 0) {
                this.averageRowProcessingTimeMs = totalProcessingTimeMs.doubleValue() / processedRows.doubleValue();
            }
        }
    }
    
    /**
     * Format duration in milliseconds to human readable format
     */
    private String formatDuration(Long durationMs) {
        if (durationMs == null || durationMs <= 0) {
            return "Unknown";
        }
        
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d hours %d minutes %d seconds", hours, minutes % 60, seconds % 60);
        } else if (minutes > 0) {
            return String.format("%d minutes %d seconds", minutes, seconds % 60);
        } else {
            return String.format("%d seconds", seconds);
        }
    }
    
    /**
     * Check if import was successful
     */
    public boolean isSuccessful() {
        return "SUCCESS".equals(status);
    }
    
    /**
     * Check if import was cancelled
     */
    public boolean wasCancelled() {
        return Boolean.TRUE.equals(wasCancelled) || "CANCELLED".equals(status);
    }
    
    /**
     * Get overall import quality assessment
     */
    public String getQualityAssessment() {
        if (successRate == null) {
            return "UNKNOWN";
        } else if (successRate >= 95.0) {
            return "EXCELLENT";
        } else if (successRate >= 85.0) {
            return "GOOD";
        } else if (successRate >= 70.0) {
            return "FAIR";
        } else {
            return "POOR";
        }
    }
}
