package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.jpa.dto.DryRunResultDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.entity.ImportJob;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.utils.SpringContextUtils;
import vn.agis.crm.repository.CustomerRepository;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.*;

/**
 * Main processor for dry-run validation
 * Orchestrates file parsing, validation, and statistics calculation
 */
public class ImportDryRunProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportDryRunProcessor.class);
    
    /**
     * Process dry-run validation for an import job
     */
    public static DryRunResultDto processDryRun(ImportJob job, String filePath) {
        Date startTime = new Date();
        
        try {
            logger.info("Starting dry-run processing for job {} with file {}", job.getId(), job.getFileName());
            
            // Parse file data
            List<LinkedHashMap<String, String>> rowsData = parseFile(filePath, job.getFileName(), job.getOptions());
            
            // Validate data
            List<ValidationResultDto> validationResults = validateData(rowsData, job.getId());
            
            // Calculate statistics
            Date endTime = new Date();
            DryRunResultDto result = ImportStatisticsCalculator.calculateStatistics(
                job.getId(), job.getFileName(), validationResults, startTime, endTime);
            
            logger.info("Completed dry-run processing for job {}. Total rows: {}, Valid: {}, Errors: {}", 
                       job.getId(), result.getTotalRows(), result.getValidRows(), result.getErrorRows());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing dry-run for job " + job.getId(), e);
            
            // Return error result
            DryRunResultDto errorResult = new DryRunResultDto();
            errorResult.setJobId(job.getId());
            errorResult.setFileName(job.getFileName());
            errorResult.setStatus("FAILED");
            errorResult.setStartedAt(startTime);
            errorResult.setFinishedAt(new Date());
            errorResult.setTotalRows(0);
            errorResult.setValidRows(0);
            errorResult.setErrorRows(0);
            errorResult.setWarnings(Arrays.asList("Xử lý thất bại: " + e.getMessage()));
            
            return errorResult;
        }
    }
    
    /**
     * Parse file based on format
     */
    static List<LinkedHashMap<String, String>> parseFile(String filePath, String fileName, String options) throws IOException {
        logger.debug("Parsing file: {}", fileName);
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            String extension = getFileExtension(fileName);
            
            switch (extension.toLowerCase()) {
                case "csv":
                    return vn.agis.crm.util.ImportDataParser.parseCSV(fis, "UTF-8");
                    
                case "xlsx":
                case "xls":
                    // Extract sheet name from options if provided
                    String sheetName = extractSheetName(options);
                    return ImportDataParser.parseExcel(fis, fileName, sheetName);
                    
                default:
                    throw new IOException("Unsupported file format: " + extension);
            }
        }
    }
    
    /**
     * Validate all rows of data with enhanced database checking
     */
    static List<ValidationResultDto> validateData(List<LinkedHashMap<String, String>> rowsData, Long jobId) {
        logger.debug("Validating {} rows of data with enhanced validation", rowsData.size());

        List<ValidationResultDto> results = new ArrayList<>();
        Set<String> existingPhones = loadExistingPhonesFromDatabase();
        Set<String> filePhones = new HashSet<>();

        for (LinkedHashMap<String, String> rowData : rowsData) {
            ValidationResultDto validation = ImportDataValidator.validateRow(
                rowData, jobId, existingPhones, filePhones);
            results.add(validation);
        }

        logger.debug("Validation completed. {} rows processed", results.size());
        return results;
    }
    
    /**
     * Load existing phone numbers from database for duplicate checking
     */
    private static Set<String> loadExistingPhonesFromDatabase() {
        try {
            logger.debug("Loading existing phone numbers from database");

            // Get CustomerRepository from Spring context using existing SpringContextUtils
            CustomerRepository customerRepository = SpringContextUtils.getBean(CustomerRepository.class);

            // Get all customer phones from database
            List<Customers> customers = customerRepository.findAll();
            Set<String> existingPhones = new HashSet<>();

            for (Customers customer : customers) {
                if (customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                    // Normalize phone for comparison
                    String normalizedPhone = normalizePhoneForComparison(customer.getPhone());
                    if (normalizedPhone != null) {
                        existingPhones.add(normalizedPhone);
                    }
                }

                // Also check additional phones if available
                if (customer.getAdditionalPhones() != null) {
                    for (String additionalPhone : customer.getAdditionalPhones()) {
                        if (additionalPhone != null && !additionalPhone.trim().isEmpty()) {
                            String normalizedPhone = normalizePhoneForComparison(additionalPhone);
                            if (normalizedPhone != null) {
                                existingPhones.add(normalizedPhone);
                            }
                        }
                    }
                }
            }

            logger.debug("Loaded {} unique phone numbers from database", existingPhones.size());
            return existingPhones;

        } catch (Exception e) {
            logger.warn("CustomerRepository not available or error loading phones, skipping database phone check: {}", e.getMessage());
            return new HashSet<>();
        }
    }

    /**
     * Remove Excel formatting artifacts from phone numbers
     */
    private static String removeExcelFormattingArtifacts(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }

        String cleaned = phone.trim();

        // Remove leading single quotes that Excel adds to force text interpretation
        if (cleaned.startsWith("'")) {
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    }

    /**
     * Normalize phone number for comparison (same logic as in validator)
     */
    private static String normalizePhoneForComparison(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }

        // Remove Excel formatting artifacts first
        String cleanPhone = removeExcelFormattingArtifacts(phone);
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");

        // Convert to +84 format
        if (cleanPhone.startsWith("0")) {
            cleanPhone = "+84" + cleanPhone.substring(1);
        } else if (cleanPhone.startsWith("84") && !cleanPhone.startsWith("+84")) {
            cleanPhone = "+" + cleanPhone;
        } else if (!cleanPhone.startsWith("+")) {
            cleanPhone = "+84" + cleanPhone;
        }

        return cleanPhone;
    }
    
    /**
     * Extract file extension from filename
     */
    private static String getFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "";
        }
        
        int lastDot = fileName.lastIndexOf('.');
        if (lastDot == -1) {
            return "";
        }
        
        return fileName.substring(lastDot + 1);
    }
    
    /**
     * Extract sheet name from options JSON
     */
    private static String extractSheetName(String options) {
        // Simple extraction - in real implementation, use JSON parser
        if (options != null && options.contains("sheet_name")) {
            try {
                // Basic regex to extract sheet_name value
                String[] parts = options.split("\"sheet_name\"\\s*:\\s*\"");
                if (parts.length > 1) {
                    String[] valueParts = parts[1].split("\"");
                    if (valueParts.length > 0) {
                        return valueParts[0];
                    }
                }
            } catch (Exception e) {
                logger.warn("Error extracting sheet name from options: {}", options, e);
            }
        }
        return null; // Use default sheet
    }
    
    /**
     * Validate file before processing
     */
    public static void validateFileForProcessing(String filePath, String fileName) throws IOException {
        // Check file exists
        java.io.File file = new java.io.File(filePath);
        if (!file.exists()) {
            throw new IOException("File not found: " + filePath);
        }
        
        // Check file size
        long maxSize = ImportConfigurationManager.getMaxFileSizeBytes();
        if (file.length() > maxSize) {
            throw new IOException("File size exceeds maximum allowed: " + file.length() + " bytes");
        }
        
        // Check file format
        if (!ImportConfigurationManager.isFormatSupported(fileName)) {
            throw new IOException("Unsupported file format: " + fileName);
        }
        
        // Check file is readable
        if (!file.canRead()) {
            throw new IOException("File is not readable: " + filePath);
        }
    }
    
    /**
     * Estimate processing time based on file size and content
     */
    public static long estimateProcessingTime(String filePath, String fileName) {
        try {
            java.io.File file = new java.io.File(filePath);
            long fileSize = file.length();
            
            // Rough estimation: 1MB = ~1000 rows, each row takes ~50ms to process
            long estimatedRows = fileSize / 1024; // Very rough estimate
            long avgTimePerRow = ImportConfigurationManager.getAvgProcessingTimePerRowMs();
            
            return estimatedRows * avgTimePerRow;
            
        } catch (Exception e) {
            logger.warn("Error estimating processing time for file: {}", filePath, e);
            return 60000; // Default to 1 minute
        }
    }
    
    /**
     * Check if dry-run processing should be stopped due to too many errors
     */
    public static boolean shouldStopProcessing(List<ValidationResultDto> results, Map<String, Object> options) {
        Boolean stopOnError = (Boolean) options.get("stop_on_error");
        if (stopOnError == null || !stopOnError) {
            return false;
        }
        
        // Count critical errors
        long criticalErrors = results.stream()
            .flatMap(r -> r.getErrors() != null ? r.getErrors().stream() : java.util.stream.Stream.empty())
            .filter(error -> "CRITICAL".equals(error.getSeverity()))
            .count();
        
        return criticalErrors > 0;
    }
    
    /**
     * Generate processing summary for logging
     */
    public static String generateProcessingSummary(DryRunResultDto result) {
        return String.format(
            "Dry-run completed for job %d: %d total rows, %d valid, %d errors, %d warnings (Processing time: %s)",
            result.getJobId(),
            result.getTotalRows(),
            result.getValidRows(), 
            result.getErrorRows(),
            result.getWarningRows(),
            result.getEstimation() != null ? result.getEstimation().getEstimatedProcessingTime() : "unknown"
        );
    }
}
