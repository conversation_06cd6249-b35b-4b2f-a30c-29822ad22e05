package vn.agis.crm.service;

import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.req.DashboardConfigCreateDTO;
import vn.agis.crm.base.jpa.entity.DashboardConfig;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class DashboardConfigService extends CrudService<DashboardConfig, Long>{

    private static final String objectKey = "DashboardConfig";

    public DashboardConfigService() {
        super(DashboardConfig.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_REPORT;
        this.category = Constants.Category.DASHBOARD_CONFIG;
    }

    public List<DashboardConfig> getListDashboardConfig (Long userId) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<DashboardConfig> responseList = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DASHBOARD_CONFIG, category, userId, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                List<DashboardConfig> responses = (List<DashboardConfig>) event.payload;
                responseList.addAll(responses);

                return responseList;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(userId), ObjectMapperUtil.toJsonString(responseList), null, null, event);
        }
    }

//    public Set<DashboardConfig> getDetail (DetailDBConfigDTO detailDTO) {
//        Event event = null;
//        event = RequestUtils.amqp(JpaConstants.Method.GET_ONE, category, detailDTO, routingKey);
//        if (event.respStatusCode == ResponseCode.OK) {
//            Set<DashboardConfig> response = (Set<DashboardConfig>) event.payload;
//
//            return response;
//        }
//
//        return null;
//    }
//
//    public boolean updateDashboardConfig (List<DashboardConfigUpdateDTO> listRequest) {
////        Event event = null;
////        event = RequestUtils.amqpWithCollectionsPayload(JpaConstants.Method.UPDATE, category, listRequest, routingKey);
////        if (event.respStatusCode == ResponseCode.OK) {
////            return true;
////        }
//
//        return false;
//    }

    public boolean createDashboardConfig (List<DashboardConfigCreateDTO> requestBody) {
        Event event = null;
        event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, requestBody, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return true;
        }

        return false;
    }
}
