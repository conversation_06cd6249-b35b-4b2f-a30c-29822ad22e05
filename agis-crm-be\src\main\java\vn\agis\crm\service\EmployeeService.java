package vn.agis.crm.service;

import static vn.agis.crm.base.constants.Constants.OAuth2IoT.CLIENT_ID;
import static vn.agis.crm.base.constants.Constants.OAuth2IoT.SECRET_ID;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants.CRMService;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.Constants.UserStatus;
import vn.agis.crm.base.constants.MessageKeyConstant.Validation;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.LoginInfo;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.Auth2RequestInit;
import vn.agis.crm.base.jpa.dto.req.CreateEmployeeReq;
import vn.agis.crm.base.jpa.dto.req.UpdateEmployeeReq;
import vn.agis.crm.base.jpa.dto.resp.Auth2Response;
import vn.agis.crm.base.jpa.dto.resp.AuthResponseWithTokenAndErrorCodeDTO;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.SimpleRole;
import vn.agis.crm.config.ApplicationProperties;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.repository.PermissionsRepository;
import vn.agis.crm.repository.SimpleRoleRepository;
import vn.agis.crm.util.BaseController;

import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.List;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

@Service
@Transactional
public class EmployeeService {

    private static Logger logger = LoggerFactory.getLogger(EmployeeService.class);

    private final int authorizationCodeLength = 36;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private ApplicationProperties applicationProperties;

    @Autowired
    private SecurityService securityService;

    @Autowired
    private SimpleRoleRepository simpleRoleRepository;

    @Autowired
    private PermissionsRepository permissionsRepository;

    @Autowired
    private OAuth2Service oAuth2Util;

    public Event process(Event event) {
        switch (event.method) {
            case Method.LOGIN:
                return login(event);
            case Method.VALIDATE_PERMISSION:
                return validatePermission(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
            case JpaConstants.Method.SEARCH:
                return processSearch(event);
            case JpaConstants.Method.CREATE:
                return processCreate(event);
            case JpaConstants.Method.UPDATE:
                return processUpdate(event);
            case JpaConstants.Method.DELETE:
                return processDelete(event);
            case JpaConstants.Method.GET_ALL:
                return processGetAll(event);
            case JpaConstants.Method.UPDATE_ONE:
                return processUpdateStatus(event);
            case JpaConstants.Method.CHECK_EXITS:
                return processCheckExits(event);
            case Method.CURRENT_USER:
                return processCurrentEmployee(event);
        }
        return event;
    }

    private Event login(Event event) {
        LoginInfo loginInfo = (LoginInfo) event.payload;
        Employee employee = this.findByEmailOrEmployeeCode(loginInfo.getEmail());

        if (employee == null) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.EMAIL_INCORRECT);
        }

        if (Boolean.FALSE.equals(employee.authenticate(loginInfo.getPassword()))) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.PASSWORD_INCORRECT);
        }

        if (employee.getStatus() == null || !employee.getStatus().equals(Employee.Status.active)) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_INACTIVE);
        }

        logger.info("Generate token for employee: {}", loginInfo.getEmail());
        Date validity;
        long now = (new Date()).getTime();
        if (Boolean.TRUE.equals(loginInfo.getRememberMe())) {
            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
        } else {
            validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        }

        String token = Jwts.builder()
                .setSubject(employee.getEmployeeCode())
                .setExpiration(validity)
                .claim(CRMService.JWT_USER_ID, employee.getId())
                .claim(CRMService.JWT_SCOPE, getAuthorities(employee))
                .signWith(SignatureAlgorithm.HS512, CRMService.JWT_SECRET)
                .compact();

        logger.info("Token generated for employee {}, token: {}", loginInfo.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);

        return event.createResponse(response, ResponseCode.OK, null);
    }

    public Employee findByEmailOrEmployeeCode(String emailOrCode) {
        Employee employee = this.employeeRepository.findOneByEmailIgnoreCase(emailOrCode);
        if (employee == null) {
            return this.employeeRepository.findOneByEmployeeCodeIgnoreCase(emailOrCode);
        }
        return employee;
    }

    public List<String> getAuthorities(Employee employee) {
        try {
            if (employee == null || employee.getRoleId() == null) {
                logger.warn("Employee or employee roleId is null when getting authorities.");
                return List.of(); // Return an empty list or handle as appropriate
            }
            List<String> authorities = permissionsRepository.getListPermissionKeyByRoleIds(employee.getRoleId().longValue());
            return authorities;
        } catch (Exception e) {
            logger.error("Error getting authorities for employee: {}. Error: {}", employee != null ? employee.getEmployeeCode() : "null", e.getMessage(), e);
            return List.of(); // Return an empty list in case of error
        }
    }

    private Event validatePermission(Event event) {
        String token = (String) event.payload;
//
//        if (securityService.validateToken(token)) {
//            event.respStatusCode = ResponseCode.OK;
//            Claims claims = securityService.parseToken(token);
//            Employee employee = current(claims);
//            if (employee == null) {
//                return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
//            }
//            return event.createResponse(employee, ResponseCode.OK, null);
//        } else {
//            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
//        }
        try {
            if (token != null && token.length() == authorizationCodeLength) {
                Auth2RequestInit auth2RequestInit = new Auth2RequestInit();
                auth2RequestInit.setClient_id(CLIENT_ID);
                auth2RequestInit.setClient_secret(SECRET_ID);
                auth2RequestInit.setGrant_type("authorization_code");
                auth2RequestInit.setCode(token);
                Auth2Response auth2Response = oAuth2Util.oauth2Login(auth2RequestInit);
                if (auth2Response != null && auth2Response.getIdToken() != null) {
                    String[] chunks = auth2Response.getIdToken().split("\\.");
                    if (chunks.length > 1) {
                        Base64.Decoder decoder = Base64.getUrlDecoder();
                        String payload = new String(decoder.decode(chunks[1]));
                        JsonObject jsonObject = new Gson().fromJson(payload, JsonObject.class);
                        String userName = jsonObject.has("userName") && !jsonObject.get("userName").isJsonNull() ? jsonObject.get("userName").getAsString() : " ";
                        String email = jsonObject.has("email") && !jsonObject.get("email").isJsonNull() ? jsonObject.get("email").getAsString() : null;

                        if (email != null) {
                            AuthResponseWithTokenAndErrorCodeDTO authResponseWithTokenAndErrorCodeDTO = createTokenEmail(email, userName);
                            if (authResponseWithTokenAndErrorCodeDTO != null) {
                                token = authResponseWithTokenAndErrorCodeDTO.getAccessToken();
                            }
                        } else {
                            logger.warn("Email not found in idToken payload for authorization code login.");
                        }
                    } else {
                        logger.warn("Invalid idToken format for authorization code login: {}", auth2Response.getIdToken());
                    }
                } else {
                    logger.warn("Auth2Response or idToken is null for authorization code login.");
                }
            }
        } catch (Exception e) {
            logger.error("Error during authorization code validation or token creation: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
        }

        try {
            if (securityService.validateToken(token)) {
                event.respStatusCode = ResponseCode.OK;
                Claims claims = securityService.parseToken(token);
                Employee user = current(claims);
                if (user == null) {
                    return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
                }
                return event.createResponse(user, ResponseCode.OK, null);
            } else {
                return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
            }
        } catch (Exception e) {
            logger.error("Error during token validation or parsing: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
        }
    }

    private AuthResponseWithTokenAndErrorCodeDTO createTokenEmail(String email, String employeeCode) {
        try {
            Employee employee = employeeRepository.findOneByEmployeeCodeIgnoreCase(employeeCode);
            if (employee == null) {
                employee = employeeRepository.findOneByEmailIgnoreCase(email);
            }
            if (employee == null) {
                logger.warn("Employee not found for email: {} or employeeCode: {}", email, employeeCode);
                return null;
            }
            if (employee.getStatus() == null || !employee.getStatus().equals(UserStatus.ACTIVE)) {
                logger.warn("Employee account is inactive for employee: {}", employee.getEmployeeCode());
                return null;
            }

            Date validity;
            long now = (new Date()).getTime();
            // Ensure applicationProperties and its nested objects are not null
            if (this.applicationProperties == null || this.applicationProperties.getTokenTime() == null) {
                logger.error("Application properties or token time configuration is null.");
                return null;
            }
            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);

            String token = Jwts.builder()
                .setSubject(employee.getEmail())
                .setExpiration(validity)
                .claim(CRMService.JWT_USER_ID, employee.getId())
                .claim(CRMService.JWT_SCOPE, getAuthorities(employee))
                .signWith(SignatureAlgorithm.HS512, CRMService.JWT_SECRET)
                .compact();

            AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
            response.setAccessToken(token);
            response.setExp(validity.getTime() / 1000);
            response.setNbf(now / 1000);

            return response;
        } catch (Exception e) {
            logger.error("Error creating token for email: {} and employeeCode: {}. Error: {}", email, employeeCode, e.getMessage(), e);
            return null;
        }
    }

    public Employee current(Claims claims) {
        try {
            if (claims == null || claims.getSubject() == null) {
                logger.warn("Claims or claims subject is null when getting current employee.");
                return null;
            }
            String employeeCode = claims.getSubject();
            Employee employee = this.employeeRepository.findOneByEmployeeCodeIgnoreCase(employeeCode);
            if (employee == null) {
                logger.warn("Employee not found for employee code: {}", employeeCode);
                return null;
            }
            // attach roles and authorities
            if (employee.getRoleId() == null) {
                logger.warn("Employee roleId is null for employee: {}", employeeCode);
                return employee; // Return employee without roles/authorities if roleId is null
            }
            employee.setRoles(
                    List.of(simpleRoleRepository.findById(employee.getRoleId().longValue()).get().getName())
            );
            employee.setAuthorities(permissionsRepository.getListPermissionKeyByRoleIds(employee.getRoleId().longValue()));
            logger.info("setAuthorities: {}", employee.getAuthorities());
            return employee;
        } catch (Exception e) {
            logger.error("Error getting current employee from claims: {}. Error: {}", claims != null ? claims.getSubject() : "null", e.getMessage(), e);
            return null;
        }
    }

    private Event processCurrentEmployee(Event event) {
        String token = (String) event.payload;
        if (securityService.validateToken(token)) {
            Claims claims = securityService.parseToken(token);
            Employee employee = current(claims);
            return event.createResponse(employee, ResponseCode.OK, null);
        }
        return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
    }

    private Event processSearch(Event event) {
        vn.agis.crm.base.jpa.dto.EmployeeSearchDto dto = (vn.agis.crm.base.jpa.dto.EmployeeSearchDto) event.payload;
        BaseController.ListRequest lr = new BaseController.ListRequest(dto.getSize(), dto.getPage(), dto.getSortBy());
        Page<Employee> page = employeeRepository.searchEmployees(
                dto.getEmployeeCode(),
                dto.getFullName(),
                dto.getPhone(),
                dto.getEmail(),
                lr.getPageable());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(vn.agis.crm.base.utils.ObjectMapperUtil.toJsonString(page.getContent()));
        return event.createResponse(pageInfo, ResponseCode.OK, null);
    }

    private Event processCreate(Event event) {
        CreateEmployeeReq req = (CreateEmployeeReq) event.payload;
        if (employeeRepository.findOneByEmployeeCodeIgnoreCase(req.getEmployeeCode()) != null) {
            return event.createResponse("employeeCode", ResponseCode.CONFLICT, "Employee code exists");
        }
        if (req.getEmail() != null && employeeRepository.findOneByEmailIgnoreCase(req.getEmail()) != null) {
            return event.createResponse("email", ResponseCode.CONFLICT, "Email exists");
        }
        if (req.getPhone() != null && employeeRepository.findOneByPhone(req.getPhone()) != null) {
            return event.createResponse("phone", ResponseCode.CONFLICT, "Phone exists");
        }
        Employee e = new Employee();
        e.setEmployeeCode(req.getEmployeeCode());
        e.setFullName(req.getFullName());
        e.setPhone(req.getPhone());
        e.setEmail(req.getEmail());
        e.setEncryptedPassword(req.getPassword());
        e.setRoleId(req.getRoleId());
        e.setCreatedBy(event.userId);
        e.setCreatedAt(new Date());
        e = employeeRepository.save(e);
        return event.createResponse(e, 201, "Created");
    }

    private Event processUpdate(Event event) {
        UpdateEmployeeReq req = (UpdateEmployeeReq) event.payload;
        var opt = employeeRepository.findById(req.getId());
        if (opt.isEmpty()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Employee e = opt.get();
        if (req.getEmail() != null && !req.getEmail().equalsIgnoreCase(e.getEmail()) && !employeeRepository.findByEmailAndIdNot(req.getEmail(), e.getId()).isEmpty()) {
            return event.createResponse("email", ResponseCode.CONFLICT, "Email exists");
        }
        if (req.getPhone() != null && !req.getPhone().equalsIgnoreCase(e.getPhone()) && !employeeRepository.findByPhoneAndIdNot(req.getPhone(), e.getId()).isEmpty()) {
            return event.createResponse("phone", ResponseCode.CONFLICT, "Phone exists");
        }
        if (!req.getEmployeeCode().equalsIgnoreCase(e.getEmployeeCode()) && employeeRepository.findOneByEmployeeCodeIgnoreCase(req.getEmployeeCode()) != null) {
            return event.createResponse("employeeCode", ResponseCode.CONFLICT, "Employee code exists");
        }
        e.setEmployeeCode(req.getEmployeeCode());
        e.setFullName(req.getFullName());
        e.setPhone(req.getPhone());
        e.setEmail(req.getEmail());
        if (req.getPassword() != null && !req.getPassword().isEmpty()) {
            e.setEncryptedPassword(req.getPassword());
        }
        try {
            e.setStatus(Employee.Status.valueOf(req.getStatus()));
        } catch (Exception ignored) {}
        e.setRoleId(req.getRoleId());
        e.setUpdatedBy(event.userId);
        e.setUpdatedAt(new Date());
        e = employeeRepository.save(e);
        return event.createResponse(e, ResponseCode.OK, "OK");
    }

    private Event processDelete(Event event) {
        Long id = (Long) event.payload;
        var opt = employeeRepository.findById(id);
        if (opt.isEmpty()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Employee e = opt.get();
        e.setDeletedAt(new Date());
        e.setUpdatedBy(event.userId);
        e.setUpdatedAt(new Date());
        employeeRepository.save(e);
        return event.createResponse(null, ResponseCode.OK, "Soft deleted");
    }

    private Event processGetAll(Event event) {
        List<Employee> all = employeeRepository.findAll();
        return event.createResponse(all, ResponseCode.OK, null);
    }

    private Event processUpdateStatus(Event event) {
        Long id = (Long) event.payload;
        var opt = employeeRepository.findById(id);
        if (opt.isEmpty()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Employee e = opt.get();
        e.setStatus(e.getStatus() == Employee.Status.active ? Employee.Status.inactive : Employee.Status.active);
        e.setUpdatedBy(event.userId);
        e.setUpdatedAt(new Date());
        employeeRepository.save(e);
        return event.createResponse(null, ResponseCode.OK, null);
    }

    private Event processCheckExits(Event event) {
        vn.agis.crm.base.jpa.dto.req.CheckExistEmployeeReq req = (vn.agis.crm.base.jpa.dto.req.CheckExistEmployeeReq) event.payload;
        boolean exists = false;
        if (req != null && req.getKey() != null && req.getValue() != null) {
            String key = req.getKey();
            String value = req.getValue();
            switch (key) {
                case "employeeCode":
                    exists = employeeRepository.findOneByEmployeeCodeIgnoreCase(value) != null;
                    break;
                case "email":
                    exists = employeeRepository.findOneByEmailIgnoreCase(value) != null;
                    break;
                case "phone":
                    exists = employeeRepository.findOneByPhone(value) != null;
                    break;
                default:
                    exists = false;
            }
        }
        return event.createResponse(exists, ResponseCode.OK, null);
    }


    private Event processGetOne(Event event) {
        Long id = (Long) event.payload;
        return employeeRepository.findById(id)
                .map(employee -> event.createResponse(employee, ResponseCode.OK, null))
                .orElse(event.createResponse(null, ResponseCode.NOT_FOUND, null));
    }
}
