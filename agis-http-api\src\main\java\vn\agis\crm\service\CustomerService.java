package vn.agis.crm.service;

import java.util.List;

import vn.agis.crm.base.jpa.dto.AssignmentHistoryResponseDto;
import vn.agis.crm.base.jpa.dto.GetAssignmentHistoryDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.CustomerSearchDto;
import vn.agis.crm.base.jpa.dto.CustomerDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;

import vn.agis.crm.util.RequestUtils;

@Service
public class CustomerService extends CrudService<Customers, Long> {

    private static final Logger logger = LoggerFactory.getLogger(CustomerService.class);

    public CustomerService() {
        super(Customers.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Category.CUSTOMER;
    }

    public Page<CustomerResDto> search(CustomerSearchDto searchDTO, Pageable pageable) {
        List<CustomerResDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), CustomerResDto.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in searchCustomers: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Customers createCustomer(CustomerDto dto) {
        Customers response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Customers) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createCustomer: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public CustomerResDto getOneRes(Long id) {
        CustomerResDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (CustomerResDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getOneRes Customer: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Customers getOne(Long id) {
        Customers response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Customers) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getOne Customer: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), response != null ? response.toString() : null, event);
        }
    }

    public CustomerResDto createCustomerRes(CustomerDto dto) {
        CustomerResDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (CustomerResDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createCustomerRes: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public CustomerResDto updateRes(CustomerDto dto) {
        CustomerResDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.UPDATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (CustomerResDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in updateRes Customer: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public CustomerResDto createCustomerV2(CustomerUpsertRequest req) {
        CustomerResDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE_CUSTOMER_V2, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (CustomerResDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createCustomerV2: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public CustomerResDto updateCustomerV2(CustomerUpsertRequest req) {
        CustomerResDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.UPDATE_CUSTOMER_V2, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (CustomerResDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in updateCustomerV2: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Customers update(CustomerDto dto) {
        Customers response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.UPDATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Customers) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_VALUE);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in update Customer: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public boolean checkExists(String key, String value) {
        Boolean response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            var req = new vn.agis.crm.base.jpa.dto.req.CheckExistCustomerReq(key, value);
            event = RequestUtils.amqp(Constants.Method.CHECK_EXISTS, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Boolean) event.payload;
                return Boolean.TRUE.equals(response);
            }
            return false;
        } catch (Exception e) {
            logger.error("Error in check customer exists: {}", e.getMessage(), e);
            return false;
        } finally {
            writeLog(timeRequest, timeResponse, key+":"+value, response != null ? response.toString() : null, event);
        }
    }

    public String updateAssignmentEmployee(vn.agis.crm.base.jpa.dto.req.UpdateAssignmentEmployeeDto request) {
        String response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.UPDATE_ASSIGNMENT_EMPLOYEE, category, request, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (String) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST || event.respStatusCode == 400) {
                throw new vn.agis.crm.base.exception.type.BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND || event.respStatusCode == 404) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in updateAssignmentEmployee: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, request != null ? request.toString() : null, response, event);
        }
    }

    public AssignmentHistoryResponseDto getAssignmentHistory(GetAssignmentHistoryDto request) {
        AssignmentHistoryResponseDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_ASSIGNMENT_HISTORY, category, request, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (AssignmentHistoryResponseDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND || event.respStatusCode == 404) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST || event.respStatusCode == 400) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getAssignmentHistory: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, request != null ? request.toString() : null,
                    response != null ? response.toString() : null, event);
        }
    }

    public void deleteAssignment(Long assignmentId) {
        String response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.DELETE_ASSIGNMENT, category, assignmentId, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (String) event.payload;
                return;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND || event.respStatusCode == 404) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST || event.respStatusCode == 400) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
        } catch (Exception e) {
            logger.error("Error in deleteAssignment: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, assignmentId != null ? assignmentId.toString() : null, response, event);
        }
    }

    private Long getCurrentUserId() {
        // This should be implemented to get the current authenticated user's ID
        // For now, returning a default value
        return 1L;
    }

    /**
     * Validates if a customer can be deleted by checking for dependencies
     */
    public CustomerDeletionValidationResult validateCustomerDeletion(Long customerId) {
        String response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.VALIDATE_CUSTOMER_DELETION, category, customerId, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == ResponseCode.BAD_REQUEST) {
                return (CustomerDeletionValidationResult) event.payload;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND || event.respStatusCode == 404) {
                throw new ResourceNotFoundException(MessageKeyConstant.CustomerDeletion.CUSTOMER_NOT_FOUND, category, (String) event.payload, MessageKeyConstant.CustomerDeletion.CUSTOMER_NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
        } catch (Exception e) {
            logger.error("Error in validateCustomerDeletion: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, customerId != null ? customerId.toString() : null, response, event);
        }
    }
}

