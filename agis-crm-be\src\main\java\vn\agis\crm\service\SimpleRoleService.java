package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.SimpleRoleSearchDto;
import vn.agis.crm.base.jpa.dto.req.SimpleRoleDto;
import vn.agis.crm.base.jpa.entity.RolePermissions;
import vn.agis.crm.base.jpa.entity.SimpleRole;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.repository.RolePermissionsRepository;
import vn.agis.crm.repository.SimpleRoleRepository;
import vn.agis.crm.util.BaseController;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Transactional
public class SimpleRoleService {

    @Autowired
    private SimpleRoleRepository roleRepository;

    @Autowired
    private RolePermissionsRepository rolePermissionsRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Method.CREATE:
                return create(event);
            case Method.UPDATE:
                return update(event);
            case Method.DELETE:
                return delete(event);
            case Method.FIND_BY_ID:
                return findById(event);
            case Method.SEARCH:
                return search(event);
            case vn.agis.crm.base.constants.Constants.Method.CHECK_EXIST_ROLE_NAME:
                return checkExistName(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event create(Event event) {
        SimpleRoleDto dto = (SimpleRoleDto) event.payload;
        if (dto.getName() == null || dto.getName().trim().isEmpty()) {
            return event.createResponse(null, 400, "Missing name");
        }
        if (roleRepository.existsByName(dto.getName())) {
            return event.createResponse(null, 409, "Duplicate role name");
        }
        SimpleRole role = new SimpleRole();
        role.setName(dto.getName());
        role.setDescription(dto.getDescription());
        role.setIsActive(dto.getIsActive() == null ? Boolean.TRUE : dto.getIsActive());
        role.setCreatedAt(new Date());
        role.setCreatedBy(event.userId);
        SimpleRole savedRole = roleRepository.save(role);

        if (dto.getPermissionIds() != null && !dto.getPermissionIds().isEmpty()) {
            List<RolePermissions> permissions = new ArrayList<>();
            for (Long permissionId : dto.getPermissionIds()) {
                RolePermissions rp = new RolePermissions();
                rp.setRoleId(savedRole.getId());
                rp.setPermissionId(permissionId);
                permissions.add(rp);
            }
            rolePermissionsRepository.saveAll(permissions);
        }

        return event.createResponse(savedRole, 201, "Created");
    }

    private Event update(Event event) {
        SimpleRoleDto dto = (SimpleRoleDto) event.payload;
        if (dto.getId() == null) return event.createResponse(null, 400, "Missing id");
        return roleRepository.findById(dto.getId()).map(existing -> {
            String targetName = dto.getName() != null ? dto.getName() : existing.getName();
            SimpleRole dup = roleRepository.findFirstByName(targetName);
            if (dup != null && !dup.getId().equals(existing.getId())) {
                return event.createResponse(null, 409, "Duplicate role name");
            }
            if (dto.getName() != null) existing.setName(dto.getName());
            if (dto.getDescription() != null) existing.setDescription(dto.getDescription());
            if (dto.getIsActive() != null) existing.setIsActive(dto.getIsActive());
            SimpleRole savedRole = roleRepository.save(existing);

            // Update permissions
            rolePermissionsRepository.deleteByRoleId(savedRole.getId());
            if (dto.getPermissionIds() != null && !dto.getPermissionIds().isEmpty()) {
                List<RolePermissions> permissions = new ArrayList<>();
                for (Long permissionId : dto.getPermissionIds()) {
                    RolePermissions rp = new RolePermissions();
                    rp.setRoleId(savedRole.getId());
                    rp.setPermissionId(permissionId);
                    permissions.add(rp);
                }
                rolePermissionsRepository.saveAll(permissions);
            }

            return event.createResponse(savedRole, 200, "Success");
        }).orElse(event.createResponse(null, 404, "Role not found"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        if (id == null) return event.createResponse(null, 400, "Missing id");
        rolePermissionsRepository.deleteByRoleId(id);
        roleRepository.deleteById(id);
        return event.createResponse(null, 200, "Success");
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        if (id == null) return event.createResponse(null, 400, "Missing id");
        return roleRepository.findById(id)
                .map(role -> {
                    List<Long> permissionIds = rolePermissionsRepository.findByRoleId(role.getId())
                            .stream().map(RolePermissions::getPermissionId).collect(Collectors.toList());
                    SimpleRoleDto dto = new SimpleRoleDto();
                    dto.setId(role.getId());
                    dto.setName(role.getName());
                    dto.setDescription(role.getDescription());
                    dto.setIsActive(role.getIsActive());
                    dto.setPermissionIds(permissionIds);
                    return event.createResponse(dto, 200, "Success");
                })
                .orElse(event.createResponse(null, 404, "Role not found"));
    }

    private Event search(Event event) {
        SimpleRoleSearchDto searchDto = (SimpleRoleSearchDto) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchDto.getSize(), searchDto.getPage(), searchDto.getSortBy());
        String name = (searchDto.getName() == null || searchDto.getName().isEmpty()) ? null : searchDto.getName();
        Page<SimpleRole> page = roleRepository.search(name, searchDto.getIsActive(), listRequest.getPageable());

        // Enhance roles with employee count statistics
        List<SimpleRole> rolesWithStats = enhanceRolesWithEmployeeCount(page.getContent());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setData(ObjectMapperUtil.toJsonString(rolesWithStats));
        pageInfo.setTotalCount(page.getTotalElements());
        return event.createResponse(pageInfo, 200, "Success");
    }

    /**
     * Enhance SimpleRole entities with employee count statistics
     */
    private List<SimpleRole> enhanceRolesWithEmployeeCount(List<SimpleRole> roles) {
        if (roles.isEmpty()) {
            return roles;
        }

        // Extract role IDs and convert Long to Integer for Employee.roleId compatibility
        List<Integer> roleIds = roles.stream()
                .map(role -> role.getId().intValue())
                .collect(Collectors.toList());

        // Batch query for employee counts by role IDs
        List<Object[]> employeeCounts = employeeRepository.countActiveEmployeesByRoleIds(roleIds);

        // Create a map for quick lookup: roleId -> employeeCount
        Map<Integer, Long> employeeCountMap = employeeCounts.stream()
                .collect(Collectors.toMap(
                    arr -> (Integer) arr[0],  // roleId
                    arr -> (Long) arr[1]      // count
                ));

        // Set employee count for each role
        for (SimpleRole role : roles) {
            Integer roleIdInt = role.getId().intValue();
            Long employeeCount = employeeCountMap.getOrDefault(roleIdInt, 0L);
            role.setTotalEmployees(employeeCount);
        }

        return roles;
    }

    private Event checkExistName(Event event) {
        String name = (String) event.payload;
        boolean exists = (name != null && !name.trim().isEmpty()) && roleRepository.existsByName(name);
        return event.createResponse(exists, 200, "Success");
    }
}

