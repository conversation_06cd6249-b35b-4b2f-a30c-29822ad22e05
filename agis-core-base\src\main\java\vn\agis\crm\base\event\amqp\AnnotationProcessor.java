package vn.agis.crm.base.event.amqp;

import org.reflections.Reflections;
import org.reflections.scanners.MethodAnnotationsScanner;
import org.reflections.util.ClasspathHelper;
import org.reflections.util.ConfigurationBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.event.*;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Created by tiemnd on 12/14/19.
 */
public class AnnotationProcessor {

    private static final Logger logger = LoggerFactory.getLogger(AnnotationProcessor.class);

    public static List<AMQPSubscriber> findSubscribers() {

        List<AMQPSubscriber> subscribers = new ArrayList<AMQPSubscriber>();

        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage("vn.agis"))
                .setScanners(new MethodAnnotationsScanner()));

        Set<Method> annontated = reflections.getMethodsAnnotatedWith(AMQPSubscribes.class);

        for (Method m : annontated) {
            // Analyze each consumer method
            AMQPSubscribes subscribes = m.getAnnotation(AMQPSubscribes.class);
            if (subscribes != null) {
                Class<? extends Object> eventType = analyzeMethod(m);
                if (subscribes.isEnabled()) {
                    subscribers.add(new AMQPSubscriber(subscribes.queue(), subscribes.routingKey(), subscribes.exchange(), m.getDeclaringClass(), m, eventType, subscribes.concurrency(), subscribes.exchangeType()));
                    logger.debug("Found consumer " + m.getName() + " in class " + m.getDeclaringClass().getName() +
                            ", queue: " + subscribes.queue() + ", routing key: " + subscribes.routingKey() + ", event type: " + eventType);
                }
            }

        }
        return subscribers;
    }

    public static List<RabbitStreamSubscriber> findStreamSubscribers() {

        List<RabbitStreamSubscriber> subscribers = new ArrayList<>();

        Reflections reflections = new Reflections(new ConfigurationBuilder()
                .setUrls(ClasspathHelper.forPackage("vn.agis"))
                .setScanners(new MethodAnnotationsScanner()));

        Set<Method> annontated = reflections.getMethodsAnnotatedWith(RabbitStreamSubscribers.class);

        for (Method m : annontated) {
            // Analyze each consumer method
            RabbitStreamSubscribers subscribes = m.getAnnotation(RabbitStreamSubscribers.class);
            if (subscribes != null) {
                Class<? extends Object> eventType = analyzeMethod(m);
                subscribers.add(new RabbitStreamSubscriber(subscribes.streamName(), subscribes.offsetType(), subscribes.concurrency(), subscribes.isSupperStream(), m, m.getDeclaringClass(), eventType));
                logger.debug("Found stream consumer " + m.getName() + " in class " + m.getDeclaringClass().getName() +
                        ", streamName: " + subscribes.streamName() + ", event type: " + eventType);
            }

        }
        return subscribers;
    }

    @SuppressWarnings("unchecked")
    private static Class<? extends Object> analyzeMethod(Method m) {

        if (m.getParameterTypes().length != 1) {
            logger.error("Method " + m.getName() + " must take ONE argument.");
            return null;
        }

        // Find type of event
        Class<?> argType = m.getParameterTypes()[0];

        // Check that the declared type is a superclass of the argument type
        if (!Object.class.isAssignableFrom(argType)) {
            logger.error("Method " + m.getName() + " argument type must extend Event.");
            return null;
        }

        return (Class<? extends Event>) argType;
    }
}
