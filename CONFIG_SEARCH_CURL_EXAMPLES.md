# Config Search API - cURL Examples

## Enhanced Config Search Endpoint

The ConfigController search endpoint now supports searching by both `configKey` and `description` fields with case-insensitive partial matching.

### Endpoint: `GET /configs/search`

## Comprehensive cURL Command Example

### **Full Search with All Parameters:**
```bash
curl -X GET "http://localhost:8080/configs/search?configKey=email&configType=1&description=c%E1%BA%A5u%20h%C3%ACnh&page=0&size=20&sort=createdAt,desc" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Parameter Breakdown:
- `configKey=email` - Search for configs with "email" in the key
- `configType=1` - Filter for single-value configs (type 1)
- `description=c%E1%BA%A5u%20h%C3%ACnh` - Search for "cấu hình" (configuration) in description (URL encoded Vietnamese)
- `page=0` - First page (0-based pagination)
- `size=20` - 20 configs per page
- `sort=createdAt,desc` - Sort by creation date, newest first

## Alternative Search Examples

### **1. Search by Config Key Only:**
```bash
curl -X GET "http://localhost:8080/configs/search?configKey=notification&page=0&size=10&sort=configKey,asc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### **2. Search by Description Only:**
```bash
curl -X GET "http://localhost:8080/configs/search?description=th%C3%B4ng%20b%C3%A1o&page=0&size=15&sort=createdAt,desc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### **3. Search by Config Type Only:**
```bash
curl -X GET "http://localhost:8080/configs/search?configType=2&page=0&size=25&sort=configKey,asc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### **4. Combined Key and Description Search:**
```bash
curl -X GET "http://localhost:8080/configs/search?configKey=system&description=h%E1%BB%87%20th%E1%BB%91ng&page=0&size=30&sort=createdAt,desc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### **5. List Configuration Search:**
```bash
curl -X GET "http://localhost:8080/configs/search?configType=2&description=danh%20s%C3%A1ch&page=0&size=10&sort=configKey,asc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## URL Encoding Reference

For Vietnamese text in search parameters:
- `cấu hình` → `c%E1%BA%A5u%20h%C3%ACnh`
- `thông báo` → `th%C3%B4ng%20b%C3%A1o`
- `hệ thống` → `h%E1%BB%87%20th%E1%BB%91ng`
- `danh sách` → `danh%20s%C3%A1ch`
- `bảo trì` → `b%E1%BA%A3o%20tr%C3%AC`
- `người dùng` → `ng%C6%B0%E1%BB%9Di%20d%C3%B9ng`
- `sao lưu` → `sao%20l%C6%B0u`

## Expected Response Format

```json
{
  "content": [
    {
      "id": 1,
      "configKey": "email_settings",
      "configType": 1,
      "configValue": "{\"smtp_host\": \"smtp.gmail.com\", \"port\": 587}",
      "description": "Cấu hình email SMTP cho hệ thống",
      "createdAt": "2024-01-15T10:30:00",
      "createdBy": 1,
      "updatedAt": "2024-01-16T14:20:00",
      "updatedBy": 2
    },
    {
      "id": 2,
      "configKey": "notification_types",
      "configType": 2,
      "configValue": "[1, 2, 3, 4]",
      "description": "Danh sách các loại thông báo trong hệ thống",
      "createdAt": "2024-01-14T09:15:00",
      "createdBy": 1,
      "updatedAt": null,
      "updatedBy": null
    }
  ],
  "pageable": {
    "sort": {
      "sorted": true,
      "unsorted": false
    },
    "pageNumber": 0,
    "pageSize": 20
  },
  "totalElements": 25,
  "totalPages": 2,
  "last": false,
  "first": true,
  "numberOfElements": 20
}
```

## Config Type Reference

- **Type 1**: Single value configurations (strings, numbers, booleans)
- **Type 2**: List/array configurations (JSON arrays, multiple values)

## Search Features

1. **Case-insensitive search** - Both configKey and description searches ignore case
2. **Partial matching** - Uses LIKE operator with wildcards for flexible searching
3. **Combined filters** - All search parameters can be used together
4. **Flexible pagination** - Configurable page size and sorting options
5. **Vietnamese text support** - Full support for Vietnamese characters in descriptions
6. **Backward compatibility** - Existing API consumers continue to work unchanged

## Usage Tips

1. **Use description search** for finding configs by their purpose or functionality
2. **Combine key and description** for more precise searches
3. **Filter by type** to separate single values from lists/arrays
4. **Use pagination** for large config sets (recommended page size: 10-50)
5. **Sort by configKey** for alphabetical browsing, by createdAt for chronological order
6. **URL encode Vietnamese text** to ensure proper search functionality

This enhanced search functionality provides comprehensive configuration discovery capabilities while maintaining full backward compatibility with existing AGIS CRM integrations.
