-- Migration Script: Add Search Performance Index for configs.description field
-- Description: Add index for better search performance on config description search functionality
-- Date: 2024-01-18
-- Author: AGIS CRM Enhancement

-- Add index for description field (for case-insensitive partial matching)
CREATE INDEX idx_configs_description ON configs (description);

-- Add composite index for common search combinations
CREATE INDEX idx_configs_key_type_desc ON configs (config_key, config_type, description);

-- Verification queries
-- Check if indexes were created successfully
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    TABLE_NAME,
    NON_UNIQUE,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'configs' 
AND INDEX_NAME LIKE 'idx_configs_%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Performance test queries
-- Test search by config key only (existing functionality)
EXPLAIN SELECT * FROM configs 
WHERE UPPER(config_key) LIKE CONCAT('%', UPPER('test'), '%') 
AND config_type = 1
ORDER BY created_at DESC 
LIMIT 10;

-- Test search by description only (new functionality)
EXPLAIN SELECT * FROM configs 
WHERE UPPER(description) LIKE CONCAT('%', UPPER('mô tả'), '%')
ORDER BY created_at DESC 
LIMIT 10;

-- Test combined search with all parameters
EXPLAIN SELECT * FROM configs 
WHERE UPPER(config_key) LIKE CONCAT('%', UPPER('config'), '%')
AND config_type = 1 
AND UPPER(description) LIKE CONCAT('%', UPPER('cấu hình'), '%')
ORDER BY created_at DESC 
LIMIT 20;

-- Sample data for testing (commented out - uncomment if needed for testing)
/*
-- Insert sample configs with Vietnamese descriptions for testing
INSERT INTO configs (config_key, config_type, config_value, description, created_at, created_by) VALUES
('email_settings', 1, '{"smtp_host": "smtp.gmail.com", "port": 587}', 'Cấu hình email SMTP cho hệ thống', NOW(), 1),
('notification_types', 2, '[1, 2, 3, 4]', 'Danh sách các loại thông báo trong hệ thống', NOW(), 1),
('system_maintenance', 1, 'false', 'Trạng thái bảo trì hệ thống', NOW(), 1),
('user_roles', 2, '["admin", "manager", "staff"]', 'Danh sách vai trò người dùng', NOW(), 1),
('backup_schedule', 1, '{"frequency": "daily", "time": "02:00"}', 'Lịch trình sao lưu dữ liệu tự động', NOW(), 1);
*/
