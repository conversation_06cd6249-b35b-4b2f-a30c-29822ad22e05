version: '3.8'
services:
  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - "5672:5672"    # AMQP
      - "15672:15672"  # UI
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin123
  redis-master:
    image: redis:7
    container_name: redis-master
    command: redis-server /usr/local/etc/redis/redis.conf
    volumes:
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "6379:6379"
    networks:
      redis-net:
        ipv4_address: ***********
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 3s
      retries: 5

  redis-sentinel:
    build:
      context: .
      dockerfile: Dockerfile.sentinel
    container_name: redis-sentinel
    command: >
      sh -c "
        until nc -z *********** 6379; do
          echo 'Waiting for redis-master...'
          sleep 2
        done
        redis-server /etc/redis/sentinel.conf --sentinel
      "
    volumes:
      - ./sentinel.conf:/etc/redis/sentinel.conf
    ports:
      - "26379:26379"
    depends_on:
      redis-master:
        condition: service_healthy
    networks:
      redis-net:
        ipv4_address: ***********

networks:
  redis-net:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16