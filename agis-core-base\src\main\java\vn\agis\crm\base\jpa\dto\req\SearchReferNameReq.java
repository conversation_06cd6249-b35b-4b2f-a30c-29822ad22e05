package vn.agis.crm.base.jpa.dto.req;

import lombok.*;
import org.apache.commons.lang3.StringUtils;
import vn.agis.crm.base.utils.SqlUtils;

import java.util.Objects;

@Data
public class SearchReferNameReq {
    private String name;
    private Long userId;
    private String provinceCode;
    private Integer page;
    private Integer size;
    private String sortBy;
    private boolean loggable;

    public SearchReferNameReq(String name, Long userId, String provinceCode, Integer page, Integer size, String sortBy, boolean loggable) {
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.userId = userId;
        this.provinceCode = StringUtils.isEmpty(provinceCode) ? " " : SqlUtils.optimizeSearchLike(provinceCode);
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
        this.loggable = loggable;
    }
}
