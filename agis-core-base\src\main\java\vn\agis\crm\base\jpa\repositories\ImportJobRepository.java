package vn.agis.crm.base.jpa.repositories;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.ImportJob;

import java.util.List;
import java.util.Optional;

@Repository
public interface ImportJobRepository extends CustomJpaRepository<ImportJob, Long> {

    /**
     * Find import jobs by file checksum to detect duplicates
     */
    @Query("SELECT ij FROM ImportJob ij WHERE ij.fileChecksum = :checksum AND ij.fileChecksum IS NOT NULL")
    List<ImportJob> findByFileChecksum(@Param("checksum") String checksum);

    /**
     * Find import job by ID and created by user (for security)
     */
    @Query("SELECT ij FROM ImportJob ij WHERE ij.id = :id AND ij.createdBy = :createdBy")
    Optional<ImportJob> findByIdAndCreatedBy(@Param("id") Long id, @Param("createdBy") Long createdBy);

    /**
     * Find import jobs by created by user with pagination support
     */
    List<ImportJob> findByCreatedByOrderByCreatedAtDesc(Long createdBy);
}
