package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.req.ManualAssignmentRequest;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.repository.ConfigRepository;
import vn.agis.crm.repository.CustomerAssignmentRepository;
import vn.agis.crm.repository.CustomerRepository;
import java.util.List;

import vn.agis.crm.base.jpa.entity.CustomerAssignments;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class AssignmentService {

    private static final Logger logger = LoggerFactory.getLogger(AssignmentService.class);

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;

    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private NotificationService notificationService;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.MANUAL_ASSIGN:
                return manualAssign(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event manualAssign(Event event) {
        ManualAssignmentRequest request = (ManualAssignmentRequest) event.payload;
        List<Long> customerIds = request.getCustomerIds();

        if (customerIds == null || customerIds.isEmpty()) {
            return event.createResponse(Collections.singletonMap("error", "Customer IDs cannot be empty."), 400, "Bad Request");
        }

        // Note: RBAC check should be implemented here based on event.userId

        if (request.isDryRun()) {
            long count = customerRepository.countByIdIn(customerIds);
            return event.createResponse(Collections.singletonMap("affectedCount", count), 200, "Dry run successful");
        }

        Date now = new Date();
        Long managerId = request.getManagerId();
        Long staffId = request.getStaffId();

        // Handle Manager Assignment
        if (managerId != null) {
            // Check for duplicate assignments and filter out customers that already have the same manager
            List<Long> customersNeedingManagerAssignment = filterCustomersForManagerAssignment(customerIds, managerId);

            if (!customersNeedingManagerAssignment.isEmpty()) {
                customerAssignmentRepository.deactivateActiveAssignments(customersNeedingManagerAssignment, 1, now, now, event.userId); // 1 for Manager
                List<CustomerAssignments> newAssignments = new ArrayList<>();
                for (Long customerId : customersNeedingManagerAssignment) {
                    CustomerAssignments assignment = new CustomerAssignments();
                    assignment.setCustomerId(customerId);
                    assignment.setEmployeeId(managerId);
                    assignment.setRoleType(1);
                    assignment.setAssignedFrom(now);
                    assignment.setCreatedAt(now);
                    assignment.setCreatedBy(event.userId);
                    newAssignments.add(assignment);
                }
                customerAssignmentRepository.saveAll(newAssignments);
                customerRepository.updateCurrentManager(customersNeedingManagerAssignment, managerId);

                // Create notifications for Manager assignments
                createNotificationsForAssignments(customersNeedingManagerAssignment, managerId, 1, event.userId);
            }
        } else {
            // Handle null manager assignment - clear current manager and deactivate active assignments
            handleNullManagerAssignment(customerIds, now, event.userId);
        }

        // Handle Staff Assignment
        if (staffId != null) {
            // Check for duplicate assignments and filter out customers that already have the same staff
            List<Long> customersNeedingStaffAssignment = filterCustomersForStaffAssignment(customerIds, staffId);

            if (!customersNeedingStaffAssignment.isEmpty()) {
                customerAssignmentRepository.deactivateActiveAssignments(customersNeedingStaffAssignment, 2, now, now, event.userId); // 2 for Staff
                List<CustomerAssignments> newAssignments = new ArrayList<>();
                for (Long customerId : customersNeedingStaffAssignment) {
                    CustomerAssignments assignment = new CustomerAssignments();
                    assignment.setCustomerId(customerId);
                    assignment.setEmployeeId(staffId);
                    assignment.setRoleType(2);
                    assignment.setAssignedFrom(now);
                    assignment.setCreatedAt(now);
                    assignment.setCreatedBy(event.userId);
                    newAssignments.add(assignment);
                }
                customerAssignmentRepository.saveAll(newAssignments);
                customerRepository.updateCurrentStaff(customersNeedingStaffAssignment, staffId);

                // Create notifications for Staff assignments
                createNotificationsForAssignments(customersNeedingStaffAssignment, staffId, 2, event.userId);
            }
        } else {
            // Handle null staff assignment - clear current staff and deactivate active assignments
            handleNullStaffAssignment(customerIds, now, event.userId);
        }

        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "Assignments completed successfully.");
        return event.createResponse(result, 200, "Success");
    }

    /**
     * Filter customers that need manager assignment (exclude those with same manager already assigned)
     */
    private List<Long> filterCustomersForManagerAssignment(List<Long> customerIds, Long managerId) {
        List<CustomerAssignments> activeManagerAssignments = customerAssignmentRepository
                .findActiveAssignmentsByCustomerIdsAndRoleType(customerIds, 1); // 1 for Manager

        Set<Long> customersWithSameManager = new HashSet<>();
        for (CustomerAssignments assignment : activeManagerAssignments) {
            if (managerId.equals(assignment.getEmployeeId())) {
                customersWithSameManager.add(assignment.getCustomerId());
            }
        }

        return customerIds.stream()
                .filter(customerId -> !customersWithSameManager.contains(customerId))
                .collect(Collectors.toList());
    }

    /**
     * Filter customers that need staff assignment (exclude those with same staff already assigned)
     */
    private List<Long> filterCustomersForStaffAssignment(List<Long> customerIds, Long staffId) {
        List<CustomerAssignments> activeStaffAssignments = customerAssignmentRepository
                .findActiveAssignmentsByCustomerIdsAndRoleType(customerIds, 2); // 2 for Staff

        Set<Long> customersWithSameStaff = new HashSet<>();
        for (CustomerAssignments assignment : activeStaffAssignments) {
            if (staffId.equals(assignment.getEmployeeId())) {
                customersWithSameStaff.add(assignment.getCustomerId());
            }
        }

        return customerIds.stream()
                .filter(customerId -> !customersWithSameStaff.contains(customerId))
                .collect(Collectors.toList());
    }

    /**
     * Handle null manager assignment - deactivate active manager assignments and clear current manager
     */
    private void handleNullManagerAssignment(List<Long> customerIds, Date now, Long userId) {
        // Deactivate active manager assignments by setting assigned_to timestamp
        customerAssignmentRepository.deactivateActiveAssignments(customerIds, 1, now, now, userId); // 1 for Manager

        // Clear current_manager_id in customers table
        customerRepository.clearCurrentManager(customerIds);
    }

    /**
     * Handle null staff assignment - deactivate active staff assignments and clear current staff
     */
    private void handleNullStaffAssignment(List<Long> customerIds, Date now, Long userId) {
        // Deactivate active staff assignments by setting assigned_to timestamp
        customerAssignmentRepository.deactivateActiveAssignments(customerIds, 2, now, now, userId); // 2 for Staff

        // Clear current_staff_id in customers table
        customerRepository.clearCurrentStaff(customerIds);
    }

    /**
     * Create notifications for new lead assignments based on system configuration
     *
     * @param customerIds List of customer IDs that were assigned
     * @param employeeId ID of the employee who received the assignments
     * @param roleType Role type (1=Manager, 2=Staff)
     * @param createdBy ID of the user who made the assignment
     */
    private void createNotificationsForAssignments(List<Long> customerIds, Long employeeId, Integer roleType, Long createdBy) {
        try {
            // Determine config key based on role type
            String configKey;
            String roleDisplayName;
            if (roleType == 1) {
                configKey = "ON_NOTIFICATION_NEW_MANAGER_LEAD";
                roleDisplayName = "Manager";
            } else if (roleType == 2) {
                configKey = "ON_NOTIFICATION_NEW_STAFF_LEAD";
                roleDisplayName = "Staff";
            } else {
                logger.warn("Unknown role type {} for notification creation, skipping", roleType);
                return;
            }

            // Check system configuration
            Config config = configRepository.findOneByConfigKeyIgnoreCase(configKey);
            if (config == null || !"ON".equalsIgnoreCase(config.getConfigValue())) {
                logger.debug("Notification disabled for {} assignments (config: {})", roleDisplayName,
                           config != null ? config.getConfigValue() : "not found");
                return;
            }

            logger.debug("Creating notifications for {} {} assignments to employee {}",
                        customerIds.size(), roleDisplayName, employeeId);

            // Get customer information for notification content
            List<Customers> customers = customerRepository.findAllById(customerIds);

            // Create notification for each customer assignment
            for (Customers customer : customers) {
                try {
                    String title = "Lead mới được phân công";
                    String content = String.format(
                        "Bạn đã được phân công chăm sóc khách hàng %s (SĐT: %s) với vai trò %s. ",
                        customer.getFullName() != null ? customer.getFullName() : "N/A",
                        customer.getPhone() != null ? customer.getPhone() : "N/A",
                        roleDisplayName
                    );

                    Notifications notification = notificationService.createNotification(
                        employeeId,
                        1, // LeadAssigned type
                        title,
                        content,
                        customer.getId(),
                        createdBy
                    );

                    logger.debug("Created notification {} for employee {} about customer {} ({})",
                               notification.getId(), employeeId, customer.getId(), customer.getFullName());

                } catch (Exception e) {
                    logger.error("Failed to create notification for employee {} about customer {} ({}): {}",
                               employeeId, customer.getId(), customer.getFullName(), e.getMessage(), e);
                    // Continue with other customers - don't fail the entire assignment process
                }
            }

            logger.info("Successfully processed notifications for {} {} assignments to employee {}",
                       customers.size(), roleDisplayName, employeeId);

        } catch (Exception e) {
            logger.error("Error creating notifications for {} assignments to employee {}: {}",
                        roleType == 1 ? "Manager" : "Staff", employeeId, e.getMessage(), e);
            // Don't throw exception - notification failure should not break assignment process
        }
    }
}

