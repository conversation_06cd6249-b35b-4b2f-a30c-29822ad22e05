package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.JobDetailsRequestDto;
import vn.agis.crm.base.jpa.entity.RuleJobHistory;
import vn.agis.crm.base.jpa.entity.RuleRun;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.repository.RuleJobHistoryRepository;
import vn.agis.crm.repository.RuleRunRepository;

@Service
@Transactional(readOnly = true)
public class AssignmentJobService {

    @Autowired
    private RuleJobHistoryRepository jobHistoryRepository;

    @Autowired
    private RuleRunRepository ruleRunRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.GET_JOB_HISTORY:
                return getJobHistory(event);
            case Constants.Method.GET_JOB_DETAILS:
                return getJobDetails(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event getJobHistory(Event event) {
        Pageable pageable = (Pageable) event.payload;
        Page<RuleJobHistory> historyPage = jobHistoryRepository.findAll(pageable);
        PageInfo pageInfo = new PageInfo(historyPage.getTotalElements(), ObjectMapperUtil.toJsonString(historyPage.getContent()));
        return event.createResponse(pageInfo, 200, "Success");
    }

    private Event getJobDetails(Event event) {
        JobDetailsRequestDto requestDto = (JobDetailsRequestDto) event.payload;
        Long jobId = requestDto.getJobId();
        Pageable pageable = requestDto.getPageable();
        Page<RuleRun> detailsPage = ruleRunRepository.findByJobId(jobId, pageable);
        PageInfo pageInfo = new PageInfo(detailsPage.getTotalElements(), ObjectMapperUtil.toJsonString(detailsPage.getContent()));
        return event.createResponse(pageInfo, 200, "Success");
    }
}

