package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.dto.EmployeeDto;

import java.util.Date;

@Data
public class NotificationDto {
    private Long id;
    private Long targetEmployeeId;
    private Long targetCustomerId;
    private Integer type;
    private String title;
    private String content;
    private Boolean isRead;
    private Date readAt;
    private Date createdAt;
    private Long createdBy;

    // Enhanced with related entity information
    private EmployeeDto targetEmployee;
    private CustomerDto targetCustomer;
    private EmployeeDto createdByEmployee;
}
