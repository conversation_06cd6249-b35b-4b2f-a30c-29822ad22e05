package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

/**
 * Configuration manager for import processing
 * Handles validation rules, thresholds, and processing options
 */
public class ImportConfigurationManager {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportConfigurationManager.class);
    
    // Default configuration values
    private static final Map<String, Object> DEFAULT_CONFIG = new HashMap<>();
    
    static {
        // File processing limits
        DEFAULT_CONFIG.put("import.max_file_size_mb", 10);
        DEFAULT_CONFIG.put("import.max_rows_per_file", 10000);
        DEFAULT_CONFIG.put("import.batch_size", 500);
        
        // Supported formats
        DEFAULT_CONFIG.put("import.supported_formats", Arrays.asList("csv", "xlsx", "xls"));
        
        // Date formats
        DEFAULT_CONFIG.put("import.date_formats", Arrays.asList(
            "d-MMM-yy", "EEEE, MMMM d, yyyy", "d/M/yyyy", "yyyy-MM-dd", "dd/MM/yyyy"
        ));
        
        // Validation settings
        DEFAULT_CONFIG.put("import.stop_on_critical_error", true);
        DEFAULT_CONFIG.put("import.max_errors_per_job", 1000);
        DEFAULT_CONFIG.put("import.phone_validation_strict", true);
        DEFAULT_CONFIG.put("import.email_validation_strict", false);
        
        // Processing timeouts
        DEFAULT_CONFIG.put("import.processing_timeout_minutes", 30);
        DEFAULT_CONFIG.put("import.dry_run_timeout_minutes", 15);
        
        // Duplicate handling
        DEFAULT_CONFIG.put("import.default_upsert_strategy", "UPSERT_BY_PHONE");
        DEFAULT_CONFIG.put("import.allow_duplicate_in_file", false);
        
        // Performance settings
        DEFAULT_CONFIG.put("import.enable_parallel_processing", true);
        DEFAULT_CONFIG.put("import.thread_pool_size", 4);
        
        // Estimation settings
        DEFAULT_CONFIG.put("import.estimation_sample_size", 100);
        DEFAULT_CONFIG.put("import.avg_processing_time_per_row_ms", 50);
    }
    
    // Static cache for configuration values
    private static final Map<String, Object> CONFIG_CACHE = new HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_TTL_MS = 5 * 60 * 1000; // 5 minutes

    /**
     * Get configuration value with fallback to default
     */
    public static Object getConfigValue(String key) {
        // Check cache first
        if (CONFIG_CACHE.containsKey(key) && (System.currentTimeMillis() - lastCacheUpdate) < CACHE_TTL_MS) {
            return CONFIG_CACHE.get(key);
        }

        // In a real implementation, this would fetch from database
        // For now, return default values and cache them
        Object value = DEFAULT_CONFIG.get(key);
        CONFIG_CACHE.put(key, value);
        lastCacheUpdate = System.currentTimeMillis();

        return value;
    }

    /**
     * Clear configuration cache (for testing or when config changes)
     */
    public static void clearCache() {
        CONFIG_CACHE.clear();
        lastCacheUpdate = 0;
    }
    
    /**
     * Get configuration value as Integer
     */
    public static Integer getIntConfig(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Integer) {
            return (Integer) value;
        } else if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                logger.warn("Invalid integer config value for key {}: {}", key, value);
                return null;
            }
        }
        return null;
    }
    
    /**
     * Get configuration value as Boolean
     */
    public static Boolean getBooleanConfig(String key) {
        Object value = getConfigValue(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        } else if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return null;
    }
    
    /**
     * Get configuration value as String
     */
    public static String getStringConfig(String key) {
        Object value = getConfigValue(key);
        return value != null ? value.toString() : null;
    }
    
    /**
     * Get configuration value as List
     */
    @SuppressWarnings("unchecked")
    public static List<String> getListConfig(String key) {
        Object value = getConfigValue(key);
        if (value instanceof List) {
            return (List<String>) value;
        }
        return new ArrayList<>();
    }
    
    /**
     * Get import options with defaults
     */
    public static Map<String, Object> getDefaultImportOptions() {
        Map<String, Object> options = new HashMap<>();
        
        options.put("mode", "DRY_RUN");
        options.put("upsert_strategy", getStringConfig("import.default_upsert_strategy"));
        options.put("stop_on_error", getBooleanConfig("import.stop_on_critical_error"));
        options.put("run_only_valid_rows", true);
        options.put("force_reimport", false);
        options.put("batch_size", getIntConfig("import.batch_size"));
        options.put("date_formats", getListConfig("import.date_formats"));
        options.put("phone_validation_strict", getBooleanConfig("import.phone_validation_strict"));
        options.put("email_validation_strict", getBooleanConfig("import.email_validation_strict"));
        
        return options;
    }
    
    /**
     * Merge user options with defaults
     */
    public static Map<String, Object> mergeWithDefaults(Map<String, Object> userOptions) {
        Map<String, Object> merged = getDefaultImportOptions();
        
        if (userOptions != null) {
            merged.putAll(userOptions);
        }
        
        return merged;
    }
    
    /**
     * Validate import options
     */
    public static List<String> validateImportOptions(Map<String, Object> options) {
        List<String> errors = new ArrayList<>();
        
        if (options == null) {
            errors.add("Import options cannot be null");
            return errors;
        }
        
        // Validate mode
        String mode = (String) options.get("mode");
        if (mode != null && !Arrays.asList("DRY_RUN", "RUN").contains(mode)) {
            errors.add("Invalid mode: " + mode + ". Must be DRY_RUN or RUN");
        }
        
        // Validate upsert strategy
        String strategy = (String) options.get("upsert_strategy");
        if (strategy != null && !Arrays.asList("UPSERT_BY_PHONE", "SKIP_DUP").contains(strategy)) {
            errors.add("Invalid upsert_strategy: " + strategy + ". Must be UPSERT_BY_PHONE or SKIP_DUP");
        }
        
        // Validate batch size
        Object batchSizeObj = options.get("batch_size");
        if (batchSizeObj != null) {
            try {
                int batchSize = Integer.parseInt(batchSizeObj.toString());
                if (batchSize <= 0 || batchSize > 1000) {
                    errors.add("Invalid batch_size: " + batchSize + ". Must be between 1 and 1000");
                }
            } catch (NumberFormatException e) {
                errors.add("Invalid batch_size format: " + batchSizeObj);
            }
        }
        
        return errors;
    }
    
    /**
     * Get file size limit in bytes
     */
    public static long getMaxFileSizeBytes() {
        Integer maxSizeMB = getIntConfig("import.max_file_size_mb");
        return maxSizeMB != null ? maxSizeMB * 1024L * 1024L : 10L * 1024L * 1024L;
    }
    
    /**
     * Get maximum rows per file
     */
    public static int getMaxRowsPerFile() {
        Integer maxRows = getIntConfig("import.max_rows_per_file");
        return maxRows != null ? maxRows : 10000;
    }
    
    /**
     * Get supported file formats
     */
    public static List<String> getSupportedFormats() {
        return getListConfig("import.supported_formats");
    }
    
    /**
     * Check if file format is supported
     */
    public static boolean isFormatSupported(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String extension = fileName.toLowerCase();
        int lastDot = extension.lastIndexOf('.');
        if (lastDot == -1) {
            return false;
        }
        
        extension = extension.substring(lastDot + 1);
        return getSupportedFormats().contains(extension);
    }
    
    /**
     * Get processing timeout in milliseconds
     */
    public static long getProcessingTimeoutMs() {
        Integer timeoutMinutes = getIntConfig("import.processing_timeout_minutes");
        return timeoutMinutes != null ? timeoutMinutes * 60L * 1000L : 30L * 60L * 1000L;
    }
    
    /**
     * Get dry-run timeout in milliseconds
     */
    public static long getDryRunTimeoutMs() {
        Integer timeoutMinutes = getIntConfig("import.dry_run_timeout_minutes");
        return timeoutMinutes != null ? timeoutMinutes * 60L * 1000L : 15L * 60L * 1000L;
    }
    
    /**
     * Get average processing time per row for estimation
     */
    public static long getAvgProcessingTimePerRowMs() {
        Integer avgTime = getIntConfig("import.avg_processing_time_per_row_ms");
        return avgTime != null ? avgTime : 50L;
    }
}
