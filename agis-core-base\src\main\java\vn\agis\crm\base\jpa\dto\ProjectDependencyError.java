package vn.agis.crm.base.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a specific dependency error when attempting to delete a project
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectDependencyError {
    
    /**
     * Type of dependency that prevents deletion
     */
    private DependencyType dependencyType;
    
    /**
     * Number of dependent records
     */
    private long count;
    
    /**
     * Human-readable error message
     */
    private String message;
    
    /**
     * Additional details about the dependency (optional)
     */
    private String details;

    public enum DependencyType {
        UNITS("Căn hộ"),
        CUSTOMER_OFFERS("Chào bán khách hàng"),
        CUSTOMER_PROPERTIES("Bất động sản khách hàng");

        private final String displayName;

        DependencyType(String displayName) {
            this.displayName = displayName;
        }

        public String getDisplayName() {
            return displayName;
        }
    }
    
    /**
     * Constructor for creating dependency error with count
     */
    public ProjectDependencyError(DependencyType dependencyType, long count) {
        this.dependencyType = dependencyType;
        this.count = count;
//        this.message = createMessage(dependencyType, count);
        this.message = "Dự án đang được sử dụng, không thể xóa.";
    }
    
    /**
     * Creates a user-friendly error message based on dependency type and count
     */
    private String createMessage(DependencyType dependencyType, long count) {
        switch (dependencyType) {
            case UNITS:
                return String.format("Không thể xóa dự án. Dự án có %d căn hộ đang hoạt động.", count);
            case CUSTOMER_OFFERS:
                return String.format("Không thể xóa dự án. Dự án có %d chào bán khách hàng đang hoạt động.", count);
            case CUSTOMER_PROPERTIES:
                return String.format("Không thể xóa dự án. Dự án có %d bất động sản khách hàng đã sở hữu.", count);
            default:
                return String.format("Không thể xóa dự án. Dự án có %d bản ghi phụ thuộc loại %s.",
                    count, dependencyType.getDisplayName());
        }
    }
}
