package vn.agis.crm.base.constants;

public interface ResponseCode {
    int OK = 200;

    /** Error from client **/
    int BAD_REQUEST = 400;
    int UNAUTHORIZED = 401;
    int FORBIDDEN = 403;
    int NOT_FOUND = 404;
    int METHOD_NOT_SUPPORTED = 405;
    int REQUEST_TIMEOUT = 408;
    int CONFLICT = 409;
    int PAYLOAD_TOO_LARGE = 413;
    int UNPROCESSABLE_ENTITY = 422; //well-formed but was unable to be followed due to semantic errors
    int LOCKED = 423;
    int TOO_EARLY = 425;
    int TOO_MANY_REQUEST = 429;
    int LOGIN_TIMEOUT = 440;
    int INVALID_TOKEN = 498;
    int TOKEN_REQUIRED = 499;
    int WRONG_OLD_PASSWORD = 444;
    int EXPIRED_PASSWORD = 445;

    /** Error from server **/
    int SERVER_INTERNAL_ERROR = 500;
    int BAD_GATEWAY = 502;
    int SERVICE_UNAVAILABLE = 503;
    int UNKNOWN_ERROR = 520;

    /** Error from device **/
    int WRONG_FOMAT = 201;
    int FILE_TO_BIG = 202;
    int COLUMN_INVALID = 203;
    int FILE_IS_EMPTY = 204;
    int MAX_ROW_FILE_IMPORT = 205;
    int MSISDN_NOTEXITS = 211;
    int MSISDN_ASSIGN  = 212;
    int MSISDN_INVALD = 213;
    int MSISDN_IS_EMPTY = 214;
    int MSISDN_IS_DUPLICATE = 215;
    int IMEI_IS_DUPLITE = 216;
    int EXPRIRED_DATE_INVALID = 217;
    int MSISDN_NOT_PERMISSION = 218;
    int IMEI_IS_EXSIT = 219;
    int IMEI_LEN = 220;
    int DEVICE_TYPE_LEN = 221;
    int COUNTRY_LEN = 222;
    int HAS_ERROR = 300;

    /** CCBS Success Code **/
    int SUCCESS = 1200;
    static String getResponseStatusMessage(Integer respStatusCode) {
        switch (respStatusCode) {
            case OK:
                return "OK";
            case BAD_REQUEST:
                return "Bad request";
            case UNAUTHORIZED:
                return "Authentication is required";
            case FORBIDDEN:
                return "No privileges";
            case NOT_FOUND:
                return "Not found";
            case METHOD_NOT_SUPPORTED:
                return "Method is not supported";
            case REQUEST_TIMEOUT:
                return "Request is timeout";
            case CONFLICT:
                return "The same object is existing";
            case PAYLOAD_TOO_LARGE:
                return "Payload is too large";
            case UNPROCESSABLE_ENTITY:
                return "Provided data is semantic error";
            case LOCKED:
                return "Object is locked";
            case TOO_EARLY:
                return "Related data is not ready now";
            case TOO_MANY_REQUEST:
                return "Too many request";
            case LOGIN_TIMEOUT:
                return "Login timeout";
            case INVALID_TOKEN:
                return "Token is invalid";
            case TOKEN_REQUIRED:
                return "Token is required";
            case BAD_GATEWAY:
                return "Invalid response from target system";
            case SERVICE_UNAVAILABLE:
                return "Service is unavailable";
            case SERVER_INTERNAL_ERROR:
                return "Server internal error";
            case WRONG_OLD_PASSWORD:
                return "Wrong old password";
            case EXPIRED_PASSWORD:
                return "Password has expired";
            default:
                return "Unknown error";
        }
    }
}
