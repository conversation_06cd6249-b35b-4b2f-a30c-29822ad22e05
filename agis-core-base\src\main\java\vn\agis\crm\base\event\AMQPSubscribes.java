package vn.agis.crm.base.event;

import vn.agis.crm.base.event.amqp.AMQPAbstractConfiguration;
import vn.agis.crm.base.event.constants.AMQPConstants;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;


/**
 * Created by tiemnd on 12/14/19.
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface AMQPSubscribes {
    public String queue() default "";

    public String routingKey() default "";

    public int concurrency() default 0;

    public String exchange() default AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME;

    public boolean isEnabled() default true;

    /**
     * using Direct or Topic
     *
     * @return
     */
    public String exchangeType() default AMQPConstants.ExchangeType.DIRECT;
}
