package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.errors.BaseException;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.InternalServerException;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CreateCommandRequest;
import vn.agis.crm.base.jpa.dto.req.SearchCommandRequest;
import vn.agis.crm.base.jpa.dto.resp.SearchCommandResponse;
import vn.agis.crm.base.jpa.entity.Command;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.List;
@Service
public class CommandService {

    private Logger logger = LoggerFactory.getLogger(DeviceTypeService.class);
    private String routingKey;
    private String category;

    public CommandService() {
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_DEVICE_MANAGEMENT;
        this.category = Constants.Category.DEVICE_COMMAND;
    }

    public List<Command> getCommandListByDeviceType(Long deviceTypeId) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();

        try {
            event = RequestUtils.amqp(Constants.Method.GET_COMMAND_LIST_BY_DEVICE_TYPE, category, deviceTypeId, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                return ObjectMapperUtil.listMapper(String.valueOf(event.payload), Command.class);
            } else {
                logger.error("Error getting commands by device type ID: {}", event.respErrorDesc);
            }
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("Error in getCommandListByDeviceType: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public Page<SearchCommandResponse> searchCommand(SearchCommandRequest searchDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<SearchCommandResponse> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DEVICE_COMMAND, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchCommandResponse.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else {
                logger.error("Error searching device command: {}", event.respErrorDesc);
            }
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        } catch (Exception e) {
            logger.error("Error in searchCommand: {}", e.getMessage(), e);
            return null;
        }
    }

    public String sendCommandToDevice(CreateCommandRequest createCommandRequest) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        BaseException exception;
        try {
            event = RequestUtils.amqp(Constants.Method.SEND_DEVICE_COMMAND, category, createCommandRequest, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                return (String) event.payload;
            } else {
                logger.error("Error sending command to device: {}", event.respErrorDesc);
                exception = new InternalServerException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
                throw exception;
            }
        } catch (Exception e) {
            logger.error("Error in sendCommandToDevice: {}", e.getMessage(), e);
            return null;
        }
    }

    public Command get(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();

        try {
            event = RequestUtils.amqp("getOne", category, id, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                return (Command) event.payload;
            } else {
                logger.error("Error getting device type: {}", event.respErrorDesc);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in get: {}", e.getMessage(), e);
            return null;
        }
    }
}
