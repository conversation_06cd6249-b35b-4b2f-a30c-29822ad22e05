package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Data
@Entity
@Table(name = "interactions_primary")
public class InteractionsPrimary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "customer_offer_id", nullable = false)
    private Long customerOfferId;

    @Column(name = "result", nullable = false, length = 100)
    private String result;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "happened_at", nullable = false)
    private Date happenedAt;

    @Lob
    @Column(name = "notes")
    private String notes;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "created_by")
    private Long createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;
}

