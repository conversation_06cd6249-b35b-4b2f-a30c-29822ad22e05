//package vn.agis.crm.core.filters;
//
//import com.google.gson.Gson;
//import jakarta.servlet.FilterChain;
//import jakarta.servlet.ServletException;
//import jakarta.servlet.ServletRequest;
//import jakarta.servlet.ServletResponse;
//import jakarta.servlet.http.HttpServletRequest;
//import jakarta.servlet.http.HttpServletRequestWrapper;
//import jakarta.servlet.http.HttpServletResponse;
//import org.springframework.http.HttpStatus;
//import org.springframework.security.authentication.AbstractAuthenticationToken;
//import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
//import org.springframework.security.core.GrantedAuthority;
//import org.springframework.security.core.authority.SimpleGrantedAuthority;
//import org.springframework.security.core.context.SecurityContextHolder;
//import org.springframework.security.web.authentication.WebAuthenticationDetails;
//import org.springframework.stereotype.Component;
//import org.springframework.web.filter.GenericFilterBean;
//import vn.agis.crm.base.configuration.SpringContext;
//import vn.agis.crm.base.constants.Constants;
//import vn.agis.crm.base.constants.ResponseCode;
//import vn.agis.crm.base.core.filters.UserPrincipal;
//import vn.agis.crm.base.event.Event;
//import vn.agis.crm.base.event.constants.AMQPConstants;
//import vn.agis.crm.base.jpa.entity.User;
//import vn.agis.crm.base.redis.RedisCache;
//import vn.agis.crm.util.RequestUtils;
//
//import java.io.IOException;
//import java.util.*;
//
//@Component
//public class PublicApiFilter extends GenericFilterBean {
//    private RedisCache redisCache = null;
//
//    public RedisCache getRedisCache() {
//        if (redisCache == null) redisCache = SpringContext.getBean(RedisCache.class);
//        return redisCache;
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        if (!request.getRequestURI().startsWith("/api/msimapi")) {
//            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }
//        String authorization = request.getHeader(Constants.ISMService.AUTH_HEADER_STRING);
//        HeaderMapRequestWrapper requestWrapper = new HeaderMapRequestWrapper(request);
//        if (authorization != null && authorization.startsWith(Constants.ISMService.AUTH_TOKEN_PREFIX)) {
//            String token = authorization.substring(Constants.ISMService.AUTH_TOKEN_PREFIX.length());
//            String checkToken  = (String) getRedisCache().get("token" + token, String.class);
//            if (checkToken == null){
//                ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
//                return;
//            }
//            //Kiểm tra token đã expire chưa
//            Event event1 = RequestUtils.amqp(Constants.Method.VALIDATE_PERMISSION_TOKEN_PUBLIC, Constants.Category.USER, token, AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT);
//            if (event1.respStatusCode.equals(ResponseCode.OK)) {
//
//            } else {
//                getRedisCache().remove(token, User.class);
//                ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
//                return;
//            }
//            // nếu token chưa expire thì lấy thông tin trong redis cache ra xử lý
//            User user = (User) getRedisCache().get(token, User.class);
//            if (user == null) {
//                Event event = RequestUtils.amqp(Constants.Method.VALIDATE_PERMISSION_TOKEN_PUBLIC, Constants.Category.USER, token, AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT);
//                logger.info("validate result: " + new Gson().toJson(event));
//                if (event.respStatusCode.equals(ResponseCode.OK)) {
//                    //build principal for authorization
//                    user = (User) event.payload;
//                    try {
//                        getRedisCache().put(token, user, User.class);
//                    } catch (Exception e) {
//                        throw new RuntimeException(e);
//                    }
//                } else {
//                    //throw new BadRequestAlertException("Invalid JWT signature.", "auth", "Invalid JWT signature.");
//                    // custom error response class used across my project
//                    ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
//                    return;
//                }
//            }
////            if (user.getTokenType() != 2){
////                ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
////                return;
////            }
//            user.setPassword("********");
//            requestWrapper.addHeader("userid", String.valueOf(user.getId()));
//            initSecurityInfo(user, token, requestWrapper);
//        }
//        filterChain.doFilter(requestWrapper, servletResponse);
//    }
//
//    private void initSecurityInfo(User user, String token, HttpServletRequest request) {
//        Set<GrantedAuthority> authorities = new HashSet<>();
//        for (String scope : user.getAuthorities()) {
//            authorities.add(new SimpleGrantedAuthority(scope));
//        }
//        String username = user.getUsername() != null ? user.getUsername() : user.getEmail();
//        String email = user.getEmail() != null ? user.getEmail() : "";
////        String fullName = user.getFullName() != null ? user.getFullName() : "";
//        UserPrincipal principal = new UserPrincipal(username, user.getPassword(), authorities, user.getId(), user.getType(), email);
//        AbstractAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, token, authorities);
//        authentication.setDetails(new WebAuthenticationDetails(request));
//        SecurityContextHolder.getContext().setAuthentication(authentication);
//    }
//
//    public class HeaderMapRequestWrapper extends HttpServletRequestWrapper {
//        /**
//         * construct a wrapper for this request
//         *
//         * @param request
//         */
//        public HeaderMapRequestWrapper(HttpServletRequest request) {
//            super(request);
//        }
//
//        private Map<String, String> headerMap = new HashMap<String, String>();
//
//        /**
//         * add a header with given name and value
//         *
//         * @param name
//         * @param value
//         */
//        public void addHeader(String name, String value) {
//            headerMap.put(name, value);
//        }
//
//        @Override
//        public String getHeader(String name) {
//            String headerValue = super.getHeader(name);
//            if (headerMap.containsKey(name)) {
//                headerValue = headerMap.get(name);
//            }
//            return headerValue;
//        }
//
//        /**
//         * get the Header names
//         */
//        @Override
//        public Enumeration<String> getHeaderNames() {
//            List<String> names = Collections.list(super.getHeaderNames());
//            for (String name : headerMap.keySet()) {
//                names.add(name);
//            }
//            return Collections.enumeration(names);
//        }
//
//        @Override
//        public Enumeration<String> getHeaders(String name) {
//            List<String> values = Collections.list(super.getHeaders(name));
//            if (headerMap.containsKey(name)) {
//                values.add(headerMap.get(name));
//            }
//            return Collections.enumeration(values);
//        }
//
//    }
//
//}
