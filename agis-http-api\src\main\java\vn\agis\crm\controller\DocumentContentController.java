//package vn.agis.crm.controller;
//
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.ResponseEntity;
//import org.springframework.web.bind.annotation.*;
//import vn.agis.crm.base.jpa.dto.docs.DocumentContent;
//import vn.agis.crm.base.jpa.dto.docs.Project;
//import vn.agis.crm.service.DocumentContentService;
//
//import java.util.List;
//import java.util.Map;
//
//@RestController
//@RequestMapping("/docs")
//public class DocumentContentController {
//    @Autowired
//    private DocumentContentService documentContentService;
//    @GetMapping("/projectInfo")
//    @CrossOrigin
//    public ResponseEntity<Project> getProjectInfo(){
//        Project project = documentContentService.getProjectInfo();
//        if(project != null){
//            return ResponseEntity.ok(project);
//        }else{
//            return ResponseEntity.notFound().build();
//        }
//    }
//
//    @GetMapping("/search")
//    @CrossOrigin
//    public ResponseEntity<List<DocumentContent>> getPageForProject(){
//        List<DocumentContent> result = documentContentService.getPageForProject();
//        if(result != null){
//            return ResponseEntity.ok(result);
//        }else{
//            return ResponseEntity.notFound().build();
//        }
//    }
//    @GetMapping("/pageInfo")
//    @CrossOrigin
//    public ResponseEntity<DocumentContent> getPageForProject(@RequestParam Map<String,Object> params){
//        DocumentContent documentContent = documentContentService.getPageInfo(params);
//        if(documentContent != null){
//            return ResponseEntity.ok(documentContent);
//        }else{
//            return ResponseEntity.notFound().build();
//        }
//    }
//
//}
