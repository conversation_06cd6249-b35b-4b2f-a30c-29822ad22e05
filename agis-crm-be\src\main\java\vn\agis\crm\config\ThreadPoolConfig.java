package vn.agis.crm.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
@EnableAsync
@Configuration
public class ThreadPoolConfig {
    @Autowired
    private ApplicationContext appContext;

    @Bean(name = "async-services")
    public ThreadPoolTaskExecutor threadPoolAsyncServices() {
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) appContext.getBean("thread-pool");
        return executor;
    }

}

