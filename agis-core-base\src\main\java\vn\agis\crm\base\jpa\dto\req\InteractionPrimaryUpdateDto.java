package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * Request DTO for updating primary interactions
 * Used for PUT /interactions-primary/{id} endpoint
 */
@Data
public class InteractionPrimaryUpdateDto {
    
    @NotNull(message = "ID is required for update")
    private Long id;
    
    @NotNull(message = "Customer offer ID is required")
    private Long customerOfferId;
    
    @NotBlank(message = "Result is required")
    @Size(max = 100, message = "Result must not exceed 100 characters")
    private String result;
    
    @NotBlank(message = "Happened at date is required")
    private String happenedAt; // dd/MM/yyyy format
    
    @Size(max = 5000, message = "Notes must not exceed 5000 characters")
    private String notes;
}
