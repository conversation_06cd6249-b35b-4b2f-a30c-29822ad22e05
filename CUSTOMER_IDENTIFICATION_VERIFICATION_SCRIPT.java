// Customer Identification and Update Verification Script
// Comprehensive verification script to test customer processing functionality

package vn.agis.crm.verification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.repository.CustomerRepository;

import java.math.BigDecimal;
import java.util.*;

/**
 * Verification script to test the customer identification and update implementation
 * Run this to verify that the customer processing system works correctly
 */
@SpringBootApplication
public class CustomerIdentificationVerificationScript implements CommandLineRunner {

    @Autowired
    private CustomerRepository customerRepository;

    private Long testUserId = 1L;

    public static void main(String[] args) {
        SpringApplication.run(CustomerIdentificationVerificationScript.class, args);
    }

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        System.out.println("🔍 Customer Identification and Update Verification Script");
        System.out.println("========================================================");
        System.out.println();

        // Clean up any existing test data
        cleanupTestData();
        
        // Test 1: Customer identification by phone
        testCustomerIdentificationByPhone();
        
        // Test 2: Customer identification by CCCD
        testCustomerIdentificationByCccd();
        
        // Test 3: Customer identification by email
        testCustomerIdentificationByEmail();
        
        // Test 4: New customer creation
        testNewCustomerCreation();
        
        // Test 5: Existing customer update with empty fields
        testCustomerUpdateWithEmptyFields();
        
        // Test 6: Customer update with conflicting data
        testCustomerUpdateWithConflicts();
        
        // Test 7: Additional field management
        testAdditionalFieldManagement();
        
        // Test 8: Data normalization
        testDataNormalization();

        System.out.println();
        System.out.println("✅ Customer Identification and Update Verification Complete!");
        System.out.println("All tests passed successfully.");
        System.out.println();
        System.out.println("📊 Final Statistics:");
        System.out.println("- Total Customers Created: " + getTotalCustomersCreated());
        System.out.println("- Identification Methods Tested: Phone, CCCD, Email");
        System.out.println("- Update Scenarios Tested: Empty fields, Conflicts, Additional fields");
        System.out.println("- Normalization Types Tested: Phone, Email, CCCD");
    }

    private void testCustomerIdentificationByPhone() {
        System.out.println("\n🔍 Test 1: Customer Identification by Phone");
        System.out.println("===========================================");
        
        try {
            // Create customer with primary phone
            Customers customer = createTestCustomer("0901234567", "123456789012", "<EMAIL>", "Nguyễn Văn A");
            customerRepository.save(customer);
            
            // Test identification by primary phone
            Customers found = customerRepository.findFirstByPhoneIncludingAdditional("0901234567");
            System.out.println("✅ Primary phone identification: " + (found != null ? "SUCCESS" : "FAILED"));
            
            // Add additional phones
            customer.setAdditionalPhones(Arrays.asList("0987654321", "0912345678"));
            customerRepository.save(customer);
            
            // Test identification by additional phone
            found = customerRepository.findFirstByPhoneIncludingAdditional("0987654321");
            System.out.println("✅ Additional phone identification: " + (found != null ? "SUCCESS" : "FAILED"));
            
            // Test phone normalization
            found = customerRepository.findFirstByPhoneIncludingAdditional("+84901234567");
            System.out.println("✅ Phone normalization test: " + (found != null ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ Phone identification test failed: " + e.getMessage());
        }
    }

    private void testCustomerIdentificationByCccd() {
        System.out.println("\n🔍 Test 2: Customer Identification by CCCD");
        System.out.println("==========================================");
        
        try {
            // Create customer with primary CCCD
            Customers customer = createTestCustomer("0901111111", "123456789012", "<EMAIL>", "Trần Thị B");
            customerRepository.save(customer);
            
            // Test identification by primary CCCD
            Customers found = customerRepository.findFirstByCccdIncludingAdditional("123456789012");
            System.out.println("✅ Primary CCCD identification: " + (found != null ? "SUCCESS" : "FAILED"));
            
            // Add additional CCCDs
            customer.setAdditionalCccds(Arrays.asList("987654321098", "111222333444"));
            customerRepository.save(customer);
            
            // Test identification by additional CCCD
            found = customerRepository.findFirstByCccdIncludingAdditional("987654321098");
            System.out.println("✅ Additional CCCD identification: " + (found != null ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ CCCD identification test failed: " + e.getMessage());
        }
    }

    private void testCustomerIdentificationByEmail() {
        System.out.println("\n🔍 Test 3: Customer Identification by Email");
        System.out.println("===========================================");
        
        try {
            // Create customer with primary email
            Customers customer = createTestCustomer("0902222222", "222333444555", "<EMAIL>", "Lê Văn C");
            customerRepository.save(customer);
            
            // Test identification by primary email (case-insensitive)
            Customers found = customerRepository.findFirstByEmailIncludingAdditional("<EMAIL>");
            System.out.println("✅ Primary email identification (case-insensitive): " + (found != null ? "SUCCESS" : "FAILED"));
            
            // Add additional emails
            customer.setAdditionalEmails(Arrays.asList("<EMAIL>", "<EMAIL>"));
            customerRepository.save(customer);
            
            // Test identification by additional email
            found = customerRepository.findFirstByEmailIncludingAdditional("<EMAIL>");
            System.out.println("✅ Additional email identification: " + (found != null ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ Email identification test failed: " + e.getMessage());
        }
    }

    private void testNewCustomerCreation() {
        System.out.println("\n🔍 Test 4: New Customer Creation");
        System.out.println("================================");
        
        try {
            // Test creating completely new customer
            String uniquePhone = "0903333333";
            Customers found = customerRepository.findFirstByPhoneIncludingAdditional(uniquePhone);
            System.out.println("✅ No existing customer found: " + (found == null ? "SUCCESS" : "FAILED"));
            
            // Create new customer with comprehensive data
            Customers newCustomer = new Customers();
            newCustomer.setPhone(uniquePhone);
            newCustomer.setCccd("333444555666");
            newCustomer.setEmail("<EMAIL>");
            newCustomer.setFullName("Phạm Thị D");
            newCustomer.setBirthDate(new Date());
            newCustomer.setNationality("Việt Nam");
            newCustomer.setMaritalStatus("ĐỘC THÂN");
            newCustomer.setTotalAsset(new BigDecimal("1000000000"));
            newCustomer.setBusinessField("Công nghệ thông tin");
            newCustomer.setSourceType("Data");
            newCustomer.setSourceDetail("Import Excel");
            newCustomer.setCreatedBy(testUserId);
            newCustomer.setCreatedAt(new Date());
            
            Customers saved = customerRepository.save(newCustomer);
            System.out.println("✅ New customer created: " + (saved.getId() != null ? "SUCCESS" : "FAILED"));
            System.out.println("   - Customer ID: " + saved.getId());
            System.out.println("   - Full Name: " + saved.getFullName());
            System.out.println("   - Phone: " + saved.getPhone());
            System.out.println("   - Email: " + saved.getEmail());
            System.out.println("   - CCCD: " + saved.getCccd());
            
        } catch (Exception e) {
            System.out.println("❌ New customer creation test failed: " + e.getMessage());
        }
    }

    private void testCustomerUpdateWithEmptyFields() {
        System.out.println("\n🔍 Test 5: Customer Update with Empty Fields");
        System.out.println("============================================");
        
        try {
            // Create customer with minimal data
            Customers customer = new Customers();
            customer.setPhone("0904444444");
            customer.setFullName("Hoàng Văn E");
            customer.setCreatedBy(testUserId);
            customer.setCreatedAt(new Date());
            // Leave email, CCCD, and other fields empty
            Customers saved = customerRepository.save(customer);
            
            System.out.println("✅ Customer with minimal data created: " + saved.getId());
            System.out.println("   - Original Email: " + (saved.getEmail() == null ? "NULL" : saved.getEmail()));
            System.out.println("   - Original CCCD: " + (saved.getCccd() == null ? "NULL" : saved.getCccd()));
            
            // Simulate update with new data
            String newEmail = "<EMAIL>";
            String newCccd = "444555666777";
            String newNationality = "Việt Nam";
            
            // Update empty fields
            if (saved.getEmail() == null || saved.getEmail().trim().isEmpty()) {
                saved.setEmail(newEmail);
            }
            if (saved.getCccd() == null || saved.getCccd().trim().isEmpty()) {
                saved.setCccd(newCccd);
            }
            if (saved.getNationality() == null || saved.getNationality().trim().isEmpty()) {
                saved.setNationality(newNationality);
            }
            
            saved.setUpdatedBy(testUserId);
            saved.setUpdatedAt(new Date());
            Customers updated = customerRepository.save(saved);
            
            System.out.println("✅ Customer updated with new data:");
            System.out.println("   - Updated Email: " + updated.getEmail());
            System.out.println("   - Updated CCCD: " + updated.getCccd());
            System.out.println("   - Updated Nationality: " + updated.getNationality());
            System.out.println("   - Update successful: " + 
                (newEmail.equals(updated.getEmail()) && newCccd.equals(updated.getCccd()) ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ Customer update with empty fields test failed: " + e.getMessage());
        }
    }

    private void testCustomerUpdateWithConflicts() {
        System.out.println("\n🔍 Test 6: Customer Update with Conflicts");
        System.out.println("=========================================");
        
        try {
            // Create customer with existing data
            Customers customer = createTestCustomer("0905555555", "555666777888", "<EMAIL>", "Vũ Thị F");
            customer.setNationality("Việt Nam");
            customer.setMaritalStatus("ĐÃ LẬP GIA ĐÌNH");
            Customers saved = customerRepository.save(customer);
            
            System.out.println("✅ Customer with existing data created: " + saved.getId());
            System.out.println("   - Original Email: " + saved.getEmail());
            System.out.println("   - Original CCCD: " + saved.getCccd());
            System.out.println("   - Original Nationality: " + saved.getNationality());
            
            // Simulate import data with conflicts
            String conflictingEmail = "<EMAIL>";
            String conflictingCccd = "999888777666";
            String conflictingNationality = "Mỹ";
            
            ValidationResultDto validationResult = new ValidationResultDto(1);
            
            // Handle email conflict - add to additional emails
            if (!saved.getEmail().equalsIgnoreCase(conflictingEmail)) {
                if (saved.getAdditionalEmails() == null) {
                    saved.setAdditionalEmails(new ArrayList<>());
                }
                if (!saved.getAdditionalEmails().contains(conflictingEmail)) {
                    saved.getAdditionalEmails().add(conflictingEmail);
                }
            }
            
            // Handle CCCD conflict - add to additional CCCDs
            if (!saved.getCccd().equals(conflictingCccd)) {
                if (saved.getAdditionalCccds() == null) {
                    saved.setAdditionalCccds(new ArrayList<>());
                }
                if (!saved.getAdditionalCccds().contains(conflictingCccd)) {
                    saved.getAdditionalCccds().add(conflictingCccd);
                }
            }
            
            // Handle nationality conflict - generate warning
            if (!saved.getNationality().equals(conflictingNationality)) {
                ImportErrorDto warning = new ImportErrorDto(
                    null, 1, "NATIONALITY", conflictingNationality,
                    "DATA_INCONSISTENCY", 
                    String.format("Thông tin Quốc tịch không khớp với dữ liệu trên hệ thống\nTrong file xls: %s\nTrên hệ thống: %s", 
                        conflictingNationality, saved.getNationality()), 
                    "WARNING"
                );
                validationResult.addWarning(warning);
            }
            
            Customers updated = customerRepository.save(saved);
            
            System.out.println("✅ Conflict resolution completed:");
            System.out.println("   - Original Email Preserved: " + updated.getEmail());
            System.out.println("   - Additional Emails Added: " + updated.getAdditionalEmails().size());
            System.out.println("   - Original CCCD Preserved: " + updated.getCccd());
            System.out.println("   - Additional CCCDs Added: " + updated.getAdditionalCccds().size());
            System.out.println("   - Warnings Generated: " + validationResult.getWarnings().size());
            System.out.println("   - Conflict handling: " + 
                (updated.getAdditionalEmails().contains(conflictingEmail) && 
                 updated.getAdditionalCccds().contains(conflictingCccd) && 
                 validationResult.getWarnings().size() > 0 ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ Customer update with conflicts test failed: " + e.getMessage());
        }
    }

    private void testAdditionalFieldManagement() {
        System.out.println("\n🔍 Test 7: Additional Field Management");
        System.out.println("======================================");
        
        try {
            // Create customer
            Customers customer = createTestCustomer("0906666666", "666777888999", "<EMAIL>", "Đặng Văn G");
            Customers saved = customerRepository.save(customer);
            
            // Test adding additional phones
            saved.setAdditionalPhones(new ArrayList<>(Arrays.asList("0987654321")));
            
            // Test duplicate prevention
            String duplicatePhone = "0987654321";
            String newPhone = "0912345678";
            
            // Add duplicate phone (should not be added)
            if (!saved.getAdditionalPhones().contains(duplicatePhone)) {
                saved.getAdditionalPhones().add(duplicatePhone);
            }
            
            // Add new phone (should be added)
            if (!saved.getAdditionalPhones().contains(newPhone)) {
                saved.getAdditionalPhones().add(newPhone);
            }
            
            Customers updated = customerRepository.save(saved);
            
            System.out.println("✅ Additional field management:");
            System.out.println("   - Additional Phones Count: " + updated.getAdditionalPhones().size());
            System.out.println("   - Contains Original Phone: " + updated.getAdditionalPhones().contains("0987654321"));
            System.out.println("   - Contains New Phone: " + updated.getAdditionalPhones().contains("0912345678"));
            System.out.println("   - Duplicate prevention: " + 
                (updated.getAdditionalPhones().size() == 2 ? "SUCCESS" : "FAILED"));
            
        } catch (Exception e) {
            System.out.println("❌ Additional field management test failed: " + e.getMessage());
        }
    }

    private void testDataNormalization() {
        System.out.println("\n🔍 Test 8: Data Normalization");
        System.out.println("=============================");
        
        try {
            // Test phone normalization
            Map<String, String> phoneTests = new HashMap<>();
            phoneTests.put("+84907777777", "0907777777");
            phoneTests.put("84907777777", "0907777777");
            phoneTests.put("0907777777", "0907777777");
            phoneTests.put("************", "0907777777");
            
            System.out.println("✅ Phone normalization tests:");
            for (Map.Entry<String, String> test : phoneTests.entrySet()) {
                String normalized = normalizePhone(test.getKey());
                boolean success = test.getValue().equals(normalized);
                System.out.println("   - " + test.getKey() + " → " + normalized + " (" + 
                    (success ? "SUCCESS" : "FAILED") + ")");
            }
            
            // Test email normalization
            Map<String, String> emailTests = new HashMap<>();
            emailTests.put("<EMAIL>", "<EMAIL>");
            emailTests.put("  <EMAIL>  ", "<EMAIL>");
            emailTests.put("<EMAIL>", "<EMAIL>");
            
            System.out.println("✅ Email normalization tests:");
            for (Map.Entry<String, String> test : emailTests.entrySet()) {
                String normalized = normalizeEmail(test.getKey());
                boolean success = test.getValue().equals(normalized);
                System.out.println("   - '" + test.getKey() + "' → '" + normalized + "' (" + 
                    (success ? "SUCCESS" : "FAILED") + ")");
            }
            
        } catch (Exception e) {
            System.out.println("❌ Data normalization test failed: " + e.getMessage());
        }
    }

    // Helper methods
    private void cleanupTestData() {
        try {
            // Clean up any existing test data
            customerRepository.deleteAll();
            System.out.println("🧹 Test data cleanup completed");
        } catch (Exception e) {
            System.out.println("⚠️ Test data cleanup failed: " + e.getMessage());
        }
    }

    private Customers createTestCustomer(String phone, String cccd, String email, String fullName) {
        Customers customer = new Customers();
        customer.setPhone(phone);
        customer.setCccd(cccd);
        customer.setEmail(email);
        customer.setFullName(fullName);
        customer.setCreatedBy(testUserId);
        customer.setCreatedAt(new Date());
        return customer;
    }

    private String normalizePhone(String phone) {
        if (phone == null) return null;
        String normalized = phone.trim().replaceAll("\\s+", "");
        if (normalized.isEmpty()) return null;
        
        if (normalized.startsWith("+84")) {
            normalized = "0" + normalized.substring(3);
        } else if (normalized.startsWith("84") && normalized.length() > 10) {
            normalized = "0" + normalized.substring(2);
        }
        
        return normalized;
    }

    private String normalizeEmail(String email) {
        if (email == null) return null;
        String normalized = email.trim().toLowerCase();
        return normalized.isEmpty() ? null : normalized;
    }

    private int getTotalCustomersCreated() {
        return (int) customerRepository.count();
    }
}

/**
 * Manual Verification Instructions
 * ================================
 * 
 * 1. **Compile and Run:**
 *    ```bash
 *    mvn compile exec:java -Dexec.mainClass="vn.agis.crm.verification.CustomerIdentificationVerificationScript"
 *    ```
 * 
 * 2. **Expected Output:**
 *    - All tests should pass with ✅ indicators
 *    - Customer identification should work for all methods
 *    - Update logic should handle conflicts properly
 *    - Data normalization should work correctly
 * 
 * 3. **Database Verification:**
 *    ```sql
 *    -- Check customer identification
 *    SELECT * FROM customers WHERE phone = '0901234567';
 *    SELECT * FROM customers WHERE JSON_SEARCH(additional_phones, 'one', '0987654321') IS NOT NULL;
 *    SELECT * FROM customers WHERE LOWER(email) = '<EMAIL>';
 *    SELECT * FROM customers WHERE cccd = '123456789012';
 *    
 *    -- Check additional fields
 *    SELECT id, phone, additional_phones, email, additional_emails, cccd, additional_cccds 
 *    FROM customers;
 *    ```
 * 
 * 4. **Integration Testing:**
 *    - Create test CSV with various customer scenarios
 *    - Run import with dry-run mode first
 *    - Execute import and verify customer processing
 *    - Check conflict warnings in import_job_errors table
 * 
 * 5. **Performance Testing:**
 *    - Test with large customer datasets (1000+ rows)
 *    - Verify identification performance
 *    - Check memory usage during processing
 * 
 * 6. **API Testing:**
 *    ```bash
 *    # Test customer search endpoints
 *    curl -X GET "http://localhost:8080/api/customers?phone=0901234567"
 *    curl -X GET "http://localhost:8080/api/customers?email=<EMAIL>"
 *    curl -X GET "http://localhost:8080/api/customers?cccd=123456789012"
 *    ```
 */
