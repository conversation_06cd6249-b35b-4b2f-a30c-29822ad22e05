package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.SearchUserResponseDTO;
import vn.agis.crm.base.jpa.dto.resp.UserResponseDTO;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.base.utils.StringUtils;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.UserService;

import java.util.List;

@RestController
@RequestMapping("/user-mgmt")
public class UserController extends CrudController<User, Long> {

    private final Logger log = LoggerFactory.getLogger(UserController.class);

    UserService userService;

    @Autowired
    public UserController(UserService service) {
        super(service);
        this.userService = service;
        this.baseUrl = "/user-mgmt";
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('searchUser')")
    public ResponseEntity<Page<SearchUserResponseDTO>> getPageUser(
            @RequestParam(name = "username", required = false, defaultValue = " ") String userName,
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "type", required = false, defaultValue = "-1") Integer type,
            @RequestParam(name = "email", required = false, defaultValue = " ") String email,
            @RequestParam(name = "status", required = false, defaultValue = "") Integer status,
            @RequestParam(name = "loggable", required = false, defaultValue = "false") boolean loggable,
            @RequestParam(name = "nameOrUsername", defaultValue = " ") String nameOrUsername,
            @RequestParam(name = "managerId", required = false, defaultValue = "-1") Long managerId,
            @RequestParam(name = "customerId", required = false, defaultValue = "-1") Long customerId,
            @RequestParam(name = "phone", defaultValue = " ") String phone,
            @RequestParam(name = "provinceCodeAddress", required = false, defaultValue = "-1") Integer provinceCodeAddress,
            @RequestParam(name = "wardCodeAddress", required = false, defaultValue = "-1") Integer wardCodeAddress,
            @RequestParam(name = "apartment", required = false, defaultValue = "-1") Integer apartment,
            @RequestParam(name = "deviceTypeId", required = false, defaultValue = "-1") Integer deviceTypeId,
            @RequestParam(name = "forDevice", required = false, defaultValue = "0") Integer forDevice,
            @RequestParam(name = "editingUserIdForDevice", required = false, defaultValue = "-1") Integer editingUserIdForDevice,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE) @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE) @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT) @RequestParam(name = "sort", required = false, defaultValue = "createdDate,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        // có nhiều province_code trong DB sau khi join => lỗi column is ambiguously defined => chỉ rõ sort theo u.province_code
        SearchUserRequest searchUserRequest = new SearchUserRequest(userName, name, type, email, status, page, size,
            StringUtils.camelToSnake(sortBy), phone, nameOrUsername, managerId, customerId, provinceCodeAddress, wardCodeAddress, apartment,
            deviceTypeId, forDevice, editingUserIdForDevice);
        searchUserRequest.setLoggable(loggable);
        Page<SearchUserResponseDTO> userLst = userService.getPage(searchUserRequest, listRequest.getPageable());
        return ResponseEntity.ok().body(userLst);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('getUser')")
    public UserResponseDTO getOneUser(@PathVariable Long id) {
        return userService.getOne(id);
    }

    @PutMapping("/change-status/{id}")
    @PreAuthorize("hasAnyAuthority('updateUser')")
    public void changeStatusUser(@PathVariable Long id) {
        userService.changeStatusUser(id);
    }

    @DeleteMapping("/delete/{id}")
    @PreAuthorize("hasAnyAuthority('deleteUser')")
    public void deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
    }


    @PostMapping("/create")
    @PreAuthorize("hasAnyAuthority('createUser')")
    public User createUser(@RequestBody CreateUserReq createUserReq) {
        return userService.createUser(createUserReq);
    }

    @PutMapping("/update/{id}")
    @PreAuthorize("hasAnyAuthority('updateUser')")
    public User updateUser(@RequestBody UpdateUserReq updateUserReq, @PathVariable Long id) {
        updateUserReq.setId(id);
        return userService.updateUser(updateUserReq);
    }

    @GetMapping("/view-profile")
    public UserResponseDTO viewProfile() {
        return userService.viewProfile();
    }

    @PutMapping("/update-profile")
    public User updateProfile(@RequestBody UpdateUserReq updateUserReq) {
        return userService.updateProfile(updateUserReq);
    }

    @GetMapping("/check-exist")
    public Integer countByEmailOrUsername(@RequestParam(required = false) String email,
                                          @RequestParam(required = false) String username) {
        CheckExistUserReq checkExistUserReq = new CheckExistUserReq();
        checkExistUserReq.setUsername(username);
        checkExistUserReq.setEmail(email);
        return userService.checkExistCreateUser(checkExistUserReq);
    }


    @GetMapping("/get-list-role")
    @PreAuthorize("hasAnyAuthority('createUser','updateUser','getUser')")
    @Operation(description = "Chỉ dùng cho phần quản lý tài khoản")
    public List<Role> getListRole(@RequestParam(required = false, defaultValue = "-1") Integer type,
                                  @RequestParam(required = false, defaultValue = "-1") Long accountRootId
    ) {
        SearchRoleCreateAccountReq search = new SearchRoleCreateAccountReq(type, accountRootId);
        return userService.getListRole(search);
    }

    @GetMapping(path = "/current")
    public User currentUser(HttpServletRequest request) {
        String authorization = request.getHeader(Constants.CRMService.AUTH_HEADER_STRING);
        if (authorization != null && authorization.startsWith(Constants.CRMService.AUTH_TOKEN_PREFIX)) {
            String token = authorization.substring(Constants.CRMService.AUTH_TOKEN_PREFIX.length());
            if (token.isEmpty()) return null;
            return userService.currentUser(token);
        } else if (authorization != null && authorization.startsWith(Constants.CRMService.AUTH_CODE_PREFIX)) {
            String authCode = authorization.substring(Constants.CRMService.AUTH_CODE_PREFIX.length());
            if (authCode.isEmpty()) return null;
            return userService.currentUser(authCode);
        } else {
            return null;
        }
    }

    @RequestMapping(path = "/forgot-password/init", method = RequestMethod.POST)
    public ResponseEntity<Void> requestForgotPassword(@RequestParam("email") String email) {
        userService.forgotPasswordInit(email);
        return (new ResponseEntity<>(HttpStatus.OK));
    }

    @RequestMapping(path = "/forgot-password/finish", method = RequestMethod.POST)
    public void changeForgotPassword(@RequestBody ResetPasswordInfo resetPasswordVM) {
        userService.forgotPasswordFinish(resetPasswordVM);
    }

    @RequestMapping(path = "/change-password", method = RequestMethod.POST)
    public void changePassword(@RequestBody ChangePasswordInfo changePasswordInfo) {
        userService.changePassword(changePasswordInfo);
    }

    @RequestMapping(path = "/validate-token-mail", method = RequestMethod.POST)
    public void validateTokenMail(@RequestBody ResetPasswordInfo resetPasswordInfo) {
        userService.validateTokenMail(resetPasswordInfo);
    }

    @RequestMapping(path = "/change-user-for-manager", method = RequestMethod.POST)
    public ResponseEntity<?> changeUserForManager(@RequestBody ChangeUserForManagerReq changeUserForManagerReq) {
        userService.changeUserForManager(changeUserForManagerReq);
        return (new ResponseEntity<>(HttpStatus.OK));
    }

    @GetMapping("/get-no-one-managed-customer-accounts")
    @PreAuthorize("hasAnyAuthority('searchUser')")
    public ResponseEntity<Page<SearchUserResponseDTO>> getNoOneManagedCustomerAccounts(
            @RequestParam(name = "username", required = false, defaultValue = " ") String userName,
            @RequestParam(name = "provinceCode", required = false, defaultValue = " ") String provinceCode,
            @RequestParam(name = "managerId", required = false, defaultValue = "-1") Long managerId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE) @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE) @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT) @RequestParam(name = "sort", required = false, defaultValue = "createdDate,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        SearchUserRequest searchUserRequest = new SearchUserRequest(userName,  managerId, page, size,
                StringUtils.camelToSnake(sortBy));
        Page<SearchUserResponseDTO> userLst = userService.getNoOneManagedCustomerAccounts(searchUserRequest, listRequest.getPageable());
        return ResponseEntity.ok().body(userLst);
    }


    @PostMapping("/getListActivatedAccount")
    public ResponseEntity<List<Long>> getListActivatedAccount (@RequestBody List<Long> listAccountId) {
        return ResponseEntity.ok().body(userService.getListActivatedAccount(listAccountId));
    }

    //load ds tài khoản ở phần sim, giống search, chỉ thêm quyền phần searchSim
    @GetMapping("/dropdown")
    @PreAuthorize("hasAnyAuthority('searchUser','searchSim')")
    public ResponseEntity<Page<SearchUserResponseDTO>> dropdownListUser(
            @RequestParam(name = "username", required = false, defaultValue = " ") String userName,
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "type", required = false, defaultValue = "-1") Integer type,
            @RequestParam(name = "email", required = false, defaultValue = " ") String email,
            @RequestParam(name = "status", required = false, defaultValue = "") Integer status,
            @RequestParam(name = "loggable", required = false, defaultValue = "false") boolean loggable,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "phone", required = false, defaultValue = " ") String phone,
            @RequestParam(name = "nameOrUsername", required = false, defaultValue = " ") String nameOrUsername,
            @RequestParam(name = "managerId", required = false, defaultValue = "-1") Long managerId,
            @RequestParam(name = "customerId", required = false, defaultValue = "-1") Long customerId,
            @RequestParam(name = "provinceCodeAddress", required = false, defaultValue = "-1") Integer provinceCodeAddress,
            @RequestParam(name = "wardCodeAddress", required = false, defaultValue = "-1") Integer wardCodeAddress,
            @RequestParam(name = "apartment", required = false, defaultValue = "-1") Integer apartment,
            @RequestParam(name = "deviceTypeId", required = false, defaultValue = "-1") Integer deviceTypeId,
            @RequestParam(name = "forDevice", required = false, defaultValue = "0") Integer forDevice,
            @RequestParam(name = "editingUserIdForDevice", required = false, defaultValue = "-1") Integer editingUserIdForDevice,
            @RequestParam(name = "sort", required = false, defaultValue = "createdDate,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);

        if (sortBy.startsWith("provinceCode")) {
            sortBy = "u." + sortBy;
        }
        SearchUserRequest searchUserRequest = new SearchUserRequest(userName, name, type, email, status, page, size,
            StringUtils.camelToSnake(sortBy), phone, nameOrUsername, managerId, customerId, provinceCodeAddress, wardCodeAddress, apartment,
            deviceTypeId, forDevice, editingUserIdForDevice);
        searchUserRequest.setLoggable(loggable);
        Page<SearchUserResponseDTO> userLst = userService.getPage(searchUserRequest, listRequest.getPageable());
        return ResponseEntity.ok().body(userLst);
    }

    @GetMapping("/get-province")
    public ResponseEntity<List<Province>> getProvince() {
        List<Province> province = userService.getProvince();
        if (province == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
        return ResponseEntity.ok(province);
    }

    @GetMapping("/get-ward")
    public ResponseEntity<List<Ward>> getWard(
            @RequestParam (name = "provinceCode", required = false, defaultValue = "-1") Integer provinceCode,
            @RequestParam (name = "keySearch", required = false, defaultValue = " ") String keySearch
    ) {
        SearchWardRequest searchWardRequest = new SearchWardRequest(provinceCode, keySearch);
        List<Ward> wardList = userService.searchWard(searchWardRequest);
        return ResponseEntity.ok(wardList);
    }


}

