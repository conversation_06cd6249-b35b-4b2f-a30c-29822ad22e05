pipeline {
    agent { label 'agent-manager-node' }

    environment {
        REGISTRY = '************:5000'
        IMAGE_NAME = 'agis-core-base'
        IMAGE_TAG = 'v1.0'
        FULL_IMAGE = "${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    }

    stages {
        stage('Clean workspace') {
            steps {
                deleteDir()
            }
        }

        stage('Checkout Source') {
            steps {
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: '*/dev']],
                    userRemoteConfigs: [[
                        url: 'https://gitlab.com/agis6491334/agis-core-base',
                        credentialsId: '2f533e09-5c53-40c4-94f2-3b95fbe52752'
                    ]]
                ])
            }
        }

        stage('Build Java App') {
            steps {
                sh 'mvn clean install -DskipTests'
            }
        }



    }
}
