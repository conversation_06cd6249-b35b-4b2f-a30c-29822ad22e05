package vn.agis.crm.base.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.util.Base64;
import java.util.Collections;
import java.util.List;

/**
 * Created by huyvv
 * Date: 17/01/2020
 * Time: 10:04 AM
 * for all issues, contact me: <EMAIL>
 **/
public class ObjectMapperUtil {
    private static final Logger logger = LoggerFactory.getLogger(ObjectMapperUtil.class);

    private ObjectMapperUtil() {

    }

    public static <T> T objectMapper(String json, Class<?> type) {
        try {
            ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            Object o = mapper.readValue(json, type);
            return (T) o;
        } catch (Exception ex) {
            logger.error(ex.toString());
            return null;
        }
    }

    public static <T> List<T> listMapper(String json, Class<?> type) {
        try {
            ObjectMapper mapper = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            List<Object> o = mapper.readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, type));
            return (List<T>) o;
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return Collections.emptyList();
        }
    }

    public static <T> String toJsonString(T object) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return "";
        }
    }

    /**
     * hàm này sẽ không lấy các field giá trị null vào jsonString
     * @param object
     * @param <T>
     * @return
     */
    public static <T> String toJsonStringExcludeNullField(T object) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            return mapper.writeValueAsString(object);
        } catch (Exception ex) {
            logger.error(ex.getMessage(), ex);
            return "";
        }
    }

    public static String serializeObject(Object obj) throws IOException {
        ByteArrayOutputStream outputStream = null;
        ObjectOutputStream objectOutputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            objectOutputStream = new ObjectOutputStream(outputStream);
            objectOutputStream.writeObject(obj);
            objectOutputStream.close();
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        }finally {
            if(objectOutputStream != null){
                objectOutputStream.close();
            }
            if(outputStream != null){
                outputStream.close();
            }
        }
    }

    public static Object deserializeObject(String str) throws IOException, ClassNotFoundException {
        ByteArrayInputStream inputStream = null;
        ObjectInputStream objectInputStream = null;
        try {
            inputStream = new ByteArrayInputStream(Base64.getDecoder().decode(str));
            objectInputStream = new ObjectInputStream(inputStream);
            return objectInputStream.readObject();
        }finally {
            if(objectInputStream != null){
                objectInputStream.close();
            }
            if(inputStream != null){
                inputStream.close();
            }
        }
    }
}
