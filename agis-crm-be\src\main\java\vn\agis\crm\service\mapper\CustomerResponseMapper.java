package vn.agis.crm.service.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;
import vn.agis.crm.base.jpa.dto.CustomerRelativeDto;
import vn.agis.crm.base.jpa.dto.CustomerPropertyDto;
import vn.agis.crm.base.jpa.dto.CustomerOfferDto;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.CustomerRelatives;
import vn.agis.crm.base.jpa.entity.CustomerProperties;
import vn.agis.crm.base.jpa.entity.CustomerOffers;
import vn.agis.crm.repository.*;

import java.util.List;
import java.util.stream.Collectors;

@Component
public class CustomerResponseMapper {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private CustomerRelativeRepository customerRelativeRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;

    public CustomerResDto toResDto(Customers c) {
        CustomerResDto dto = new CustomerResDto();
        dto.setId(c.getId()); dto.setFullName(c.getFullName()); dto.setPhone(c.getPhone()); dto.setEmail(c.getEmail()); dto.setCccd(c.getCccd());
        dto.setBirthDate(c.getBirthDate()); dto.setAddressContact(c.getAddressContact()); dto.setAddressPermanent(c.getAddressPermanent());
        dto.setNationality(c.getNationality()); dto.setMaritalStatus(c.getMaritalStatus()); dto.setTotalAsset(c.getTotalAsset());
        dto.setBusinessField(c.getBusinessField()); dto.setAvatarUrl(c.getAvatarUrl()); dto.setZaloStatus(c.getZaloStatus());
        dto.setFacebookLink(c.getFacebookLink()); dto.setSourceType(c.getSourceType()); dto.setSourceDetail(c.getSourceDetail());
        dto.setNotes(c.getNotes());

        // Map multiple contact information fields
        dto.setAdditionalPhones(c.getAdditionalPhones());
        dto.setAdditionalEmails(c.getAdditionalEmails());
        dto.setAdditionalCccds(c.getAdditionalCccds());

        // Map interests field
        dto.setInterests(c.getInterests());
        if (c.getCurrentManagerId() != null) {
            employeeRepository.findById(c.getCurrentManagerId()).ifPresent(e -> dto.setCurrentManager(toEmployeeSummaryWithAssignment(e, c.getId(), 1)));
        }
        if (c.getCurrentStaffId() != null) {
            employeeRepository.findById(c.getCurrentStaffId()).ifPresent(e -> dto.setCurrentStaff(toEmployeeSummaryWithAssignment(e, c.getId(), 2)));
        }
        // load relatives
        List<CustomerRelatives> rels = customerRelativeRepository.findByCustomerId(c.getId());
        dto.setRelatives(rels.stream().map(this::toRelativeDto).collect(Collectors.toList()));

        // properties
        List<CustomerProperties> props = customerPropertyRepository.findByCustomerId(c.getId());
        dto.setCustomerProperties(props.stream().map(p -> {
            CustomerPropertyDto dtoP = this.toPropertyDto(p);
            java.util.List<vn.agis.crm.base.jpa.entity.InteractionsSecondary> list = interactionsSecondaryRepository.findByCustomerPropertyId(p.getId());
            if (list != null) {
                dtoP.setInteractionsSecondary(list.stream().map(this::toInteractionSecondaryDto).collect(Collectors.toList()));
            }
            return dtoP;
        }).collect(Collectors.toList()));

        // offers
        List<CustomerOffers> offers = customerOfferRepository.findByCustomerId(c.getId());
        dto.setCustomerOffers(offers.stream().map(o -> {
            CustomerOfferDto dtoO = this.toOfferDto(o);
            java.util.List<vn.agis.crm.base.jpa.entity.InteractionsPrimary> list = interactionsPrimaryRepository.findByCustomerOfferId(o.getId());
            if (list != null) {
                dtoO.setInteractionsPrimary(list.stream().map(this::toInteractionPrimaryDto).collect(Collectors.toList()));
            }
            return dtoO;
        }).collect(Collectors.toList()));
        return dto;
    }

    private CustomerRelativeDto toRelativeDto(CustomerRelatives r) {
        CustomerRelativeDto dto = new CustomerRelativeDto();
        dto.setId(r.getId());
        dto.setRelationType(r.getRelationType());
        dto.setFullName(r.getFullName());
        dto.setYearOfBirth(r.getYearOfBirth());
        dto.setPhone(r.getPhone());
        dto.setNotes(r.getNotes());
        return dto;
    }

    private EmployeeSummaryDto toEmployeeSummary(Employee e) {
        EmployeeSummaryDto s = new EmployeeSummaryDto();
        s.setId(e.getId()); s.setEmployeeCode(e.getEmployeeCode()); s.setFullName(e.getFullName());
        s.setPhone(e.getPhone()); s.setEmail(e.getEmail());
        return s;
    }

    private EmployeeSummaryDto toEmployeeSummaryWithAssignment(Employee e, Long customerId, Integer roleType) {
        EmployeeSummaryDto s = new EmployeeSummaryDto();
        s.setId(e.getId()); s.setEmployeeCode(e.getEmployeeCode()); s.setFullName(e.getFullName());
        s.setPhone(e.getPhone()); s.setEmail(e.getEmail());

        // Fetch the most recent active assignment for this employee, customer, and role type
        java.util.List<vn.agis.crm.base.jpa.entity.CustomerAssignments> assignments =
            customerAssignmentRepository.findByCustomerIdAndEmployeeIdAndRoleTypeOrderByAssignedFromDesc(customerId, e.getId(), roleType);

        if (!assignments.isEmpty()) {
            vn.agis.crm.base.jpa.entity.CustomerAssignments latestAssignment = assignments.get(0);
            s.setAssignmentId(latestAssignment.getId()); // Bổ sung assignmentId
            s.setAssignedFrom(latestAssignment.getAssignedFrom());
            s.setAssignedTo(latestAssignment.getAssignedTo());
        }

        return s;
    }

    private CustomerPropertyDto toPropertyDto(CustomerProperties cp) {
        CustomerPropertyDto dto = new CustomerPropertyDto();
        dto.setId(cp.getId());
        dto.setProjectId(cp.getProjectId());
        dto.setUnitId(cp.getUnitId());
        dto.setTransactionDate(cp.getTransactionDate());
        dto.setContractPrice(cp.getContractPrice());
        dto.setExternalAgencyName(cp.getExternalAgencyName());
        dto.setExternalSaleName(cp.getExternalSaleName());
        dto.setExternalSalePhone(cp.getExternalSalePhone());
        dto.setExternalSaleEmail(cp.getExternalSaleEmail());
        dto.setEmployeeId(cp.getEmployeeId());
        dto.setNotes(cp.getNotes());
        dto.setLegalStatus(cp.getLegalStatus()); // NEW: Map legal status
        dto.setFirstInteraction(cp.getFirstInteraction());
        dto.setLastInteraction(cp.getLastInteraction());
        return dto;
    }

    private CustomerOfferDto toOfferDto(CustomerOffers co) {
        CustomerOfferDto dto = new CustomerOfferDto();
        dto.setId(co.getId());
        dto.setProjectId(co.getProjectId());
        dto.setFirstInteraction(co.getFirstInteraction());
        dto.setLastInteraction(co.getLastInteraction());
        dto.setStatus(co.getStatus());
        dto.setNotes(co.getNotes());
        return dto;
    }

    private vn.agis.crm.base.jpa.dto.InteractionSecondaryDto toInteractionSecondaryDto(vn.agis.crm.base.jpa.entity.InteractionsSecondary is) {
        vn.agis.crm.base.jpa.dto.InteractionSecondaryDto dto = new vn.agis.crm.base.jpa.dto.InteractionSecondaryDto();
        dto.setId(is.getId());
        dto.setExpectedSellPrice(is.getExpectedSellPrice());
        dto.setExpectedRentPrice(is.getExpectedRentPrice());
        dto.setResult(is.getResult());
        if (is.getHappenedAt() != null) {
            dto.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").format(is.getHappenedAt()));
        }
        dto.setNotes(is.getNotes());

        // NEW: Set audit fields for tracking who created the interaction
        dto.setCreatedBy(is.getCreatedBy());
        if (is.getCreatedBy() != null) {
            Employee employee = employeeRepository.findById(is.getCreatedBy()).orElse(null);
            dto.setCreatedName(employee != null ? employee.getFullName() : null);
        }

        return dto;
    }

    private vn.agis.crm.base.jpa.dto.InteractionPrimaryDto toInteractionPrimaryDto(vn.agis.crm.base.jpa.entity.InteractionsPrimary ip) {
        vn.agis.crm.base.jpa.dto.InteractionPrimaryDto dto = new vn.agis.crm.base.jpa.dto.InteractionPrimaryDto();
        dto.setId(ip.getId());
        dto.setResult(ip.getResult());
        if (ip.getHappenedAt() != null) {
            dto.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").format(ip.getHappenedAt()));
        }
        dto.setNotes(ip.getNotes());

        // NEW: Set audit fields for tracking who created the interaction
        dto.setCreatedBy(ip.getCreatedBy());
        if (ip.getCreatedBy() != null) {
            Employee employee = employeeRepository.findById(ip.getCreatedBy()).orElse(null);
            dto.setCreatedName(employee != null ? employee.getFullName() : null);
        }

        return dto;
    }
}

