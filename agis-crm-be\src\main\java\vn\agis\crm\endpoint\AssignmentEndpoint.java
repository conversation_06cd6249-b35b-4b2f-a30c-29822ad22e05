package vn.agis.crm.endpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.service.AssignmentService;

@Component
public class AssignmentEndpoint {

    @Autowired
    private AssignmentService assignmentService;

    public Event process(Event event) {
        return assignmentService.process(event);
    }
}

