# Assignment Service Notification Enhancement

## Overview

The `AssignmentService` has been enhanced to automatically create notifications when new leads are assigned to employees. This feature integrates with the existing notification system and is configurable through system settings.

## ✅ **Implementation Details**

### **Enhanced Dependencies**
```java
@Service
@Transactional
public class AssignmentService {
    
    private static final Logger logger = LoggerFactory.getLogger(AssignmentService.class);
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;
    
    @Autowired
    private ConfigRepository configRepository; // NEW
    
    @Autowired
    private NotificationService notificationService; // NEW
}
```

### **Enhanced manualAssign Method**

The `manualAssign` method now includes notification creation after successful assignments:

**Manager Assignment Enhancement:**
```java
// After successful manager assignment
customerAssignmentRepository.saveAll(newAssignments);
customerRepository.updateCurrentManager(customersNeedingManagerAssignment, managerId);

// NEW: Create notifications for Manager assignments
createNotificationsForAssignments(customersNeedingManagerAssignment, managerId, 1, event.userId);
```

**Staff Assignment Enhancement:**
```java
// After successful staff assignment
customerAssignmentRepository.saveAll(newAssignments);
customerRepository.updateCurrentStaff(customersNeedingStaffAssignment, staffId);

// NEW: Create notifications for Staff assignments
createNotificationsForAssignments(customersNeedingStaffAssignment, staffId, 2, event.userId);
```

### **New Notification Creation Method**

```java
/**
 * Create notifications for new lead assignments based on system configuration
 * 
 * @param customerIds List of customer IDs that were assigned
 * @param employeeId ID of the employee who received the assignments
 * @param roleType Role type (1=Manager, 2=Staff)
 * @param createdBy ID of the user who made the assignment
 */
private void createNotificationsForAssignments(List<Long> customerIds, Long employeeId, 
                                             Integer roleType, Long createdBy)
```

## ✅ **Configuration-Based Notifications**

### **System Configuration Keys**

1. **Manager Notifications**: `"ON_NOTIFICATION_NEW_MANAGER_LEAD"`
   - Controls notifications for Manager role assignments (roleType = 1)
   - Value: `"ON"` = enabled, any other value = disabled

2. **Staff Notifications**: `"ON_NOTIFICATION_NEW_STAFF_LEAD"`
   - Controls notifications for Staff role assignments (roleType = 2)
   - Value: `"ON"` = enabled, any other value = disabled

### **Configuration Check Logic**
```java
// Check system configuration
Config config = configRepository.findOneByConfigKeyIgnoreCase(configKey);
if (config == null || !"ON".equalsIgnoreCase(config.getConfigValue())) {
    logger.debug("Notification disabled for {} assignments", roleDisplayName);
    return;
}
```

## ✅ **Notification Content**

### **Notification Structure**
- **Type**: `1` (LeadAssigned)
- **Title**: `"Lead mới được phân công"`
- **Content**: Detailed information about the customer and role
- **Target Employee**: The assigned employee
- **Target Customer**: The assigned customer
- **Created By**: The user who made the assignment

### **Content Template**
```java
String content = String.format(
    "Bạn đã được phân công chăm sóc khách hàng %s (SĐT: %s) với vai trò %s. " +
    "Hãy liên hệ trong vòng 24 giờ để tạo ấn tượng tốt.",
    customer.getFullName() != null ? customer.getFullName() : "N/A",
    customer.getPhone() != null ? customer.getPhone() : "N/A",
    roleDisplayName // "Manager" or "Staff"
);
```

## ✅ **Smart Assignment Detection**

### **Only Creates Notifications for Real Changes**

The notification system leverages the existing assignment logic that already filters out duplicate assignments:

1. **Manager Assignments**: Uses `filterCustomersForManagerAssignment()` to identify customers that actually need new manager assignment
2. **Staff Assignments**: Uses `filterCustomersForStaffAssignment()` to identify customers that actually need new staff assignment

### **No Notifications for:**
- Customers already assigned to the same manager/staff
- Dry run operations (`request.isDryRun() == true`)
- Null assignments (clearing assignments)

## ✅ **Error Handling & Reliability**

### **Graceful Error Handling**
```java
try {
    // Create notification for each customer
    Notifications notification = notificationService.createNotification(...);
    logger.debug("Created notification {} for employee {}", notification.getId(), employeeId);
    
} catch (Exception e) {
    logger.error("Failed to create notification: {}", e.getMessage(), e);
    // Continue with other customers - don't fail the entire assignment process
}
```

### **Transaction Integrity**
- Notification failures do not affect assignment success
- Each notification is created independently
- Comprehensive logging for debugging and monitoring
- Assignment process continues even if notification creation fails

## ✅ **Logging & Monitoring**

### **Debug Logging**
- Configuration check results
- Number of notifications being created
- Individual notification creation success

### **Info Logging**
- Summary of notification processing results

### **Error Logging**
- Individual notification creation failures
- System-level notification processing errors

### **Example Log Output**
```
DEBUG: Notification disabled for Manager assignments (config: OFF)
DEBUG: Creating notifications for 3 Staff assignments to employee 456
DEBUG: Created notification 789 for employee 456 about customer 123 (Nguyễn Văn A)
INFO:  Successfully processed notifications for 3 Staff assignments to employee 456
```

## ✅ **Usage Examples**

### **Example 1: Manager Assignment with Notifications Enabled**

**System Configuration:**
```sql
INSERT INTO configs (config_key, config_value, config_type, description) 
VALUES ('ON_NOTIFICATION_NEW_MANAGER_LEAD', 'ON', 1, 'Enable notifications for new manager lead assignments');
```

**Assignment Request:**
```json
{
  "customerIds": [123, 456, 789],
  "managerId": 100,
  "staffId": null,
  "dryRun": false
}
```

**Result:**
- 3 customers assigned to manager 100
- 3 notifications created for employee 100
- Each notification contains customer-specific information

### **Example 2: Staff Assignment with Notifications Disabled**

**System Configuration:**
```sql
INSERT INTO configs (config_key, config_value, config_type, description) 
VALUES ('ON_NOTIFICATION_NEW_STAFF_LEAD', 'OFF', 1, 'Disable notifications for new staff lead assignments');
```

**Assignment Request:**
```json
{
  "customerIds": [123, 456],
  "managerId": null,
  "staffId": 200,
  "dryRun": false
}
```

**Result:**
- 2 customers assigned to staff 200
- No notifications created (disabled by configuration)
- Assignment completes successfully

### **Example 3: Mixed Assignment with Partial Notifications**

**System Configuration:**
```sql
INSERT INTO configs (config_key, config_value, config_type, description) 
VALUES ('ON_NOTIFICATION_NEW_MANAGER_LEAD', 'ON', 1, 'Enable notifications for manager assignments');
INSERT INTO configs (config_key, config_value, config_type, description) 
VALUES ('ON_NOTIFICATION_NEW_STAFF_LEAD', 'OFF', 1, 'Disable notifications for staff assignments');
```

**Assignment Request:**
```json
{
  "customerIds": [123, 456, 789],
  "managerId": 100,
  "staffId": 200,
  "dryRun": false
}
```

**Result:**
- 3 customers assigned to both manager 100 and staff 200
- 3 notifications created for manager 100 (enabled)
- No notifications created for staff 200 (disabled)

## ✅ **Backward Compatibility**

### **No Breaking Changes**
- All existing API endpoints work unchanged
- Existing assignment logic remains intact
- New notification functionality is additive only
- Configuration-based enabling/disabling

### **Default Behavior**
- If configuration keys don't exist: notifications disabled
- If configuration values are not "ON": notifications disabled
- Assignment process works normally regardless of notification status

## ✅ **Performance Considerations**

### **Efficient Database Operations**
- Single config lookup per role type per assignment batch
- Batch customer information retrieval
- Individual notification creation (for error isolation)

### **Minimal Performance Impact**
- Notifications created after successful assignment
- No additional database queries for duplicate detection (reuses existing logic)
- Asynchronous notification creation (doesn't block assignment response)

## ✅ **Testing Recommendations**

### **Unit Tests**
1. Test notification creation with enabled configuration
2. Test notification skipping with disabled configuration
3. Test error handling when notification creation fails
4. Test assignment success when notification service is unavailable

### **Integration Tests**
1. Test end-to-end assignment with notification creation
2. Test configuration changes affecting notification behavior
3. Test notification content accuracy with real customer data

### **Configuration Tests**
1. Test with missing configuration keys
2. Test with various configuration values ("ON", "OFF", "on", "off", null, etc.)
3. Test configuration changes without service restart

This enhancement provides a robust, configurable notification system for lead assignments while maintaining full backward compatibility and transaction integrity with the existing AGIS CRM assignment workflow.
