package vn.agis.crm.base.redis.apigw;

import java.io.Serializable;
import java.util.List;

public class LoginInfoCache implements Serializable {
    private String clientIP;
    private Long uacpId;
    private String protocol;
    private List<Long> uacpIds;
    private String username;
    private String loginType;
    private Long expireTime;
    private Long userId;
    private String sessionId;
    private Long updated;
    private Long resetExpireSessionPeriod;
    public String promoNumber;
    public String virtualIsdn;

    public String getClientIP() {
        return clientIP;
    }

    public void setClientIP(String clientIP) {
        this.clientIP = clientIP;
    }

    public List<Long> getUacpIds() {
        return uacpIds;
    }

    public void setUacpIds(List<Long> uacpIds) {
        this.uacpIds = uacpIds;
    }

    public String getLoginType() {
        return loginType;
    }

    public void setLoginType(String loginType) {
        this.loginType = loginType;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Long getUacpId() {
        return uacpId;
    }

    public void setUacpId(Long uacpId) {
        this.uacpId = uacpId;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public Long getResetExpireSessionPeriod() {
        return resetExpireSessionPeriod;
    }

    public void setResetExpireSessionPeriod(Long resetExpireSessionPeriod) {
        this.resetExpireSessionPeriod = resetExpireSessionPeriod;
    }

    public String getPromoNumber() {
        return promoNumber;
    }

    public void setPromoNumber(String promoNumber) {
        this.promoNumber = promoNumber;
    }

    public String getVirtualIsdn() {
        return virtualIsdn;
    }

    public void setVirtualIsdn(String virtualIsdn) {
        this.virtualIsdn = virtualIsdn;
    }
}
