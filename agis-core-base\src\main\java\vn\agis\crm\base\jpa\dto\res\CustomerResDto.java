package vn.agis.crm.base.jpa.dto.res;

import lombok.Data;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CustomerResDto {
    private Long id;
    private String fullName;
    private String phone;
    private String email;
    private String cccd;

    // Multiple contact information fields
    private List<String> additionalPhones;
    private List<String> additionalEmails;
    private List<String> additionalCccds;

    // Customer interests and preferences
    private String interests;
    private Date birthDate;
    private String addressContact;
    private String addressPermanent;
    private String nationality;
    private String maritalStatus;
    private BigDecimal totalAsset;
    private String businessField;
    private String avatarUrl;
    private String zaloStatus;
    private String facebookLink;
    private String sourceType;
    private String sourceDetail;
    private String notes;

    private EmployeeSummaryDto currentManager;
    private EmployeeSummaryDto currentStaff;

    // Added for v2 response
    private java.util.List<vn.agis.crm.base.jpa.dto.CustomerRelativeDto> relatives;
    private java.util.List<vn.agis.crm.base.jpa.dto.CustomerPropertyDto> customerProperties;
    private java.util.List<vn.agis.crm.base.jpa.dto.CustomerOfferDto> customerOffers;
}

