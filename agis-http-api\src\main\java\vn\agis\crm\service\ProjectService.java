package vn.agis.crm.service;

import java.util.HashMap;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.ProjectDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.ProjectSearchDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.dto.res.ProjectWithStatsDto;
import vn.agis.crm.base.jpa.entity.Projects;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

@Service
public class ProjectService extends CrudService<Projects, Long> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectService.class);

    public ProjectService() {
        super(Projects.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Category.PROJECT;
    }

    public Page<ProjectWithStatsDto> search(ProjectSearchDto searchDTO, Pageable pageable) {
        List<ProjectWithStatsDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), ProjectWithStatsDto.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in search: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Projects createProject(ProjectDto projectDto) {
        Projects response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, projectDto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Projects) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createProject: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, projectDto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Projects getOne(Long id) {
        Projects response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Projects) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in updateAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Projects update(ProjectDto projectDto) {
        Projects response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.UPDATE, category, projectDto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Projects) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createProject: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, projectDto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Boolean checkExistName(String name) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CHECK_EXIST_PROJECT_NAME, category, name, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                Object payload = event.payload;
                if (payload instanceof Boolean) return (Boolean) payload;
                // fallback parse
                return Boolean.valueOf(String.valueOf(payload));
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in checkExistName: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, name, event != null ? String.valueOf(event.payload) : null, event);
        }
    }

    /**
     * Validates if a project can be deleted by checking for dependencies
     */
    public ProjectDeletionValidationResult validateProjectDeletion(Long projectId) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.VALIDATE_PROJECT_DELETION, category, projectId, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == ResponseCode.BAD_REQUEST) {
                return (ProjectDeletionValidationResult) event.payload;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
        } catch (Exception e) {
            logger.error("Error in validateProjectDeletion: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(projectId),
                event != null ? ObjectMapperUtil.toJsonString(event.payload) : null, event);
        }
        return null;
    }

    public List<Projects> getBoughtProjects() {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.GET_BOUGHT_PROJECT, category, new HashMap<>(), routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return (List<Projects>) event.payload;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in checkExistName: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, "", event != null ? String.valueOf(event.payload) : null, event);
        }
    }

    public List<Projects> getGreetingProjects() {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.GET_GREETING_PROJECT, category, new HashMap<>(), routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return (List<Projects>) event.payload;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in checkExistName: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, "", event != null ? String.valueOf(event.payload) : null, event);
        }
    }
}
