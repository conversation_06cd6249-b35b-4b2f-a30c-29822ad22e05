# API Endpoints Design - Import Customer

## 1. Upload File & Create Import Job

### POST /api/import/customers/upload

**Purpose**: Upload file và tạo import job mới

**Request**:
```json
{
  "source": "WEB_UPLOAD|GOOGLE_DRIVE",
  "file": "multipart/form-data", // for WEB_UPLOAD
  "google_drive_file_id": "string", // for GOOGLE_DRIVE
  "google_drive_link": "string", // for GOOGLE_DRIVE
  "sheet_name": "string", // optional for Excel files
  "options": {
    "duplicate_handling": "UPSERT|SKIP", // default: UPSERT
    "stop_on_error": true, // default: false
    "chunk_size": 100 // default: 100
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "file_name": "customers_data.xlsx",
    "file_checksum": "abc123def456",
    "total_rows": 1500,
    "status": "PENDING",
    "created_at": "2025-09-09T15:08:34Z",
    "duplicate_warning": {
      "is_duplicate": false,
      "previous_job_id": null,
      "previous_imported_at": null
    }
  }
}
```

**Error Cases**:
- File too large (>10MB)
- Too many rows (>10k)
- Unsupported format
- Google Drive access denied
- Duplicate checksum (with force option)

---

## 2. Dry-run Validation

### POST /api/import/customers/{job_id}/dry-run

**Purpose**: Chạy validation không ghi database

**Request**:
```json
{
  "options": {
    "duplicate_handling": "UPSERT|SKIP",
    "validate_references": true // validate project_id, employee_id exists
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "status": "DRY_RUN_COMPLETED",
    "summary": {
      "total_rows": 1500,
      "valid_rows": 1350,
      "error_rows": 150,
      "warning_rows": 50,
      "duplicate_rows": 25
    },
    "validation_details": {
      "required_fields_missing": 45,
      "phone_format_invalid": 30,
      "email_format_invalid": 15,
      "date_format_invalid": 20,
      "enum_value_invalid": 10,
      "phone_duplicate_in_file": 15,
      "phone_duplicate_in_system": 10,
      "reference_not_found": 5
    },
    "estimated_impact": {
      "customers_to_create": 1200,
      "customers_to_update": 150,
      "customer_relatives_to_create": 800,
      "customer_properties_to_create": 300,
      "customer_offers_to_create": 400
    }
  }
}
```

---

## 3. Get Validation Errors

### GET /api/import/customers/{job_id}/errors

**Purpose**: Lấy danh sách lỗi chi tiết với phân trang

**Query Parameters**:
- `page`: int (default: 1)
- `per_page`: int (default: 50, max: 200)
- `error_type`: string (filter by error type)
- `column_name`: string (filter by column)

**Response**:
```json
{
  "success": true,
  "data": {
    "errors": [
      {
        "id": 1,
        "row_num": 15,
        "column_name": "phone",
        "error_type": "INVALID_FORMAT",
        "description": "Phone number must be 10-11 digits",
        "raw_value": "123abc",
        "suggested_value": null
      },
      {
        "id": 2,
        "row_num": 23,
        "column_name": "email",
        "error_type": "INVALID_FORMAT", 
        "description": "Invalid email format",
        "raw_value": "invalid-email",
        "suggested_value": null
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 50,
      "total": 150,
      "last_page": 3
    },
    "error_summary": {
      "REQUIRED_FIELD_MISSING": 45,
      "INVALID_FORMAT": 60,
      "DUPLICATE_VALUE": 25,
      "REFERENCE_NOT_FOUND": 20
    }
  }
}
```

---

## 4. Execute Import

### POST /api/import/customers/{job_id}/execute

**Purpose**: Thực thi import với background job

**Request**:
```json
{
  "options": {
    "duplicate_handling": "UPSERT|SKIP",
    "stop_on_error": false,
    "import_only_valid": true, // skip error rows
    "auto_assign_leads": true // apply lead rules after import
  }
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "status": "RUNNING",
    "started_at": "2025-09-09T15:08:34Z",
    "estimated_duration": "5-10 minutes",
    "progress_url": "/api/import/customers/123/status"
  }
}
```

---

## 5. Job Status & Progress

### GET /api/import/customers/{job_id}/status

**Purpose**: Theo dõi tiến độ import job

**Response**:
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "status": "RUNNING", // PENDING|RUNNING|SUCCESS|FAILED
    "progress": {
      "current_row": 750,
      "total_rows": 1500,
      "percentage": 50,
      "processed_valid": 680,
      "processed_errors": 70,
      "estimated_remaining": "3 minutes"
    },
    "started_at": "2025-09-09T15:08:34Z",
    "updated_at": "2025-09-09T15:13:34Z",
    "completed_at": null,
    "result": null // will be populated when completed
  }
}
```

**When Completed**:
```json
{
  "success": true,
  "data": {
    "job_id": 123,
    "status": "SUCCESS",
    "progress": {
      "current_row": 1500,
      "total_rows": 1500,
      "percentage": 100
    },
    "started_at": "2025-09-09T15:08:34Z",
    "completed_at": "2025-09-09T15:18:34Z",
    "result": {
      "total_processed": 1500,
      "successful_imports": 1350,
      "failed_imports": 150,
      "customers_created": 1200,
      "customers_updated": 150,
      "customer_relatives_created": 800,
      "customer_properties_created": 300,
      "customer_offers_created": 400,
      "leads_auto_assigned": 1100
    }
  }
}
```

---

## 6. Download Template

### GET /api/import/customers/template

**Purpose**: Tải file template mẫu

**Query Parameters**:
- `format`: xlsx|csv (default: xlsx)
- `with_sample`: boolean (default: false)

**Response**: File download

---

## 7. Import History

### GET /api/import/customers/history

**Purpose**: Lịch sử các lần import

**Query Parameters**:
- `page`: int
- `per_page`: int
- `status`: string filter
- `created_by`: int filter

**Response**:
```json
{
  "success": true,
  "data": {
    "imports": [
      {
        "id": 123,
        "file_name": "customers_data.xlsx",
        "source": "WEB_UPLOAD",
        "status": "SUCCESS",
        "total_rows": 1500,
        "valid_rows": 1350,
        "error_rows": 150,
        "created_at": "2025-09-09T15:08:34Z",
        "created_by": {
          "id": 1,
          "name": "Admin User"
        }
      }
    ],
    "pagination": {
      "current_page": 1,
      "per_page": 20,
      "total": 45,
      "last_page": 3
    }
  }
}
```

---

## Error Response Format

All endpoints follow consistent error format:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "The given data was invalid",
    "details": {
      "field_name": ["Error message"]
    }
  }
}
```

## Status Codes

- `200`: Success
- `201`: Created
- `400`: Bad Request (validation error)
- `401`: Unauthorized
- `403`: Forbidden
- `404`: Not Found
- `413`: Payload Too Large
- `422`: Unprocessable Entity
- `500`: Internal Server Error
