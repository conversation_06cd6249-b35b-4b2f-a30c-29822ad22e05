package vn.agis.crm.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.dto.req.RoleSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchRoleResponeDTO;
import vn.agis.crm.base.jpa.entity.Role;
import vn.agis.crm.base.jpa.repositories.CustomJpaRepository;
import vn.agis.crm.constant.SQL;
import vn.agis.crm.constant.sql.SQLRole;

import java.util.List;

@Repository
public interface RoleRepository extends CustomJpaRepository<Role, Long> {
    @Transactional
    @Query("SELECT rp.permissionId FROM RolePermission rp WHERE rp.roleId = :roleId")
    List<Long> getRolePermissions(Long roleId);


    @Query(nativeQuery = true, value = SQL.SEARCH_ROLE, countQuery = SQL.COUNT_SEARCH_ROLE)
    Page<SearchRoleResponeDTO> searchRole(@Param("search") RoleSearchDTO search, Pageable pageable, List<Long> userIdList, Integer type);

    @Query("SELECT COUNT(rp.name) FROM Role rp WHERE rp.name = :name")
    long countByName(String name);


    @Query(value = """
            select * from role s
                     where 
                        (s.type = 0 OR s.type = :selectedType) AND
                        (
                        ( :type = 1)
                        OR ( :type = 2 AND (s.ID in (SELECT ur.ROLE_ID from user_role ur where ur.USER_ID in (:userIdList)) or s.created_by in (:userIdList))))
                     ORDER BY UPPER(name) ASC""", nativeQuery = true)
    List<Role> findByTypeInAndStatus(Integer type, List<Long> userIdList, Integer selectedType);

    List<Role> findByIdInAndStatus(List<Long> ids, Integer status);

    @Query(value = SQLRole.GET_ROLES_ACTIVE_BY_USER_ID)
    List<Role> getRolesByUserId(Long userId);

    Role findFirstByName(String name);
}
