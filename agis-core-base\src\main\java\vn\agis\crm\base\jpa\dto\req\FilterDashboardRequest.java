package vn.agis.crm.base.jpa.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Data
public class FilterDashboardRequest {
    @JsonProperty("id")
    Long id;
    @JsonProperty("paramsValue")
    List<ParamDashboard> paramsValue;
    @JsonProperty("customerCodes")
    List<String> customerCodes;
    public Map<String, Object> toMap(){
        Map<String, Object> map = new TreeMap<>();
        for (ParamDashboard param : this.paramsValue){
            map.put(param.prKey, param);
        }
        return map;
    }
}
