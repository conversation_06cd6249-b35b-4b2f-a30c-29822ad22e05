package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.LeadRuleDto;
import vn.agis.crm.base.jpa.entity.LeadRule;
import vn.agis.crm.base.jpa.dto.res.LeadRuleResDto;
import vn.agis.crm.service.AssignmentRuleApiService;

import java.util.List;

@RestController
@RequestMapping("/assignment-rules")
public class AssignmentRuleController {

    private final AssignmentRuleApiService ruleService;

    @Autowired
    public AssignmentRuleController(AssignmentRuleApiService ruleService) {
        this.ruleService = ruleService;
    }

    @GetMapping
    @Operation(description = "Get all assignment rules, ordered by priority")
    public ResponseEntity<List<LeadRuleResDto>> getAllRules() {
        return ResponseEntity.ok(ruleService.getAllRules());
    }

    @PostMapping
    @Operation(description = "Create a new assignment rule")
    public ResponseEntity<LeadRuleResDto> createRule(@RequestBody LeadRuleDto ruleDto) {
        return ResponseEntity.ok(ruleService.createRule(ruleDto));
    }

    @GetMapping("/{id}")
    @Operation(description = "Get a specific assignment rule by ID")
    public ResponseEntity<LeadRuleResDto> getRuleById(@PathVariable Long id) {
        return ResponseEntity.ok(ruleService.getOneWithStaff(id));
    }

    @PutMapping("/{id}")
    @Operation(description = "Update an existing assignment rule")
    public ResponseEntity<LeadRuleResDto> updateRule(@PathVariable Long id, @RequestBody LeadRuleDto ruleDto) {
        return ResponseEntity.ok(ruleService.updateRule(id, ruleDto));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "Delete an assignment rule")
    public ResponseEntity<Void> deleteRule(@PathVariable Long id) {
        ruleService.deleteById(id);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/update-priority")
    @Operation(description = "Update the priority order of all rules")
    public ResponseEntity<Void> updatePriority(@RequestBody vn.agis.crm.base.jpa.dto.req.UpdateRulePriorityRequest request) {
        ruleService.updatePriority(request);
        return ResponseEntity.ok().build();
    }
}

