package vn.agis.crm;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableScheduling;
import io.github.jhipster.config.JHipsterProperties;
import org.springframework.context.annotation.ImportResource;
import vn.agis.crm.rmi.RmiCommandLine;


@SpringBootApplication
@EntityScan({"vn.agis.crm.base.jpa.entity", "vn.agis.crm.model.entity"})
@EnableConfigurationProperties(JHipsterProperties.class)
@EnableScheduling
@ImportResource({"file:config/beans.xml", "file:config/schedule-conf.xml"})
//@ImportResource({ "classpath*:beans.xml", "classpath*:schedule-conf.xml" })
public class CrmCoreMgmtApplication {

    public static void main(String[] args) {
        if (args != null && args.length >= 2) {
            if (args[0].equals("sendRmi")) {
                String[] rmiArgs = new String[args.length - 1];
                System.arraycopy(args, 1, rmiArgs, 0, args.length - 1);
                RmiCommandLine.processRmi(rmiArgs);
                System.exit(0);
            }
        }
        SpringApplication.run(CrmCoreMgmtApplication.class, args);
    }

}
