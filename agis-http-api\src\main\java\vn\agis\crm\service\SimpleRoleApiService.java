package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.SimpleRoleSearchDto;
import vn.agis.crm.base.jpa.dto.req.SimpleRoleDto;
import vn.agis.crm.base.jpa.entity.SimpleRole;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.List;

@Service
public class SimpleRoleApiService extends CrudService<SimpleRole, Long> {

    private static final Logger logger = LoggerFactory.getLogger(SimpleRoleApiService.class);

    public SimpleRoleApiService() {
        super(SimpleRole.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.ROLE_SIMPLE;
    }

    public Page<SimpleRole> search(SimpleRoleSearchDto searchDTO, Pageable pageable) {
        Event event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDTO, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            PageInfo pageInfo = (PageInfo) event.payload;
            List<SimpleRole> roles = ObjectMapperUtil.listMapper(pageInfo.getData(), SimpleRole.class);
            return new PageImpl<>(roles, pageable, pageInfo.getTotalCount());
        }
        return Page.empty();
    }

    public SimpleRole createRole(SimpleRoleDto dto) {
        Event event = RequestUtils.amqp(Constants.Method.CREATE, category, dto, routingKey);
        if (event.respStatusCode == ResponseCode.CONFLICT) {
            throw new DuplicateException(event.respErrorDesc, category, String.valueOf(event.payload), "");
        }
        SimpleRole role = (SimpleRole) event.payload;
        return role;
    }

    public SimpleRole updateRole(SimpleRoleDto dto) {
        Event event = RequestUtils.amqp(Constants.Method.UPDATE, category, dto, routingKey);
        if (event.respStatusCode == ResponseCode.CONFLICT) {
            throw new DuplicateException(event.respErrorDesc, category, String.valueOf(event.payload), "");
        }
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new ResourceNotFoundException(event.respErrorDesc, category, (String) null, "");
        }
        SimpleRole role = (SimpleRole) event.payload;
        return role;
    }

    public SimpleRoleDto findById(Long id) {
        Event event = RequestUtils.amqp(Constants.Method.FIND_BY_ID, category, id, routingKey);
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new ResourceNotFoundException(event.respErrorDesc, category, (String) null, "");
        }
        return (SimpleRoleDto) event.payload;
    }

    public Boolean checkExistName(String name) {
        Event event = RequestUtils.amqp(Constants.Method.CHECK_EXIST_ROLE_NAME, category, name, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (Boolean) event.payload;
        }
        return false;
    }
}

