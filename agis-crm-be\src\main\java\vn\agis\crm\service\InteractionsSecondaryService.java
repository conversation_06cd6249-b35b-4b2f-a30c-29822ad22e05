package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryUpdateDto;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;
import vn.agis.crm.base.utils.DateUtils;
import vn.agis.crm.repository.CustomerPropertyRepository;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.repository.InteractionsSecondaryRepository;
import vn.agis.crm.service.mapper.InteractionsSecondaryMapper;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service layer for InteractionsSecondary entity
 * Handles business logic, validation, and data transformation
 */
@Service
@Transactional
public class InteractionsSecondaryService {

    private static final Logger logger = LoggerFactory.getLogger(InteractionsSecondaryService.class);
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private InteractionsSecondaryMapper interactionsSecondaryMapper;

    /**
     * Main event processing method following the established pattern
     */
    public Event process(Event event) {
        try {
            switch (event.method) {
                case Method.CREATE:
                    return create(event);
                case Method.UPDATE:
                    return update(event);
                case Method.DELETE:
                    return delete(event);
                case Method.FIND_BY_ID:
                    return findById(event);
                case Method.SEARCH:
                    return search(event);
                case "FIND_BY_CUSTOMER_PROPERTY_ID":
                    return findByCustomerPropertyId(event);
                case "COUNT_BY_CUSTOMER_PROPERTY_ID":
                    return countByCustomerPropertyId(event);
                case "COUNT_BY_UNIT_ID":
                    return countByUnitId(event);
                default:
                    return event.createResponse(null, ResponseCode.NOT_FOUND, "Method not found");
            }
        } catch (Exception e) {
            logger.error("Error processing InteractionsSecondary event: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Internal server error: " + e.getMessage());
        }
    }

    /**
     * Create new secondary interaction
     */
    private Event create(Event event) {
        try {
            InteractionSecondaryCreateDto createDto = (InteractionSecondaryCreateDto) event.payload;
            
            // Validate customer property exists
            if (!customerPropertyRepository.existsById(createDto.getCustomerPropertyId())) {
                return event.createResponse(null, ResponseCode.BAD_REQUEST, "Customer property not found");
            }

            // Convert DTO to entity
            InteractionsSecondary entity = interactionsSecondaryMapper.toEntity(createDto);
            
            // Set audit fields
            entity.setCreatedAt(new Date());
            entity.setCreatedBy(event.userId);
            entity.setUpdatedAt(new Date());
            entity.setUpdatedBy(event.userId);

            // Save entity
            InteractionsSecondary saved = interactionsSecondaryRepository.save(entity);
            
            // Convert to response DTO
            InteractionSecondaryDto responseDto = interactionsSecondaryMapper.toDto(saved);
            
            logger.info("Created secondary interaction with ID: {}", saved.getId());
            return event.createResponse(responseDto, ResponseCode.CREATED, "Secondary interaction created successfully");
            
        } catch (Exception e) {
            logger.error("Error creating secondary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error creating secondary interaction: " + e.getMessage());
        }
    }

    /**
     * Update existing secondary interaction
     */
    private Event update(Event event) {
        try {
            InteractionSecondaryUpdateDto updateDto = (InteractionSecondaryUpdateDto) event.payload;
            
            // Find existing entity
            Optional<InteractionsSecondary> existingOpt = interactionsSecondaryRepository.findById(updateDto.getId());
            if (!existingOpt.isPresent()) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Secondary interaction not found");
            }

            InteractionsSecondary existing = existingOpt.get();
            
            // Validate customer property exists
            if (!customerPropertyRepository.existsById(updateDto.getCustomerPropertyId())) {
                return event.createResponse(null, ResponseCode.BAD_REQUEST, "Customer property not found");
            }

            // Update entity from DTO
            interactionsSecondaryMapper.updateEntityFromDto(existing, updateDto);
            
            // Set audit fields
            existing.setUpdatedAt(new Date());
            existing.setUpdatedBy(event.userId);

            // Save updated entity
            InteractionsSecondary saved = interactionsSecondaryRepository.save(existing);
            
            // Convert to response DTO
            InteractionSecondaryDto responseDto = interactionsSecondaryMapper.toDto(saved);
            
            logger.info("Updated secondary interaction with ID: {}", saved.getId());
            return event.createResponse(responseDto, ResponseCode.OK, "Secondary interaction updated successfully");
            
        } catch (Exception e) {
            logger.error("Error updating secondary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error updating secondary interaction: " + e.getMessage());
        }
    }

    /**
     * Delete secondary interaction
     */
    private Event delete(Event event) {
        try {
            Long id = (Long) event.payload;
            
            if (!interactionsSecondaryRepository.existsById(id)) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Secondary interaction not found");
            }

            interactionsSecondaryRepository.deleteById(id);
            
            logger.info("Deleted secondary interaction with ID: {}", id);
            return event.createResponse(null, ResponseCode.OK, "Secondary interaction deleted successfully");
            
        } catch (Exception e) {
            logger.error("Error deleting secondary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error deleting secondary interaction: " + e.getMessage());
        }
    }

    /**
     * Find secondary interaction by ID
     */
    private Event findById(Event event) {
        try {
            Long id = (Long) event.payload;
            
            Optional<InteractionsSecondary> entityOpt = interactionsSecondaryRepository.findById(id);
            if (!entityOpt.isPresent()) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Secondary interaction not found");
            }

            InteractionSecondaryDto responseDto = interactionsSecondaryMapper.toDto(entityOpt.get());
            
            return event.createResponse(responseDto, ResponseCode.OK, "Secondary interaction found");
            
        } catch (Exception e) {
            logger.error("Error finding secondary interaction by ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error finding secondary interaction: " + e.getMessage());
        }
    }

    /**
     * Search secondary interactions with filters and pagination
     */
    private Event search(Event event) {
        try {
            InteractionSecondarySearchDto searchDto = (InteractionSecondarySearchDto) event.payload;
            
            // Build specification for filtering
            Specification<InteractionsSecondary> spec = buildSearchSpecification(searchDto);
            
            // Create pageable
            Pageable pageable = org.springframework.data.domain.PageRequest.of(
                searchDto.getPage(), 
                searchDto.getSize(),
                org.springframework.data.domain.Sort.by(parseSortBy(searchDto.getSortBy()))
            );
            
            // Execute search
            Page<InteractionsSecondary> page = interactionsSecondaryRepository.findAll(spec, pageable);
            
            // Convert to DTOs
            Page<InteractionSecondaryDto> responsePage = page.map(entity -> interactionsSecondaryMapper.toDto(entity));
            
            return event.createResponse(responsePage, ResponseCode.OK, "Search completed successfully");
            
        } catch (Exception e) {
            logger.error("Error searching secondary interactions: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error searching secondary interactions: " + e.getMessage());
        }
    }

    /**
     * Find secondary interactions by customer property ID
     */
    private Event findByCustomerPropertyId(Event event) {
        try {
            Long customerPropertyId = (Long) event.payload;
            
            List<InteractionsSecondary> entities = interactionsSecondaryRepository.findByCustomerPropertyId(customerPropertyId);
            List<InteractionSecondaryDto> responseDtos = entities.stream()
                .map(entity -> interactionsSecondaryMapper.toDto(entity))
                .collect(java.util.stream.Collectors.toList());
            
            return event.createResponse(responseDtos, ResponseCode.OK, "Secondary interactions found");
            
        } catch (Exception e) {
            logger.error("Error finding secondary interactions by customer property ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error finding secondary interactions: " + e.getMessage());
        }
    }

    /**
     * Count secondary interactions by customer property ID
     */
    private Event countByCustomerPropertyId(Event event) {
        try {
            Long customerPropertyId = (Long) event.payload;
            
            long count = interactionsSecondaryRepository.countByCustomerPropertyId(customerPropertyId);
            
            return event.createResponse(count, ResponseCode.OK, "Count completed successfully");
            
        } catch (Exception e) {
            logger.error("Error counting secondary interactions by customer property ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error counting secondary interactions: " + e.getMessage());
        }
    }

    /**
     * Count secondary interactions by unit ID (through customer properties)
     */
    private Event countByUnitId(Event event) {
        try {
            Long unitId = (Long) event.payload;
            
            long count = interactionsSecondaryRepository.countSecondaryInteractionsByUnitId(unitId);
            
            return event.createResponse(count, ResponseCode.OK, "Count by unit ID completed successfully");
            
        } catch (Exception e) {
            logger.error("Error counting secondary interactions by unit ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error counting secondary interactions by unit ID: " + e.getMessage());
        }
    }

    /**
     * Build JPA Specification for search filtering
     */
    private Specification<InteractionsSecondary> buildSearchSpecification(InteractionSecondarySearchDto searchDto) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by customer property ID
            if (searchDto.getCustomerPropertyId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("customerPropertyId"), searchDto.getCustomerPropertyId()));
            }

            // Filter by result (case-insensitive partial match)
            if (searchDto.getResult() != null && !searchDto.getResult().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("result")), 
                    "%" + searchDto.getResult().toLowerCase() + "%"
                ));
            }

            // Filter by expected sell price range
            if (searchDto.getExpectedSellPriceFrom() != null || searchDto.getExpectedSellPriceTo() != null) {
                if (searchDto.getExpectedSellPriceFrom() != null && searchDto.getExpectedSellPriceTo() != null) {
                    predicates.add(criteriaBuilder.between(root.get("expectedSellPrice"), 
                        searchDto.getExpectedSellPriceFrom(), searchDto.getExpectedSellPriceTo()));
                } else if (searchDto.getExpectedSellPriceFrom() != null) {
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("expectedSellPrice"), 
                        searchDto.getExpectedSellPriceFrom()));
                } else {
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("expectedSellPrice"), 
                        searchDto.getExpectedSellPriceTo()));
                }
            }

            // Filter by expected rent price range
            if (searchDto.getExpectedRentPriceFrom() != null || searchDto.getExpectedRentPriceTo() != null) {
                if (searchDto.getExpectedRentPriceFrom() != null && searchDto.getExpectedRentPriceTo() != null) {
                    predicates.add(criteriaBuilder.between(root.get("expectedRentPrice"), 
                        searchDto.getExpectedRentPriceFrom(), searchDto.getExpectedRentPriceTo()));
                } else if (searchDto.getExpectedRentPriceFrom() != null) {
                    predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("expectedRentPrice"), 
                        searchDto.getExpectedRentPriceFrom()));
                } else {
                    predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("expectedRentPrice"), 
                        searchDto.getExpectedRentPriceTo()));
                }
            }

            // Filter by happened at date range
            if (searchDto.getHappenedAtFrom() != null || searchDto.getHappenedAtTo() != null) {
                try {
                    if (searchDto.getHappenedAtFrom() != null && searchDto.getHappenedAtTo() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getHappenedAtFrom());
                        Date toDate = dateFormat.parse(searchDto.getHappenedAtTo());
                        predicates.add(criteriaBuilder.between(root.get("happenedAt"), fromDate, toDate));
                    } else if (searchDto.getHappenedAtFrom() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getHappenedAtFrom());
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("happenedAt"), fromDate));
                    } else {
                        Date toDate = dateFormat.parse(searchDto.getHappenedAtTo());
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("happenedAt"), toDate));
                    }
                } catch (Exception e) {
                    logger.warn("Invalid date format in search criteria: {}", e.getMessage());
                }
            }

            // Filter by created by
            if (searchDto.getCreatedBy() != null) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), searchDto.getCreatedBy()));
            }

            // Filter by created at date range
            if (searchDto.getCreatedAtFrom() != null || searchDto.getCreatedAtTo() != null) {
                try {
                    if (searchDto.getCreatedAtFrom() != null && searchDto.getCreatedAtTo() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getCreatedAtFrom());
                        Date toDate = dateFormat.parse(searchDto.getCreatedAtTo());
                        predicates.add(criteriaBuilder.between(root.get("createdAt"), fromDate, toDate));
                    } else if (searchDto.getCreatedAtFrom() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getCreatedAtFrom());
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), fromDate));
                    } else {
                        Date toDate = dateFormat.parse(searchDto.getCreatedAtTo());
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), toDate));
                    }
                } catch (Exception e) {
                    logger.warn("Invalid date format in created at search criteria: {}", e.getMessage());
                }
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Parse sort by string to Sort.Order array
     */
    private org.springframework.data.domain.Sort.Order[] parseSortBy(String sortBy) {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            return new org.springframework.data.domain.Sort.Order[]{
                org.springframework.data.domain.Sort.Order.desc("happenedAt")
            };
        }

        String[] parts = sortBy.split(",");
        if (parts.length == 2) {
            String property = parts[0].trim();
            String direction = parts[1].trim();
            
            if ("desc".equalsIgnoreCase(direction)) {
                return new org.springframework.data.domain.Sort.Order[]{
                    org.springframework.data.domain.Sort.Order.desc(property)
                };
            } else {
                return new org.springframework.data.domain.Sort.Order[]{
                    org.springframework.data.domain.Sort.Order.asc(property)
                };
            }
        }

        return new org.springframework.data.domain.Sort.Order[]{
            org.springframework.data.domain.Sort.Order.desc("happenedAt")
        };
    }
}
