package vn.agis.crm.service.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryUpdateDto;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;
import vn.agis.crm.repository.EmployeeRepository;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * Mapper for InteractionsPrimary entity and DTOs
 * Handles conversion between entity and various DTO types
 */
@Component
public class InteractionsPrimaryMapper {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Autowired
    private EmployeeRepository employeeRepository;

    /**
     * Convert InteractionsPrimary entity to InteractionPrimaryDto
     */
    public InteractionPrimaryDto toDto(InteractionsPrimary entity) {
        if (entity == null) {
            return null;
        }

        InteractionPrimaryDto dto = new InteractionPrimaryDto();
        dto.setId(entity.getId());
        dto.setResult(entity.getResult());
        dto.setHappenedAt(entity.getHappenedAt() != null ? dateFormat.format(entity.getHappenedAt()) : null);
        dto.setNotes(entity.getNotes());
        dto.setDeleted(entity.getDeleted());
        dto.setCreatedBy(entity.getCreatedBy());

        // Set created name from employee
        if (entity.getCreatedBy() != null) {
            Optional<Employee> employeeOpt = employeeRepository.findById(entity.getCreatedBy());
            if (employeeOpt.isPresent()) {
                Employee employee = employeeOpt.get();
                dto.setCreatedName(employee.getFullName() != null ? employee.getFullName() : employee.getUsername());
            }
        }

        return dto;
    }

    /**
     * Convert InteractionPrimaryCreateDto to InteractionsPrimary entity
     */
    public InteractionsPrimary toEntity(InteractionPrimaryCreateDto createDto) {
        if (createDto == null) {
            return null;
        }

        InteractionsPrimary entity = new InteractionsPrimary();
        entity.setCustomerOfferId(createDto.getCustomerOfferId());
        entity.setResult(createDto.getResult());
        entity.setNotes(createDto.getNotes());
        entity.setDeleted(false); // Default to not deleted

        // Parse happened at date
        if (createDto.getHappenedAt() != null && !createDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(createDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }

        return entity;
    }

    /**
     * Update existing InteractionsPrimary entity from InteractionPrimaryUpdateDto
     */
    public void updateEntityFromDto(InteractionsPrimary entity, InteractionPrimaryUpdateDto updateDto) {
        if (entity == null || updateDto == null) {
            return;
        }

        entity.setCustomerOfferId(updateDto.getCustomerOfferId());
        entity.setResult(updateDto.getResult());
        entity.setNotes(updateDto.getNotes());

        // Parse happened at date
        if (updateDto.getHappenedAt() != null && !updateDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(updateDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }
    }

    /**
     * Convert InteractionPrimaryUpdateDto to InteractionsPrimary entity
     * Used when creating a new entity from update DTO (for validation purposes)
     */
    public InteractionsPrimary toEntity(InteractionPrimaryUpdateDto updateDto) {
        if (updateDto == null) {
            return null;
        }

        InteractionsPrimary entity = new InteractionsPrimary();
        entity.setId(updateDto.getId());
        entity.setCustomerOfferId(updateDto.getCustomerOfferId());
        entity.setResult(updateDto.getResult());
        entity.setNotes(updateDto.getNotes());
        entity.setDeleted(false); // Default to not deleted

        // Parse happened at date
        if (updateDto.getHappenedAt() != null && !updateDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(updateDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }

        return entity;
    }
}
