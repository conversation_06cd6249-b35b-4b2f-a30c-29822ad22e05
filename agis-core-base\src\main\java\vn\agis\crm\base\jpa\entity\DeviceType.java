package vn.agis.crm.base.jpa.entity;

import lombok.Data;

import jakarta.persistence.*;

@Table(name = "DEVICE_TYPE")
@Entity
@Data
public class DeviceType {
    @Id
    private Long id;

    @Column(name = "type_code")
    private String typeCode;

    @Column(name = "type_name")
    private String typeName;

    @Column(name = "model_code")
    private String modelCode;

    @Column(name = "model_description", length = 1024)
    private String modelDescription;

    @Column(name = "telemetry_config_schema", columnDefinition = "json")
    private String telemetryConfigSchema;
}
