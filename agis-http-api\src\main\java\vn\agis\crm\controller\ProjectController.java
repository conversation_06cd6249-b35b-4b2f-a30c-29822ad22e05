package vn.agis.crm.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Parameter;
import vn.agis.crm.base.jpa.dto.ProjectDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.ProjectSearchDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.dto.res.ProjectWithStatsDto;
import vn.agis.crm.base.jpa.entity.Projects;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.ProjectService;

import java.util.List;

@RestController
@RequestMapping("/project-mgmt")
public class ProjectController extends CrudController<Projects, Long> {

    private final Logger log = LoggerFactory.getLogger(ProjectController.class);

    ProjectService projectService;

    @Autowired
    public ProjectController(ProjectService service) {
        super(service);
        this.projectService = service;
        this.baseUrl = "/project-mgmt";
    }

    @GetMapping("/search")
//    @PreAuthorize("hasAnyAuthority('searchProject')")
    public ResponseEntity<Page<ProjectWithStatsDto>> getPageProject(
        @RequestParam(name = "name", required = false, defaultValue = "") String name,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE) @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE) @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT) @RequestParam(name = "sort", required = false, defaultValue = "createdAt,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        ProjectSearchDto searchProjectRequest = new ProjectSearchDto(name, page, size, sortBy);

        Page<ProjectWithStatsDto> projectList = projectService.search(searchProjectRequest, listRequest.getPageable());
        return ResponseEntity.ok().body(projectList);
    }

    @GetMapping("/{id}")
//    @PreAuthorize("hasAnyAuthority('getProject')")
    public Projects getOneProject(@PathVariable Long id) {
        return projectService.getOne(id);
    }

    @DeleteMapping("/delete/{id}")
//    @PreAuthorize("hasAnyAuthority('deleteProject')")
    public ResponseEntity<?> deleteProject(@PathVariable Long id) {
        try {
            // First validate if project can be deleted
            ProjectDeletionValidationResult validationResult = projectService.validateProjectDeletion(id);

            if (!validationResult.isCanDelete()) {
                // Return validation result with dependency information
                return ResponseEntity.badRequest().body(validationResult);
            }

            // If validation passes, proceed with deletion
            projectService.deleteById(id);
            return ResponseEntity.ok().build();

        } catch (Exception e) {
            log.error("Error deleting project with id {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body("Đã xảy ra lỗi khi xóa dự án: " + e.getMessage());
        }
    }

    @PostMapping("/create")
//    @PreAuthorize("hasAnyAuthority('createProject')")
    public Projects createProject(@RequestBody ProjectDto createProjectReq) {
        return projectService.createProject(createProjectReq);
    }

    @PutMapping("/update/{id}")
//    @PreAuthorize("hasAnyAuthority('updateProject')")
    public Projects updateProject(@RequestBody ProjectDto updateProjectReq, @PathVariable Long id) {
        updateProjectReq.setId(id);
        return projectService.update(updateProjectReq);
    }

    @GetMapping("/checkExistName")
    public ResponseEntity<Boolean> checkExistName(@RequestParam(name = "name") String name) {
        Boolean exists = projectService.checkExistName(name);
        return ResponseEntity.ok(exists != null ? exists : Boolean.FALSE);
    }

    @GetMapping("/validate-deletion/{id}")
//    @PreAuthorize("hasAnyAuthority('deleteProject')")
    public ResponseEntity<ProjectDeletionValidationResult> validateProjectDeletion(@PathVariable Long id) {
        try {
            ProjectDeletionValidationResult validationResult = projectService.validateProjectDeletion(id);
            return ResponseEntity.ok(validationResult);
        } catch (Exception e) {
            log.error("Error validating project deletion for id {}: {}", id, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/bought")
    public ResponseEntity<List<Projects>> getBoughtProjects() {
        List<Projects> result = projectService.getBoughtProjects();
        return ResponseEntity.ok(result);
    }

    @GetMapping("/greeting")
    public ResponseEntity<List<Projects>> getGreetingProjects() {
        List<Projects> result = projectService.getGreetingProjects();
        return ResponseEntity.ok(result);
    }

}
