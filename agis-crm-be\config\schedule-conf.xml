<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-4.0.xsd
                            http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-4.0.xsd
                            http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-4.0.xsd">

<!--    <task:scheduled-tasks scheduler="jobCallMinIOGetFile1">-->
<!--        <task:scheduled ref="jobGetFileMinIO2-1" method="execute" fixed-delay="30000"/>-->
<!--        <task:scheduled ref="jobGetFileMinIO2-2" method="execute" fixed-delay="30000"/>-->
<!--    </task:scheduled-tasks>-->
<!--    <task:scheduler id="jobCallMinIOGetFile1" pool-size="1"/>-->

<!--    <task:scheduled-tasks scheduler="jobImportFile1">-->
<!--        <task:scheduled ref="jobImportFile-1" method="execute" fixed-delay="30000"/>-->
<!--        <task:scheduled ref="jobImportFile-2" method="execute" fixed-delay="30000"/>-->
<!--    </task:scheduled-tasks>-->
<!--    <task:scheduler id="jobImportFile1" pool-size="1"/>-->

<!--    <task:scheduled-tasks scheduler="jobUpdateSubscriber1">-->
<!--        <task:scheduled ref="jobUpdateData-1" method="execute" fixed-delay="60000"/>-->
<!--        <task:scheduled ref="jobUpdateData-2" method="execute" fixed-delay="60000"/>-->
<!--    </task:scheduled-tasks>-->
<!--    <task:scheduler id="jobUpdateSubscriber1" pool-size="1"/>-->

<!--    <task:scheduled-tasks scheduler="jobCompensationScan1">-->
<!--        <task:scheduled ref="jobCompensationScan" method="execute" fixed-delay="300000"/>-->
<!--    </task:scheduled-tasks>-->
<!--    <task:scheduler id="jobCompensationScan1" pool-size="1"/>-->

<!--    <task:scheduled-tasks scheduler="jobMonitoringFile">-->
<!--        <task:scheduled ref="jobMonitoringFile-1" method="execute" cron="0 0 23 * * ?"/>-->
<!--&lt;!&ndash;        <task:scheduled ref="jobMonitoringFile-2" method="execute" cron="0 0/2 * * * ?"/>&ndash;&gt;-->
<!--    </task:scheduled-tasks>-->
<!--    <task:scheduler id="jobMonitoringFile" pool-size="1"/>-->
</beans>
