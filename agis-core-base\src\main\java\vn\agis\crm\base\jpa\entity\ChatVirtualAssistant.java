package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Query;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@Table(name = "chat_virtual_assistant")
@Entity
public class ChatVirtualAssistant extends AbstractEntity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "description")
    private String description;

    @Transient
    private List<QueryVirtualAssistant> queries;
}
