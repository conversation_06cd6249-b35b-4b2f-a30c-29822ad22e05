package vn.agis.crm.base.event.amqp;

import com.google.gson.Gson;
import com.rabbitmq.stream.Environment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.AmqpException;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.AsyncRabbitTemplate;
import org.springframework.amqp.rabbit.RabbitMessageFuture;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.rabbit.stream.producer.RabbitStreamTemplate;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.errors.CommonException;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;

import java.util.HashMap;
import java.util.UUID;
import java.util.concurrent.*;

import static vn.agis.crm.base.event.constants.AMQPConstants.Xchange.XCHANGE_TYPE_MAP;

/**
 * Created by tiemnd on 12/14/19.
 */
public class AMQPEventPublisher extends AMQPAbstractConfiguration {

    private ExecutorService pool;
    private LinkedBlockingQueue<Runnable> publishQueue = new LinkedBlockingQueue<Runnable>();
    private static final Logger log = LoggerFactory.getLogger(AMQPEventPublisher.class);

    public AMQPEventPublisher(CachingConnectionFactory connectionFactory, String xchangeName) {
        pool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, publishQueue);
        this.exchangeName = xchangeName;
        this.CONNECTION_FACTORY = connectionFactory;
        String exchangeType = XCHANGE_TYPE_MAP.get(xchangeName);
        if (exchangeType == null || exchangeType.isBlank() || exchangeType.equals(AMQPConstants.ExchangeType.DIRECT))
            amqpAdmin().declareExchange(directExchange(xchangeName));
        else if (exchangeType.equals(AMQPConstants.ExchangeType.TOPIC)){
            amqpAdmin().declareExchange(topicExchange(xchangeName));
        } else if (exchangeType.equals(AMQPConstants.ExchangeType.X_DELAY_MESSAGE)) {
            amqpAdmin().declareExchange(xDelayedMessageExchange(xchangeName));
        } else {
            amqpAdmin().declareExchange(customExchange(xchangeName, exchangeType));
        }
    }

    public AMQPEventPublisher(CachingConnectionFactory connectionFactory, String exchangeName, String exchangeType) {
        pool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, publishQueue);
        this.exchangeName = exchangeName;
        this.CONNECTION_FACTORY = connectionFactory;
        if (exchangeType == null || exchangeType.isEmpty()) {
            throw new RuntimeException("exchangeType must be not empty!");
        }
        if (exchangeType.equals(AMQPConstants.ExchangeType.TOPIC))
            amqpAdmin().declareExchange(topicExchange(exchangeName));
        else if (exchangeType.equals(AMQPConstants.ExchangeType.DIRECT)) {
            amqpAdmin().declareExchange(directExchange(exchangeName));
        } else if (exchangeType.equals(AMQPConstants.ExchangeType.X_DELAY_MESSAGE)) {
            amqpAdmin().declareExchange(xDelayedMessageExchange(exchangeName));
        } else {
            amqpAdmin().declareExchange(customExchange(exchangeName, exchangeType));
        }
    }

    /** Voi superStream, trao doi thong qua exchange -> cac partion => phai tao exchange, partition va retention
     Doi voi normalStream ben gui se publish thang vao stream queue , ban nhan se consume tu stream queue => phai tao stream queue, retention
     Cac thong so ve partition, retention se do consumer của stream khoi tao
     Ben publisher stream se khong gui duoc ban tin toi stream cho toi khi ben nhan khoi tao xong **/
    public AMQPEventPublisher(CachingConnectionFactory connectionFactory, String exchangeName, Environment streamEvn, boolean isSuperStream) {
        pool = new ThreadPoolExecutor(2, 2, 0L, TimeUnit.MILLISECONDS, publishQueue);
        this.exchangeName = exchangeName;
        this.CONNECTION_FACTORY = connectionFactory;
        this.streamEnv = streamEvn;
        this.isSuperStream = isSuperStream;
    }

    @Override
    public void configureRabbitTemplate(RabbitTemplate template) {
        template.setExchange(exchangeName);
        template.setUserCorrelationId(true);
    }

    public void publishStream(byte[] data) {
        pool.submit(new PublishStreamTask(data));
    }

    public Event publish(String routingKey, Event event) {
        if ((event.id == null) || (event.id.isEmpty())) {
            event.id = UUID.randomUUID().toString();
        }
        pool.submit(new PublishObjectTask(routingKey, event));
        return event;
    }

    public Event publishWithDelay(String routingKey, Event event, Integer delayedInterval) {
        if ((event.id == null) || (event.id.isEmpty())) {
            event.id = UUID.randomUUID().toString();
        }
        pool.submit(new DelayPublishObjectTask(routingKey, event, delayedInterval));
        return event;
    }

    public Event publishAndReceiveSynch(String routingKey, Event event, Integer receivedTimeout) {
        if ((event.id == null)||(event.id.isEmpty())) {
            event.id = UUID.randomUUID().toString();
        }
        return (new PublishObjectTask(routingKey, event, receivedTimeout)).sendAndReceive();
    }

    public Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event) {
        return pool.submit(new AsyncPublishObjectTask(routingKey, event, null, getDefaultReplyQueue()));
    }

    public Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event, Integer receivedTimeout) {
        return pool.submit(new AsyncPublishObjectTask(routingKey, event, receivedTimeout, getDefaultReplyQueue()));
    }

    public void publishAndReceive(String routingKey, Event event, Integer receivedTimeout, ListenableResultCallBack callback) {
        Future<RabbitMessageFuture> resultFuture = publishAndReceive(routingKey, event, receivedTimeout);
        try {
            RabbitMessageFuture rabbitConverterFuture = resultFuture.get();
            rabbitConverterFuture.whenComplete((message, ex) -> {
                if (ex == null) {
                    if (byte[].class.getName().equals(message.getMessageProperties().getHeaders().get("conversionClass"))) {
                        log.info("Received response [] amqp properties {}", message.getMessageProperties().toString());
                    } else {
                        log.debug("Received response {} amqp properties {}", new String(message.getBody()), message.getMessageProperties().toString());
                    }
                    HashMap<String, Object> map = (HashMap<String, Object>) asyncRabbitTemplateMap.get(exchangeName).getMessageConverter().fromMessage(message);
                    callback.onResponse((Event) map.get("event"));
                }
                else {
                    log.error("Failed to received response for message {}, properties {}, reason {}", event, ex.getMessage());
                    ex.printStackTrace();
                    callback.onFailure(event, ex);
                }
            });
        } catch (Throwable e) {
            log.error(e.getMessage(), e);
            e.printStackTrace();
            callback.onFailure(event, e);
        }
    }

    private class AsyncPublishObjectTask implements Callable<RabbitMessageFuture> {
        private String routingKey;
        private Object message;
        private AsyncRabbitTemplate asyncRabbitTemplate;

        public AsyncPublishObjectTask(String routingKey, Object event, Integer receivedTimeout, String replyQueue) {
            this.routingKey = routingKey;
            this.asyncRabbitTemplate = asyncRabbitTemplate(replyQueue);
            this.message = this.asyncRabbitTemplate.getMessageConverter().toMessage(event, new MessageProperties());
            if (receivedTimeout != null) {
                ((Message) this.message).getMessageProperties().setExpiration(String.valueOf(receivedTimeout));
                this.asyncRabbitTemplate.setReceiveTimeout(receivedTimeout);
            } else {

            }
        }

        @Override
        public RabbitMessageFuture call() throws Exception {
            Message sendMsg = (Message) this.message;
            RabbitMessageFuture future = this.asyncRabbitTemplate.sendAndReceive(exchangeName, routingKey, sendMsg);
            if (byte[].class.getName().equals(sendMsg.getMessageProperties().getHeaders().get("conversionClass"))) {
                log.info("Send message [] to routingKey {} amqp properties {}", routingKey, sendMsg.getMessageProperties().toString());
            } else {
                log.info("Send message ###{}### to routingKey {} amqp properties {}", new String(sendMsg.getBody()), routingKey, sendMsg.getMessageProperties().toString());
            }
            return future;
        }
    }

    private class PublishObjectTask implements Runnable {

        private String routingKey;
        private Object message;
        private RabbitTemplate rabbitTemplate;

        public PublishObjectTask(String routingKey, Object event) {
            this.routingKey = routingKey;
            this.message = event;
            this.rabbitTemplate = rabbitTemplate();
        }

        public PublishObjectTask(String routingKey, Event event, Integer receivedTimeout) {
            this.routingKey = routingKey;
            this.rabbitTemplate = rabbitTemplate();
            this.message = rabbitTemplate().getMessageConverter().toMessage(event, new MessageProperties());
            if (receivedTimeout != null && receivedTimeout > 0) {
                setTimeForMessage(receivedTimeout, (Message) this.message);
            }
        }

        protected void setTimeForMessage(Integer miliSecond, Message message) {
            message.getMessageProperties().setExpiration(String.valueOf(miliSecond));
            rabbitTemplate.setReceiveTimeout(miliSecond);
            rabbitTemplate.setReplyTimeout(miliSecond);
        }

        @Override
        public void run() {
            final Message[] sendMsg = new Message[1];
            rabbitTemplate.convertAndSend(exchangeName, routingKey, message, new MessagePostProcessor() {
                @Override
                public Message postProcessMessage(Message m) throws AmqpException {
                    m.getMessageProperties().setContentType("application/json");
                    return (sendMsg[0] = m);
                }
            });
            if (byte[].class.getName().equals(sendMsg[0].getMessageProperties().getHeaders().get("conversionClass"))) {
                log.info("Send message [] to routingKey {} amqp properties {}", routingKey, sendMsg[0].getMessageProperties().toString());
            } else {
                log.info("Send message ###{}### to routingKey {} amqp properties {}", new String(sendMsg[0].getBody()), routingKey, sendMsg[0].getMessageProperties().toString());
            }
        }

        private Event sendAndReceive() {
            final Message[] sendMsg = new Message[1];
            HashMap<String, Object> result = (HashMap<String, Object>) rabbitTemplate().convertSendAndReceive(routingKey, message, new MessagePostProcessor() {
                @Override
                public Message postProcessMessage(Message m) throws AmqpException {
                    m.getMessageProperties().setContentType("application/json");
                    return (sendMsg[0] = m);
                }
            });
            if (byte[].class.getName().equals(sendMsg[0].getMessageProperties().getHeaders().get("conversionClass"))) {
                log.info("Send message [] to routingKey {} amqp properties {}", routingKey, sendMsg[0].getMessageProperties().toString());
            } else {
                log.info("Send message ###{}### to routingKey {} amqp properties {}", new String(sendMsg[0].getBody()), routingKey, sendMsg[0].getMessageProperties().toString());
            }
            // timeout occur
            if (result == null) {
                if (byte[].class.getName().equals(sendMsg[0].getMessageProperties().getHeaders().get("conversionClass"))) {
                    log.info("Timeout for message ###{}### to routingKey {}", sendMsg[0].getMessageProperties().toString(), routingKey);
                } else {
                    log.info("Timeout for message ###{}### to routingKey {}", new String(((Message) message).getBody()), routingKey);
                }
                throw new CommonException(ResponseCode.REQUEST_TIMEOUT, "Timeout for message " + ((Message) message).getMessageProperties().getMessageId());
            }
            MessageProperties messageProperties = (MessageProperties) result.get("MessageProperties");
            if (byte[].class.getName().equals(messageProperties.getHeaders().get("conversionClass"))) {
                log.info("Received response ###[]### amqp properties {}", messageProperties);
            } else {
                log.info("Received response ###{}### amqp properties {}", new Gson().toJson(result.get("event")), messageProperties);
            }
            return (Event) result.get("event");
        }

    }

    private class DelayPublishObjectTask extends PublishObjectTask {

        public DelayPublishObjectTask(String routingKey, Event event, Integer delayedInterval) {
            super(routingKey, event, delayedInterval);
        }

        @Override
        protected void setTimeForMessage(Integer miliSecond, Message message) {
            message.getMessageProperties().getHeaders().put("x-delay", miliSecond);
        }
    }

    private class PublishStreamTask implements Runnable {
        private byte[] payload;
        private RabbitStreamTemplate rabbitTemplate;

        public PublishStreamTask(byte[] payload) {
            this.payload = payload;
            this.rabbitTemplate = rabbitStreamTemplate();
        }

        @Override
        public void run() {
            com.rabbitmq.stream.Message message = rabbitTemplate.messageBuilder().addData(payload).build();
            rabbitTemplate.send(message);
            log.info("Send message ###{}### to stream {}", new String(message.getBodyAsBinary()), exchangeName);
        }
    }
}
