package vn.agis.crm.base.jpa.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SearchAlertRespone {
    private Long id;
    private String name;
    private Long customerId;
    private String statusSim;
    private String subscriptionNumber;
    private Long groupId;
    private Integer status;
    private Integer severity;
}
