# Đặc tả <PERSON>ỹ thuật: <PERSON><PERSON> thống Import Dữ liệu Khách hàng

Phiên bản: 1.0  
Ngày: 2025-09-11  
Tác giả: Cascade AI

---

## 1. Tóm tắt điều hành
Tài liệu này mô tả giải pháp kỹ thuật xây dựng hệ thống import dữ liệu khách hàng cho nền tảng AGIS CRM, đáp <PERSON>ng yêu cầu mục 5.1.4. <PERSON><PERSON> thống gồm API và tiến trình nền để xử lý tệp tải lên (máy người dùng hoặc Google Drive), hỗ trợ dry-run (kiểm tra trước khi ghi), xử lý trùng (upsert theo phone hoặc bỏ qua), đảm bảo idempotent bằng checksum, và báo cáo lỗi chi tiết. Kiến trúc tối ưu cho độ bền, <PERSON><PERSON><PERSON> năng mở rộng, v<PERSON> <PERSON><PERSON> bả<PERSON> tr<PERSON>, t<PERSON><PERSON> hợ<PERSON> chặt với schema MariaDB hiện có.

---

## 2. <PERSON><PERSON> tích hệ thống
### 2.1. Schema CSDL (agis_crm.sql)
- customers: trung tâm, duy nhất theo phone (uq_customer_phone); gồm thông tin cá nhân, source_type(enum: Data, Leads, Event, Refer), shortcut current_manager_id/current_staff_id.
- customer_assignments: lịch sử phân công; active khi assigned_to IS NULL; view vw_current_assignments.
- customer_relatives: thông tin người thân.
- customer_properties (đã giao dịch) & customer_offers (đang chào): liên kết projects, units; có first/last_interaction, status/notes.
- interactions_secondary (gắn customer_properties) & interactions_primary (gắn customer_offers): lưu kết quả tương tác, thời điểm, ghi chú, giá kỳ vọng.
- employees, projects, units: dữ liệu danh mục dùng để đối soát/lookup.
- import_jobs & import_job_errors: quản lý job import, đếm dòng, trạng thái, options(JSON), lỗi từng dòng.
- audit_logs: ghi nhận before/after (tuỳ chọn khi import).

### 2.2. Mẫu CSV (template/DATA-FORMAT-V2.csv)
- Định dạng bảng rộng, gộp nhiều thực thể trên một dòng: Khách hàng, Người thân, BĐS đã giao dịch, BĐS đang chào, Tương tác sơ cấp/thứ cấp, Nguồn, Nhân sự chăm sóc (nội bộ), Sales/bên ngoài.
- Đa dạng định dạng ngày ("11-Oct-86", "Saturday, July 31, 2021", "7/12/2025"), số tiền có phân tách hàng nghìn ("25,000,000,000"), văn bản tự do.
- Một dòng có thể tạo nhiều bản ghi ở nhiều bảng (customers, customer_relatives, customer_properties, interactions, ...).

### 2.3. Yêu cầu (Mục 5.1.4)
- Nguồn tệp: tải lên trực tiếp hoặc Google Drive. Hỗ trợ .xlsx/.xls/.csv (UTF-8), chọn sheet, tải template/hướng dẫn.
- Quy trình 3 bước: (1) Chọn nguồn; (2) Dry-run (kiểm tra, trả tổng quan + bảng lỗi); (3) Xác nhận & chạy (job nền), chọn chỉ chạy dòng hợp lệ hoặc dừng khi lỗi.
- Idempotency: dùng checksum; cảnh báo trùng file; cho phép force re-import.
- Xử lý trùng: Upsert theo phone (khuyến nghị) hoặc Skip.

---

## 3. Kiến trúc kỹ thuật
### 3.1. Thiết kế tổng thể
- Lớp API: REST endpoints tạo/giám sát/quản lý job import.
- Lớp Service: phân tích cú pháp, chuẩn hoá, xác thực, ánh xạ dữ liệu.
- Bộ xử lý Job nền: xử lý không đồng bộ (hàng đợi như Redis/RabbitMQ hoặc polling DB).

### 3.2. Sơ đồ luồng dữ liệu
```mermaid
sequenceDiagram
  participant User
  participant Frontend
  participant ImportAPI
  participant JobQueue
  participant ImportWorker
  participant Database
  User->>Frontend: Upload tệp & chọn tuỳ chọn
  Frontend->>ImportAPI: POST /api/imports (mode=DRY_RUN)
  ImportAPI->>Database: Tạo import_jobs (PENDING)
  ImportAPI->>JobQueue: Đưa job Dry-run vào hàng đợi
  Frontend-->>User: Hiển thị Job ID
  JobQueue->>ImportWorker: Phân phối job
  ImportWorker->>Database: Cập nhật RUNNING
  ImportWorker->>ImportWorker: Phân tích & Validate
  ImportWorker->>Database: Ghi lỗi import_job_errors
  ImportWorker->>Database: Cập nhật kết quả (SUCCESS/FAILED)
  User->>Frontend: Xem kết quả & xác nhận chạy
  Frontend->>ImportAPI: POST /api/imports/{id}/run
  ImportAPI->>Database: Cập nhật job (PENDING, RUN)
  ImportAPI->>JobQueue: Đưa job Run vào hàng đợi
  JobQueue->>ImportWorker: Phân phối job
  ImportWorker->>Database: Cập nhật RUNNING
  loop Mỗi dòng
    ImportWorker->>Database: BEGIN; Upsert/Insert...; COMMIT/ROLLBACK
  end
  ImportWorker->>Database: Cập nhật kết quả cuối
```

### 3.3. Tích hợp CSDL
- Giao dịch: xử lý theo từng dòng (nhóm thực thể của 1 khách) trong 1 transaction để đảm bảo toàn vẹn.
- Tra cứu: projects/units/employees theo tên/mã (không phân biệt hoa-thường), nên cache để tăng tốc.
- Cạnh tranh: khoá lạc quan/bi quan khi nhận job để tránh worker trùng xử lý cùng job.

---

## 4. Chi tiết triển khai
### 4.1. Ánh xạ trường (ví dụ chính)
- HỌ VÀ TÊN KHÁCH HÀNG → customers.full_name (bắt buộc)
- PHONE → customers.phone (bắt buộc, duy nhất, chuẩn E.164)
- NGÀY THÁNG NĂM SINH → customers.birth_date (date)
- TÊN DỰ ÁN + MÃ CĂN → tra projects.name & units(project_id, code) → customer_properties.unit_id
- GIAO DỊCH NGÀY → customer_properties.transaction_date; GIÁ GỐC TRÊN HỢP ĐỒNG → customer_properties.contract_price
- DỰ ÁN ĐANG CHÀO → customer_offers.project_id
- MÃ SỐ NHÂN VIÊN → lookup employees.employee_code → customer_assignments (role_type=2 Staff) + cập nhật customers.current_staff_id
- MỐI QUAN HỆ, HỌ VÀ TÊN NGƯỜI THÂN → customer_relatives.relation_type, full_name; năm sinh → year_of_birth; phone → phone; ghi chú → notes
- TƯƠNG TÁC (THỨ CẤP) → interactions_secondary gắn customer_properties; TƯƠNG TÁC (SƠ CẤP) → interactions_primary gắn customer_offers

### 4.2. Quy tắc xác thực
- Bắt buộc: full_name, phone. Nếu có BĐS/Offer thì phải resolve được project/unit.
- Phone: chuẩn hoá E.164, kiểm trùng trong file và trong DB; tuỳ chiến lược UPSERT/ SKIP.
- Email: regex; có thể CẢNH BÁO nếu sai.
- Ngày: hỗ trợ nhiều format (cấu hình được), chuyển về ISO.
- Số tiền: bỏ dấu phân tách, đổi về decimal(18,2).
- Enum/map: source_type, marital_status, kết quả tương tác… map theo cấu hình; nếu không map được → lỗi/cảnh báo.
- Khoá ngoại: project/unit/employee phải tồn tại (theo policy); nếu thiếu → FK_NOT_FOUND.

### 4.3. Chiến lược lỗi
- Phân loại và lưu import_job_errors(row_num, column_name, error_type, description).
- Loại lỗi: MISSING_REQUIRED, INVALID_FORMAT, DUP_IN_FILE, DUP_IN_DB, FK_NOT_FOUND, UPSERT_CONFLICT, ASSIGNMENT_EMPLOYEE_NOT_FOUND, MAPPING_NOT_FOUND, DB_TRANSACTION_ERROR.
- Tuỳ chọn: stop_on_error (dừng ở lỗi đầu) hoặc run_only_valid_rows (bỏ qua dòng lỗi).

---

## 5. Pha phát triển (incremental)
1) Hạ tầng Job & API: endpoints cơ bản, worker nền, bảng import_jobs/errors.
2) Phân tích tệp & Dry-run: parser CSV/Excel, validate đầy đủ, ghi lỗi.
3) Ghi DB (Run): giao dịch theo dòng; hỗ trợ UPSERT_BY_PHONE & SKIP_DUP; tích hợp đủ thực thể.
4) UI & tính năng nâng cao: giao diện 3 bước, tích hợp Google Drive, checksum idempotent.

---

## 6. Đặc tả API
### POST /api/imports
- Tạo job import. multipart/form-data: file, source=WEB_UPLOAD|GOOGLE_DRIVE, source_link, options(JSON).
- options (ví dụ):
```json
{
  "mode": "DRY_RUN",
  "upsert_strategy": "UPSERT_BY_PHONE",
  "stop_on_error": false,
  "sheet_name": "Sheet1"
}
```
- Phản hồi 201:
```json
{
  "id": 123,
  "status": "PENDING",
  "mode": "DRY_RUN",
  "file_checksum": "sha256:...",
  "created_at": "2025-09-11T10:30:00Z"
}
```

### GET /api/imports/{id}
- Xem trạng thái/kết quả job. 200:
```json
{
  "id": 123,
  "file_name": "customer_data.csv",
  "status": "SUCCESS",
  "mode": "DRY_RUN",
  "total_rows": 100,
  "valid_rows": 95,
  "error_rows": 5,
  "created_at": "...",
  "finished_at": "..."
}
```

### GET /api/imports/{id}/errors
- Danh sách lỗi (phân trang). 200:
```json
{
  "data": [
    {"row_num": 10, "column_name": "PHONE", "error_type": "INVALID_FORMAT", "description": "Số điện thoại không hợp lệ"}
  ],
  "pagination": {"page":1,"limit":50,"total":5}
}
```

### POST /api/imports/{id}/run
- Xác nhận chạy job (RUN). 202:
```json
{"id":123,"status":"PENDING","mode":"RUN"}
```

---

## 7. Quy trình xử lý dữ liệu
### 7.1. Giả mã xử lý mỗi dòng
```javascript
async function processCustomerRow(row, options) {
  const tx = await db.beginTransaction();
  try {
    const customerData = {
      phone: normalizePhone(row['PHONE']),
      full_name: row['HỌ VÀ TÊN KHÁCH HÀNG'],
      email: row['EMAIL'],
      birth_date: parseDate(row['NGÀY THÁNG NĂM SINH'])
    };
    validateRequired(customerData, ['phone','full_name']);
    let customer;
    if (options.upsert_strategy === 'UPSERT_BY_PHONE') customer = await upsertCustomer(customerData);
    else customer = await insertCustomerIfNotExists(customerData);

    if (row['HỌ VÀ TÊN NGƯỜI THÂN']) await insertCustomerRelative({...});

    if (row['TÊN DỰ ÁN'] && row['MÃ CĂN']) {
      const project = await findProjectByName(row['TÊN DỰ ÁN']);
      const unit = await findUnitByCode(project.id, row['MÃ CĂN']);
      const property = await insertCustomerProperty({...});
      if (row['KẾT QUẢ (THỨ CẤP)']) await insertSecondaryInteraction({...});
    }

    if (row['DỰ ÁN ĐANG CHÀO (SƠ CẤP)']) {
      const offerProject = await findProjectByName(row['DỰ ÁN ĐANG CHÀO (SƠ CẤP)']);
      const offer = await insertCustomerOffer({...});
      if (row['KẾT QUẢ (SƠ CẤP)']) await insertPrimaryInteraction({...});
    }

    if (row['MÃ SỐ NHÂN VIÊN']) {
      const emp = await findEmployeeByCode(row['MÃ SỐ NHÂN VIÊN']);
      await assignStaffToCustomer(customer.id, emp.id, {assigned_from: parseDateTime(row['THỜI GIAN NHẬN ĐƯỢC LEAD ĐỂ CHĂM'])});
    }
    await tx.commit();
    return { success: true };
  } catch (e) {
    await tx.rollback();
    return { success: false, error: e.message };
  }
}
```

### 7.2. Chiến lược theo lô
- Đọc/ghi theo lô 500–1000 dòng; dùng streaming parser.
- Pool kết nối DB; theo dõi tiến độ qua import_jobs.updated_at và options.progress.
- Giải phóng bộ nhớ sau mỗi lô.

---

## 8. Quản lý cấu hình
### 8.1. Schema tuỳ chọn import (options)
```json
{
  "mode": "DRY_RUN | RUN",
  "upsert_strategy": "UPSERT_BY_PHONE | SKIP_DUP",
  "stop_on_error": false,
  "run_only_valid_rows": true,
  "force_reimport": false,
  "sheet_name": "Sheet1",
  "date_formats": ["d-MMM-yy","EEEE, MMMM d, yyyy","d/M/yyyy","yyyy-MM-dd"],
  "number_locale": "vi-VN",
  "source_type_mapping": {"Data":"Data","Marketing":"Leads","Network":"Refer","Event":"Event"},
  "marital_status_mapping": {"ĐỘC THÂN":"single","ĐÃ LẬP GIA ĐÌNH":"married","LY THÂN":"divorced"},
  "result_mappings": {"secondary":{"MUỐN BÁN":"want_to_sell","ĐÃ CHO THUÊ":"rented_out","KHÔNG BÁN":"not_selling"},"primary":{"QUAN TÂM":"interested","KO QUAN TÂM":"not_interested","KO BẮT MÁY":"no_answer"}},
  "assignment_policy": {"identify_employee_by":"employee_code","missing_employee":"WARN_SKIP","role":"STAFF"},
  "project_unit_policy": {"require_existing_project": true, "require_existing_unit": true}
}
```

### 8.2. Cấu hình hệ thống (bảng configs)
```sql
INSERT INTO configs (config_key, config_type, config_value, description) VALUES
('import.max_file_size_mb',1,'10','Giới hạn dung lượng tệp (MB)'),
('import.max_rows_per_file',1,'10000','Giới hạn số dòng/tệp'),
('import.supported_formats',2,'["csv","xlsx","xls"]','Định dạng hỗ trợ'),
('import.default_date_formats',2,'["d-MMM-yy","EEEE, MMMM d, yyyy","d/M/yyyy"]','Định dạng ngày mặc định');
```

---

## 9. Chiến lược lỗi & phục hồi
### 9.1. Ma trận phân loại lỗi
- CRITICAL: DB_CONNECTION_FAILED → dừng job, retry sau khi kết nối phục hồi.
- ERROR: MISSING_REQUIRED_FIELD, FK_NOT_FOUND → bỏ dòng, ghi lỗi; cần can thiệp dữ liệu.
- WARNING: INVALID_FORMAT → dùng giá trị mặc định/bỏ trường; nên làm sạch dữ liệu.
- INFO: DUPLICATE_IN_FILE → gộp/bỏ qua theo chiến lược.

### 9.2. Phục hồi
- Khởi động lại job từ lô gần nhất; chấp nhận thành công một phần; rollback theo dòng cho lỗi nghiêm trọng; công cụ cho admin sửa dữ liệu và chạy lại dòng chọn lọc.

---

## 10. Bảo mật & phân quyền
- Chỉ Admin được gọi API import; xác thực (JWT/session); audit mọi thao tác vào audit_logs.
- An toàn tệp: kiểm tra kích thước/loại; quét virus nếu có; xác thực cấu trúc nội dung trước xử lý.

---

## 11. Giám sát & quan sát
- Metrics: tỉ lệ thành công, thời gian xử lý/job & theo dòng, tỉ lệ lỗi theo loại, chất lượng dữ liệu (tỉ lệ hợp lệ).
- Logging có cấu trúc (job bắt đầu/kết thúc, lỗi theo dòng, chi tiết khoá ngoại không tìm thấy...).

---

## 12. Tối ưu hiệu năng
- Chỉ số/Index thiết yếu:
```sql
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_projects_name_lower ON projects(LOWER(name));
CREATE INDEX idx_units_project_code ON units(project_id, code);
CREATE INDEX idx_employees_code ON employees(employee_code);
CREATE INDEX idx_import_jobs_status ON import_jobs(status);
CREATE INDEX idx_import_job_errors_job_id ON import_job_errors(import_job_id);
```
- Cache tại ứng dụng cho projects/employees/units truy cập thường xuyên.

---

## 13. Kiểm thử
- Unit: parser, validator, chuẩn hoá dữ liệu.
- Tích hợp: từ API đến ghi DB với dữ liệu seed.
- E2E: quy trình 3 bước trên UI.
- Bộ dữ liệu kiểm thử: trường hợp chuẩn, biên, lỗi; tệp lớn 10k+ dòng; tệp hỏng.

Ví dụ Jest:
```javascript
describe('Customer Import Service', () => {
  test('import hợp lệ', async () => {/* ... */});
  test('xử lý trùng phone', async () => {/* ... */});
});
```

---

## 14. Triển khai vận hành
- Hạ tầng: service worker nền (scale ngang); hàng đợi (Redis/RabbitMQ) hoặc cron-polling DB.
- CSDL: đảm bảo index; chạy import giờ thấp điểm với tệp lớn.
- Bộ nhớ: streaming parser; giới hạn lô.
- Bảo mật: RBAC; quét tệp; giới hạn quyền truy cập storage.

Checklist triển khai:
- [ ] Migration DB & Index
- [ ] Cấu hình giá trị & biến môi trường
- [ ] Worker nền & hàng đợi
- [ ] Phân quyền API & bảo mật tải tệp
- [ ] Giám sát/Logging
- [ ] Benchmark hiệu năng

---

## 15. Định hướng mở rộng
- Cập nhật realtime (SSE/WebSocket); rule validate nâng cao; làm sạch/chuẩn hoá dữ liệu; import theo lịch; đa tenant; webhook CRM; dashboard chất lượng dữ liệu; ML gợi ý.

---

## Kết luận
Thiết kế này cung cấp bản thiết kế tổng thể và hướng dẫn triển khai chi tiết cho chức năng import dữ liệu khách hàng theo yêu cầu 5.1.4. Giải pháp bảo đảm tính toàn vẹn dữ liệu, hiệu năng và bảo mật, đồng thời cho phép phát triển gia tăng và mở rộng trong tương lai.
