package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "ALERT_FILTER")
@Data
public class AlertFilter {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ALERT_CONFIG_ID")
    private Long alertConfigId;

    @Column(name = "CUSTOMER_CODE")
    private String customerCode;

    @Column(name = "DATAPOOL_PKG_CODE")
    private String dataPackCode;

}
