package vn.agis.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import vn.agis.crm.base.jpa.entity.QuerySuggest;
import vn.agis.crm.service.QuerySuggestService;

import java.util.List;

@RestController
@RequestMapping("/assistant/query-suggest")
public class QuerySuggestController extends CrudController<QuerySuggest, Long>{

    private QuerySuggestService querySuggestService;
    @Autowired
    public QuerySuggestController(QuerySuggestService service) {
        super(service);
        this.querySuggestService = service;
        this.baseUrl = "/assistant/query-suggest";
    }

    @GetMapping("")
    public ResponseEntity<List<QuerySuggest>> getPageCustomers() {
        List<QuerySuggest> list = querySuggestService.search();
        return ResponseEntity.ok().body(list);
    }
}
