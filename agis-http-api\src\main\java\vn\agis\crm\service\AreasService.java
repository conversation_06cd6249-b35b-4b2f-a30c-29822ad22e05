package vn.agis.crm.service;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.AreasSearchResDTO;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.resp.SearchUserResponseDTO;
import vn.agis.crm.base.jpa.entity.Areas;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.dto.AreasSearchDTO;
import vn.agis.crm.util.RequestUtils;

@Service
public class AreasService extends CrudService<Areas, Long> {

    private static final Logger logger = LoggerFactory.getLogger(AreasService.class);

    public AreasService() {
        super(Areas.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.AREAS;
    }

    public Page<AreasSearchResDTO> searchAreas(AreasSearchDTO searchDTO, Pageable pageable) {
        List<AreasSearchResDTO> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), AreasSearchResDTO.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchUserResponseDTO.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in searchAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public List<Areas> getAreaHierarchy(Long parentId) {
        Event event = RequestUtils.amqp(JpaConstants.Method.GET_AREA_HIERARCHY, category, parentId, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (List<Areas>) event.payload;
        }
        return null;
    }

    public Areas createAreas(Areas areas) {
        Areas response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, areas, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Areas) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, areas.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Areas updateAreas(Areas areas) {
        Areas response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE, category, areas, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Areas) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in updateAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, areas.toString(), response != null ? response.toString() : null, event);
        }
    }

    public void changeStatus(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE_ONE, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
        } catch (Exception e) {
            logger.error("Error in changeStatus: {}", e.getMessage(), e);
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), null, event);
        }
    }

    public List<Areas> getAllAreas() {
        List<Areas> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.GET_ALL, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<Areas>) event.payload;
                return response;
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getAllAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, null, response != null ? response.toString() : null, event);
        }
    }

    public Boolean checkAreasNameExists(String name) {
        Boolean response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.CHECK_EXITS, category, name, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Boolean) event.payload;
                return response;
            }
            return false;
        } catch (Exception e) {
            logger.error("Error in checkAreasNameExists: {}", e.getMessage(), e);
            return false;
        } finally {
            writeLog(timeRequest, timeResponse, name, response != null ? response.toString() : null, event);
        }
    }
}
