package vn.agis.crm.base.jpa.dto.docs;

import com.google.gson.reflect.TypeToken;
import vn.agis.crm.base.jpa.entity.DocumentEntity;
import vn.agis.crm.base.utils.Utils;

import java.util.Map;

public class DocumentContent extends DTO {
    private String name;
    private Map<String,String> title;
    private String key;
    private Long parentId;
    private Long previous;
    private DocumentContent previousInstance;
    private Long next;
    private DocumentContent nextInstance;
    private String path;
    private Map<String,String> content;
    private Map<String,String> contentHtml;
    private Integer typeDocument;
    private String icon;
    private Long projectId;
    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, String> getTitle() {
        return title;
    }

    public void setTitle(Map<String, String> title) {
        this.title = title;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getPrevious() {
        return previous;
    }

    public void setPrevious(Long previous) {
        this.previous = previous;
    }

    public DocumentContent getPreviousInstance() {
        return previousInstance;
    }

    public void setPreviousInstance(DocumentContent previousInstance) {
        this.previousInstance = previousInstance;
    }

    public Long getNext() {
        return next;
    }

    public void setNext(Long next) {
        this.next = next;
    }

    public DocumentContent getNextInstance() {
        return nextInstance;
    }

    public void setNextInstance(DocumentContent nextInstance) {
        this.nextInstance = nextInstance;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Map<String, String> getContent() {
        return content;
    }

    public void setContent(Map<String, String> content) {
        this.content = content;
    }

    public Map<String, String> getContentHtml() {
        return contentHtml;
    }

    public void setContentHtml(Map<String, String> contentHtml) {
        this.contentHtml = contentHtml;
    }

    public int getTypeDocument() {
        return typeDocument;
    }

    public void setTypeDocument(int typeDocument) {
        this.typeDocument = typeDocument;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getProjectId() {
        return projectId;
    }

    public void setProjectId(Long projectId) {
        this.projectId = projectId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public DocumentContent() {};
    public DocumentContent(DocumentEntity entity){
        id = entity.getId();
        name = entity.getName();
        if(entity.getTitle() != null){
            title = Utils.gson.fromJson(entity.getTitle(), new TypeToken<Map<String,String>>(){}.getType());
        }
        key = entity.getKey();
        parentId = entity.getParentId();
        previous = entity.getPrevious();
        next = entity.getNext();
        path = entity.getPath();
        if(entity.getContent() != null){
            content = Utils.gson.fromJson(entity.getContent(), new TypeToken<Map<String, String>>(){}.getType());
        }
        typeDocument = entity.getTypeDocument();
        icon = entity.getIcon();
        projectId = entity.getProjectId();
        description = entity.getDescription();
    }

    public DocumentEntity convertToEntity(){
        DocumentEntity entity = new DocumentEntity();
        entity.setId(id);
        entity.setName(name);
        entity.setTitle(Utils.gson.toJson(title));
        entity.setKey(key);
        entity.setParentId(parentId);
        entity.setPrevious(previous);
        entity.setNext(next);
        entity.setPath(path);
        entity.setContent(Utils.gson.toJson(content));
        entity.setTypeDocument(typeDocument);
        entity.setIcon(icon);
        entity.setProjectId(projectId);
        entity.setDescription(description);
        return entity;
    }
}
