package vn.agis.crm.base.jpa.services;

import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.RSQLParserException;
import cz.jirutka.rsql.parser.ast.Node;
import jakarta.transaction.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.util.StringUtils;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.KeyValuePair;
import vn.agis.crm.base.jpa.entity.AbstractEntity;
import vn.agis.crm.base.jpa.repositories.CustomJpaRepository;
import vn.agis.crm.base.rsql.CustomRsqlVisitor;
import vn.agis.crm.base.utils.ObjectMapperUtil;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import java.io.Serializable;
import java.util.*;
import java.util.function.Function;

@Transactional
@SuppressWarnings("Duplicates")
public abstract class CrudService<T extends AbstractEntity, ID extends Serializable> {
    private static Logger logger = LoggerFactory.getLogger(CrudService.class);
    protected CustomJpaRepository<T, ID> repository;
    private final Class<T> typeParameterClass;
    @PersistenceContext
    protected EntityManager entityManager;
    @Autowired
    JdbcTemplate jdbcTemplate;

    public CrudService(Class<T> typeParameterClass) {
        this.typeParameterClass = typeParameterClass;
    }

    @SuppressWarnings("unchecked")
    public Event process(Event event) {
        switch (event.method) {
            case JpaConstants.Method.CREATE:
            case JpaConstants.Method.CREATE_ONE:
                return processCreate(event);
            case JpaConstants.Method.CREATE_MANY:
                return processCreateMany(event);
            case JpaConstants.Method.UPDATE:
            case JpaConstants.Method.UPDATE_ONE:
                return processUpdate(event);
            case JpaConstants.Method.UPDATE_MANY:
                return processUpdateMany(event);
            case JpaConstants.Method.DELETE:
            case JpaConstants.Method.DELETE_ONE:
                return processDelete(event);
            case JpaConstants.Method.DELETE_MANY:
                return processBatchDelete(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
            case JpaConstants.Method.GET_MANY:
                return processGetMany(event);
            case JpaConstants.Method.GET_ALL:
                return processGetAll(event);
            case JpaConstants.Method.GET_BY_KEY:
                return processGetByKey(event);
            default:
                return event.createResponse(null, ResponseCode.METHOD_NOT_SUPPORTED, null);
        }
    }

    public PageInfo search(Specification<T> specification, Pageable pageable) {
        Page<T> page = repository.findAll(specification, pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(page.getContent()));
        return pageInfo;
    }

    public PageInfo searchAndConvert(Specification<T> specification, Pageable pageable) {
        Page<T> page = repository.findAll(specification, pageable);
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        List<Object> dataConverted = new ArrayList<>();
        for (T t : page.getContent()) {
            dataConverted.add(convertEntityToDTO(t));
        }
        pageInfo.setData(ObjectMapperUtil.toJsonString(dataConverted));
        return pageInfo;
    }

    public Event processCreate(Event request) {
        return request.createResponse(create(request.payload), ResponseCode.OK, null);
    }

    public List<Object> createMany(List payload) {
        List<Object> result = new ArrayList<>();
        for (Object t: payload) {
            result.add(create(t));
        }
        return result;
    }

    public Event processCreateMany(Event request) {
        try {
            return request.createResponse(createMany((List) request.payload), ResponseCode.OK, null);
        } catch (Exception ex) {
            return request.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
        }
    }

    public List<Object> updateMany(List payload) {
        List<Object> result = new ArrayList<>();
        for (Object t : payload) {
            result.add(update(t));
        }
        return result;
    }

    public Event processUpdateMany(Event request) {
        try {
            return request.createResponse(updateMany((List<T>) request.payload), ResponseCode.OK, null);
        } catch (Exception ex) {
            return request.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
        }
    }

    public Event processUpdate(Event request) {
        return request.createResponse(update(request.payload), ResponseCode.OK, null);
    }

    public Event processDelete(Event request) {
        ID id = (ID) request.payload;
        delete(id);
        return request.createResponse(null, ResponseCode.OK, null);
    }

    private List batchDeleteReturnFail(List<ID> ids) {
        List<ID> fail = new ArrayList<>();
        for (ID id : ids) {
            try {
                delete((ID) id);
            } catch (Exception ex) {
                logger.error(ex.getMessage(), ex);
                fail.add(id);
            }
        }
        return fail;
    }

    public Event processBatchDelete(Event request) {
        List<ID> ids = (List<ID>) request.payload;
        return request.createResponse(batchDeleteReturnFail(ids), ResponseCode.OK, null);
    }

    public Event processGetOne(Event request) {
        return request.createResponse(convertEntityToDTO(get((ID) request.payload)), ResponseCode.OK, null);
    }

    public Event processGetAll(Event request) {
        List<T> result = repository.findAll();
        List<Object> resultByDto = new ArrayList<>();
        result.forEach(s -> resultByDto.add(convertEntityToDTO(s)));
        return request.createResponse(resultByDto, ResponseCode.OK, null);
    }

    public Event processGetMany(Event request) {
        List<T> result = this.getByListIds((List<ID>) request.payload);
        List<Object> resultByDto = new ArrayList<>();
        result.forEach(s -> resultByDto.add(convertEntityToDTO(s)));
        return request.createResponse(resultByDto, ResponseCode.OK, null);
    }

    public T get(ID id) {
        return repository.findById(id).orElse(null);
    }

    public List<T> findAll() {
        return repository.findAll();
    }

    public Page<T> findAll(Pageable pageable) {
        return repository.findAll(pageable);
    }

    public Long countByQuery(String query) {
        if (StringUtils.isEmpty(query)) {
            return repository.count();
        }
        try {
            Node rootNode = new RSQLParser().parse(query);
            Specification<T> spec = rootNode.accept(new CustomRsqlVisitor<T>());
            return repository.count(spec);
        } catch (RSQLParserException pe) {
            logger.error("SEARCH FAIL: {}", query);
            logger.error(pe.getMessage(), pe);
            return 0l;
        } catch (Exception e) {
            logger.error("SEARCH FAIL: {}", query);
            logger.error(e.getMessage(), e);
            return 0l;
        }
    }

    public Object create(Object input) {
        T entity = beforeCreate(input);
        repository.save(entity);
        return afterCreate(entity);
    }

    public Object update(Object input) {
        T entity = beforeUpdate(input);
        repository.save(entity);
        return afterUpdate(entity);
    }

    public void delete(Object input) {
        T entity = beforeDelete(input);
        repository.delete(entity);
        afterDelete(entity);
    }

    public void delete(ID id) {
        T entity = beforeDelete(id);
        repository.deleteById(id);
        afterDelete(entity);
    }

    public void deleteByIds(List<ID> ids) {
        for (ID id : ids)
            delete(id);
    }

    public void deleteById(ID id) {
        delete(id);
    }

    public Long count() {
        return repository.count();
    }

    public void batchDelete(List<ID> ids) {
        for (ID id : ids) {
            delete(id);
        }
    }

    public List<T> getByListIds(List<ID> ids) {
        return repository.findAllById((Iterable<ID>) ids);
    }

    public T beforeCreate(Object input) {
        T entity;
        if (!(input.getClass().equals(typeParameterClass))) entity = convertDTOToEntityWhenCreate(input);
        else entity = (T) input;
//        if (entity instanceof AbstractEntity abs) {
//            abs.setCreatedAt(new Date());
//        }
        entity.setCreatedAt(new Date());
        return entity;
    }

    public Object afterCreate(T entity) {
        //do something after create
        return convertEntityToDTOWhenCreate(entity);
    }

    public T beforeUpdate(Object input) {
        T entity;
        if (!(input.getClass().equals(typeParameterClass))) entity = convertDTOToEntityWhenUpdate(input);
        else entity = (T) input;
        return entity;
    }

    public Object afterUpdate(T updated) {
        //do something after update
        return convertEntityToDTOWhenUpdate(updated);
    }

    public T beforeDelete(Object input) {
        T entity;
        if (!(input.getClass().equals(typeParameterClass))) entity = convertDTOToEntityWhenDelete(input);
        else entity = (T) input;
        return entity;
    }

    public T beforeDelete(ID id) {
        return null;
    }


    public Object afterDelete(T deleted) {
        //do something after delete
        return null;
    }

    public Event processGetByKey(Event event) {
        KeyValuePair keyValuePair = (KeyValuePair) event.payload;
        String query = keyValuePair.getKey() + "==" + keyValuePair.getValue();
        Node rootNode = new RSQLParser().parse(query);
        Specification<T> spec = rootNode.accept(new CustomRsqlVisitor<T>());
        return event.createResponse(repository.findAll(spec), ResponseCode.OK, null);
    }

    public Page<T> emptyPage() {
        return new Page<T>() {
            @Override
            public int getTotalPages() {
                return 0;
            }

            @Override
            public long getTotalElements() {
                return 0;
            }

            @Override
            public <U> Page<U> map(Function<? super T, ? extends U> function) {
                return null;
            }

            @Override
            public int getNumber() {
                return 0;
            }

            @Override
            public int getSize() {
                return 0;
            }

            @Override
            public int getNumberOfElements() {
                return 0;
            }

            @Override
            public List<T> getContent() {
                return new ArrayList<>();
            }

            @Override
            public boolean hasContent() {
                return false;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public boolean isFirst() {
                return false;
            }

            @Override
            public boolean isLast() {
                return false;
            }

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }

            @Override
            public Pageable nextPageable() {
                return null;
            }

            @Override
            public Pageable previousPageable() {
                return null;
            }

            @Override
            public Iterator<T> iterator() {
                return null;
            }
        };
    }

    public Object convertEntityToDTO(T t) {
        return t;
    }

    public T convertDTOToEntity(Object t) {
        return (T) t;
    }

    public Object convertEntityToDTOWhenUpdate(T t) {
        return convertEntityToDTO(t);
    }

    public T convertDTOToEntityWhenUpdate(Object t) {
        return convertDTOToEntity(t);
    }

    public Object convertEntityToDTOWhenCreate(T t) {
        return convertEntityToDTO(t);
    }

    public T convertDTOToEntityWhenCreate(Object t) {
        return convertDTOToEntity(t);
    }

    public Object convertEntityToDTOWhenDelete(T t) {
        return convertEntityToDTO(t);
    }

    public T convertDTOToEntityWhenDelete(Object t) {
        return convertDTOToEntity(t);
    }

}
