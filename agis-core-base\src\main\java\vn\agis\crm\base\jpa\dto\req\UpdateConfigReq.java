package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class UpdateConfigReq {
    @NotNull
    private Long id;

    @Size(max = 100)
    private String configKey; // optional change

    private Integer configType; // optional change

    private String configValue;

    @Size(max = 255)
    private String description;
}

