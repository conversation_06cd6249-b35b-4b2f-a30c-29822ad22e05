package vn.agis.crm.service;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.UnitDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.UnitSearchDto;
import vn.agis.crm.base.jpa.dto.req.UnitCodeCheckDto;
import vn.agis.crm.base.jpa.dto.req.UnitDto;
import vn.agis.crm.base.jpa.entity.Units;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

@Service
public class UnitService extends CrudService<Units, Long> {

    private static final Logger logger = LoggerFactory.getLogger(UnitService.class);

    public UnitService() {
        super(Units.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Category.UNIT;
    }

    public Page<Units> search(UnitSearchDto searchDTO, Pageable pageable) {
        List<Units> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), Units.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in searchUnits: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Units createUnit(UnitDto dto) {
        Units response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Units) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in createUnit: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Units getOne(Long id) {
        Units response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Units) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getOne Unit: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Units update(UnitDto dto) {
        Units response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.UPDATE, category, dto, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Units) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in update Unit: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, dto.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Boolean checkExistCode(Long projectId, String code) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            UnitCodeCheckDto payload = new UnitCodeCheckDto();
            payload.setProjectId(projectId);
            payload.setCode(code);
            event = RequestUtils.amqp(Constants.Method.CHECK_EXIST_UNIT_CODE, category, payload, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                Object pl = event.payload;
                if (pl instanceof Boolean) return (Boolean) pl;
                return Boolean.valueOf(String.valueOf(pl));
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in checkExistCode: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, String.format("%s-%s", projectId, code), event != null ? String.valueOf(event.payload) : null, event);
        }
    }

    /**
     * Validate unit deletion before proceeding with actual deletion
     */
    public UnitDeletionValidationResult validateUnitDeletion(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("VALIDATE_UNIT_DELETION", category, id, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                return (UnitDeletionValidationResult) event.payload;
            } else {
                // Return error result for any non-OK response
                return new UnitDeletionValidationResult(false, "Lỗi hệ thống khi kiểm tra căn hộ", 0L);
            }
        } catch (Exception e) {
            logger.error("Error in validateUnitDeletion: {}", e.getMessage(), e);
            return new UnitDeletionValidationResult(false, "Lỗi hệ thống khi kiểm tra căn hộ", 0L);
        } finally {
            writeLog(timeRequest, timeResponse, String.valueOf(id), event != null ? String.valueOf(event.payload) : null, event);
        }
    }
}

