package vn.agis.crm.base.jpa.dto;

import java.util.Date;

public class AssignmentHistoryDto {
    private Long id;
    private Long customerId;
    private Long employeeId;
    private String employeeName;
    private String employeeCode;
    private String employeeEmail;
    private String employeePhone;
    private Integer roleType;
    private String roleTypeName;
    private Date assignedFrom;
    private Date assignedTo;
    private Boolean active;

    // Default constructor
    public AssignmentHistoryDto() {
    }

    // Constructor with all fields
    public AssignmentHistoryDto(Long id, Long customerId, Long employeeId, String employeeName,
                                String employeeCode, String employeeEmail, String employeePhone,
                                Integer roleType, String roleTypeName, Date assignedFrom,
                                Date assignedTo, Boolean active) {
        this.id = id;
        this.customerId = customerId;
        this.employeeId = employeeId;
        this.employeeName = employeeName;
        this.employeeCode = employeeCode;
        this.employeeEmail = employeeEmail;
        this.employeePhone = employeePhone;
        this.roleType = roleType;
        this.roleTypeName = roleTypeName;
        this.assignedFrom = assignedFrom;
        this.assignedTo = assignedTo;
        this.active = active;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Long getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Long employeeId) {
        this.employeeId = employeeId;
    }

    public String getEmployeeName() {
        return employeeName;
    }

    public void setEmployeeName(String employeeName) {
        this.employeeName = employeeName;
    }

    public String getEmployeeCode() {
        return employeeCode;
    }

    public void setEmployeeCode(String employeeCode) {
        this.employeeCode = employeeCode;
    }

    public String getEmployeeEmail() {
        return employeeEmail;
    }

    public void setEmployeeEmail(String employeeEmail) {
        this.employeeEmail = employeeEmail;
    }

    public String getEmployeePhone() {
        return employeePhone;
    }

    public void setEmployeePhone(String employeePhone) {
        this.employeePhone = employeePhone;
    }

    public Integer getRoleType() {
        return roleType;
    }

    public void setRoleType(Integer roleType) {
        this.roleType = roleType;
    }

    public String getRoleTypeName() {
        return roleTypeName;
    }

    public void setRoleTypeName(String roleTypeName) {
        this.roleTypeName = roleTypeName;
    }

    public Date getAssignedFrom() {
        return assignedFrom;
    }

    public void setAssignedFrom(Date assignedFrom) {
        this.assignedFrom = assignedFrom;
    }

    public Date getAssignedTo() {
        return assignedTo;
    }

    public void setAssignedTo(Date assignedTo) {
        this.assignedTo = assignedTo;
    }

    public Boolean getActive() {
        return active;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    @Override
    public String toString() {
        return "AssignmentHistoryDto{" +
                "id=" + id +
                ", customerId=" + customerId +
                ", employeeId=" + employeeId +
                ", employeeName='" + employeeName + '\'' +
                ", employeeCode='" + employeeCode + '\'' +
                ", employeeEmail='" + employeeEmail + '\'' +
                ", employeePhone='" + employeePhone + '\'' +
                ", roleType=" + roleType +
                ", roleTypeName='" + roleTypeName + '\'' +
                ", assignedFrom=" + assignedFrom +
                ", assignedTo=" + assignedTo +
                ", active=" + active +
                '}';
    }
}
