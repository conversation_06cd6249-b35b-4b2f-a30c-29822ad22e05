package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SearchDeviceTelemetryReq {
    private Long deviceId;
    private Integer page;
    private Integer size;
    private String sortBy;

    public SearchDeviceTelemetryReq(Long deviceId, Integer page, Integer size, String sortBy) {
        this.deviceId = deviceId;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
