// Project Validation Verification Script
// Quick verification script to test the enhanced project validation logic

package vn.agis.crm.verification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.Projects;
import vn.agis.crm.repository.ProjectRepository;

import java.util.Arrays;
import java.util.List;

/**
 * Verification script to test the enhanced project validation and creation logic
 * Run this to verify that the implementation works correctly
 */
@SpringBootApplication
public class ProjectValidationVerificationScript implements CommandLineRunner {

    @Autowired
    private ProjectRepository projectRepository;

    public static void main(String[] args) {
        SpringApplication.run(ProjectValidationVerificationScript.class, args);
    }

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        System.out.println("🏗️ Project Validation Verification Script");
        System.out.println("==========================================");
        System.out.println();

        // Test data - various project names with different cases
        List<String> testProjectNames = Arrays.asList(
            "Vinhomes Grand Park",
            "VINHOMES GRAND PARK", 
            "vinhomes grand park",
            "Vinhomes Central Park",
            "MASTERI THAO DIEN",
            "Masteri Thao Dien",
            "masteri thao dien",
            "The Manor Central Park",
            "  Trimmed Project Name  ",
            "New Project 2024"
        );

        System.out.println("📋 Testing Project Validation Logic");
        System.out.println("===================================");

        for (String projectName : testProjectNames) {
            testProjectValidation(projectName);
        }

        System.out.println();
        System.out.println("📊 Final Database State");
        System.out.println("=======================");
        
        List<Projects> allProjects = projectRepository.findAll();
        System.out.println("Total projects in database: " + allProjects.size());
        
        for (Projects project : allProjects) {
            System.out.println(String.format("ID: %d | Name: '%s' | Active: %s | Created: %s", 
                project.getId(), 
                project.getName(), 
                project.isActive(),
                project.getCreatedAt()));
        }

        System.out.println();
        System.out.println("✅ Verification Complete!");
        System.out.println("Expected Results:");
        System.out.println("- No duplicate projects for same name with different cases");
        System.out.println("- New projects created only when they don't exist");
        System.out.println("- Project names properly trimmed and normalized");
    }

    private void testProjectValidation(String projectName) {
        System.out.println(String.format("\n🔍 Testing: '%s'", projectName));
        
        try {
            // Simulate the validation logic
            Projects result = validateAndCreateProject(projectName, 1L);
            
            System.out.println(String.format("✅ Result: ID=%d, Name='%s'", 
                result.getId(), result.getName()));
                
        } catch (Exception e) {
            System.out.println(String.format("❌ Error: %s", e.getMessage()));
        }
    }

    /**
     * Simulated version of the validateAndCreateProject method
     * This mimics the actual implementation in ImportExecutionProcessor
     */
    private Projects validateAndCreateProject(String projectName, Long userId) {
        if (projectName == null || projectName.trim().isEmpty()) {
            throw new IllegalArgumentException("Tên dự án không được để trống");
        }
        
        String trimmedProjectName = projectName.trim();
        
        try {
            // Check if project already exists (case-insensitive)
            Projects existingProject = projectRepository.findFirstByNameIgnoreCase(trimmedProjectName);
            
            if (existingProject != null) {
                System.out.println(String.format("   📁 Found existing project: %s (ID: %d)", 
                    existingProject.getName(), existingProject.getId()));
                return existingProject;
            }
            
            // Project doesn't exist, create new one
            System.out.println(String.format("   🆕 Creating new project: %s", trimmedProjectName));
            
            Projects newProject = new Projects();
            newProject.setName(trimmedProjectName);
            newProject.setCreatedBy(userId);
            newProject.setCreatedAt(new java.util.Date());
            newProject.setActive(true);
            
            Projects savedProject = projectRepository.save(newProject);
            System.out.println(String.format("   ✅ Created project: %s (ID: %d)", 
                savedProject.getName(), savedProject.getId()));
            
            return savedProject;
            
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi xử lý dự án '" + trimmedProjectName + "': " + e.getMessage(), e);
        }
    }
}

/**
 * Manual Testing Instructions
 * ===========================
 * 
 * 1. **Compile and Run:**
 *    ```bash
 *    mvn compile exec:java -Dexec.mainClass="vn.agis.crm.verification.ProjectValidationVerificationScript"
 *    ```
 * 
 * 2. **Expected Output:**
 *    - First occurrence of each unique project name (case-insensitive) should create new project
 *    - Subsequent occurrences with different cases should find existing project
 *    - Project names should be trimmed of leading/trailing whitespace
 *    - No duplicate projects should be created
 * 
 * 3. **Database Verification:**
 *    ```sql
 *    SELECT id, name, is_active, created_at, created_by 
 *    FROM projects 
 *    WHERE deleted_at IS NULL 
 *    ORDER BY created_at DESC;
 *    ```
 * 
 * 4. **Import Testing:**
 *    Create a test CSV file with project names in different cases:
 *    ```csv
 *    TÊN DỰ ÁN,MÃ CĂN,HỌ VÀ TÊN KHÁCH HÀNG,PHONE
 *    Vinhomes Grand Park,A1.01,Nguyen Van A,0901234567
 *    VINHOMES GRAND PARK,A1.02,Tran Thi B,0901234568
 *    vinhomes grand park,A1.03,Le Van C,0901234569
 *    Masteri Thao Dien,B2.01,Pham Thi D,0901234570
 *    MASTERI THAO DIEN,B2.02,Hoang Van E,0901234571
 *    ```
 * 
 * 5. **API Testing:**
 *    ```bash
 *    # Upload test file
 *    curl -X POST "http://localhost:8080/imports" \
 *      -F "file=@test_projects.csv" \
 *      -F "options={\"mode\":\"DRY_RUN\"}"
 *    
 *    # Check results
 *    curl -X GET "http://localhost:8080/imports/{jobId}"
 *    
 *    # Confirm import
 *    curl -X POST "http://localhost:8080/imports/{jobId}/confirm"
 *    ```
 * 
 * 6. **Verification Checklist:**
 *    ✅ No duplicate projects created for same name with different cases
 *    ✅ New projects created automatically when they don't exist  
 *    ✅ Project IDs correctly used in customer properties
 *    ✅ Proper error handling for invalid project names
 *    ✅ Vietnamese error messages displayed
 *    ✅ Transaction consistency maintained
 *    ✅ Performance acceptable for bulk imports
 * 
 * 7. **Performance Testing:**
 *    Test with larger files (100+ rows) to verify:
 *    - Database query performance
 *    - Memory usage
 *    - Transaction handling
 *    - Error recovery
 */
