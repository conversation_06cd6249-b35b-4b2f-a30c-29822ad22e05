{"info": {"name": "CRM - Customer v2 Upsert", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "b9c9f0a0-1f2e-4d79-9a06-2f8d0d0c0c12"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}, {"key": "token", "value": "REPLACE_WITH_BEARER_TOKEN"}], "item": [{"name": "Customer - Create v2 (assignments + relatives + properties + offers)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/customer-mgmt/create-v2", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "create-v2"]}, "body": {"mode": "raw", "raw": "{\n  \"id\": null,\n  \"fullName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"phone\": \"0987654321\",\n  \"email\": \"nguy<PERSON><EMAIL>\",\n  \"birthDate\": \"15/05/1985\",\n  \"addressContact\": \"123 Đường ABC, Quận 1, TP.HCM\",\n  \"addressPermanent\": \"456 Đường XYZ, Quận 2, TP.HCM\",\n  \"nationality\": \"Việt Nam\",\n  \"maritalStatus\": \"Đã kết hôn\",\n  \"totalAsset\": 5000000000,\n  \"businessField\": \"<PERSON><PERSON> doanh bất động sản\",\n  \"avatarUrl\": \"link_anh_dai_dien\",\n  \"zaloStatus\": \"Đang hoạt động\",\n  \"facebookLink\": \"https://facebook.com/nguyenvanan\",\n  \"sourceType\": \"Data\",\n  \"sourceDetail\": \"Website\",\n  \"notes\": \"<PERSON>h<PERSON><PERSON> hàng tiềm năng, quan tâm B<PERSON> cao cấp\",\n  \"customerRelatives\": [\n    {\n      \"id\": null,\n      \"relationType\": \"Vợ\",\n      \"fullName\": \"<PERSON>rầ<PERSON>\",\n      \"birthYear\": 1987,\n      \"phone\": \"0912345678\",\n      \"notes\": \"Vợ, làm việc tại ngân hàng\"\n    },\n    {\n      \"id\": null,\n      \"relationType\": \"Con\",\n      \"fullName\": \"Nguyễn Văn Cường\",\n      \"birthYear\": 2010,\n      \"phone\": null,\n      \"notes\": \"Con trai đầu\"\n    }\n  ],\n  \"customerAssignments\": [\n    {\n      \"id\": null,\n      \"employeeId\": 1,\n      \"roleType\": 1,\n      \"assignedFrom\": \"01/01/2024\",\n      \"assignedTo\": \"31/12/2024\"\n    },\n    {\n      \"id\": null,\n      \"employeeId\": 3,\n      \"roleType\": 2,\n      \"assignedFrom\": \"15/06/2024\",\n      \"assignedTo\": \"15/12/2024\"\n    }\n  ],\n  \"customerProperties\": [\n    {\n      \"id\": null,\n      \"projectId\": 1,\n      \"unitId\": 1,\n      \"transactionDate\": \"15/03/2024\",\n      \"contractPrice\": 3500000000,\n      \"externalAgencyName\": \"ABC Realty\",\n      \"externalSaleName\": \"Lê Thị Dung\",\n      \"externalSalePhone\": \"0909123456\",\n      \"externalSaleEmail\": \"<EMAIL>\",\n      \"employeeId\": 1,\n      \"notes\": \"Giao dịch thành công\",\n      \"firstInteraction\": \"2024-01-15\",\n      \"lastInteraction\": \"2024-03-15\"\n    }\n  ],\n  \"customerOffers\": [\n    {\n      \"id\": null,\n      \"projectId\": 2,\n      \"notes\": \"Quan tâm đến căn hộ 3PN view biển\",\n      \"firstInteraction\": \"01/04/2024\",\n      \"lastInteraction\": \"15/08/2024\",\n      \"status\": \"Đang tiến hành\"\n    },\n    {\n      \"id\": null,\n      \"projectId\": 3,\n      \"notes\": \"Tìm hiểu về biệt thự\",\n      \"firstInteraction\": \"10/07/2024\",\n      \"lastInteraction\": \"20/08/2024\",\n      \"status\": \"Đang chờ\"\n    }\n  ]\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200/201', function () { pm.expect([200,201]).to.include(pm.response.code); });", "pm.test('Response has id and relatives', function () { var b = pm.response.json(); pm.expect(b).to.have.property('id'); pm.expect(b).to.have.property('relatives'); });"], "type": "text/javascript"}}]}, {"name": "Customer - Update v2 (assignments + relatives + properties + offers)", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/customer-mgmt/update-v2/{{id}}", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "update-v2", "{{id}}"], "variable": [{"key": "id", "value": "555"}]}, "body": {"mode": "raw", "raw": "{\n  \"id\": 555,\n  \"customerAssignments\": [ { \"employeeId\": 44, \"roleType\": 2, \"assignedFrom\": \"07/09/2025\" } ],\n  \"customerRelatives\": [\n    { \"id\": 9001, \"fullName\": \"<PERSON> cập nhật\", \"phone\": \"0912000000\" },\n    { \"id\": 9002, \"deleted\": true }\n  ],\n  \"customerProperties\": [\n    { \"id\": 7001, \"deleted\": true },\n    { \"projectId\": 110, \"unitId\": 301, \"transactionDate\": \"01/03/2025\", \"contractPrice\": 3200000000 }\n  ],\n  \"customerOffers\": [\n    { \"id\": 8002, \"status\": \"CLOSED\" },\n    { \"projectId\": 111, \"status\": \"OPEN\", \"notes\": \"Mới quan tâm\" }\n  ]\n}"}}, "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () { pm.expect(pm.response.code).to.eql(200); });", "pm.test('Response has id and relatives', function () { var b = pm.response.json(); pm.expect(b).to.have.property('id'); pm.expect(b).to.have.property('relatives'); });"], "type": "text/javascript"}}]}]}