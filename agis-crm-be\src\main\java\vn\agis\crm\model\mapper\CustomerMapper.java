package vn.agis.crm.model.mapper;

import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.entity.Customers;

@Component
public class CustomerMapper {

    public Customers toEntity(CustomerDto dto) {
        if (dto == null) return null;
        Customers e = new Customers();
        // required
        e.setFullName(dto.getFullName());
        e.setPhone(dto.getPhone());
        // optionals
        if (dto.getEmail() != null) e.setEmail(dto.getEmail());
        if (dto.getCccd() != null) e.setCccd(dto.getCccd());
        if (dto.getBirthDate() != null) e.setBirthDate(dto.getBirthDate());
        if (dto.getAddressContact() != null) e.setAddressContact(dto.getAddressContact());
        if (dto.getAddressPermanent() != null) e.setAddressPermanent(dto.getAddressPermanent());
        if (dto.getNationality() != null) e.setNationality(dto.getNationality());
        if (dto.getMaritalStatus() != null) e.setMaritalStatus(dto.getMaritalStatus());
        if (dto.getTotalAsset() != null) e.setTotalAsset(dto.getTotalAsset());
        if (dto.getBusinessField() != null) e.setBusinessField(dto.getBusinessField());
        if (dto.getAvatarUrl() != null) e.setAvatarUrl(dto.getAvatarUrl());
        if (dto.getZaloStatus() != null) e.setZaloStatus(dto.getZaloStatus());
        if (dto.getFacebookLink() != null) e.setFacebookLink(dto.getFacebookLink());
        if (dto.getSourceType() != null) e.setSourceType(dto.getSourceType());
        if (dto.getSourceDetail() != null) e.setSourceDetail(dto.getSourceDetail());
        if (dto.getNotes() != null) e.setNotes(dto.getNotes());

        // Map multiple contact information fields
        if (dto.getAdditionalPhones() != null) e.setAdditionalPhones(dto.getAdditionalPhones());
        if (dto.getAdditionalEmails() != null) e.setAdditionalEmails(dto.getAdditionalEmails());
        if (dto.getAdditionalCccds() != null) e.setAdditionalCccds(dto.getAdditionalCccds());

        // Map interests field
        if (dto.getInterests() != null) e.setInterests(dto.getInterests());

        return e;
    }

    public void updateEntityFromDto(Customers e, CustomerDto dto) {
        if (e == null || dto == null) return;
        if (dto.getFullName() != null) e.setFullName(dto.getFullName());
        if (dto.getPhone() != null) e.setPhone(dto.getPhone());
        if (dto.getEmail() != null) e.setEmail(dto.getEmail());
        if (dto.getCccd() != null) e.setCccd(dto.getCccd());
        if (dto.getBirthDate() != null) e.setBirthDate(dto.getBirthDate());
        if (dto.getAddressContact() != null) e.setAddressContact(dto.getAddressContact());
        if (dto.getAddressPermanent() != null) e.setAddressPermanent(dto.getAddressPermanent());
        if (dto.getNationality() != null) e.setNationality(dto.getNationality());
        if (dto.getMaritalStatus() != null) e.setMaritalStatus(dto.getMaritalStatus());
        if (dto.getTotalAsset() != null) e.setTotalAsset(dto.getTotalAsset());
        if (dto.getBusinessField() != null) e.setBusinessField(dto.getBusinessField());
        if (dto.getAvatarUrl() != null) e.setAvatarUrl(dto.getAvatarUrl());
        if (dto.getZaloStatus() != null) e.setZaloStatus(dto.getZaloStatus());
        if (dto.getFacebookLink() != null) e.setFacebookLink(dto.getFacebookLink());
        if (dto.getSourceType() != null) e.setSourceType(dto.getSourceType());
        if (dto.getSourceDetail() != null) e.setSourceDetail(dto.getSourceDetail());
        if (dto.getNotes() != null) e.setNotes(dto.getNotes());

        // Update multiple contact information fields
        if (dto.getAdditionalPhones() != null) e.setAdditionalPhones(dto.getAdditionalPhones());
        if (dto.getAdditionalEmails() != null) e.setAdditionalEmails(dto.getAdditionalEmails());
        if (dto.getAdditionalCccds() != null) e.setAdditionalCccds(dto.getAdditionalCccds());

        // Update interests field
        if (dto.getInterests() != null) e.setInterests(dto.getInterests());
    }
}

