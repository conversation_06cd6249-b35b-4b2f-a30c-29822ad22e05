package vn.agis.crm.service;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.NotificationDto;
import vn.agis.crm.base.jpa.dto.NotificationSearchDto;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.mapper.NotificationMapper;
import vn.agis.crm.repository.NotificationRepository;
import vn.agis.crm.repository.spec.NotificationSpecs;
import vn.agis.crm.util.BaseController;

@Service
@Transactional
public class NotificationService {

    private static final Logger logger = LoggerFactory.getLogger(NotificationService.class);

    @Autowired
    private NotificationRepository notificationRepository;

    @Autowired
    private NotificationMapper notificationMapper;

    public Event process(Event event) {
        switch (event.method) {
            case Method.SEARCH:
                return search(event);
            case Method.FIND_BY_ID:
                return findById(event);
            case "GET_ONE_WITH_DETAILS":
                return getOneWithDetails(event);
            case "MARK_AS_READ":
                return markAsRead(event);
            case "MARK_AS_UNREAD":
                return markAsUnread(event);
            case "GET_UNREAD_NOTIFICATIONS":
                return getUnreadNotifications(event);
            case "DELETE_NOTIFICATION":
                return deleteNotification(event);
            case "DELETE_NOTIFICATIONS_BATCH":
                return deleteNotificationsBatch(event);
            default:
                return event.createResponse(null, 404, "Phương thức không được tìm thấy");
        }
    }

    private Event search(Event event) {
        NotificationSearchDto searchDto = (NotificationSearchDto) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(
                searchDto.getSize(), searchDto.getPage(), searchDto.getSortBy());

        Specification<Notifications> spec = NotificationSpecs.buildSearchSpecification(
                searchDto.getTargetEmployeeId(),
                searchDto.getTargetCustomerId(),
                searchDto.getType(),
                searchDto.getTitle(),
                searchDto.getContent(),
                searchDto.getIsRead(),
                searchDto.getReadAt()
        );

        Page<Notifications> page = notificationRepository.findAll(spec, listRequest.getPageable());

        // Convert to DTOs with details
        List<NotificationDto> dtoList = page.getContent().stream()
                .map(notificationMapper::toDtoWithDetails)
                .collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setData(ObjectMapperUtil.toJsonString(dtoList));
        pageInfo.setTotalCount(page.getTotalElements());
        return event.createResponse(pageInfo, 200, "Thành công");
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Thiếu ID thông báo");
        }
        return notificationRepository.findById(id)
                .map(notification -> {
                    NotificationDto dto = notificationMapper.toDto(notification);
                    return event.createResponse(dto, 200, "Thành công");
                })
                .orElse(event.createResponse(null, 404, "Không tìm thấy thông báo"));
    }

    private Event getOneWithDetails(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Thiếu ID thông báo");
        }
        return notificationRepository.findById(id)
                .map(notification -> {
                    NotificationDto dto = notificationMapper.toDtoWithDetails(notification);
                    return event.createResponse(dto, 200, "Thành công");
                })
                .orElse(event.createResponse(null, 404, "Không tìm thấy thông báo"));
    }

    private Event markAsRead(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Thiếu ID thông báo");
        }

        Optional<Notifications> notificationOpt = notificationRepository.findById(id);
        if (!notificationOpt.isPresent()) {
            return event.createResponse(null, 404, "Không tìm thấy thông báo");
        }

        Notifications notification = notificationOpt.get();
        notification.setIsRead(true);
        notification.setReadAt(new Date());
        
        Notifications saved = notificationRepository.save(notification);
        NotificationDto dto = notificationMapper.toDtoWithDetails(saved);
        
        return event.createResponse(dto, 200, "Đã đánh dấu thông báo là đã đọc");
    }

    private Event markAsUnread(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Thiếu ID thông báo");
        }

        Optional<Notifications> notificationOpt = notificationRepository.findById(id);
        if (!notificationOpt.isPresent()) {
            return event.createResponse(null, 404, "Không tìm thấy thông báo");
        }

        Notifications notification = notificationOpt.get();
        notification.setIsRead(false);
        notification.setReadAt(null);
        
        Notifications saved = notificationRepository.save(notification);
        NotificationDto dto = notificationMapper.toDtoWithDetails(saved);
        
        return event.createResponse(dto, 200, "Đã đánh dấu thông báo là chưa đọc");
    }

    /**
     * Create a new notification for internal service calls
     * This method is used by other services within the same module to create notifications
     *
     * @param targetEmployeeId ID của nhân viên nhận thông báo (bắt buộc)
     * @param type Loại thông báo (1=LeadAssigned, 2=CustomerBirthday, 3=LeadUncaredWarning, 4=LeadInactiveWarning)
     * @param title Tiêu đề thông báo (bắt buộc)
     * @param content Nội dung thông báo (bắt buộc)
     * @param targetCustomerId ID khách hàng liên quan (có thể null)
     * @param createdBy ID người tạo thông báo (có thể null, sẽ dùng system user)
     * @return Notifications entity đã được tạo và lưu vào database
     * @throws IllegalArgumentException nếu các tham số bắt buộc không hợp lệ
     */
    public Notifications createNotification(Long targetEmployeeId, Integer type, String title,
                                          String content, Long targetCustomerId, Long createdBy) {
        logger.debug("Creating notification for employee {} with type {} and title: {}",
                    targetEmployeeId, type, title);

        // Validate required parameters
        if (targetEmployeeId == null) {
            throw new IllegalArgumentException("Target employee ID không được để trống");
        }

        if (type == null || type < 1 || type > 4) {
            throw new IllegalArgumentException("Type phải nằm trong khoảng 1-4 (1=LeadAssigned, 2=CustomerBirthday, 3=LeadUncaredWarning, 4=LeadInactiveWarning)");
        }

        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("Title không được để trống");
        }

        if (content == null || content.trim().isEmpty()) {
            throw new IllegalArgumentException("Content không được để trống");
        }

        // Validate title length
        if (title.trim().length() > 255) {
            throw new IllegalArgumentException("Title không được vượt quá 255 ký tự");
        }

        try {
            // Create new notification entity
            Notifications notification = new Notifications();
            notification.setTargetEmployeeId(targetEmployeeId);
            notification.setTargetCustomerId(targetCustomerId);
            notification.setType(type);
            notification.setTitle(title.trim());
            notification.setContent(content.trim());

            // Set default read status
            notification.setIsRead(false);
            notification.setReadAt(null);

            // Set audit fields
            notification.setCreatedAt(new Date());
            notification.setCreatedBy(createdBy); // Can be null for system-generated notifications

            // Save to database
            Notifications savedNotification = notificationRepository.save(notification);

            logger.info("Successfully created notification with ID {} for employee {} with type {}",
                       savedNotification.getId(), targetEmployeeId, type);

            return savedNotification;

        } catch (Exception e) {
            logger.error("Error creating notification for employee {} with type {}: {}",
                        targetEmployeeId, type, e.getMessage(), e);
            throw new RuntimeException("Lỗi khi tạo thông báo: " + e.getMessage(), e);
        }
    }

    /**
     * Overloaded method for creating notification without createdBy (system-generated)
     */
    public Notifications createNotification(Long targetEmployeeId, Integer type, String title,
                                          String content, Long targetCustomerId) {
        return createNotification(targetEmployeeId, type, title, content, targetCustomerId, null);
    }

    /**
     * Overloaded method for creating notification without targetCustomerId and createdBy
     */
    public Notifications createNotification(Long targetEmployeeId, Integer type, String title, String content) {
        return createNotification(targetEmployeeId, type, title, content, null, null);
    }

    /**
     * Get unread notifications for the current authenticated user
     * @param event The event containing user information
     * @return Event with list of unread notifications
     */
    public Event getUnreadNotifications(Event event) {
        logger.debug("Getting unread notifications for user: {}", event.userId);

        try {
            // Validate user authentication
            if (event.userId == null) {
                logger.warn("Attempted to get unread notifications without authenticated user");
                return event.createResponse(null, 401, "Người dùng chưa được xác thực");
            }

            // Get unread notifications for the current user
            List<Notifications> unreadNotifications = notificationRepository
                .findByTargetEmployeeIdAndIsReadFalseOrderByCreatedAtDesc(event.userId);

            // Convert to DTOs with details
            List<NotificationDto> notificationDtos = unreadNotifications.stream()
                .map(notification -> notificationMapper.toDtoWithDetails(notification))
                .collect(java.util.stream.Collectors.toList());

            logger.debug("Found {} unread notifications for user {}", notificationDtos.size(), event.userId);

            // Return as JSON string for AMQP transport
            String jsonResponse = ObjectMapperUtil.toJsonString(notificationDtos);
            return event.createResponse(jsonResponse, 200, "Success");

        } catch (Exception e) {
            logger.error("Error getting unread notifications for user {}: {}", event.userId, e.getMessage(), e);
            return event.createResponse(null, 500, "Lỗi khi lấy danh sách thông báo chưa đọc: " + e.getMessage());
        }
    }

    /**
     * Delete a single notification with authorization check
     * @param event The event containing notification ID and user information
     * @return Event with deletion result
     */
    public Event deleteNotification(Event event) {
        logger.debug("Deleting notification for user: {}", event.userId);

        try {
            // Validate user authentication
            if (event.userId == null) {
                logger.warn("Attempted to delete notification without authenticated user");
                return event.createResponse(null, 401, "Người dùng chưa được xác thực");
            }

            Long notificationId = (Long) event.payload;
            if (notificationId == null) {
                logger.warn("Attempted to delete notification without notification ID");
                return event.createResponse(null, 400, "ID thông báo không được để trống");
            }

            // Check if notification exists and belongs to the current user
            Notifications notification = notificationRepository.findByIdAndTargetEmployeeId(notificationId, event.userId);
            if (notification == null) {
                logger.warn("User {} attempted to delete notification {} that doesn't exist or doesn't belong to them",
                           event.userId, notificationId);
                return event.createResponse(null, 404, "Không tìm thấy thông báo hoặc bạn không có quyền xóa thông báo này");
            }

            // Perform deletion
            int deletedCount = notificationRepository.deleteByIdAndTargetEmployeeId(notificationId, event.userId);

            if (deletedCount > 0) {
                logger.info("Successfully deleted notification {} for user {}", notificationId, event.userId);

                // Create response DTO
                vn.agis.crm.base.jpa.dto.resp.NotificationDeleteResponse response =
                    new vn.agis.crm.base.jpa.dto.resp.NotificationDeleteResponse(notificationId);

                return event.createResponse(ObjectMapperUtil.toJsonString(response), 200, "Xóa thông báo thành công");
            } else {
                logger.warn("Failed to delete notification {} for user {}", notificationId, event.userId);
                return event.createResponse(null, 500, "Không thể xóa thông báo");
            }

        } catch (Exception e) {
            logger.error("Error deleting notification for user {}: {}", event.userId, e.getMessage(), e);
            return event.createResponse(null, 500, "Lỗi khi xóa thông báo: " + e.getMessage());
        }
    }

    /**
     * Delete multiple notifications with authorization check
     * @param event The event containing list of notification IDs and user information
     * @return Event with batch deletion result
     */
    public Event deleteNotificationsBatch(Event event) {
        logger.debug("Batch deleting notifications for user: {}", event.userId);

        try {
            // Validate user authentication
            if (event.userId == null) {
                logger.warn("Attempted to batch delete notifications without authenticated user");
                return event.createResponse(null, 401, "Người dùng chưa được xác thực");
            }

            // Parse request payload
            vn.agis.crm.base.jpa.dto.req.NotificationBatchDeleteRequest request =
                ObjectMapperUtil.objectMapper((String) event.payload, vn.agis.crm.base.jpa.dto.req.NotificationBatchDeleteRequest.class);

            if (request == null || !request.isValid()) {
                logger.warn("Invalid batch delete request from user {}", event.userId);
                return event.createResponse(null, 400, "Danh sách ID thông báo không hợp lệ");
            }

            List<Long> notificationIds = request.getIds();
            logger.debug("User {} requesting to delete {} notifications", event.userId, notificationIds.size());

            // Create response object
            vn.agis.crm.base.jpa.dto.resp.NotificationBatchDeleteResponse response =
                new vn.agis.crm.base.jpa.dto.resp.NotificationBatchDeleteResponse(notificationIds.size());

            // Find notifications that belong to the current user
            List<Notifications> ownedNotifications = notificationRepository
                .findByIdsAndTargetEmployeeId(notificationIds, event.userId);

            List<Long> ownedIds = ownedNotifications.stream()
                .map(Notifications::getId)
                .collect(java.util.stream.Collectors.toList());

            // Identify unauthorized notifications
            for (Long id : notificationIds) {
                if (!ownedIds.contains(id)) {
                    response.addFailure(id, "Không tìm thấy thông báo hoặc bạn không có quyền xóa", 404);
                }
            }

            // Delete owned notifications
            if (!ownedIds.isEmpty()) {
                int deletedCount = notificationRepository.deleteByIdsAndTargetEmployeeId(ownedIds, event.userId);

                if (deletedCount > 0) {
                    // Add successful deletions to response
                    for (Long id : ownedIds) {
                        response.addSuccess(id);
                    }
                    logger.info("Successfully deleted {} notifications for user {}", deletedCount, event.userId);
                } else {
                    // If no notifications were deleted, mark all as failed
                    for (Long id : ownedIds) {
                        response.addFailure(id, "Không thể xóa thông báo", 500);
                    }
                }
            }

            logger.debug("Batch deletion completed for user {}: {} successful, {} failed",
                        event.userId, response.getSuccessfullyDeleted(), response.getFailed());

            // Return response as JSON string
            String jsonResponse = ObjectMapperUtil.toJsonString(response);
            return event.createResponse(jsonResponse, response.getStatusCode(), response.getMessage());

        } catch (Exception e) {
            logger.error("Error in batch deleting notifications for user {}: {}", event.userId, e.getMessage(), e);
            return event.createResponse(null, 500, "Lỗi khi xóa thông báo hàng loạt: " + e.getMessage());
        }
    }
}
