package vn.agis.crm.base.jpa.entity;
import java.util.Date;
import jakarta.persistence.*;
import lombok.Data;
@Data
@Entity
@Table(name = "RECEIVING_GROUP")
public class ReceivingGroup extends AbstractEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "NAME")
    private String name;

    @Column(name = "EMAILS")
    private String emails;

    @Column(name = "MSISDNS")
    private String msisdns;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;
}
