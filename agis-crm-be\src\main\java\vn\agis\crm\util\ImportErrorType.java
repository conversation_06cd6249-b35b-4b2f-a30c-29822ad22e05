package vn.agis.crm.util;

/**
 * Error types for import validation as defined in technical specification
 */
public enum ImportErrorType {
    // Required field errors
    MISSING_REQUIRED_FIELD("MISSING_REQUIRED_FIELD", "A mandatory field is missing"),
    
    // Format validation errors
    INVALID_FORMAT("INVALID_FORMAT", "The data is in an incorrect format"),
    INVALID_PHONE_FORMAT("INVALID_PHONE_FORMAT", "Phone number is not in a valid format"),
    INVALID_EMAIL_FORMAT("INVALID_EMAIL_FORMAT", "Email address is not in a valid format"),
    INVALID_DATE_FORMAT("INVALID_DATE_FORMAT", "Date is not in a recognized format"),
    INVALID_NUMBER_FORMAT("INVALID_NUMBER_FORMAT", "Number is not in a valid format"),
    
    // Duplicate handling
    DUPLICATE_IN_SYSTEM("DUPLICATE_IN_SYSTEM", "The phone number already exists in the database"),
    DUPLICATE_IN_FILE("DUPLICATE_IN_FILE", "The same phone number appears multiple times in the file"),
    
    // Foreign key validation
    FK_NOT_FOUND("FK_NOT_FOUND", "A referenced entity was not found"),
    PROJECT_NOT_FOUND("PROJECT_NOT_FOUND", "The specified project was not found"),
    UNIT_NOT_FOUND("UNIT_NOT_FOUND", "The specified unit was not found"),
    EMPLOYEE_NOT_FOUND("EMPLOYEE_NOT_FOUND", "The specified employee was not found"),
    
    // Data integrity
    DATA_INCONSISTENCY("DATA_INCONSISTENCY", "Data inconsistency detected"),
    INVALID_RELATIONSHIP("INVALID_RELATIONSHIP", "Invalid relationship between data fields"),
    
    // File structure
    INVALID_FILE_STRUCTURE("INVALID_FILE_STRUCTURE", "File structure is invalid"),
    MISSING_REQUIRED_COLUMNS("MISSING_REQUIRED_COLUMNS", "Required columns are missing from the file"),
    EMPTY_ROW("EMPTY_ROW", "Row is empty or contains no valid data"),
    
    // Business logic
    BUSINESS_RULE_VIOLATION("BUSINESS_RULE_VIOLATION", "Business rule validation failed"),
    
    // System errors
    DB_TRANSACTION_ERROR("DB_TRANSACTION_ERROR", "A database error occurred during processing"),
    PROCESSING_ERROR("PROCESSING_ERROR", "An error occurred during row processing");
    
    private final String code;
    private final String description;
    
    ImportErrorType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
