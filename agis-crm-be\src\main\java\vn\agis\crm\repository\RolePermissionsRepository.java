package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.RolePermissions;

import java.util.List;

@Repository
public interface RolePermissionsRepository extends JpaRepository<RolePermissions, RolePermissions.RolePermissionId> {

    List<RolePermissions> findByRoleId(Long roleId);

    void deleteByRoleId(Long roleId);
}

