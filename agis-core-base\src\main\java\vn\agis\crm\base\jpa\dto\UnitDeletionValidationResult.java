package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * DTO for unit deletion validation result
 * Contains validation status and detailed error information if deletion is not allowed
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitDeletionValidationResult {
    
    /**
     * Whether the unit can be safely deleted
     */
    private boolean canDelete;
    
    /**
     * User-friendly message in Vietnamese explaining the validation result
     */
    private String message;
    
    /**
     * Count of secondary interactions that prevent deletion
     */
    private Long secondaryInteractionsCount;
    
    /**
     * Create a successful validation result (unit can be deleted)
     */
    public static UnitDeletionValidationResult success() {
        return new UnitDeletionValidationResult(true, "Căn hộ có thể xóa an toàn.", 0L);
    }
    
    /**
     * Create a failed validation result with secondary interactions dependency
     */
    public static UnitDeletionValidationResult failedWithSecondaryInteractions(Long count) {
        String message = "Căn hộ đang được sử dụng, không thể xóa.";
        return new UnitDeletionValidationResult(false, message, count);
    }
    
    /**
     * Create a validation result for unit not found
     */
    public static UnitDeletionValidationResult unitNotFound() {
        return new UnitDeletionValidationResult(false, "Không tìm thấy căn hộ", 0L);
    }
}
