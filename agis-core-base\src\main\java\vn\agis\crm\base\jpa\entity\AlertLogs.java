package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;
@Table(name = "ALERT_LOGS")
@Data
@Entity
public class AlertLogs {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "MSISDN")
    private Long msisdn;
    @Column(name = "TYPE")
    private Integer type;
    @Column(name = "STATUS")
    private Integer status;
    @Column(name = "CREATED_DATE")
    private Date createdDate;
    @Column(name = "ALERT_DATA")
    private Long alertData;
    @Column(name = "ALERT_ID")
    private Long alertId;
}
