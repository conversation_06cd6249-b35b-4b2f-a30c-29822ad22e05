package vn.agis.crm.base.event.amqp;

import com.rabbitmq.client.Channel;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.amqp.support.converter.MessageConverter;

import java.util.HashMap;

public class CustomMessageListenerAdapter extends MessageListenerAdapter {

    private static final Logger log = LoggerFactory.getLogger(CustomMessageListenerAdapter.class);

    public CustomMessageListenerAdapter(Object delegate, MessageConverter messageConverter) {
        super(delegate, messageConverter);
    }


    protected Object[] buildListenerArguments(Object extractedMessage) {
        HashMap<String, Object> incomingMessage = (HashMap<String, Object>) extractedMessage;
        return new Object[]{incomingMessage.get("event"), incomingMessage.get("MessageProperties")};
    }

    public void onMessage(Message message, Channel channel) throws Exception {
        if (byte[].class.getName().equals(message.getMessageProperties().getHeaders().get("conversionClass"))) {
            log.info("Received message [] amqp properties {}", message.getMessageProperties().toString());
        } else {
            log.info("Received message ###{}### amqp properties {}", new String(message.getBody()), message.getMessageProperties().toString());
        }
        super.onMessage(message, channel);
    }
}
