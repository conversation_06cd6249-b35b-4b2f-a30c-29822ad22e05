package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

@Data
public class AssignmentUpsertDto {
    private Long id; // Đ<PERSON> cập nhật bản ghi hiện tại
    private Long employeeId;
    private Integer roleType; // 1=Manager, 2=Staff
    // assignedFrom và assignedTo sẽ được tự động xử lý trong backend
    // không cần gửi từ frontend
    private <PERSON>olean deleted;     // optional for future
}

