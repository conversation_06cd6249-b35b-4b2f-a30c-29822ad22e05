package vn.agis.crm.base.constants;

import java.util.HashMap;
import java.util.Map;

public enum CycleTypeEnum {
    UNSET(-1, ""),
    DAILY(0, "ngày"),
    WEEKLY(1, "tuần"),
    MONTH<PERSON><PERSON>(2, "tháng"),
    YEARLY(3, "năm"),
    QUARTER(4, "quý");

    public final int value;
    public final String name;
    private static Map<Integer, CycleTypeEnum> map = new HashMap();

    public static CycleTypeEnum valueOf(int value) {
        return (CycleTypeEnum)map.get(value);
    }

    private CycleTypeEnum(final int value, final String name) {
        this.value = value;
        this.name = name;
    }

    static {
        CycleTypeEnum[] var0 = values();
        int var1 = var0.length;

        for(int var2 = 0; var2 < var1; ++var2) {
            CycleTypeEnum cycleTypeEnum = var0[var2];
            map.put(cycleTypeEnum.value, cycleTypeEnum);
        }

    }
}
