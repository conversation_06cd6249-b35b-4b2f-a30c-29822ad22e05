package vn.agis.crm.base.jpa.entity;

import java.io.IOException;
import java.util.Date;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.*;
import lombok.Data;

@Table(name = "ALERT")
@Data
@Entity
public class Alert extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "ALERT_NAME")
    private String alertName;

    @Column(name = "SEVERITY")
    private Integer severity;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "EMAIL_SUBJECT")
    private String emailSubject;

    @Column(name = "EMAIL_CONTENT")
    private String emailContent;

    @Column(name = "EMAILS_NOTIFY")
    private String emailsNotify;

    @Column(name = "SMS_CONTENT")
    private String smsContent;

    @Column(name = "MSISDNS_NOTIFY")
    private String msisdnsNotify;

    @Column(name = "ZALO_CONTENT")
    private String zaloContent;

    @Column(name = "ZALO_NOTIFY")
    private String zaloNotify;

    @Column(name = "EVENT_TYPE")
    private Integer eventType;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "NOTIFICATION_URL")
    private String notificationUrl;

    @Column(name = "UPDATED_DATE")
    private Date updatedDate;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;

    @Column(name = "RULE")
    private String rule;

    @Column(name = "ACTION_TYPE")
    private Integer actionType;

    @Column(name = "DEVICE_TYPE_ID")
    private Long deviceTypeId;

    @Column(name = "USER_ENTERPRISE_ID")
    private Long userEnterpriseId;

    @Column(name = "USER_CUSTOMER_ID")
    private Long userCustomerId;

    @Column(name = "DEVICE_ID")
    private Long deviceId;

    @Column(name = "SENDING_METHOD")
    private String sendingMethod;

    @Override
    public String toString() {
        ObjectMapper jsonMapper = new ObjectMapper();
        try {
            return jsonMapper.writeValueAsString(this);
        } catch (IOException ex) {
            throw new RuntimeException(ex);
        }
    }
}
