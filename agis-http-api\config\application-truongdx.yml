server:
  port: 8081
  servlet:
    context-path: /api
spring:
  rabbitmq:
    addresses: server_test
    port: 5672
    username: agis_crm
    password: agis_crm
    virtual-host: agis_crm
  redis:
    mode: Standalone  # Change mode to Standalone
    standalone:
      host: server_test   # 🔹 service name trong docker-compose (or the actual host if not running in Docker)
      port: 6379
      username:  # Add username if your Redis instance requires authentication
      password:  <PERSON><PERSON><PERSON><PERSON><PERSON>@123456
    cluster:
      nodes:  # Remove or comment out the cluster configuration
      username:
      password:
    sentinel:
      password: # Remove or comment out the sentinel configuration
      master: mymaster
      nodes: localhost:26379
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
management:
  endpoints:
    web:
      exposure:
        include: health
google:
  api-key: AIzaSyB7FzJScOWtqr7IjUgaMYhSVDnyG1urh58
app:
  redis-limiter:
    redis-pool-max-total: 200       # max total connection              default：200
    redis-key-prefix: RL           # key prefix for visit footprint    default: #RL
    check-action-timeout: 100       # check action execution timeout    default: 100
    enable-dynamical-conf: true     # the switch for enable dynamical   default：false
    channel: #channel
  application:
    name: httpapi
