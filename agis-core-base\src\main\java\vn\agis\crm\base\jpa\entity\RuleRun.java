package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;

@Entity
@Table(name = "rule_runs")
@Data
public class RuleRun extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "job_id")
    private Long jobId;

    @Column(name = "rule_id", nullable = false)
    private Long ruleId;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "employee_id", nullable = false)
    private Long employeeId;

    @Column(name = "assigned_role", nullable = false)
    private Integer assignedRole; // 1 = Manager, 2 = Staff

    @Column(name = "executed_at", nullable = false)
    private Date executedAt;

    @Column(name = "created_by")
    private Long createdBy;
}

