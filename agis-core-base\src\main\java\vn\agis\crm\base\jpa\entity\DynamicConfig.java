package vn.agis.crm.base.jpa.entity;


import jakarta.persistence.*;
import lombok.Data;

@Table(name = "DYNAMIC_CONFIG")
@Entity
@Data
public class DynamicConfig extends AbstractEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    private String name;
    private String type;
    private String subType;
    private String query;
    private String typeQuery;
    private String datasetConfig;
    private String optionConfig;
    private String filterParams;
    private String schema;
    private String description;
}
