# Customer Birthday Notification Scheduler

## Overview

A comprehensive scheduled job system for automatically sending customer birthday notifications to employees in the AGIS CRM system. The scheduler runs daily at 8:00 AM (Vietnam timezone) and creates notifications based on configurable advance notice periods.

## ✅ **Implementation Details**

### **Scheduler Configuration**
```java
@Component
@Scheduled(cron = "0 0 8 * * *", zone = "Asia/Ho_Chi_Minh")
public class CustomerBirthdayNotificationScheduler {
    // Daily execution at 8:00 AM Vietnam time
}
```

### **System Configuration**
- **Config Key**: `"NOTIFICATION_BIRTHDAY_DAYS_BEFORE"`
- **Config Value**: Number of days before birthday (e.g., "3" for 3 days advance notice)
- **Config Type**: 1 (single value)
- **Validation**: Must be integer between 0-365

### **Employee Priority System**
The scheduler determines notification recipients using a 3-tier priority system:

1. **Priority 1**: Current Staff (`current_staff_id`) - **Single notification** to assigned staff only
2. **Priority 2**: Current Manager (`current_manager_id`) - **Single notification** to assigned manager only
3. **Priority 3**: Admin Employees (roleId = 1, fallback) - **Multiple notifications** to ALL active admin employees

## ✅ **Core Functionality**

### **1. Configuration Validation**
```java
private Integer getDaysBeforeBirthdayFromConfig() {
    Config config = configRepository.findOneByConfigKeyIgnoreCase("NOTIFICATION_BIRTHDAY_DAYS_BEFORE");
    // Validates existence, format, and range (0-365 days)
}
```

### **2. Customer Birthday Detection**
```java
private List<Customers> findCustomersWithUpcomingBirthdays(int daysBeforeBirthday) {
    LocalDate targetDate = LocalDate.now(ZoneId.of("Asia/Ho_Chi_Minh")).plusDays(daysBeforeBirthday);
    // Uses existing CustomerRepository.search() with birthdayDay/birthdayMonth parameters
}
```

### **3. Smart Employee Assignment**
```java
private List<Long> determineTargetEmployees(Customers customer) {
    // Priority 1: Active current staff (single employee)
    // Priority 2: Active current manager (single employee)
    // Priority 3: ALL active admin employees (multiple employees)
    // Returns empty list if no suitable employees found
}
```

### **4. Multiple Notification Creation**
```java
// Create notifications for all target employees (1 for Staff/Manager, multiple for Admins)
for (Long targetEmployeeId : targetEmployeeIds) {
    Notifications notification = notificationService.createNotification(
        targetEmployeeId,
        2, // CustomerBirthday type
        "Nhắc nhở sinh nhật khách hàng",
        content,
        customer.getId(),
        null // System-generated
    );
}
```

## ✅ **Notification Content Templates**

### **Same Day Birthday (daysBeforeBirthday = 0)**
```
"Khách hàng [Tên] có sinh nhật hôm nay ([dd/MM]). Hãy gửi lời chúc mừng để duy trì mối quan hệ tốt."
```

### **Advance Notice (daysBeforeBirthday > 0)**
```
"Khách hàng [Tên] sẽ có sinh nhật vào ngày [dd/MM] (sau [x] ngày). Hãy chuẩn bị lời chúc mừng để duy trì mối quan hệ tốt."
```

### **Example Content**
```
"Khách hàng Nguyễn Văn A sẽ có sinh nhật vào ngày 25/12 (sau 3 ngày). Hãy chuẩn bị lời chúc mừng để duy trì mối quan hệ tốt."
```

## ✅ **Enhanced Admin Notification Logic**

### **Multiple Admin Notifications**
When a customer has no assigned Staff or Manager, the system now sends birthday notifications to **ALL** active admin employees instead of just one:

```java
// Priority 3: All Admin employees (fallback)
List<Employee> adminEmployees = findAdminEmployees();
if (!adminEmployees.isEmpty()) {
    for (Employee admin : adminEmployees) {
        targetEmployeeIds.add(admin.getId());
    }
    logger.debug("Using {} admin employees as fallback for customer {}: {}",
               adminEmployees.size(), customer.getId(), targetEmployeeIds);
    return targetEmployeeIds; // Return all admin employee IDs
}
```

### **Benefits of Multiple Admin Notifications**
1. **Improved Coverage**: Ensures no unassigned customer birthdays are missed
2. **Load Distribution**: Multiple admins can handle customer assignment decisions
3. **Redundancy**: If one admin is unavailable, others can still respond
4. **Team Awareness**: All admins are informed about unassigned customers

### **Individual Error Handling**
Each admin notification is processed independently:
```java
int successCount = 0;
int errorCount = 0;

for (Long targetEmployeeId : targetEmployeeIds) {
    try {
        // Create notification for this admin
        successCount++;
    } catch (Exception e) {
        errorCount++;
        // Continue with other admins
    }
}

// Return true if at least one notification was created successfully
return successCount > 0;
```

## ✅ **Error Handling & Reliability**

### **Transaction Management**
```java
@Transactional(propagation = Propagation.REQUIRES_NEW)
public boolean processCustomerBirthdayNotification(Customers customer, int daysBeforeBirthday) {
    // Each notification processed in separate transaction
    // Prevents rollback of entire batch on individual failures
}
```

### **Comprehensive Error Handling**
- **Configuration Errors**: Invalid/missing config gracefully handled
- **Customer Processing Errors**: Individual failures don't stop batch processing
- **Employee Assignment Errors**: Fallback to admin employees
- **Notification Creation Errors**: Logged but don't crash job

### **Logging Strategy**
```java
// Job-level logging
logger.info("🎂 Starting customer birthday notification job");
logger.info("Found {} customers with upcoming birthdays", count);
logger.info("🎂 Job completed. Processed: {}, Success: {}, Errors: {}", total, success, errors);

// Individual processing logging
logger.debug("Created birthday notification {} for employee {} about customer {}", ...);
logger.warn("No target employee found for customer {} ({})", ...);
logger.error("Failed to process birthday notification for customer {}: {}", ...);
```

## ✅ **Performance Optimizations**

### **Efficient Database Queries**
- **Single Configuration Query**: One-time config lookup per job run
- **Optimized Customer Search**: Leverages existing indexed search with day/month filters
- **Batch Employee Lookup**: Efficient employee validation queries
- **Minimal N+1 Queries**: Careful query design to avoid performance issues

### **Memory Management**
- **Streaming Processing**: Processes customers one by one, not loading all into memory
- **Transaction Boundaries**: Separate transactions prevent memory buildup
- **Garbage Collection Friendly**: Minimal object retention between iterations

## ✅ **Configuration Examples**

### **Enable 3-Day Advance Notice**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_BIRTHDAY_DAYS_BEFORE', '3', 1, 'Số ngày trước sinh nhật để gửi thông báo nhắc nhở', NOW(), 1);
```

### **Same-Day Notifications Only**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_BIRTHDAY_DAYS_BEFORE', '0', 1, 'Gửi thông báo sinh nhật trong ngày', NOW(), 1);
```

### **Disable Birthday Notifications**
```sql
-- Option 1: Delete the config
DELETE FROM configs WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- Option 2: Set empty value
UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- Option 3: Set invalid value
UPDATE configs SET config_value = 'DISABLED' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';
```

## ✅ **Integration Features**

### **Spring Boot Integration**
```java
@SpringBootApplication
@EnableScheduling // Added to enable scheduling
public class CrmCoreMgmtApplication {
    // Scheduler automatically discovered and registered
}
```

### **Timezone Support**
- **Vietnam Timezone**: All date calculations use `Asia/Ho_Chi_Minh`
- **Consistent Timing**: Job runs at 8:00 AM local time regardless of server timezone
- **Date Formatting**: Birthday dates formatted according to Vietnamese conventions (dd/MM)

### **Existing Service Integration**
- **NotificationService**: Uses existing `createNotification()` method
- **ConfigRepository**: Leverages existing configuration system
- **CustomerRepository**: Uses existing search functionality with birthday filters
- **EmployeeRepository**: Uses existing employee lookup methods

## ✅ **Monitoring & Observability**

### **Job Execution Metrics**
```java
// Logged metrics per job run:
- Total customers processed
- Successful notifications created
- Error count
- Job start/completion times
- Configuration values used
```

### **Health Indicators**
```java
// Warning conditions logged:
- No customers found with upcoming birthdays
- Configuration disabled/missing
- No suitable employees found for customers
- Individual notification creation failures
```

### **Debug Information**
```java
// Debug-level logging includes:
- Target date calculations
- Employee priority selection logic
- Individual customer processing details
- Notification content generation
```

## ✅ **Production Considerations**

### **Scalability**
- **Batch Size**: Currently processes all customers in single job run
- **Future Enhancement**: Consider pagination for large customer bases
- **Database Load**: Optimized queries minimize database impact
- **Memory Usage**: Streaming approach prevents memory issues

### **Reliability**
- **Idempotent**: Safe to run multiple times (notifications have unique constraints)
- **Fault Tolerant**: Individual failures don't affect other customers
- **Recoverable**: Failed customers can be reprocessed manually if needed

### **Maintenance**
- **Configuration Changes**: No code changes needed to adjust notification timing
- **Employee Changes**: Automatically adapts to staff/manager assignment changes
- **Customer Changes**: Automatically includes new customers with birthdays

## ✅ **Testing Recommendations**

### **Unit Tests**
1. Configuration parsing and validation
2. Date calculation logic (timezone handling)
3. Employee priority selection logic
4. Notification content generation
5. Error handling scenarios

### **Integration Tests**
1. End-to-end job execution with test data
2. Database transaction behavior
3. NotificationService integration
4. Configuration change effects

### **Manual Testing**
1. Test with various configuration values (0, 1, 3, 7, 30 days)
2. Test with customers having different employee assignments
3. Test with missing/inactive employees
4. Test timezone behavior across different server locations

This implementation provides a robust, configurable, and maintainable solution for automatic customer birthday notifications while following AGIS CRM architectural patterns and coding standards.
