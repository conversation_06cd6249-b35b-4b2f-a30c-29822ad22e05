package vn.agis.crm.base.jpa.dto.req;


import java.util.Objects;
import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

@Getter
@Setter
public class SearchUserRequest {
    private Long managerId;
    private Long customerId;
    private String username;
    private Integer type;
    private String email;
    private Integer status;
    private Integer page;
    private Integer size;
    private String sortBy;
    private boolean loggable;
    private String name;
    private String phone;
    private String nameOrUsername;
    private Integer provinceCodeAddress;
    private Integer wardCodeAddress;
    private Integer apartment;
    private Integer deviceTypeId;
    private Integer forDevice; // flag phục vụ dropdown màn device
    private Integer editingUserIdForDevice;

    public SearchUserRequest(String username, String name, Integer type, String email, Integer page, Integer size,
        String sortBy) {
        this.username =  Objects.isNull(username) ? " " : SqlUtils.optimizeSearchLike(username);
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.type = type;
        this.email = Objects.isNull(email) ? " " : SqlUtils.optimizeSearchLike(email);
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
    public SearchUserRequest(String username, Long managerId, Integer page, Integer size,
                             String sortBy) {
        this.username =  Objects.isNull(username) ? " " : SqlUtils.optimizeSearchLike(username);
        this.managerId = managerId;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
    public SearchUserRequest(String username, String name, Integer type, String email, Integer status, String phone, Integer page, Integer size,
                             String sortBy) {
        this.username =  Objects.isNull(username) ? " " : SqlUtils.optimizeSearchLike(username);
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.type = type;
        this.email = Objects.isNull(email) ? " " : SqlUtils.optimizeSearchLike(email);
        this.status = status;
        this.phone = Objects.isNull(phone) ? " " : SqlUtils.optimizeSearchLike(phone);
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
    public SearchUserRequest( String email, Integer page, Integer size, String sortBy) {
        this.email = email;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }

    public SearchUserRequest(String username, String name, Integer type, String email, Integer status,Integer page, Integer size,
                             String sortBy, String phone, String nameOrUsername, Long managerId, Long customerId, Integer provinceCodeAddress, Integer wardCodeAddress, Integer apartment, Integer deviceTypeId, Integer forDevice, Integer editingUserIdForDevice) {
        this.username =  Objects.isNull(username) ? " " : SqlUtils.optimizeSearchLike(username);
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.type = type;
        this.email = Objects.isNull(email) ? " " : SqlUtils.optimizeSearchLike(email);
        this.status = status;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
        this.phone = Objects.isNull(phone) ? " " : SqlUtils.optimizeSearchLike(phone);
        this.nameOrUsername = Objects.isNull(nameOrUsername) ? " " : SqlUtils.optimizeSearchLike(nameOrUsername);
        this.managerId = managerId;
        this.customerId = customerId;
        this.provinceCodeAddress = provinceCodeAddress;
        this.wardCodeAddress = wardCodeAddress;
        this.deviceTypeId = deviceTypeId;
        this.forDevice = Objects.isNull(forDevice) ? 0 : forDevice;
        this.editingUserIdForDevice = Objects.isNull(editingUserIdForDevice) ? -1 : editingUserIdForDevice;
    }
}
