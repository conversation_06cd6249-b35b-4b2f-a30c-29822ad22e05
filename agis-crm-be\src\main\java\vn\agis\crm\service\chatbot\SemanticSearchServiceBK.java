package vn.agis.crm.service.chatbot;

import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore; // <--- sửa ở đây
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

//@Service
public class SemanticSearchServiceBK {

    private final OpenAiEmbeddingModel embeddingModel;
    private final InMemoryEmbeddingStore<String> embeddingStore;

    public SemanticSearchServiceBK(String apiKey) {
        this.embeddingModel = OpenAiEmbeddingModel.builder()
                .apiKey(apiKey)
                .modelName("text-embedding-3-small")
                .build();
        this.embeddingStore = new InMemoryEmbeddingStore<>();
    }

    // Thêm template
    public void addTemplate(String question, String sqlTemplate) {
        Embedding embedding = embeddingModel.embed(question).content();
        // add trả về id (nếu bạn cần)
        embeddingStore.add(embedding, sqlTemplate);
    }

    public String findClosestTemplate(String question) {
        Embedding embedding = embeddingModel.embed(question).content();
        var matches = embeddingStore.findRelevant(embedding, 1);
        if (matches.isEmpty()) {
            return null;
        }
        return matches.get(0).embedded();  // <- bây giờ sẽ là String
    }
}
