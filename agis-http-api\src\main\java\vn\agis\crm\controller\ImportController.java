package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.agis.crm.base.jpa.dto.*;
import vn.agis.crm.base.utils.SecurityUtils;
import vn.agis.crm.service.ImportJobService;
import vn.agis.crm.service.ImportTemplateService;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/imports")
@Tag(name = "Customer Import", description = "Customer data import management APIs")
public class ImportController {

    private static final Logger logger = LoggerFactory.getLogger(ImportController.class);

    @Autowired
    private ImportJobService importJobService;

    @Autowired
    private ImportTemplateService importTemplateService;

    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "Create import job from file upload",
               description = "Upload a file and create an import job for customer data. Supports both direct file uploads (WEB_UPLOAD) and files downloaded from Google Drive (GOOGLE_DRIVE).")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Import job created successfully",
                    content = @Content(schema = @Schema(implementation = ImportJobDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid file or request parameters"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - Admin role required"),
        @ApiResponse(responseCode = "500", description = "Internal server error")
    })
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> createImportJob(
            @Parameter(description = "File to upload (CSV, XLSX, XLS)", required = true)
            @RequestParam("file") MultipartFile file,

            @Parameter(description = "Source type (WEB_UPLOAD or GOOGLE_DRIVE)", required = true)
            @RequestParam("source") String source,

            @Parameter(description = "Import mode (must be DRY_RUN for step 1)")
            @RequestParam(value = "mode", defaultValue = "DRY_RUN") String mode,

            @Parameter(description = "Additional options as JSON string")
            @RequestParam(value = "options", required = false) String options) {

        try {
            // Validate source parameter
            if (!"WEB_UPLOAD".equals(source) && !"GOOGLE_DRIVE".equals(source)) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "INVALID_SOURCE",
                               "message", "Source must be either WEB_UPLOAD or GOOGLE_DRIVE"));
            }

            // Force DRY_RUN mode for step 1
            if (!"DRY_RUN".equals(mode)) {
                return ResponseEntity.badRequest()
                    .body(Map.of("error", "INVALID_MODE", 
                               "message", "Only DRY_RUN mode is allowed in step 1"));
            }

            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportJobDto result = importJobService.createFromWebUpload(file, options, userId);
            return ResponseEntity.status(HttpStatus.CREATED).body(result);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request parameters", e);
            String errorType = determineErrorType(e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", errorType, "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error creating import job from upload", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }



    @GetMapping("/{id}")
    @Operation(summary = "Get import job details", description = "Retrieve import job information by ID")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getImportJob(@PathVariable Long id) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportJobDto job = importJobService.getById(id, userId);

            return ResponseEntity.ok(job);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", "JOB_NOT_FOUND", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving import job", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    @GetMapping("/{id}/sheets")
    @Operation(summary = "Get Excel sheet list", description = "List available sheets in Excel file")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getExcelSheets(@PathVariable Long id) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ExcelSheetDto sheets = importJobService.getExcelSheets(id, userId);
            return ResponseEntity.ok(sheets);

        } catch (IllegalArgumentException e) {
            String errorType = e.getMessage().contains("not found") ? "JOB_NOT_FOUND" : 
                              e.getMessage().contains("not an Excel") ? "NOT_EXCEL_FILE" : "INVALID_REQUEST";
            return ResponseEntity.badRequest()
                .body(Map.of("error", errorType, "message", e.getMessage()));
        } catch (UnsupportedOperationException e) {
            return ResponseEntity.status(HttpStatus.NOT_IMPLEMENTED)
                .body(Map.of("error", "NOT_IMPLEMENTED", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving Excel sheets", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    @GetMapping("/{id}/file-metadata")
    @Operation(summary = "Get file metadata", description = "Retrieve file metadata for import job")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getFileMetadata(@PathVariable Long id) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            FileMetadataDto metadata = importJobService.getFileMetadata(id, userId);
            return ResponseEntity.ok(metadata);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", "JOB_NOT_FOUND", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving file metadata", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    @GetMapping("/supported-formats")
    @Operation(summary = "Get supported formats", description = "Get information about supported file formats and limits")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<SupportedFormatsDto> getSupportedFormats() {
        try {
            SupportedFormatsDto formats = importJobService.getSupportedFormats();
            return ResponseEntity.ok(formats);
        } catch (Exception e) {
            logger.error("Error retrieving supported formats", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping("/template")
    @Operation(summary = "Download import template", description = "Download the CSV template file for customer import")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<Resource> downloadTemplate() {
        try {
            Resource template = importTemplateService.getTemplate();
            String filename = importTemplateService.getTemplateFilename();
            String contentType = importTemplateService.getTemplateContentType();

            return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + filename + "\"")
                .body(template);

        } catch (Exception e) {
            logger.error("Error downloading template", e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    @PostMapping("/{id}/dry-run")
    @Operation(summary = "Start dry-run validation",
               description = "Start dry-run validation for an uploaded import job")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "202", description = "Dry-run started successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid job ID or job not in correct state"),
        @ApiResponse(responseCode = "404", description = "Import job not found"),
        @ApiResponse(responseCode = "401", description = "Unauthorized"),
        @ApiResponse(responseCode = "403", description = "Forbidden - Admin role required")
    })
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> startDryRun(@PathVariable Long id) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportJobDto result = importJobService.startDryRun(id, userId);
            return ResponseEntity.status(HttpStatus.ACCEPTED).body(result);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid dry-run request", e);
            return ResponseEntity.badRequest()
                .body(Map.of("error", "INVALID_REQUEST", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error starting dry-run", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    @GetMapping("/{id}/dry-run-result")
    @Operation(summary = "Get dry-run validation results",
               description = "Get detailed results of dry-run validation including errors and statistics")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getDryRunResult(@PathVariable Long id) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            DryRunResultDto result = importJobService.getDryRunResult(id, userId);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", "JOB_NOT_FOUND", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving dry-run result", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    @GetMapping("/{id}/errors")
    @Operation(summary = "Get import errors",
               description = "Get paginated list of validation errors for an import job")
//    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> getImportErrors(
            @PathVariable Long id,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "50") int size,
            @RequestParam(required = false) String severity,
            @RequestParam(required = false) String errorType) {
        try {
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            // Create filter parameters
            Map<String, Object> filters = new HashMap<>();
            if (severity != null) filters.put("severity", severity);
            if (errorType != null) filters.put("errorType", errorType);

            Object result = importJobService.getImportErrors(id, userId, page, size, filters);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                .body(Map.of("error", "JOB_NOT_FOUND", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving import errors", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "Internal server error"));
        }
    }

    /**
     * Determine error type based on exception message
     */
    private String determineErrorType(String message) {
        if (message.contains("Invalid file type")) {
            return "INVALID_FILE_TYPE";
        } else if (message.contains("File size exceeds")) {
            return "FILE_TOO_LARGE";
        } else if (message.contains("File is empty")) {
            return "EMPTY_FILE";
        } else {
            return "INVALID_REQUEST";
        }
    }

    // ================================
    // STEP 3: CONFIRMATION & EXECUTION
    // ================================

    @PostMapping("/{id}/confirm")
//    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Confirm and start import execution",
               description = "Confirms the dry-run results and starts the actual import execution")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "202", description = "Import execution started successfully",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ImportProgressDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request or job not ready for execution"),
        @ApiResponse(responseCode = "404", description = "Import job not found"),
        @ApiResponse(responseCode = "409", description = "Job is not in the correct state for execution")
    })
    public ResponseEntity<?> confirmImport(
            @Parameter(description = "Import job ID", required = true) @PathVariable Long id,
            @Parameter(description = "Import options (JSON string)") @RequestParam(required = false) String options) {

        try {
            // Get current user ID
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportProgressDto result = importJobService.confirmImport(id, options, userId);
            return ResponseEntity.status(HttpStatus.ACCEPTED).body(result);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for confirm import: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", "INVALID_REQUEST", "message", e.getMessage()));
        } catch (IllegalStateException e) {
            logger.warn("Invalid state for confirm import: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(Map.of("error", "INVALID_STATE", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error confirming import", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "An unexpected error occurred"));
        }
    }

    @GetMapping("/{id}/progress")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get import progress",
               description = "Retrieves real-time progress information for a running import job")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Progress retrieved successfully",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ImportProgressDto.class))),
        @ApiResponse(responseCode = "404", description = "Import job not found")
    })
    public ResponseEntity<?> getImportProgress(
            @Parameter(description = "Import job ID", required = true) @PathVariable Long id) {

        try {
            // Get current user ID
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportProgressDto progress = importJobService.getImportProgress(id, userId);
            return ResponseEntity.ok(progress);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for import progress: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", "INVALID_REQUEST", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving import progress", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "An unexpected error occurred"));
        }
    }

    @PostMapping("/{id}/cancel")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Cancel running import",
               description = "Requests cancellation of a running import job")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Cancellation requested successfully",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ImportProgressDto.class))),
        @ApiResponse(responseCode = "400", description = "Invalid request or job cannot be cancelled"),
        @ApiResponse(responseCode = "404", description = "Import job not found"),
        @ApiResponse(responseCode = "409", description = "Job is not in a cancellable state")
    })
    public ResponseEntity<?> cancelImport(
            @Parameter(description = "Import job ID", required = true) @PathVariable Long id,
            @Parameter(description = "Cancellation reason") @RequestParam(required = false) String reason) {

        try {
            // Get current user ID
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportProgressDto result = importJobService.cancelImport(id, reason, userId);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for cancel import: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", "INVALID_REQUEST", "message", e.getMessage()));
        } catch (IllegalStateException e) {
            logger.warn("Invalid state for cancel import: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(Map.of("error", "INVALID_STATE", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error cancelling import", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "An unexpected error occurred"));
        }
    }

    @GetMapping("/{id}/result")
    @PreAuthorize("hasRole('ADMIN')")
    @Operation(summary = "Get import result",
               description = "Retrieves the final results and statistics of a completed import job")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Import result retrieved successfully",
                    content = @Content(mediaType = "application/json", schema = @Schema(implementation = ImportResultDto.class))),
        @ApiResponse(responseCode = "404", description = "Import job not found"),
        @ApiResponse(responseCode = "409", description = "Import job is not yet completed")
    })
    public ResponseEntity<?> getImportResult(
            @Parameter(description = "Import job ID", required = true) @PathVariable Long id) {

        try {
            // Get current user ID
            Long userId = SecurityUtils.getCurrentUserId();
            if (userId == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(Map.of("error", "UNAUTHORIZED", "message", "User not authenticated"));
            }

            ImportResultDto result = importJobService.getImportResult(id, userId);
            return ResponseEntity.ok(result);

        } catch (IllegalArgumentException e) {
            logger.warn("Invalid request for import result: {}", e.getMessage());
            return ResponseEntity.badRequest()
                .body(Map.of("error", "INVALID_REQUEST", "message", e.getMessage()));
        } catch (IllegalStateException e) {
            logger.warn("Invalid state for import result: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT)
                .body(Map.of("error", "INVALID_STATE", "message", e.getMessage()));
        } catch (Exception e) {
            logger.error("Error retrieving import result", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(Map.of("error", "INTERNAL_ERROR", "message", "An unexpected error occurred"));
        }
    }


}
