package vn.agis.crm.sync;

import java.nio.charset.StandardCharsets;
import org.springframework.web.util.ContentCachingResponseWrapper;
import org.springframework.web.util.WebUtils;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletResponse;

public abstract class AbsLoggableModule  {
    public String getResponsePayload(HttpServletResponse response) {
        ContentCachingResponseWrapper wrapper = (ContentCachingResponseWrapper) WebUtils.getNativeResponse((ServletResponse)response, ContentCachingResponseWrapper.class);
        if (wrapper != null) {
            byte[] buf = wrapper.getContentAsByteArray();
            if (buf.length > 0) {
                int length = Math.min(buf.length, 5120);
                return new String(buf, 0, length, StandardCharsets.UTF_8);
            }
        }
        return "[unknown]";
    }
}

