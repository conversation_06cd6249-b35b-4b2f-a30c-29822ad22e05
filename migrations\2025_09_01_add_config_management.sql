/*
  Migration: Add configuration management (5.1.8)
  Purpose: Config categories/options, notification settings, external integrations, mail server, and product type for units
  Date: 2025-09-01
  Target: MariaDB 10.11 (utf8mb4/utf8mb4_unicode_ci)
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 1) <PERSON>h mục cấu hình: Nhóm danh mục
CREATE TABLE IF NOT EXISTS `config_categories` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã danh mục (duy nhất)',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên danh mục hiển thị',
  `description` varchar(500) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_system` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1: danh mục hệ thống',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` datetime NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_cfg_cat_code` (`code`) USING BTREE,
  INDEX `fk_cfg_cat_created_by` (`created_by`) USING BTREE,
  INDEX `fk_cfg_cat_updated_by` (`updated_by`) USING BTREE,
  CONSTRAINT `fk_cfg_cat_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_cfg_cat_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;

-- 1) Danh mục cấu hình: Mục trong danh mục
CREATE TABLE IF NOT EXISTS `config_options` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` int(10) UNSIGNED NOT NULL,
  `code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã option trong phạm vi category',
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên hiển thị',
  `value` varchar(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Giá trị nội bộ nếu cần',
  `metadata` longtext COLLATE utf8mb4_bin NULL DEFAULT NULL CHECK (json_valid(`metadata`)),
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_cfg_opt_cat_code` (`category_id`, `code`) USING BTREE,
  INDEX `fk_cfg_opt_category` (`category_id`) USING BTREE,
  INDEX `fk_cfg_opt_created_by` (`created_by`) USING BTREE,
  INDEX `fk_cfg_opt_updated_by` (`updated_by`) USING BTREE,
  CONSTRAINT `fk_cfg_opt_category` FOREIGN KEY (`category_id`) REFERENCES `config_categories` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_cfg_opt_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_cfg_opt_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;

-- 2) Cấu hình thông báo theo từng loại
CREATE TABLE IF NOT EXISTS `notification_type_settings` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `notif_type` tinyint(4) NOT NULL COMMENT '1=LeadAssigned, 2=CustomerBirthday, 3=LeadUncaredWarning, 4=LeadInactiveWarning',
  `enable_web` tinyint(1) NOT NULL DEFAULT 1,
  `enable_email` tinyint(1) NOT NULL DEFAULT 0,
  `enable_sms_brandname` tinyint(1) NOT NULL DEFAULT 0,
  `enable_zns` tinyint(1) NOT NULL DEFAULT 0,
  `enable_instagram` tinyint(1) NOT NULL DEFAULT 0,
  `days_before_birthday` int(11) NULL DEFAULT NULL COMMENT 'Áp dụng cho type=2',
  `days_since_assigned_no_interaction` int(11) NULL DEFAULT NULL COMMENT 'Áp dụng cho type=3',
  `days_since_last_interaction` int(11) NULL DEFAULT NULL COMMENT 'Áp dụng cho type=4',
  `created_at` datetime NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_notif_type` (`notif_type`) USING BTREE,
  INDEX `fk_nts_created_by` (`created_by`) USING BTREE,
  INDEX `fk_nts_updated_by` (`updated_by`) USING BTREE,
  CONSTRAINT `fk_nts_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_nts_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;

-- 3) Cấu hình kết nối hệ thống ngoài (tùy biến qua JSON)
CREATE TABLE IF NOT EXISTS `integration_endpoints` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `system_code` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `display_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `config` longtext COLLATE utf8mb4_bin NULL DEFAULT NULL CHECK (json_valid(`config`)),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_integration_code` (`system_code`) USING BTREE,
  INDEX `fk_ie_created_by` (`created_by`) USING BTREE,
  INDEX `fk_ie_updated_by` (`updated_by`) USING BTREE,
  CONSTRAINT `fk_ie_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_ie_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;

-- 4) Cấu hình Mail Server
CREATE TABLE IF NOT EXISTS `mail_server_settings` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `provider` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SMTP',
  `host` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `port` int(11) NOT NULL DEFAULT 587,
  `username` varchar(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `password_cipher` varchar(512) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Mật khẩu đã mã hóa',
  `use_tls` tinyint(1) NOT NULL DEFAULT 1,
  `use_ssl` tinyint(1) NOT NULL DEFAULT 0,
  `from_name` varchar(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `from_email` varchar(255) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` datetime NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_ms_created_by` (`created_by`) USING BTREE,
  INDEX `fk_ms_updated_by` (`updated_by`) USING BTREE,
  CONSTRAINT `fk_ms_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_ms_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=Dynamic;

-- 5) Bổ sung loại sản phẩm cho Units
ALTER TABLE `units` 
  ADD COLUMN IF NOT EXISTS `product_type` varchar(100) COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Loại sản phẩm (tham chiếu danh mục PRODUCT_TYPES)' 
  AFTER `view`;

-- 6) Seed dữ liệu mẫu cho danh mục
INSERT INTO `config_categories` (`code`, `name`, `description`, `is_system`, `is_active`, `sort_order`)
VALUES
  ('SOURCE_DETAIL_DATA', 'Nguồn chi tiết - Data', 'Nguồn chi tiết áp dụng cho khách hàng nguồn Data', 1, 1, 10),
  ('SOURCE_DETAIL_LEADS', 'Nguồn chi tiết - Leads', 'Nguồn chi tiết áp dụng cho Leads', 1, 1, 11),
  ('RELATIONSHIP_TYPES', 'Mối quan hệ người thân', 'Danh sách loại quan hệ', 1, 1, 20),
  ('INTERACTION_RESULT_PRIMARY', 'KQ tương tác sơ cấp (mời bán)', 'Danh sách kết quả tương tác sơ cấp', 1, 1, 30),
  ('INTERACTION_RESULT_SECONDARY', 'KQ tương tác thứ cấp (chuyển nhượng)', 'Danh sách kết quả tương tác thứ cấp', 1, 1, 31),
  ('PRODUCT_TYPES', 'Loại sản phẩm', 'Danh sách loại sản phẩm BĐS', 1, 1, 40)
ON DUPLICATE KEY UPDATE `name`=VALUES(`name`), `description`=VALUES(`description`), `is_system`=VALUES(`is_system`), `is_active`=VALUES(`is_active`), `sort_order`=VALUES(`sort_order`);

-- Lấy id các category vào biến để insert option dễ hơn (MariaDB)
SET @cat_data := (SELECT id FROM config_categories WHERE code='SOURCE_DETAIL_DATA' LIMIT 1);
SET @cat_leads := (SELECT id FROM config_categories WHERE code='SOURCE_DETAIL_LEADS' LIMIT 1);
SET @cat_rel := (SELECT id FROM config_categories WHERE code='RELATIONSHIP_TYPES' LIMIT 1);
SET @cat_pri := (SELECT id FROM config_categories WHERE code='INTERACTION_RESULT_PRIMARY' LIMIT 1);
SET @cat_sec := (SELECT id FROM config_categories WHERE code='INTERACTION_RESULT_SECONDARY' LIMIT 1);
SET @cat_prod := (SELECT id FROM config_categories WHERE code='PRODUCT_TYPES' LIMIT 1);

-- Seed options: SOURCE_DETAIL_DATA
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_data, 'DATA_EVENT', 'Sự kiện/triển lãm', NULL, 1, 1),
  (@cat_data, 'DATA_PARTNER', 'Đối tác cung cấp', NULL, 2, 1),
  (@cat_data, 'DATA_WEBSITE', 'Website công ty', NULL, 3, 1),
  (@cat_data, 'DATA_REFERRAL', 'Giới thiệu nội bộ', NULL, 4, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- Seed options: SOURCE_DETAIL_LEADS
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_leads, 'LEADS_FB_ADS', 'Facebook Ads', NULL, 1, 1),
  (@cat_leads, 'LEADS_GOOGLE_ADS', 'Google Ads', NULL, 2, 1),
  (@cat_leads, 'LEADS_ZALO_ADS', 'Zalo Ads', NULL, 3, 1),
  (@cat_leads, 'LEADS_TIKTOK_ADS', 'TikTok Ads', NULL, 4, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- Seed options: RELATIONSHIP_TYPES
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_rel, 'SPOUSE', 'Vợ/Chồng', NULL, 1, 1),
  (@cat_rel, 'CHILD', 'Con', NULL, 2, 1),
  (@cat_rel, 'PARENT', 'Cha/Mẹ', NULL, 3, 1),
  (@cat_rel, 'SIBLING', 'Anh/Chị/Em', NULL, 4, 1),
  (@cat_rel, 'PARTNER', 'Đối tác', NULL, 5, 1),
  (@cat_rel, 'FRIEND', 'Bạn bè', NULL, 6, 1),
  (@cat_rel, 'OTHER', 'Khác', NULL, 7, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- Seed options: INTERACTION_RESULT_PRIMARY (mời bán)
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_pri, 'INVITE_INTERESTED', 'Quan tâm', NULL, 1, 1),
  (@cat_pri, 'INVITE_NOT_INTERESTED', 'Không quan tâm', NULL, 2, 1),
  (@cat_pri, 'INVITE_FOLLOW_UP', 'Cần theo dõi', NULL, 3, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- Seed options: INTERACTION_RESULT_SECONDARY (chuyển nhượng)
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_sec, 'TRANSFER_INTERESTED', 'Quan tâm', NULL, 1, 1),
  (@cat_sec, 'TRANSFER_NOT_INTERESTED', 'Không quan tâm', NULL, 2, 1),
  (@cat_sec, 'TRANSFER_FOLLOW_UP', 'Cần theo dõi', NULL, 3, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- Seed options: PRODUCT_TYPES
INSERT INTO `config_options` (`category_id`, `code`, `display_name`, `value`, `sort_order`, `is_active`)
VALUES
  (@cat_prod, 'APARTMENT', 'Căn hộ', NULL, 1, 1),
  (@cat_prod, 'SHOPHOUSE', 'Shophouse', NULL, 2, 1),
  (@cat_prod, 'VILLA', 'Biệt thự', NULL, 3, 1),
  (@cat_prod, 'OFFICETEL', 'Officetel', NULL, 4, 1),
  (@cat_prod, 'LAND', 'Đất nền', NULL, 5, 1)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `value`=VALUES(`value`), `sort_order`=VALUES(`sort_order`), `is_active`=VALUES(`is_active`);

-- 7) Seed cấu hình thông báo mặc định
INSERT INTO `notification_type_settings` (`notif_type`, `enable_web`, `enable_email`, `enable_sms_brandname`, `enable_zns`, `enable_instagram`, `days_before_birthday`, `days_since_assigned_no_interaction`, `days_since_last_interaction`)
VALUES
  (1, 1, 1, 0, 0, 0, NULL, NULL, NULL),  -- LeadAssigned
  (2, 1, 0, 0, 0, 0, 3, NULL, NULL),      -- Birthday: gửi trước 3 ngày
  (3, 1, 0, 0, 0, 0, NULL, 2, NULL),      -- Uncared: 2 ngày sau khi phân mà chưa tương tác
  (4, 1, 0, 0, 0, 0, NULL, NULL, 7)       -- Inactive: 7 ngày không có tương tác
ON DUPLICATE KEY UPDATE 
  `enable_web`=VALUES(`enable_web`),
  `enable_email`=VALUES(`enable_email`),
  `enable_sms_brandname`=VALUES(`enable_sms_brandname`),
  `enable_zns`=VALUES(`enable_zns`),
  `enable_instagram`=VALUES(`enable_instagram`),
  `days_before_birthday`=VALUES(`days_before_birthday`),
  `days_since_assigned_no_interaction`=VALUES(`days_since_assigned_no_interaction`),
  `days_since_last_interaction`=VALUES(`days_since_last_interaction`);

-- 8) Seed mẫu Integration endpoints (tắt mặc định)
INSERT INTO `integration_endpoints` (`system_code`, `display_name`, `config`, `is_active`)
VALUES
  ('ZALO_ZNS', 'Zalo ZNS', '{"endpoint":"https://api.zalo.me/","access_token":"<your-token>","oa_id":"<oa-id>"}', 0),
  ('SMS_BRAND', 'SMS Brandname', '{"provider":"viettel","api_key":"<api-key>","brand_name":"<brand>"}', 0),
  ('SENDGRID', 'SendGrid', '{"api_key":"<your-sendgrid-api-key>"}', 0)
ON DUPLICATE KEY UPDATE `display_name`=VALUES(`display_name`), `config`=VALUES(`config`), `is_active`=VALUES(`is_active`);

-- 9) Seed mẫu Mail server (tắt mặc định)
INSERT INTO `mail_server_settings` (`provider`, `host`, `port`, `username`, `password_cipher`, `use_tls`, `use_ssl`, `from_name`, `from_email`, `is_active`)
VALUES ('SMTP', 'smtp.example.com', 587, '<EMAIL>', '<encrypted-password>', 1, 0, 'CRM System', '<EMAIL>', 0);

SET FOREIGN_KEY_CHECKS = 1;

