package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants.RoutingKey;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.exception.type.NotFoundException;
import vn.agis.crm.base.jpa.dto.LoginInfo;
import vn.agis.crm.base.jpa.dto.SSOLoginDTO;
import vn.agis.crm.base.jpa.dto.resp.AuthResponseWithTokenAndErrorCodeDTO;
import vn.agis.crm.util.RequestUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * Created by huyvv
 * Date: 21/03/2020
 * Time: 8:39 AM
 * for all issues, contact me: <EMAIL>
 **/
@Service
public class AuthService {
    private static Logger logger = LoggerFactory.getLogger(AuthService.class);
    protected String routingKey = RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
    protected String category = Constants.Category.USER;
    protected String categoryEmployee = Constants.Category.EMPLOYEE;
    protected String categoryClientAuthentication = Constants.Category.CLIENT_AUTHENTICATION;
    ;


    public AuthResponseWithTokenAndErrorCodeDTO token(LoginInfo loginInfo) {
        logger.info("Request token for user #{}", loginInfo.getEmail());
        Event event = RequestUtils.amqp(Method.LOGIN, category, loginInfo, routingKey);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        if (event.payload != null) {
            response = (AuthResponseWithTokenAndErrorCodeDTO) event.payload;
//        response.setAccessToken((String) event.payload);
            response.setErrorCode(event.respErrorDesc);
        }
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new NotFoundException(event.respErrorDesc, category, event.respErrorDesc, event.respErrorDesc);
        }
        return response;
    }

    public AuthResponseWithTokenAndErrorCodeDTO login(LoginInfo loginInfo) {
        logger.info("Request token for user #{}", loginInfo.getEmail());
        Event event = RequestUtils.amqp(Method.LOGIN, categoryEmployee, loginInfo, routingKey);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        if (event.payload != null) {
            response = (AuthResponseWithTokenAndErrorCodeDTO) event.payload;
//        response.setAccessToken((String) event.payload);
            response.setErrorCode(event.respErrorDesc);
        }
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new NotFoundException(event.respErrorDesc, category, event.respErrorDesc, event.respErrorDesc);
        }
        return response;
    }

    public AuthResponseWithTokenAndErrorCodeDTO verifySsoToken(SSOLoginDTO loginInfo) {
        logger.info("Request token for ssoLoginDTO token: #{}, subscription: #{}", loginInfo.getBosToken(), loginInfo.getSubscription());
        Event event = RequestUtils.amqp(Method.SSO_LOGIN, category, loginInfo, routingKey);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        if (event.payload != null) {
            response = (AuthResponseWithTokenAndErrorCodeDTO) event.payload;
            response.setErrorCode(event.respErrorDesc);
        }
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new NotFoundException(event.respErrorDesc, category, event.respErrorDesc, event.respErrorDesc);
        }
        return response;
    }

    public String genAuthCode(Map<String, Object> params, String tokenAuth) {
        Map<String,Object> data = new HashMap<>();
        data.put("params", params);
        data.put("tokenObject", tokenAuth.substring(tokenAuth.indexOf(" ") + 1, tokenAuth.length()));
        Event event = RequestUtils.amqp(Method.GEN_AUTH_CODE, categoryClientAuthentication , data, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            String authCode = (String) event.payload;

            return authCode;
        }

        return null;
    }
}
