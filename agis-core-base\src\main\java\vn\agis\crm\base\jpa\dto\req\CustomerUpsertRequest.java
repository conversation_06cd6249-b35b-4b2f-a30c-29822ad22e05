package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class CustomerUpsertRequest {
    private Long id;
    private String fullName;
    private String phone;
    private String email;
    private String cccd;

    // Multiple contact information fields
    private List<String> additionalPhones;
    private List<String> additionalEmails;
    private List<String> additionalCccds;

    // Customer interests and preferences
    private String interests;
    // Payload uses dd/MM/yyyy; we will parse accordingly in BE
    private String birthDate;
    private String addressContact;
    private String addressPermanent;
    private String nationality;
    private String maritalStatus;
    private BigDecimal totalAsset;
    private String businessField;
    private String avatarUrl;
    private String zaloStatus;
    private String facebookLink;
    private String sourceType;   // Data | Leads | Event | Refer
    private String sourceDetail;
    private String notes;

    // Align names with payload
    private java.util.List<RelativeUpsertDto> customerRelatives;
    private java.util.List<AssignmentUpsertDto> customerAssignments;
    private java.util.List<PropertyUpsertDto> customerProperties; // each may include interactionsSecondary
    private java.util.List<OfferUpsertDto> customerOffers;         // each may include interactionsPrimary
}

