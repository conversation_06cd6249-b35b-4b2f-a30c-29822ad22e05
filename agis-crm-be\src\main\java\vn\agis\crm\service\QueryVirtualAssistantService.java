package vn.agis.crm.service;

import com.google.gson.reflect.TypeToken;
import jakarta.persistence.EntityManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.CustomerPropertyDto;
import vn.agis.crm.base.jpa.dto.CustomerSearchDto;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.dto.req.UnitDto;
import vn.agis.crm.base.jpa.dto.req.virtualassistant.ParamSearchAdvance;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Answer;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Question;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.utils.ObjectUtils;
import vn.agis.crm.base.utils.Utils;
import vn.agis.crm.repository.*;
import vn.agis.crm.service.chatbot.SemanticSearchService;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Transactional
public class QueryVirtualAssistantService {
    private final Logger logger = LoggerFactory.getLogger(QueryVirtualAssistantService.class);
    @Autowired
    private QueryVirtualAssistantRepository queryVirtualAssistantRepository;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CustomerService customerService;
    @Autowired
    SemanticSearchService semanticSearchService;
    @Autowired
    EntityManager entityManager;

    public Event process(Event event) {
        switch (event.method){
            case JpaConstants.Method.CREATE:
                return create(event);
            case JpaConstants.Method.SEARCH:
                return search(event);
        }
        return event;
    }

    private Event create(Event event) {
        QueryVirtualAssistant queryVirtualAssistant = create((QueryVirtualAssistant) event.payload, event);
        if(queryVirtualAssistant == null){
            return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, "Loi");
        }
        return event.createResponse(queryVirtualAssistant, ResponseCode.OK, "");
    }

    public QueryVirtualAssistant create(QueryVirtualAssistant query, Event event) {
        try {
            logger.info("start create query");
            query.setCreatedAt(new Date());
            query.setCreatedBy(event.userId);
            String questionZip = Utils.compress(Utils.gson.toJson(query.getQuestionInfo()));
            query.setQuestion(questionZip);
            List<CustomerDto> customerDtos = new ArrayList<>();
            setAnswer(query, event, customerDtos);
            queryVirtualAssistantRepository.save(query);
            QueryVirtualAssistant q = new QueryVirtualAssistant();
            BeanUtils.copyProperties(query, q);
            q.setAnswer(null);
            q.setQuestion(null);
            q.getAnswerInfo().setCustomers(customerDtos);
            return q;
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    private void setAnswer(QueryVirtualAssistant query, Event event, List<CustomerDto> customerDtos) throws IOException {
        logger.info("start set answer");
        Question question = query.getQuestionInfo();
        Answer answer = new Answer();
        answer.setCreated(System.currentTimeMillis());
        answer.setCustomers(new ArrayList<>());
        answer.setCustomerIds(new ArrayList<>());
        if(ObjectUtils.empty(question.getText())){
            logger.info("question search advance");
            CustomerSearchDto params = question.getParams();
            params.setPage(0);
            params.setSize(Integer.MAX_VALUE);
            params.setSortBy("created_at,asc");
            Event eventSearch = new Event();
            eventSearch.userId = event.userId;
            eventSearch.userType = event.userType;
            eventSearch.method = Constants.Method.SEARCH;
            eventSearch.payload = params;
            eventSearch = customerService.process(eventSearch);
            List<CustomerResDto> customers = new ArrayList<>();
            if(eventSearch.respStatusCode == ResponseCode.OK){
                PageInfo pageInfo = (PageInfo) eventSearch.payload;
                customers = ObjectMapperUtil.listMapper(pageInfo.getData(), CustomerResDto.class);
            }
            setupCustomerForAnswer(customers, answer, customerDtos);
        }else{
            logger.info("question search text");
            try {
                String sql = semanticSearchService.buildQuery(question.getText());
                List<Customers> customers = entityManager.createNativeQuery(sql, Customers.class).getResultList();
                List<CustomerResDto> resDtos = customers.stream().map(customer -> {
                    CustomerResDto resDto = new CustomerResDto();
                    resDto.setId(customer.getId());
                    resDto.setFullName(customer.getFullName());
                    resDto.setEmail(customer.getEmail());
                    resDto.setPhone(customer.getPhone());
                    resDto.setAvatarUrl(customer.getAvatarUrl());
                    return resDto;
                }).collect(Collectors.toList());
                setupCustomerForAnswer(resDtos, answer, customerDtos);
            }catch (Exception e){
                logger.error(e.getMessage(), e);
            }
        }
        query.setAnswerInfo(answer);
        String answerZip = Utils.compress(Utils.gson.toJson(answer));
        query.setAnswer(answerZip);
    }

    private void setupCustomerForAnswer(List<CustomerResDto> customers, Answer answer, List<CustomerDto> customerDtos) {
        if(!ObjectUtils.empty(customers)){
            answer.setCreated(System.currentTimeMillis());
            List<CustomerResDto> sub = customers.subList(0, Math.min(2, customers.size()));
            convertToListCustomerDto(customerDtos, sub);
            answer.setCustomerIds(customers.stream().map(el -> el.getId()).collect(Collectors.toList()));
        }
    }

    public List<CustomerDto> getPageCustomerForAnswer(List<Long> ids, Long userId, Integer userType){
        List<Customers> customers = new ArrayList<>();
        if(userType.equals(Constants.UserType.ADMIN)){
            customers = customerRepository.findAllById(ids);
        }else if(userType.equals(Constants.UserType.MANAGER)){
            customers = customerRepository.findAllByCurrentManagerId(userId);
        }else if(userType.equals(Constants.UserType.STAFF)){
            customers = customerRepository.findAllByCurrentStaffId(userId);
        }
        List<CustomerResDto> customerResDtos = new ArrayList<>();
        List<CustomerDto> customerDtos = new ArrayList<>();
        if(ObjectUtils.empty(customers)){
            for(Long id : ids){
                customerResDtos.add(new CustomerResDto());
            }
        }else{
            for(Long id : ids){
                Customers customer = null;
                for(int i = 0; i < customers.size(); i++){
                    if(customers.get(i).getId().equals(id)){
                        customer = customers.get(i);
                        break;
                    }
                }
                if(customer != null){
                    CustomerResDto resDto = new CustomerResDto();
                    resDto.setId(customer.getId());
                    resDto.setFullName(customer.getFullName());
                    resDto.setEmail(customer.getEmail());
                    resDto.setPhone(customer.getPhone());
                    resDto.setAvatarUrl(customer.getAvatarUrl());
                    customerResDtos.add(resDto);
                }else{
                    CustomerResDto resDto = new CustomerResDto();
                    customerResDtos.add(resDto);
                }
            }
        }
        convertToListCustomerDto(customerDtos, customerResDtos);
        return customerDtos;
    }

    public void convertToListCustomerDto(List<CustomerDto> customerDtos, List<CustomerResDto> sub) {
        for(CustomerResDto customer: sub){
            if(customer.getId() == null){
                customerDtos.add(new CustomerDto());
                continue;
            }
            CustomerDto customerDto = new CustomerDto();
            customerDto.setId(customer.getId());
            customerDto.setEmail(customer.getEmail());
            customerDto.setFullName(customer.getFullName());
            customerDto.setPhone(customer.getPhone());
            customerDto.setAvatarUrl(customer.getAvatarUrl());

            List<CustomerProperties> customerProperties = customerPropertyRepository.findByCustomerId(customer.getId());
            List<CustomerPropertyDto> customerPropertyDtos = new ArrayList<>();
            for(CustomerProperties customerProperty: customerProperties){
                CustomerPropertyDto customerPropertyDto = new CustomerPropertyDto();
                customerPropertyDto.setId(customerProperty.getId());
                customerPropertyDto.setProjectId(customerProperty.getProjectId());
                customerPropertyDto.setUnitId(customerProperty.getUnitId());
                customerPropertyDto.setTransactionDate(customerProperty.getTransactionDate());
                customerPropertyDto.setContractPrice(customerProperty.getContractPrice());
                customerPropertyDto.setExternalAgencyName(customerProperty.getExternalAgencyName());
                customerPropertyDto.setExternalSaleName(customerProperty.getExternalSaleName());
                customerPropertyDto.setExternalSalePhone(customerProperty.getExternalSalePhone());
                customerPropertyDto.setExternalSaleEmail(customerProperty.getExternalSaleEmail());
                customerPropertyDto.setEmployeeId(customerProperty.getEmployeeId());
                customerPropertyDto.setNotes(customerProperty.getNotes());
                customerPropertyDto.setLegalStatus(customerProperty.getLegalStatus()); // NEW: Map legal status
                customerPropertyDto.setFirstInteraction(customerProperty.getFirstInteraction());
                customerPropertyDto.setLastInteraction(customerProperty.getLastInteraction());

                Projects project = projectRepository.findById(customerProperty.getProjectId()).orElse(new Projects());
                ProjectDto projectDto = new ProjectDto();
                projectDto.setName(project.getName());
                Units unit = unitRepository.findById(customerProperty.getUnitId()).orElse(new Units());
                UnitDto unitDto = new UnitDto();
                unitDto.setArea(unit.getArea());
                unitDto.setProductType(unit.getProductType());
                customerPropertyDto.setProject(projectDto);
                customerPropertyDto.setUnit(unitDto);
                customerPropertyDtos.add(customerPropertyDto);
            }
            customerDto.setProducts(customerPropertyDtos);
            customerDtos.add(customerDto);
        }
    }

    private Event search(Event event) {
        try {
            Map<String, Object> params = (Map<String, Object>) event.payload;
            Long chatId = ((Double) params.get("chatId")).longValue();
            Integer page = ((Double) params.get("page")).intValue();
            Integer size = ((Double) params.get("size")).intValue();
            Pageable pageable = PageRequest.of(page, size);
            List<QueryVirtualAssistant> queryVirtualAssistants = queryVirtualAssistantRepository.searchByChatIdOrderByIdDesc(chatId, pageable);
            List<QueryVirtualAssistant> result = new ArrayList<>();
            queryVirtualAssistants.forEach(q -> {
                QueryVirtualAssistant queryVirtualAssistant = new QueryVirtualAssistant();
                BeanUtils.copyProperties(q, queryVirtualAssistant);
                try {
                    queryVirtualAssistant.setQuestionInfo(ObjectMapperUtil.objectMapper(Utils.decompress(queryVirtualAssistant.getQuestion()), Question.class));
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                queryVirtualAssistant.setQuestion(null);
                if(ObjectUtils.empty(queryVirtualAssistant.getAnswer())){
                    Answer answer = new Answer();
                    answer.setCreated(System.currentTimeMillis());
                    answer.setCustomers(new ArrayList<>());
                    answer.setCustomerIds(new ArrayList<>());
                    queryVirtualAssistant.setAnswerInfo(answer);
                }else{
                    try {
                        queryVirtualAssistant.setAnswerInfo(ObjectMapperUtil.objectMapper(Utils.decompress(queryVirtualAssistant.getAnswer()), Answer.class));
                        if(!ObjectUtils.empty(queryVirtualAssistant.getAnswerInfo().getCustomerIds())){
                            List<Long> ids = queryVirtualAssistant.getAnswerInfo().getCustomerIds().subList(0, Math.min(2, queryVirtualAssistant.getAnswerInfo().getCustomerIds().size()));
                            List<CustomerDto> customerDtos = getPageCustomerForAnswer(ids, event.userId, event.userType);
                            queryVirtualAssistant.getAnswerInfo().setCustomers(customerDtos);
                        }
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                    queryVirtualAssistant.setAnswer(null);
                }
                result.add(queryVirtualAssistant);
            });
            return event.createResponse(result, ResponseCode.OK, "");
        }catch (Exception e){
            logger.error(e.getMessage(), e);
            return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, "");
        }
    }
}
