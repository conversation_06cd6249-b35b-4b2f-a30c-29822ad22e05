package vn.agis.crm.base.jpa.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.Date;
import java.util.List;

@Entity
@Table(
        name = "employees",
        indexes = {
                @Index(name = "uq_employee_code", columnList = "employee_code", unique = true),
                @Index(name = "phone", columnList = "phone", unique = true),
                @Index(name = "email", columnList = "email", unique = true)
        }
)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Employee extends AbstractEntity {

    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "employee_code", nullable = false, length = 50, unique = true)
    private String employeeCode;

    @Column(name = "full_name", nullable = false, length = 255)
    private String fullName;

    @Column(name = "phone", length = 32, unique = true)
    private String phone;

    @Column(name = "email", length = 255, unique = true)
    private String email;

    @Column(name = "password", nullable = false, length = 255)
    @JsonIgnore
    private String password;

    @Column(name = "role_id", nullable = false)
    private Integer roleId;

    public enum Status {
        active, inactive
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private Status status = Status.active;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "deleted_at")
    private Date deletedAt;

    // created_at and created_by are inherited from AbstractEntity

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Transient
    private List<String> roles;

    @Transient
    private List<String> authorities;

    @Transient
    private Integer synchronizeStatus;

    @Transient
    private String tokenOauth2;

    @JsonIgnore
    public void setEncryptedPassword(String rawPassword) {
        this.password = passwordEncoder.encode(rawPassword);
    }

    public Boolean authenticate(String rawPassword) {
        return passwordEncoder.matches(rawPassword, this.password);
    }
}

