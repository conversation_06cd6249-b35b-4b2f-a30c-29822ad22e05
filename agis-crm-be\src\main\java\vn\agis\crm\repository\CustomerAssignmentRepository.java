package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.CustomerAssignments;

import java.util.Date;
import java.util.List;

@Repository
public interface CustomerAssignmentRepository extends JpaRepository<CustomerAssignments, Long> {

    @Modifying
    @Query("UPDATE CustomerAssignments ca SET ca.assignedTo = :now, ca.updatedAt = :updatedAt, ca.updatedBy = :updatedBy WHERE ca.customerId IN :customerIds AND ca.roleType = :roleType AND ca.assignedTo IS NULL")
    void deactivateActiveAssignments(@Param("customerIds") List<Long> customerIds, @Param("roleType") Integer roleType, @Param("now") Date now, @Param("updatedAt") Date updatedAt, @Param("updatedBy") Long updatedBy);

    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.customerId = :customerId AND ca.employeeId = :employeeId AND ca.roleType = :roleType ORDER BY ca.assignedFrom DESC")
    List<CustomerAssignments> findByCustomerIdAndEmployeeIdAndRoleTypeOrderByAssignedFromDesc(@Param("customerId") Long customerId, @Param("employeeId") Long employeeId, @Param("roleType") Integer roleType);

    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.id = :id AND ca.customerId = :customerId AND ca.roleType = :roleType")
    CustomerAssignments findByIdAndCustomerIdAndRoleType(@Param("id") Long id, @Param("customerId") Long customerId, @Param("roleType") Integer roleType);

    @Modifying
    @Query("UPDATE CustomerAssignments ca SET ca.employeeId = :newEmployeeId, ca.updatedAt = :updatedAt, ca.updatedBy = :updatedBy WHERE ca.id = :id AND ca.customerId = :customerId AND ca.roleType = :roleType")
    int updateEmployeeIdByIdAndCustomerIdAndRoleType(@Param("id") Long id, @Param("customerId") Long customerId, @Param("roleType") Integer roleType, @Param("newEmployeeId") Long newEmployeeId, @Param("updatedAt") Date updatedAt, @Param("updatedBy") Long updatedBy);

    @Query(
        value = "SELECT ca.id, ca.customer_id, ca.employee_id, e.full_name, e.employee_code, e.email, e.phone, " +
                "ca.role_type, ca.assigned_from, ca.assigned_to, ca.created_at, cb.full_name, ca.updated_at, ub.full_name " +
                "FROM customer_assignments ca " +
                "LEFT JOIN employees e ON e.id = ca.employee_id " +
                "LEFT JOIN employees cb ON cb.id = ca.created_by " +
                "LEFT JOIN employees ub ON ub.id = ca.updated_by " +
                "WHERE ca.customer_id = :customerId " +
                "ORDER BY ca.assigned_from DESC",
        countQuery = "SELECT COUNT(ca.id) FROM customer_assignments ca WHERE ca.customer_id = :customerId",
        nativeQuery = true
    )
    Page<Object[]> findAssignmentHistoryByCustomerId(@Param("customerId") Long customerId, Pageable pageable);

    /**
     * Find assignment by ID with employee details for validation
     */
    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.id = :assignmentId")
    CustomerAssignments findByAssignmentId(@Param("assignmentId") Long assignmentId);

    /**
     * Soft delete assignment by setting assignedTo to current date
     */
    @Modifying
    @Query("UPDATE CustomerAssignments ca SET ca.assignedTo = :deletedAt, ca.updatedAt = :updatedAt, ca.updatedBy = :updatedBy WHERE ca.id = :assignmentId")
    int softDeleteAssignment(@Param("assignmentId") Long assignmentId, @Param("deletedAt") Date deletedAt, @Param("updatedAt") Date updatedAt, @Param("updatedBy") Long updatedBy);

    /**
     * Check if assignment exists and is active (assignedTo is null)
     */
    @Query("SELECT COUNT(ca) > 0 FROM CustomerAssignments ca WHERE ca.id = :assignmentId AND ca.assignedTo IS NULL")
    boolean existsActiveAssignmentById(@Param("assignmentId") Long assignmentId);

    /**
     * Find current active assignment for a customer and role type
     */
    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.customerId = :customerId AND ca.roleType = :roleType AND ca.assignedTo IS NULL")
    CustomerAssignments findActiveAssignmentByCustomerIdAndRoleType(@Param("customerId") Long customerId, @Param("roleType") Integer roleType);

    /**
     * Find active assignments assigned before a specific date (for uncared lead warnings)
     */
    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.assignedTo IS NULL AND ca.assignedFrom <= :cutoffDate ORDER BY ca.assignedFrom ASC")
    List<CustomerAssignments> findActiveAssignmentsBeforeDate(@Param("cutoffDate") Date cutoffDate);

    /**
     * Find current active assignments for multiple customers and role type
     */
    @Query("SELECT ca FROM CustomerAssignments ca WHERE ca.customerId IN :customerIds AND ca.roleType = :roleType AND ca.assignedTo IS NULL")
    List<CustomerAssignments> findActiveAssignmentsByCustomerIdsAndRoleType(@Param("customerIds") List<Long> customerIds, @Param("roleType") Integer roleType);
}
