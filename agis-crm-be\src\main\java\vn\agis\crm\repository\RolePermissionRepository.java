package vn.agis.crm.repository;

import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.RolePermission;
import vn.agis.crm.base.jpa.entity.RolePermissionId;

@Repository
public interface RolePermissionRepository extends CrudRepository<RolePermission, RolePermissionId> {
    void deleteAllByRoleId(Long roleId);
    void deleteByPermissionId(Long permistionId);
}
