package vn.agis.crm.core.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;

/**
 * Created by HIEUDT on 12/12/2019.
 */
public class MessageSubscriber implements MessageListener {
    private static final Logger logger = LoggerFactory.getLogger(MessageSubscriber.class);

    @Override
    public void onMessage(Message message, byte[] bytes) {
        String key = new String(message.getBody());
        String channel = new String(message.getChannel());
        if (channel.contains("set") && key.contains(MessageMap.REQUEST_DOMAIN)) {
            String requestId = "";
            String[] keyArray = key.split(":");
            for (String keyElement : keyArray) {
                if (keyElement.contains(MessageMap.REQUEST_DOMAIN))
                    requestId = keyElement.replaceAll(MessageMap.REQUEST_DOMAIN, "");
            }
            if (!"".equals(requestId)) {
            }
        }
    }
}
