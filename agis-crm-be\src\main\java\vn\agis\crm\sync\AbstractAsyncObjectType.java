package vn.agis.crm.sync;

import java.io.IOException;
import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

public abstract class AbstractAsyncObjectType implements AsyncObjectType {
    protected final Logger logger = LogManager.getLogger(getClass());

    @Override
    public void update() {
        throw new AsyncServiceException();
    }

    @Override
    public void update(String... args) {
        throw new AsyncServiceException();
    }

    @Override
    public void update(Object... args) throws IOException {
        throw new AsyncServiceException();
    }

    @Override
    public String update(Map<String, String> map) {
        throw new AsyncServiceException();
    }

}
