package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;
import java.util.Date;

@Entity
@Table(name = "rule_job_history")
@Data
public class RuleJobHistory extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "job_started_at", nullable = false)
    private Date jobStartedAt;

    @Column(name = "job_finished_at")
    private Date jobFinishedAt;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private JobStatus status;

    @Column(name = "total_leads_assigned", nullable = false)
    private Integer totalLeadsAssigned = 0;

    @Column(name = "triggered_by")
    private String triggeredBy;

    public enum JobStatus {
        RUNNING, COMPLETED, FAILED
    }
}

