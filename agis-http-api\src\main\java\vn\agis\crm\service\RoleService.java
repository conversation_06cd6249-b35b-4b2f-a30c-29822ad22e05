package vn.agis.crm.service;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.RoleSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchRoleRespone;
import vn.agis.crm.base.jpa.entity.Role;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.util.RequestUtils;

@Service
public class RoleService extends CrudService<Role, Long> {

    private Logger logger = LoggerFactory.getLogger(RoleService.class);

    private static final String objectKeyRole = "Role";

    @Autowired
    private MessageSource messageSource;

    public RoleService() {
        super(Role.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.ROLE;
    }

//    public Role getRoleDetail(Long roleId) {
//        Event event = RequestUtils.amqp(GET_ONE, category, roleId, routingKey);
//        if (event.respStatusCode == ResponseCode.OK) {
//            return (Role) event.payload;
//        } else {
//        }
//        return null;
//    }

    public Role updateRoleDetail(Role role) {
        Role response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE, category, role, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (Role) event.payload;
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                    MessageKeyConstant.Validation.DUPLICATE_NAME);
            }else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(role.getId(), role.getName(), objectKeyRole, null, timeRequest, timeResponse,
//                ObjectMapperUtil.toJsonString(role),ObjectMapperUtil.toJsonString(response), null, null, event);
        }

    }

    public Role createRole(Role role) {
        Role response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, role, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response =  (Role) event.payload;
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.Validation.DUPLICATE_NAME);
            }else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        }finally {
//            TransactionLogger.writeLogITrans(response != null ? response.getId() : null, response != null ? response.getName() : null, objectKeyRole, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(role), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public ResponseBase updateRoleStatus(Long roleId) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        ResponseBase result = new ResponseBase();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE_ONE, category, roleId, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                result.setErrorCode(ResponseCode.OK);
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                result.setErrorCode(event.respStatusCode);
            }else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return result;
        }finally {
//            TransactionLogger.writeLogITrans(roleId, null, objectKeyRole, null, timeRequest, timeResponse,
//                ObjectMapperUtil.toJsonString(roleId), null, null, null, event);
        }

    }

    public Page<SearchRoleRespone> searchRole(RoleSearchDTO roleSearchDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, category, roleSearchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), SearchRoleRespone.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        }finally {
//            TransactionLogger.writeLogITrans(null, null, objectKeyRole, null, timeRequest, timeResponse,
//                ObjectMapperUtil.toJsonString(roleSearchDTO), ObjectMapperUtil.toJsonString(event.payload), null, null, event);
        }

    }

    private BadRequestException throwBadRequestException(String message, String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(message, values, LocaleContextHolder.getLocale());
        return new BadRequestException(messageNotFound, entity, field, message);
    }

}
