/**
 * <AUTHOR> VienNN
 * @version	: 1.0
 *
 */
package vn.agis.crm.base.utils;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.exception.ApiError;

import java.io.Serializable;


@SuppressWarnings("serial")
public class ResponseData<T> implements Serializable {

    @Getter
    @Setter
    private String status;

    @Getter
    @Setter
    private ApiError error;

    @Getter
    @Setter
    private T body;

    public ResponseData() {}

    public ResponseData(String status, ApiError error) {
        this.status = status;
        this.error = error;
        this.body = null;
    }

    public ResponseData(String status, ApiError error, T body) {
        this.status = status;
        this.error = error;
        this.body = body;
    }

    public ResponseData(String status, T body) {
        this.status = status;
        this.error = null;
        this.body = body;
    }
}
