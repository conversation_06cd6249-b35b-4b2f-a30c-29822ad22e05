package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.entity.ChatVirtualAssistant;
import vn.agis.crm.base.jpa.entity.QueryVirtualAssistant;
import vn.agis.crm.service.ChatVirtualAssistantService;
import vn.agis.crm.service.QueryVirtualAssistantService;

import java.util.List;

@RestController
@RequestMapping("/assistant/query")
public class QueryVirtualAssistantController extends CrudController<QueryVirtualAssistant, Long>{

    private QueryVirtualAssistantService queryVirtualAssistantService;
    @Autowired
    public QueryVirtualAssistantController(QueryVirtualAssistantService service) {
        super(service);
        this.queryVirtualAssistantService = service;
        this.baseUrl = "/assistant/query";
    }

    @PostMapping("")
    public ResponseEntity<QueryVirtualAssistant> create(@RequestBody QueryVirtualAssistant query, HttpServletRequest request) {
        return super.create(query, request);
    }

    @GetMapping("search")
    public ResponseEntity<List<QueryVirtualAssistant>> search(
            @RequestParam(name = "chatId") Long chatId,
            @RequestParam(name = "page") Integer page,
            @RequestParam(name = "size") Integer size
    ) {
        return queryVirtualAssistantService.search(chatId, page, size);
    }
}
