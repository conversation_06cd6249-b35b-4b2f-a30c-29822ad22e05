package vn.agis.crm.base.jpa.dto.resp;

import lombok.Data;

@Data
public class AuthResponseWithTokenAndErrorCodeDTO {
    private String accessToken;
    private String errorCode;
    private Long nbf;
    private Long exp;

    public AuthResponseWithTokenAndErrorCodeDTO() {
    }

    public AuthResponseWithTokenAndErrorCodeDTO(String accessToken, String errorCode) {
        this.accessToken = accessToken;
        this.errorCode = errorCode;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public Long getNbf() {
        return nbf;
    }

    public void setNbf(Long nbf) {
        this.nbf = nbf;
    }

    public Long getExp() {
        return exp;
    }

    public void setExp(Long exp) {
        this.exp = exp;
    }
}
