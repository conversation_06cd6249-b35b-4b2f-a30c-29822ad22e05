package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Entity
@Table(name = "ALERT_HISTORY")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlertHistory {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ALERT_ID")
    private Long alertId;

    @Column(name = "ALERT_NAME")
    private String alertName;

    @Column(name = "SEVERITY")
    private Integer severity;

    @Column(name = "EVENT_TYPE")
    private Integer eventType;

    @Column(name = "RAISED_DATE", nullable = false)
    private Date raisedDate;

    @Column(name = "SMS_CONTENT")
    private String smsContent;

    @Column(name = "EMAIL_CONTENT")
    private String emailContent;

    @Column(name = "ACTION_TYPE")
    private Integer actionType;

    @Column(name = "ALERT_EMAILS")
    private String alertEmails;

    @Column(name = "ALERT_MSISDNS")
    private String alertMsisdns;

    @Column(name = "DEVICE_ID")
    private Long deviceId;


}