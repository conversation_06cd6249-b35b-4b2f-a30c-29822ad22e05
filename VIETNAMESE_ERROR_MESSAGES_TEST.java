// Vietnamese Error Messages Test
// Verifies that all error messages in the import validation system are properly localized to Vietnamese

package vn.agis.crm.test;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.dto.DryRunResultDto;
import vn.agis.crm.util.ImportDataValidator;
import vn.agis.crm.util.ImportStatisticsCalculator;
import vn.agis.crm.util.ImportValidationRules;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class to verify that all error messages in the import validation system
 * are properly localized to Vietnamese language
 */
@SpringBootTest
@ActiveProfiles("test")
public class VietnameseErrorMessagesTest {

    @Test
    public void testRequiredFieldValidationMessages() {
        System.out.println("=== Testing Required Field Validation Messages ===");
        
        // Test data with missing required fields
        Map<String, String> rowData = new HashMap<>();
        rowData.put("EMAIL", "<EMAIL>"); // Only email provided
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertNotNull(result, "Validation result should not be null");
        assertTrue(result.hasErrors(), "Should have validation errors for missing required fields");
        
        // Check that error messages are in Vietnamese
        for (ImportErrorDto error : result.getErrors()) {
            String description = error.getErrorDescription();
            System.out.println("Error: " + description);
            
            // Verify Vietnamese error message pattern
            assertTrue(description.contains("Trường bắt buộc"), 
                "Error message should contain 'Trường bắt buộc': " + description);
            assertTrue(description.contains("bị thiếu hoặc trống"), 
                "Error message should contain 'bị thiếu hoặc trống': " + description);
        }
        
        System.out.println("✅ Required field validation messages are in Vietnamese");
    }

    @Test
    public void testPhoneValidationMessages() {
        System.out.println("\n=== Testing Phone Validation Messages ===");
        
        // Test invalid phone format
        Map<String, String> rowData = new HashMap<>();
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Nguyễn Văn A");
        rowData.put("PHONE", "invalid-phone");
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertTrue(result.hasErrors(), "Should have phone validation error");
        
        ImportErrorDto phoneError = result.getErrors().stream()
            .filter(error -> "phone".equals(error.getColumnName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(phoneError, "Should have phone validation error");
        
        String description = phoneError.getErrorDescription();
        System.out.println("Phone Error: " + description);
        
        assertTrue(description.contains("Số điện thoại"), 
            "Phone error should contain 'Số điện thoại': " + description);
        assertTrue(description.contains("không đúng định dạng"), 
            "Phone error should contain 'không đúng định dạng': " + description);
        
        System.out.println("✅ Phone validation messages are in Vietnamese");
    }

    @Test
    public void testEmailValidationMessages() {
        System.out.println("\n=== Testing Email Validation Messages ===");
        
        // Test invalid email format
        Map<String, String> rowData = new HashMap<>();
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Nguyễn Văn A");
        rowData.put("PHONE", "0901234567");
        rowData.put("EMAIL", "invalid-email");
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertTrue(result.hasErrors(), "Should have email validation error");
        
        ImportErrorDto emailError = result.getErrors().stream()
            .filter(error -> "email".equals(error.getColumnName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(emailError, "Should have email validation error");
        
        String description = emailError.getErrorDescription();
        System.out.println("Email Error: " + description);
        
        assertTrue(description.contains("Email"), 
            "Email error should contain 'Email': " + description);
        assertTrue(description.contains("không đúng định dạng"), 
            "Email error should contain 'không đúng định dạng': " + description);
        
        System.out.println("✅ Email validation messages are in Vietnamese");
    }

    @Test
    public void testBirthDateValidationMessages() {
        System.out.println("\n=== Testing Birth Date Validation Messages ===");
        
        // Test invalid birth date format
        Map<String, String> rowData = new HashMap<>();
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Nguyễn Văn A");
        rowData.put("PHONE", "0901234567");
        rowData.put("NGÀY SINH", "invalid-date");
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertTrue(result.hasErrors(), "Should have birth date validation error");
        
        ImportErrorDto dateError = result.getErrors().stream()
            .filter(error -> "birth_date".equals(error.getColumnName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(dateError, "Should have birth date validation error");
        
        String description = dateError.getErrorDescription();
        System.out.println("Birth Date Error: " + description);
        
        assertTrue(description.contains("Ngày sinh"), 
            "Birth date error should contain 'Ngày sinh': " + description);
        assertTrue(description.contains("không đúng định dạng"), 
            "Birth date error should contain 'không đúng định dạng': " + description);
        
        System.out.println("✅ Birth date validation messages are in Vietnamese");
    }

    @Test
    public void testMaritalStatusValidationMessages() {
        System.out.println("\n=== Testing Marital Status Validation Messages ===");
        
        // Test invalid marital status
        Map<String, String> rowData = new HashMap<>();
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Nguyễn Văn A");
        rowData.put("PHONE", "0901234567");
        rowData.put("TÌNH TRẠNG HÔN NHÂN", "invalid-status");
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertTrue(result.hasErrors(), "Should have marital status validation error");
        
        ImportErrorDto maritalError = result.getErrors().stream()
            .filter(error -> "marital_status".equals(error.getColumnName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(maritalError, "Should have marital status validation error");
        
        String description = maritalError.getErrorDescription();
        System.out.println("Marital Status Error: " + description);
        
        assertTrue(description.contains("Tình trạng hôn nhân"), 
            "Marital status error should contain 'Tình trạng hôn nhân': " + description);
        assertTrue(description.contains("không hợp lệ"), 
            "Marital status error should contain 'không hợp lệ': " + description);
        
        System.out.println("✅ Marital status validation messages are in Vietnamese");
    }

    @Test
    public void testDuplicateDetectionMessages() {
        System.out.println("\n=== Testing Duplicate Detection Messages ===");
        
        // Test duplicate phone in file
        Set<String> filePhones = new HashSet<>();
        filePhones.add("0901234567"); // Pre-existing phone
        
        Map<String, String> rowData = new HashMap<>();
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Nguyễn Văn A");
        rowData.put("PHONE", "0901234567"); // Duplicate phone
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, 2, 
            new HashSet<>(), filePhones);
        
        assertTrue(result.hasErrors(), "Should have duplicate phone error");
        
        ImportErrorDto duplicateError = result.getErrors().stream()
            .filter(error -> "phone".equals(error.getColumnName()))
            .findFirst()
            .orElse(null);
        
        assertNotNull(duplicateError, "Should have duplicate phone error");
        
        String description = duplicateError.getErrorDescription();
        System.out.println("Duplicate Error: " + description);
        
        assertTrue(description.contains("Số điện thoại"), 
            "Duplicate error should contain 'Số điện thoại': " + description);
        assertTrue(description.contains("trùng lặp"), 
            "Duplicate error should contain 'trùng lặp': " + description);
        
        System.out.println("✅ Duplicate detection messages are in Vietnamese");
    }

    @Test
    public void testStatisticsWarningMessages() {
        System.out.println("\n=== Testing Statistics Warning Messages ===");
        
        // Create validation results with high error rate
        List<ValidationResultDto> validationResults = new ArrayList<>();
        
        // Add rows with errors (to create high error rate)
        for (int i = 1; i <= 6; i++) {
            ValidationResultDto result = new ValidationResultDto(i);
            result.addError(new ImportErrorDto(1L, i, "phone", "invalid", 
                "INVALID_FORMAT", "Số điện thoại không đúng định dạng", "ERROR"));
            validationResults.add(result);
        }
        
        // Add a few valid rows
        for (int i = 7; i <= 10; i++) {
            ValidationResultDto result = new ValidationResultDto(i);
            validationResults.add(result);
        }
        
        // Calculate statistics
        DryRunResultDto dryRunResult = ImportStatisticsCalculator.calculateStatistics(
            1L, "test.xlsx", validationResults, new Date(), new Date());
        
        assertNotNull(dryRunResult.getWarnings(), "Should have warnings");
        assertFalse(dryRunResult.getWarnings().isEmpty(), "Should have warning messages");
        
        // Check warning messages are in Vietnamese
        for (String warning : dryRunResult.getWarnings()) {
            System.out.println("Warning: " + warning);
            
            // Check for Vietnamese warning patterns
            boolean isVietnamese = warning.contains("Tỷ lệ lỗi cao") || 
                                 warning.contains("số điện thoại trùng lặp") ||
                                 warning.contains("dòng thiếu thông tin") ||
                                 warning.contains("lỗi định dạng dữ liệu");
            
            assertTrue(isVietnamese, "Warning message should be in Vietnamese: " + warning);
        }
        
        System.out.println("✅ Statistics warning messages are in Vietnamese");
    }

    @Test
    public void testBusinessRuleValidationMessages() {
        System.out.println("\n=== Testing Business Rule Validation Messages ===");
        
        // Test unit code validation
        Map<String, String> rowData = new HashMap<>();
        rowData.put("TÊN DỰ ÁN", "Test Project");
        rowData.put("MÃ CĂN", "VERY_LONG_UNIT_CODE_THAT_EXCEEDS_LIMIT");
        
        ImportErrorDto unitError = ImportValidationRules.BusinessRules.validateProjectUnit(
            rowData, 1L, 1);
        
        assertNotNull(unitError, "Should have unit code validation error");
        
        String description = unitError.getErrorDescription();
        System.out.println("Unit Code Error: " + description);
        
        assertTrue(description.contains("Mã căn"), 
            "Unit error should contain 'Mã căn': " + description);
        assertTrue(description.contains("quá dài"), 
            "Unit error should contain 'quá dài': " + description);
        
        System.out.println("✅ Business rule validation messages are in Vietnamese");
    }

    @Test
    public void testErrorMessageConsistency() {
        System.out.println("\n=== Testing Error Message Consistency ===");
        
        // Test that all error messages follow Vietnamese patterns
        Map<String, String> testData = new HashMap<>();
        testData.put("HỌ VÀ TÊN KHÁCH HÀNG", ""); // Empty required field
        testData.put("PHONE", "invalid-phone"); // Invalid phone
        testData.put("EMAIL", "invalid-email"); // Invalid email
        testData.put("NGÀY SINH", "invalid-date"); // Invalid date
        testData.put("TÌNH TRẠNG HÔN NHÂN", "invalid-status"); // Invalid marital status
        
        ValidationResultDto result = ImportDataValidator.validateRow(testData, 1L, 1, 
            new HashSet<>(), new HashSet<>());
        
        assertTrue(result.hasErrors(), "Should have multiple validation errors");
        
        // Check all error messages for Vietnamese patterns
        for (ImportErrorDto error : result.getErrors()) {
            String description = error.getErrorDescription();
            System.out.println("Error: " + description);
            
            // Verify no English words in error descriptions
            assertFalse(description.matches(".*\\b(is|are|not|invalid|format|missing|empty|required)\\b.*"), 
                "Error message should not contain English words: " + description);
            
            // Verify Vietnamese patterns
            boolean hasVietnamesePattern = description.contains("không") || 
                                         description.contains("bị") ||
                                         description.contains("thiếu") ||
                                         description.contains("trống") ||
                                         description.contains("hợp lệ") ||
                                         description.contains("định dạng");
            
            assertTrue(hasVietnamesePattern, 
                "Error message should contain Vietnamese patterns: " + description);
        }
        
        System.out.println("✅ All error messages are consistently in Vietnamese");
    }

    public static void main(String[] args) {
        System.out.println("🇻🇳 Vietnamese Error Messages Validation Test");
        System.out.println("==============================================");
        System.out.println("");
        System.out.println("This test verifies that:");
        System.out.println("1. ✅ All error descriptions are in Vietnamese");
        System.out.println("2. ✅ Required field validation messages are localized");
        System.out.println("3. ✅ Phone/Email/Date validation messages are localized");
        System.out.println("4. ✅ Marital status validation messages are localized");
        System.out.println("5. ✅ Duplicate detection messages are localized");
        System.out.println("6. ✅ Statistics warning messages are localized");
        System.out.println("7. ✅ Business rule validation messages are localized");
        System.out.println("8. ✅ No English words remain in user-facing error messages");
        System.out.println("");
        System.out.println("Run with: mvn test -Dtest=VietnameseErrorMessagesTest");
        System.out.println("");
        
        // Manual verification steps
        System.out.println("📋 Manual Verification Steps:");
        System.out.println("1. Import a file with validation errors");
        System.out.println("2. Check dry-run results for Vietnamese error messages");
        System.out.println("3. Verify error summary descriptions are in Vietnamese");
        System.out.println("4. Test various validation scenarios");
        System.out.println("");
        
        System.out.println("🎯 Expected Results:");
        System.out.println("✅ All error descriptions in Vietnamese");
        System.out.println("✅ Consistent Vietnamese terminology");
        System.out.println("✅ User-friendly Vietnamese error messages");
        System.out.println("✅ No English text in user-facing errors");
    }
}

/**
 * Integration Test for Vietnamese Error Messages
 * 
 * This test class verifies that all error messages in the enhanced import validation system
 * have been properly converted from English to Vietnamese, including:
 * 
 * 1. Required field validation messages
 * 2. Phone number validation messages  
 * 3. Email validation messages
 * 4. Birth date validation messages
 * 5. Marital status validation messages
 * 6. Duplicate detection messages
 * 7. Statistics warning messages
 * 8. Business rule validation messages
 * 
 * The test ensures that:
 * - All user-facing error descriptions are in Vietnamese
 * - Error message patterns are consistent
 * - No English words remain in error descriptions
 * - Vietnamese grammar and terminology are correct
 * - All validation functionality is preserved
 */
