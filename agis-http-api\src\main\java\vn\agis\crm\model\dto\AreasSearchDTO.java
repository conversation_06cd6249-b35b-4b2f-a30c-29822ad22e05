package vn.agis.crm.model.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class AreasSearchDTO {
    private String name;
    private Integer status;
    private Integer type;
    private Long parentId;
    private Long createdBy;
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "id,asc";
    
    public AreasSearchDTO() {
    }
    
    public AreasSearchDTO(String name, Integer status) {
        this.name = name;
        this.status = status;
    }
    
    public AreasSearchDTO(String name, Integer status, Integer type, Long parentId, Long createdBy, Integer page, Integer size, String sortBy) {
        this.name = name;
        this.status = status;
        this.type = type;
        this.parentId = parentId;
        this.createdBy = createdBy;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
    
    @Override
    public String toString() {
        return "AreasSearchDTO{" +
                "name='" + name + '\'' +
                ", status=" + status +
                ", type='" + type + '\'' +
                ", parentId=" + parentId +
                ", createdBy=" + createdBy +
                ", page=" + page +
                ", size=" + size +
                ", sortBy='" + sortBy + '\'' +
                '}';
    }
}
