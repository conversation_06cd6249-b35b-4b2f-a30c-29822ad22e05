package vn.agis.crm.endpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.service.AssignmentRuleService;

@Component
public class AssignmentRuleEndpoint {

    @Autowired
    private AssignmentRuleService ruleService;

    public Event process(Event event) {
        return ruleService.process(event);
    }
}

