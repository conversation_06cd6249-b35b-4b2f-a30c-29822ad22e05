package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.req.UpdateRulePriorityRequest;
import vn.agis.crm.base.jpa.entity.LeadRule;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;
import vn.agis.crm.base.jpa.dto.res.LeadRuleResDto;
import vn.agis.crm.repository.AssignmentRuleRepository;
import vn.agis.crm.repository.EmployeeRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Transactional
public class AssignmentRuleService {

    @Autowired
    private AssignmentRuleRepository ruleRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.GET_ALL_RULES:
                return getAllRules(event);
            case Constants.Method.CREATE:
                return create(event);
            case Constants.Method.UPDATE:
                return update(event);
            case Constants.Method.DELETE:
                return delete(event);
            case JpaConstants.Method.GET_ONE:
                return findById(event);
            case Constants.Method.UPDATE_RULE_PRIORITY:
                return updatePriority(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event getAllRules(Event event) {
        List<LeadRule> rules = ruleRepository.findAllByOrderByPriorityAsc();
        List<LeadRuleResDto> result = new ArrayList<>();
        for (LeadRule r : rules) {
            result.add(toResDto(r));
        }
        return event.createResponse(result, 200, "Success");
    }

    private Event create(Event event) {
        LeadRule rule = (LeadRule) event.payload;
        rule.setCreatedBy(event.userId);
        rule.setCreatedAt(new Date());
        LeadRule savedRule = ruleRepository.save(rule);
        return event.createResponse(toResDto(savedRule), ResponseCode.OK, "Created");
    }

    private Event update(Event event) {
        LeadRule ruleData = (LeadRule) event.payload;
        if (ruleData.getId() == null) {
            return event.createResponse(null, 400, "Missing rule ID");
        }
        return ruleRepository.findById(ruleData.getId()).map(existingRule -> {
            // Keep original creation data
            ruleData.setCreatedBy(existingRule.getCreatedBy());
            ruleData.setCreatedAt(existingRule.getCreatedAt());
            // Set update data
            ruleData.setUpdatedBy(event.userId);
            ruleData.setUpdatedAt(new Date());
            LeadRule updatedRule = ruleRepository.save(ruleData);
            return event.createResponse(toResDto(updatedRule), 200, "Success");
        }).orElse(event.createResponse(null, 404, "Rule not found"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        ruleRepository.deleteById(id);
        return event.createResponse(null, 200, "Success");
    }


    private Event updatePriority(Event event) {
        UpdateRulePriorityRequest request = (UpdateRulePriorityRequest) event.payload;
        List<Long> orderedIds = request.getOrderedRuleIds();

        if (orderedIds == null || orderedIds.isEmpty()) {
            return event.createResponse(null, 400, "Rule ID list cannot be empty");
        }

        List<LeadRule> rulesToUpdate = new ArrayList<>();
        for (int i = 0; i < orderedIds.size(); i++) {
            final int priority = i + 1; // Priority is 1-based
            ruleRepository.findById(orderedIds.get(i)).ifPresent(rule -> {
                rule.setPriority(priority);
                rule.setUpdatedAt(new Date());
                rule.setUpdatedBy(event.userId);
                rulesToUpdate.add(rule);
            });
        }

        ruleRepository.saveAll(rulesToUpdate);
        return event.createResponse(null, 200, "Priority updated successfully");
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        return ruleRepository.findById(id)
                .map(rule -> event.createResponse(toResDto(rule), 200, "Success"))
                .orElse(event.createResponse(null, 404, "Rule not found"));
    }

    private LeadRuleResDto toResDto(LeadRule r) {
        LeadRuleResDto dto = new LeadRuleResDto();
        dto.setId(r.getId());
        dto.setName(r.getName());
        dto.setPriority(r.getPriority());
        dto.setConditions(r.getConditions());
        dto.setConflictPolicy(r.getConflictPolicy());
        dto.setIsActive(r.getIsActive());
        if (r.getManagerId() != null) {
            employeeRepository.findById(r.getManagerId()).ifPresent(e -> dto.setManager(toEmployeeSummary(e)));
        }
        if (r.getStaffId() != null) {
            employeeRepository.findById(r.getStaffId()).ifPresent(e -> dto.setStaff(toEmployeeSummary(e)));
        }
        return dto;
    }

    private EmployeeSummaryDto toEmployeeSummary(Employee e) {
        EmployeeSummaryDto s = new EmployeeSummaryDto();
        s.setId(e.getId());
        s.setEmployeeCode(e.getEmployeeCode());
        s.setFullName(e.getFullName());
        s.setPhone(e.getPhone());
        s.setEmail(e.getEmail());
        return s;
    }
}


