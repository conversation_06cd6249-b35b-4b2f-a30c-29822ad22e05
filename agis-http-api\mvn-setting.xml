<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0
                              https://maven.apache.org/xsd/settings-1.0.0.xsd">
    <servers>
        <server>
            <id>vnpt-nexus</id>
            <username>${server.user}</username>
            <password>${server.pwd}</password>
            <filePermissions>664</filePermissions>
            <directoryPermissions>775</directoryPermissions>
            <configuration></configuration>
        </server>
    </servers>

    <mirrors>
        <mirror>
            <id>vnpt-nexus</id>
            <name>NEXUS repo</name>
            <url>http://rdrepo.vnpt-technology.vn/repository/maven_public/</url>
            <mirrorOf>!central</mirrorOf>
        </mirror>
        <mirror>
            <id>maven-default-http-blocker</id>
            <url>http://rdrepo.vnpt-technology.vn/repository/maven_public/</url>
            <mirrorOf>!central</mirrorOf>
        </mirror>
    </mirrors>

    <!-- Set up public repository as the main repository -->
    <profiles>
        <profile>
            <id>vnpt-repo</id>
            <repositories>
                <repository>
                    <id>central</id>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </repository>
                <repository>
                    <id>vnpt-nexus</id>
                    <url>http://rdrepo.vnpt-technology.vn/repository/maven_public/</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>true</enabled>
                    </snapshots>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <id>central</id>
                    <url>https://repo.maven.apache.org/maven2</url>
                    <releases>
                        <enabled>true</enabled>
                    </releases>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                </pluginRepository>
            </pluginRepositories>
        </profile>
    </profiles>

    <activeProfiles>
        <activeProfile>vnpt-repo</activeProfile>
    </activeProfiles>
</settings>