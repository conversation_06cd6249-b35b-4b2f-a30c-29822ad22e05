package vn.agis.crm.base.event;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RabbitStreamSubscribers {
    String streamName();

    int concurrency() default 3;

    short offsetType() default TYPE_FIRST;

    boolean isSupperStream() default true;

    short TYPE_NONE = 0;
    short TYPE_FIRST = 1;
    short TYPE_LAST = 2;
    short TYPE_NEXT = 3;
}
