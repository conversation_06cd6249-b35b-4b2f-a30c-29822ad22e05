package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.UserRole;

import java.util.List;

@Repository
public interface UserRoleRepository extends JpaRepository<UserRole, Long> {
    @Modifying
    void deleteByUserId(Long userId);

    @Query("SELECT up.roleId FROM UserRole up WHERE up.userId = :userId")
    List<Long> getRoleIdByUserId(Long userId);
}
