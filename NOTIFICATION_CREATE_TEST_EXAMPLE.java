// Example test demonstrating the usage of NotificationService.createNotification()
// This would typically be in a test class or used within other services

package vn.agis.crm.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.Notifications;

@SpringBootTest
@Transactional
public class NotificationServiceCreateTest {

    @Autowired
    private NotificationService notificationService;

    @Test
    public void testCreateCustomerBirthdayNotification() {
        // Test data
        Long targetEmployeeId = 123L;
        Long targetCustomerId = 456L;
        Long createdBy = 789L;
        
        // Create customer birthday notification
        Notifications notification = notificationService.createNotification(
            targetEmployeeId,
            2, // CustomerBirthday
            "Sinh nhật khách hàng",
            "<PERSON>h<PERSON>ch hàng <PERSON>ễn Văn A có sinh nhật hôm nay (22/01). <PERSON><PERSON><PERSON> gửi lời chúc mừng để duy trì mối quan hệ tốt.",
            targetCustomerId,
            createdBy
        );
        
        // Verify notification was created
        assert notification != null;
        assert notification.getId() != null;
        assert notification.getTargetEmployeeId().equals(targetEmployeeId);
        assert notification.getTargetCustomerId().equals(targetCustomerId);
        assert notification.getType().equals(2);
        assert notification.getTitle().equals("Sinh nhật khách hàng");
        assert notification.getIsRead().equals(false);
        assert notification.getReadAt() == null;
        assert notification.getCreatedAt() != null;
        assert notification.getCreatedBy().equals(createdBy);
        
        System.out.println("✅ Customer birthday notification created successfully with ID: " + notification.getId());
    }

    @Test
    public void testCreateLeadAssignmentNotification() {
        // Test data
        Long targetEmployeeId = 123L;
        Long targetCustomerId = 456L;
        Long assignedBy = 789L;
        
        // Create lead assignment notification
        Notifications notification = notificationService.createNotification(
            targetEmployeeId,
            1, // LeadAssigned
            "Lead mới được phân công",
            "Bạn đã được phân công chăm sóc khách hàng Trần Thị B (SĐT: 0987654321). Hãy liên hệ trong vòng 24 giờ để tạo ấn tượng tốt.",
            targetCustomerId,
            assignedBy
        );
        
        // Verify notification was created
        assert notification != null;
        assert notification.getType().equals(1);
        assert notification.getTitle().equals("Lead mới được phân công");
        
        System.out.println("✅ Lead assignment notification created successfully with ID: " + notification.getId());
    }

    @Test
    public void testCreateSystemNotificationWithoutCustomer() {
        // Test data
        Long targetEmployeeId = 123L;
        
        // Create system notification without customer reference
        Notifications notification = notificationService.createNotification(
            targetEmployeeId,
            1, // LeadAssigned (general work reminder)
            "Nhắc nhở tuần mới",
            "Chúc bạn một tuần làm việc hiệu quả! Hãy kiểm tra danh sách khách hàng cần chăm sóc và lên kế hoạch liên hệ."
        );
        
        // Verify notification was created
        assert notification != null;
        assert notification.getTargetCustomerId() == null;
        assert notification.getCreatedBy() == null; // System-generated
        
        System.out.println("✅ System notification created successfully with ID: " + notification.getId());
    }

    @Test
    public void testCreateLeadUncaredWarningNotification() {
        // Test data
        Long targetEmployeeId = 123L;
        Long targetCustomerId = 456L;
        
        // Create lead uncared warning notification
        Notifications notification = notificationService.createNotification(
            targetEmployeeId,
            3, // LeadUncaredWarning
            "Cảnh báo: Lead chưa được chăm sóc",
            "Khách hàng Lê Văn C (SĐT: 0912345678) đã không được liên hệ trong 7 ngày. Hãy liên hệ ngay để tránh mất cơ hội bán hàng.",
            targetCustomerId
        );
        
        // Verify notification was created
        assert notification != null;
        assert notification.getType().equals(3);
        assert notification.getTitle().contains("Cảnh báo");
        
        System.out.println("✅ Lead uncared warning notification created successfully with ID: " + notification.getId());
    }

    @Test
    public void testCreateLeadInactiveWarningNotification() {
        // Test data
        Long targetEmployeeId = 123L;
        Long targetCustomerId = 456L;
        
        // Create lead inactive warning notification
        Notifications notification = notificationService.createNotification(
            targetEmployeeId,
            4, // LeadInactiveWarning
            "Cảnh báo: Lead không hoạt động",
            "Khách hàng Phạm Thị D đã không có hoạt động nào trong 30 ngày. Cân nhắc chuyển sang chiến lược chăm sóc khác hoặc tái phân công.",
            targetCustomerId
        );
        
        // Verify notification was created
        assert notification != null;
        assert notification.getType().equals(4);
        assert notification.getTitle().contains("không hoạt động");
        
        System.out.println("✅ Lead inactive warning notification created successfully with ID: " + notification.getId());
    }

    @Test
    public void testValidationErrors() {
        try {
            // Test null targetEmployeeId
            notificationService.createNotification(null, 1, "Title", "Content");
            assert false : "Should throw IllegalArgumentException for null targetEmployeeId";
        } catch (IllegalArgumentException e) {
            System.out.println("✅ Correctly caught validation error for null targetEmployeeId: " + e.getMessage());
        }

        try {
            // Test invalid type
            notificationService.createNotification(123L, 5, "Title", "Content");
            assert false : "Should throw IllegalArgumentException for invalid type";
        } catch (IllegalArgumentException e) {
            System.out.println("✅ Correctly caught validation error for invalid type: " + e.getMessage());
        }

        try {
            // Test empty title
            notificationService.createNotification(123L, 1, "", "Content");
            assert false : "Should throw IllegalArgumentException for empty title";
        } catch (IllegalArgumentException e) {
            System.out.println("✅ Correctly caught validation error for empty title: " + e.getMessage());
        }

        try {
            // Test empty content
            notificationService.createNotification(123L, 1, "Title", "");
            assert false : "Should throw IllegalArgumentException for empty content";
        } catch (IllegalArgumentException e) {
            System.out.println("✅ Correctly caught validation error for empty content: " + e.getMessage());
        }

        try {
            // Test title too long
            String longTitle = "A".repeat(256); // 256 characters
            notificationService.createNotification(123L, 1, longTitle, "Content");
            assert false : "Should throw IllegalArgumentException for title too long";
        } catch (IllegalArgumentException e) {
            System.out.println("✅ Correctly caught validation error for title too long: " + e.getMessage());
        }
    }
}

// Example usage in a real service class
@Service
@Transactional
class CustomerBirthdayService {
    
    @Autowired
    private NotificationService notificationService;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private CustomerAssignmentRepository assignmentRepository;
    
    public void processCustomerBirthdays() {
        System.out.println("🎂 Processing customer birthdays...");
        
        // Find customers with birthdays today
        List<Customers> birthdayCustomers = customerRepository.findCustomersWithBirthdayToday();
        
        for (Customers customer : birthdayCustomers) {
            // Find assigned employee
            Optional<CustomerAssignment> assignment = assignmentRepository
                .findActiveAssignmentByCustomerId(customer.getId());
            
            if (assignment.isPresent()) {
                Long employeeId = assignment.get().getEmployeeId();
                
                String title = "Sinh nhật khách hàng";
                String content = String.format(
                    "Khách hàng %s có sinh nhật hôm nay (%s). " +
                    "Hãy gửi lời chúc mừng để duy trì mối quan hệ tốt.",
                    customer.getFullName(),
                    formatBirthDate(customer.getBirthDate())
                );
                
                try {
                    Notifications notification = notificationService.createNotification(
                        employeeId,
                        2, // CustomerBirthday
                        title,
                        content,
                        customer.getId()
                    );
                    
                    System.out.println("✅ Created birthday notification " + notification.getId() + 
                                     " for employee " + employeeId + 
                                     " about customer " + customer.getFullName());
                    
                } catch (Exception e) {
                    System.err.println("❌ Failed to create birthday notification for customer " + 
                                     customer.getFullName() + ": " + e.getMessage());
                }
            } else {
                System.out.println("⚠️ No assigned employee found for customer " + customer.getFullName());
            }
        }
        
        System.out.println("🎂 Finished processing customer birthdays");
    }
    
    private String formatBirthDate(Date birthDate) {
        // Format birth date for display
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM");
        return sdf.format(birthDate);
    }
}
