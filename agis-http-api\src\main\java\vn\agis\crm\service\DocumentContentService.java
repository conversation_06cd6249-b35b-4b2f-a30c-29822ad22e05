package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.docs.DocumentContent;
import vn.agis.crm.base.jpa.dto.docs.Project;
import vn.agis.crm.util.RequestUtils;

import java.util.List;
import java.util.Map;

@Service
public class DocumentContentService {
    private Logger logger = LoggerFactory.getLogger(PermissionService.class);
    private String routingKey;
    private String category;

    private static final String objectKey = "DocumentGuide";

    public DocumentContentService(){
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.DOCS;
    }

    public Project getProjectInfo(){
        Project response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_PROJECT_INFO, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (Project) event.payload;
            } else {
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public List<DocumentContent> getPageForProject(){
        List<DocumentContent> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_LIST_PAGE, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (List<DocumentContent>) event.payload;
            } else {
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public DocumentContent getPageInfo(Map<String, Object> params){
        DocumentContent response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_PAGE_INFO, category, params, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (DocumentContent) event.payload;
            } else {
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }
}
