package vn.agis.crm.base.configuration;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.amqp.AMQPEventBus;
import vn.agis.crm.base.event.amqp.AMQPEventListener;
import java.util.Map;

@Component
public final class SpringContext implements ApplicationContextAware, ApplicationRunner{
    public static final Logger logger = LoggerFactory.getLogger(SpringContext.class);

    private SpringContext(){

    }
    private static ApplicationContext context;
    private static String parentPackagePath;
    private static String applicationId = null;
    private static Integer x_message_ttl = null;
    private static Boolean purgeAllRpcQueue = true;
    private static String serviceName = null;

    public static <T extends Object> T getBean(Class<T> beanClass) {
        return context.getBean(beanClass);
    }

    public static <T extends Object> T getBean(String beanName) {
        return (T) context.getBean(beanName);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        if (context == null) setContext(applicationContext);
    }

    public static String getParentPackagePath() {
        return parentPackagePath;
    }

    public static void setServiceName(String serviceName) {
        SpringContext.serviceName = serviceName;
    }

    public static String getServiceName() {
        return serviceName;
    }

    public static void setContext(ApplicationContext ctx) {
        if (context != null) return;
        context = ctx;
        Map<String, Object> candidates = context.getBeansWithAnnotation(SpringBootApplication.class);
        Class<?> clazz = candidates.values().toArray()[0].getClass();
        String className = clazz.getName();
        String simpleName = clazz.getSimpleName();
        parentPackagePath = className.substring(0, className.length() - simpleName.length());
        applicationId = context.getEnvironment().getProperty("nodeName");
        String queueMessageTTL = context.getEnvironment().getProperty("queueMessageTTL");
        if (queueMessageTTL != null && !queueMessageTTL.isEmpty())
            x_message_ttl = Integer.valueOf(queueMessageTTL);
        String purgeAllRpcQueueStr = context.getEnvironment().getProperty("purgeAllRpcQueue");
        if (purgeAllRpcQueueStr != null && !purgeAllRpcQueueStr.isEmpty())
            purgeAllRpcQueue = Boolean.valueOf(queueMessageTTL);
    }

    public static <T> T getEnvironmentProperty(String key, Class<T> targetClass, T defaultValue) {
        if (key == null || targetClass == null) {
            throw new NullPointerException();
        }
        T value = null;
        if (context != null) {
            logger.info("Enviroment Property: " + context.getEnvironment().getProperty(key));
            value = context.getEnvironment().getProperty(key, targetClass, defaultValue);
        }
        return value;
    }

    public static String getApplicationId() {
        if (context == null) {
            logger.warn("Application has not completed initialization yet!");
            return null;
        }

        return applicationId;
    }

    public static Integer getX_message_ttl() {
        if (context == null) {
            logger.warn("Application has not completed initialization yet!");
            return null;
        }
        return x_message_ttl;
    }

    public static Boolean isPurgeAllRpcQueue() {
        if (context == null) {
            logger.warn("Application has not completed initialization yet!");
            return null;
        }
        return purgeAllRpcQueue;
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String[] beanNameList = context.getBeanNamesForType(AMQPEventBus.class);
        if (beanNameList == null || beanNameList.length == 0) return;
        for (String name: beanNameList) {
            AMQPEventBus bus = context.getBean(name, AMQPEventBus.class);
            if (bus == null) continue;
            Map<String, AMQPEventListener> map = bus.getSubscribers();
            if (map == null || map.values() == null || map.values().isEmpty()) continue;
            for (AMQPEventListener listener : map.values()) {
                if (listener.getListenerContainer() != null) listener.getListenerContainer().start();
                if (listener.getStreamListenerContainer() != null) listener.getStreamListenerContainer().start();
            }
        }
    }
}
