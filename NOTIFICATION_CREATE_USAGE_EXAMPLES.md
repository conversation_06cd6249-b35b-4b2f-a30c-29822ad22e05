# NotificationService.createNotification() Usage Examples

## Overview

The `createNotification` method has been added to the `NotificationService` in the `agis-crm-be` module to allow other services within the same module to create notifications for employees.

## Method Signatures

```java
// Full method with all parameters
public Notifications createNotification(Long targetEmployeeId, Integer type, String title, 
                                      String content, Long targetCustomerId, Long createdBy)

// Without createdBy (system-generated)
public Notifications createNotification(Long targetEmployeeId, Integer type, String title, 
                                      String content, Long targetCustomerId)

// Without targetCustomerId and createdBy (general notifications)
public Notifications createNotification(Long targetEmployeeId, Integer type, String title, String content)
```

## Notification Types

- **Type 1**: LeadAssigned - New lead assigned to employee
- **Type 2**: CustomerBirthday - Customer birthday reminder
- **Type 3**: LeadUncaredWarning - Lead hasn't been contacted recently
- **Type 4**: LeadInactiveWarning - Lead has been inactive for too long

## Usage Examples

### 1. Customer Birthday Notification (from CustomerService)

```java
@Service
@Transactional
public class CustomerService {
    
    @Autowired
    private NotificationService notificationService;
    
    public void checkCustomerBirthdays() {
        List<Customers> birthdayCustomers = customerRepository.findCustomersWithBirthdayToday();
        
        for (Customers customer : birthdayCustomers) {
            // Get assigned employee for this customer
            Long assignedEmployeeId = getAssignedEmployeeId(customer.getId());
            
            if (assignedEmployeeId != null) {
                String title = "Sinh nhật khách hàng";
                String content = String.format("Khách hàng %s có sinh nhật hôm nay (%s). " +
                                              "Hãy gửi lời chúc mừng để duy trì mối quan hệ tốt.",
                                              customer.getFullName(), 
                                              formatDate(customer.getBirthDate()));
                
                notificationService.createNotification(
                    assignedEmployeeId,
                    2, // CustomerBirthday
                    title,
                    content,
                    customer.getId()
                );
            }
        }
    }
}
```

### 2. Lead Assignment Notification (from AssignmentService)

```java
@Service
@Transactional
public class AssignmentService {
    
    @Autowired
    private NotificationService notificationService;
    
    public void assignLeadToEmployee(Long customerId, Long employeeId, Long assignedBy) {
        // Perform assignment logic...
        
        // Create notification for assigned employee
        Customers customer = customerRepository.findById(customerId).orElse(null);
        Employee employee = employeeRepository.findById(employeeId).orElse(null);
        
        if (customer != null && employee != null) {
            String title = "Lead mới được phân công";
            String content = String.format("Bạn đã được phân công chăm sóc khách hàng %s (SĐT: %s). " +
                                          "Hãy liên hệ trong vòng 24 giờ để tạo ấn tượng tốt.",
                                          customer.getFullName(),
                                          customer.getPhone());
            
            notificationService.createNotification(
                employeeId,
                1, // LeadAssigned
                title,
                content,
                customerId,
                assignedBy
            );
        }
    }
}
```

### 3. Lead Care Warning Notification (from LeadManagementService)

```java
@Service
@Transactional
public class LeadManagementService {
    
    @Autowired
    private NotificationService notificationService;
    
    public void checkUncaredLeads() {
        List<CustomerAssignment> uncaredAssignments = findUncaredLeads(7); // 7 days without contact
        
        for (CustomerAssignment assignment : uncaredAssignments) {
            Customers customer = assignment.getCustomer();
            
            String title = "Cảnh báo: Lead chưa được chăm sóc";
            String content = String.format("Khách hàng %s (SĐT: %s) đã không được liên hệ trong %d ngày. " +
                                          "Hãy liên hệ ngay để tránh mất cơ hội bán hàng.",
                                          customer.getFullName(),
                                          customer.getPhone(),
                                          getDaysSinceLastContact(assignment));
            
            notificationService.createNotification(
                assignment.getEmployeeId(),
                3, // LeadUncaredWarning
                title,
                content,
                customer.getId()
            );
        }
    }
    
    public void checkInactiveLeads() {
        List<CustomerAssignment> inactiveAssignments = findInactiveLeads(30); // 30 days inactive
        
        for (CustomerAssignment assignment : inactiveAssignments) {
            Customers customer = assignment.getCustomer();
            
            String title = "Cảnh báo: Lead không hoạt động";
            String content = String.format("Khách hàng %s đã không có hoạt động nào trong %d ngày. " +
                                          "Cân nhắc chuyển sang chiến lược chăm sóc khác hoặc tái phân công.",
                                          customer.getFullName(),
                                          getDaysSinceLastActivity(assignment));
            
            notificationService.createNotification(
                assignment.getEmployeeId(),
                4, // LeadInactiveWarning
                title,
                content,
                customer.getId()
            );
        }
    }
}
```

### 4. System Notification (from ScheduledTaskService)

```java
@Service
public class ScheduledTaskService {
    
    @Autowired
    private NotificationService notificationService;
    
    @Scheduled(cron = "0 0 9 * * MON") // Every Monday at 9 AM
    public void sendWeeklyReminders() {
        List<Employee> salesEmployees = employeeRepository.findBySalesRole();
        
        for (Employee employee : salesEmployees) {
            String title = "Nhắc nhở tuần mới";
            String content = "Chúc bạn một tuần làm việc hiệu quả! " +
                           "Hãy kiểm tra danh sách khách hàng cần chăm sóc và lên kế hoạch liên hệ.";
            
            // System notification without specific customer
            notificationService.createNotification(
                employee.getId(),
                1, // LeadAssigned (general work reminder)
                title,
                content
            );
        }
    }
}
```

### 5. Error Handling Example

```java
@Service
@Transactional
public class SomeService {
    
    @Autowired
    private NotificationService notificationService;
    
    public void createNotificationWithErrorHandling(Long employeeId, String customerName) {
        try {
            notificationService.createNotification(
                employeeId,
                2, // CustomerBirthday
                "Sinh nhật khách hàng",
                "Khách hàng " + customerName + " có sinh nhật hôm nay",
                null // No specific customer ID
            );
            
            logger.info("Notification created successfully for employee {}", employeeId);
            
        } catch (IllegalArgumentException e) {
            logger.error("Invalid parameters for notification creation: {}", e.getMessage());
            // Handle validation errors
            
        } catch (RuntimeException e) {
            logger.error("Failed to create notification: {}", e.getMessage());
            // Handle database or system errors
        }
    }
}
```

## Best Practices

### 1. Dependency Injection
```java
@Service
@Transactional
public class YourService {
    
    @Autowired
    private NotificationService notificationService;
    
    // Your service methods...
}
```

### 2. Validation Before Creating Notifications
```java
public void createNotificationSafely(Long employeeId, Long customerId) {
    // Validate employee exists
    if (!employeeRepository.existsById(employeeId)) {
        logger.warn("Employee {} not found, skipping notification", employeeId);
        return;
    }
    
    // Validate customer exists (if provided)
    if (customerId != null && !customerRepository.existsById(customerId)) {
        logger.warn("Customer {} not found, creating notification without customer reference", customerId);
        customerId = null;
    }
    
    // Create notification
    notificationService.createNotification(employeeId, 1, "Title", "Content", customerId);
}
```

### 3. Batch Notification Creation
```java
public void createBatchNotifications(List<Long> employeeIds, String title, String content) {
    for (Long employeeId : employeeIds) {
        try {
            notificationService.createNotification(employeeId, 1, title, content);
        } catch (Exception e) {
            logger.error("Failed to create notification for employee {}: {}", employeeId, e.getMessage());
            // Continue with other employees
        }
    }
}
```

### 4. Conditional Notification Creation
```java
public void createConditionalNotification(Long employeeId, Customers customer) {
    // Only create notification if employee hasn't been notified recently
    boolean hasRecentNotification = notificationRepository
        .findByTargetEmployeeIdAndTypeAndCreatedAtAfter(
            employeeId, 2, getDateDaysAgo(1)
        ).size() > 0;
    
    if (!hasRecentNotification) {
        notificationService.createNotification(
            employeeId,
            2,
            "Sinh nhật khách hàng",
            "Khách hàng " + customer.getFullName() + " có sinh nhật hôm nay",
            customer.getId()
        );
    }
}
```

## Integration Notes

- **Module Scope**: This method is designed for internal service calls within the `agis-crm-be` module
- **No AMQP**: Direct service call, no messaging required
- **Transaction Support**: Runs within the calling service's transaction context
- **Error Handling**: Throws `IllegalArgumentException` for validation errors and `RuntimeException` for system errors
- **Logging**: Comprehensive logging for debugging and monitoring
- **Performance**: Efficient single database operation per notification

This implementation provides a clean, validated, and well-documented way for services to create notifications while maintaining consistency with the existing AGIS CRM architecture patterns.
