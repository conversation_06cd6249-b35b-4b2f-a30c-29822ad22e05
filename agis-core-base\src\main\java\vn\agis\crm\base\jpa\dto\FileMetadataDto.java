package vn.agis.crm.base.jpa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import vn.agis.crm.base.domain.imports.ImportJobSource;

import java.util.Date;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class FileMetadataDto {
    
    private String fileName;
    private ImportJobSource source;
    private String sourceLink;
    private String mimeType;
    private Long sizeBytes;
    private String fileChecksum;
    private Boolean duplicateWarning;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdAt;
}
