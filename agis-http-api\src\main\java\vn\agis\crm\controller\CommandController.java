package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.CreateCommandRequest;
import vn.agis.crm.base.jpa.dto.req.SearchCommandRequest;
import vn.agis.crm.base.jpa.dto.resp.SearchCommandResponse;
import vn.agis.crm.base.jpa.entity.Command;
import vn.agis.crm.service.CommandService;

import java.util.List;

@RestController
@RequestMapping("/command")
public class CommandController {
    private final Logger logger = LoggerFactory.getLogger(CommandController.class);

    @Autowired
    CommandService commandService;

    @Autowired
    public CommandController(CommandService service) {
        this.commandService = service;
    }

    @GetMapping("/get-by-device-type/{deviceTypeId}")
    public ResponseEntity<List<Command>> getDeviceTypeByDeviceTypeId(@PathVariable Long deviceTypeId, HttpServletRequest httpServletRequest) {
        List<Command> commandList = commandService.getCommandListByDeviceType(deviceTypeId);
        return ResponseEntity.ok().body(commandList);
    }

    @GetMapping("/search-command/{deviceId}")
    public ResponseEntity<Page<SearchCommandResponse>> searchCommand(
            @PathVariable Long deviceId,
            @RequestParam(name = "commandName", required = false, defaultValue = " ") String commandName,
            @RequestParam(name = "commandId", required = false, defaultValue = " ") String commandId,
            @RequestParam(name = "commandType", required = false, defaultValue = " ") String commandType,
            @RequestParam(name = "data", required = false, defaultValue = " ") String data,
            @RequestParam(name = "status", required = false, defaultValue = "-1") Integer status,
            @RequestParam(name = "createdAtStart", required = false, defaultValue = "-1") Long createdAtStart,
            @RequestParam(name = "createdAtEnd", required = false, defaultValue = "-1") Long createdAtEnd,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "commandName,desc") String sortBy) {
        SearchCommandRequest searchDTO = new SearchCommandRequest(deviceId, commandId, commandType, commandName, data, createdAtStart, createdAtEnd, status, page, size, sortBy);
        CrudController.ListRequest listRequest = new CrudController.ListRequest(size, page, sortBy);
        Page<SearchCommandResponse> responsePage = commandService.searchCommand(searchDTO, listRequest.getPageable());
        return ResponseEntity.ok().body(responsePage);
    }

    @PostMapping("/send-command")
    @PreAuthorize("hasAnyAuthority('sendCommand')")
    public ResponseEntity sendCommandToDevice(@RequestBody CreateCommandRequest createCommandRequest, HttpServletRequest httpServletRequest) {
        return ResponseEntity.ok().body(commandService.sendCommandToDevice(createCommandRequest));
    }
}
