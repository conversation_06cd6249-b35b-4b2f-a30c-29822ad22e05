package vn.agis.crm.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.repository.ConfigRepository;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.service.NotificationService;

import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.TimeZone;

/**
 * Scheduled job for automatic customer birthday notifications
 * Runs daily at 8:00 AM (Asia/Ho_Chi_Minh timezone) to send birthday reminders to employees
 */
@Component
public class CustomerBirthdayNotificationScheduler {

    private static final Logger logger = LoggerFactory.getLogger(CustomerBirthdayNotificationScheduler.class);
    
    private static final String CONFIG_KEY_DAYS_BEFORE = "NOTIFICATION_BIRTHDAY_DAYS_BEFORE";
    private static final String TIMEZONE_VIETNAM = "Asia/Ho_Chi_Minh";
    
    @Autowired
    private ConfigRepository configRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private NotificationService notificationService;

    /**
     * Scheduled job that runs daily at 8:00 AM to process customer birthday notifications
     * Cron expression: 0 0 8 * * * (second minute hour day month weekday)
     */
    @Scheduled(cron = "0 0 8 * * *", zone = TIMEZONE_VIETNAM)
    public void processCustomerBirthdayNotifications() {
        logger.info("🎂 Starting customer birthday notification job at {}", new Date());
        
        try {
            // Step 1: Check system configuration
            Integer daysBeforeBirthday = getDaysBeforeBirthdayFromConfig();
            if (daysBeforeBirthday == null) {
                logger.info("Birthday notification job disabled or not configured. Exiting.");
                return;
            }
            
            logger.info("Processing birthday notifications for {} days before birthday", daysBeforeBirthday);
            
            // Step 2: Find customers with upcoming birthdays
            List<Customers> birthdayCustomers = findCustomersWithUpcomingBirthdays(daysBeforeBirthday);
            
            if (birthdayCustomers.isEmpty()) {
                logger.info("No customers found with birthdays in {} days. Job completed.", daysBeforeBirthday);
                return;
            }
            
            logger.info("Found {} customers with upcoming birthdays", birthdayCustomers.size());
            
            // Step 3: Process notifications for each customer
            int successCount = 0;
            int errorCount = 0;
            
            for (Customers customer : birthdayCustomers) {
                try {
                    boolean notificationCreated = processCustomerBirthdayNotification(customer, daysBeforeBirthday);
                    if (notificationCreated) {
                        successCount++;
                    }
                } catch (Exception e) {
                    errorCount++;
                    logger.error("Failed to process birthday notification for customer {} ({}): {}", 
                               customer.getId(), customer.getFullName(), e.getMessage(), e);
                }
            }
            
            logger.info("🎂 Birthday notification job completed. Processed: {}, Success: {}, Errors: {}", 
                       birthdayCustomers.size(), successCount, errorCount);
            
        } catch (Exception e) {
            logger.error("Fatal error in birthday notification job: {}", e.getMessage(), e);
        }
    }

    /**
     * Get the number of days before birthday from system configuration
     * @return Number of days before birthday, or null if disabled/not configured
     */
    private Integer getDaysBeforeBirthdayFromConfig() {
        try {
            Config config = configRepository.findOneByConfigKeyIgnoreCase(CONFIG_KEY_DAYS_BEFORE);
            
            if (config == null) {
                logger.debug("Configuration key '{}' not found", CONFIG_KEY_DAYS_BEFORE);
                return null;
            }
            
            String configValue = config.getConfigValue();
            if (configValue == null || configValue.trim().isEmpty()) {
                logger.debug("Configuration key '{}' has empty value", CONFIG_KEY_DAYS_BEFORE);
                return null;
            }
            
            try {
                int daysBeforeBirthday = Integer.parseInt(configValue.trim());
                if (daysBeforeBirthday < 0 || daysBeforeBirthday > 365) {
                    logger.warn("Invalid days before birthday value: {}. Must be between 0-365", daysBeforeBirthday);
                    return null;
                }
                return daysBeforeBirthday;
            } catch (NumberFormatException e) {
                logger.warn("Invalid number format for days before birthday: '{}'", configValue);
                return null;
            }
            
        } catch (Exception e) {
            logger.error("Error reading birthday notification configuration: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find customers with birthdays in the specified number of days
     * @param daysBeforeBirthday Number of days before birthday
     * @return List of customers with upcoming birthdays
     */
    private List<Customers> findCustomersWithUpcomingBirthdays(int daysBeforeBirthday) {
        try {
            // Calculate target date (current date + daysBeforeBirthday)
            LocalDate targetDate = LocalDate.now(ZoneId.of(TIMEZONE_VIETNAM)).plusDays(daysBeforeBirthday);
            int targetDay = targetDate.getDayOfMonth();
            int targetMonth = targetDate.getMonthValue();
            
            logger.debug("Looking for customers with birthday on {}/{} (in {} days)", 
                        targetDay, targetMonth, daysBeforeBirthday);
            
            // Use existing search method with birthday day/month parameters
            // Note: Using a large page size to get all results, in production consider pagination
            return customerRepository.search(
                null, // fullName
                null, // phone
                null, // email
                null, // cccd
                null, // address
                null, // sourceType
                null, // sourceDetail
                null, // businessField
                null, // interests
                null, // relativeName
                null, // birthDateFrom
                null, // birthDateTo
                targetDay, // birthdayDay
                targetMonth, // birthdayMonth
                null, // projectId
                null, // purchasedProjectId
                null, // activeOfferProjectId
                null, // propertyType
                null, // employeeId
                org.springframework.data.domain.PageRequest.of(0, 10000)
            ).getContent().stream()
            .filter(customer -> customer.getDeletedAt() == null) // Only active customers
            .toList();
            
        } catch (Exception e) {
            logger.error("Error finding customers with upcoming birthdays: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Process birthday notification for a single customer
     * @param customer Customer with upcoming birthday
     * @param daysBeforeBirthday Number of days before birthday
     * @return true if notification was created, false otherwise
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processCustomerBirthdayNotification(Customers customer, int daysBeforeBirthday) {
        try {
            // Step 1: Determine target employees based on priority
            List<Long> targetEmployeeIds = determineTargetEmployees(customer);

            if (targetEmployeeIds.isEmpty()) {
                logger.warn("No target employees found for customer {} ({}). Skipping notification.",
                           customer.getId(), customer.getFullName());
                return false;
            }

            // Step 2: Create notification content
            String title = "Nhắc nhở sinh nhật khách hàng";
            String content = createBirthdayNotificationContent(customer, daysBeforeBirthday);

            // Step 3: Create notifications for all target employees
            int successCount = 0;
            int errorCount = 0;

            for (Long targetEmployeeId : targetEmployeeIds) {
                try {
                    Notifications notification = notificationService.createNotification(
                        targetEmployeeId,
                        2, // CustomerBirthday type
                        title,
                        content,
                        customer.getId(),
                        null // System-generated notification
                    );

                    logger.debug("Created birthday notification {} for employee {} about customer {} ({})",
                                notification.getId(), targetEmployeeId, customer.getId(), customer.getFullName());
                    successCount++;

                } catch (Exception e) {
                    errorCount++;
                    logger.error("Failed to create birthday notification for employee {} about customer {} ({}): {}",
                               targetEmployeeId, customer.getId(), customer.getFullName(), e.getMessage(), e);
                }
            }

            logger.debug("Birthday notification processing for customer {} ({}): {} success, {} errors",
                        customer.getId(), customer.getFullName(), successCount, errorCount);

            // Return true if at least one notification was created successfully
            return successCount > 0;

        } catch (Exception e) {
            logger.error("Error processing birthday notification for customer {} ({}): {}",
                        customer.getId(), customer.getFullName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Determine target employees for notification based on priority:
     * 1. Current Staff (current_staff_id) - single employee
     * 2. Current Manager (current_manager_id) - single employee
     * 3. All Admin employees (fallback) - multiple employees
     * @param customer Customer to find target employees for
     * @return List of Employee IDs (empty list if no suitable employees found)
     */
    private List<Long> determineTargetEmployees(Customers customer) {
        try {
            List<Long> targetEmployeeIds = new ArrayList<>();

            // Priority 1: Current Staff
            if (customer.getCurrentStaffId() != null) {
                Employee staff = employeeRepository.findById(customer.getCurrentStaffId()).orElse(null);
                if (staff != null && staff.getStatus() == Employee.Status.active && staff.getDeletedAt() == null) {
                    logger.debug("Using current staff {} for customer {}", staff.getId(), customer.getId());
                    targetEmployeeIds.add(staff.getId());
                    return targetEmployeeIds; // Return immediately - only staff gets notification
                }
            }

            // Priority 2: Current Manager
            if (customer.getCurrentManagerId() != null) {
                Employee manager = employeeRepository.findById(customer.getCurrentManagerId()).orElse(null);
                if (manager != null && manager.getStatus() == Employee.Status.active && manager.getDeletedAt() == null) {
                    logger.debug("Using current manager {} for customer {}", manager.getId(), customer.getId());
                    targetEmployeeIds.add(manager.getId());
                    return targetEmployeeIds; // Return immediately - only manager gets notification
                }
            }

            // Priority 3: All Admin employees (fallback)
            List<Employee> adminEmployees = findAdminEmployees();
            if (!adminEmployees.isEmpty()) {
                for (Employee admin : adminEmployees) {
                    targetEmployeeIds.add(admin.getId());
                }
                logger.debug("Using {} admin employees as fallback for customer {}: {}",
                           adminEmployees.size(), customer.getId(), targetEmployeeIds);
                return targetEmployeeIds; // Return all admin employee IDs
            }

            logger.warn("No suitable employees found for customer {} ({})", customer.getId(), customer.getFullName());
            return targetEmployeeIds; // Return empty list

        } catch (Exception e) {
            logger.error("Error determining target employees for customer {} ({}): {}",
                        customer.getId(), customer.getFullName(), e.getMessage(), e);
            return new ArrayList<>(); // Return empty list on error
        }
    }

    /**
     * Find admin employees as fallback for notifications
     * Note: This is a simplified implementation. In a real system, you might want to
     * query based on specific admin roles or permissions
     * @return List of admin employees
     */
    private List<Employee> findAdminEmployees() {
        try {
            // Find active employees with roleId = 1 (assuming 1 is admin role)
            // This is a simplified approach - in production, you might want to:
            // 1. Query based on specific permissions
            // 2. Use a dedicated admin role configuration
            // 3. Have a fallback admin employee configuration
            
            List<Employee> activeEmployees = employeeRepository.findByStatus(Employee.Status.active);
            return activeEmployees.stream()
                .filter(emp -> emp.getDeletedAt() == null)
                .filter(emp -> emp.getRoleId() != null && emp.getRoleId() == 1) // Assuming roleId 1 is admin
                .toList();
                
        } catch (Exception e) {
            logger.error("Error finding admin employees: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Create birthday notification content
     * @param customer Customer with upcoming birthday
     * @param daysBeforeBirthday Number of days before birthday
     * @return Formatted notification content
     */
    private String createBirthdayNotificationContent(Customers customer, int daysBeforeBirthday) {
        try {
            String customerName = customer.getFullName() != null ? customer.getFullName() : "N/A";
            String birthdayDate = formatBirthdayDate(customer.getBirthDate());
            
            if (daysBeforeBirthday == 0) {
                return String.format(
                    "Khách hàng %s có sinh nhật hôm nay (%s). ",
                    customerName, birthdayDate
                );
            } else {
                return String.format(
                    "Khách hàng %s sẽ có sinh nhật vào ngày %s (sau %d ngày). ",
                    customerName, birthdayDate, daysBeforeBirthday
                );
            }
            
        } catch (Exception e) {
            logger.error("Error creating birthday notification content: {}", e.getMessage(), e);
            return "Khách hàng có sinh nhật sắp tới";
        }
    }

    /**
     * Format birthday date for display (dd/MM format)
     * @param birthDate Customer's birth date
     * @return Formatted date string
     */
    private String formatBirthdayDate(Date birthDate) {
        if (birthDate == null) {
            return "N/A";
        }
        
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM");
            sdf.setTimeZone(TimeZone.getTimeZone(TIMEZONE_VIETNAM));
            return sdf.format(birthDate);
        } catch (Exception e) {
            logger.error("Error formatting birthday date: {}", e.getMessage(), e);
            return "N/A";
        }
    }
}
