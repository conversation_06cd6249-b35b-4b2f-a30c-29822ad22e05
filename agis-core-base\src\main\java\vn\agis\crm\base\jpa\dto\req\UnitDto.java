package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
public class UnitDto {
    private Long id;
    private Long projectId;
    private String code;
    private String productType;
    private String sector;
    private BigDecimal area;
    private String doorDirection; // default handled server-side if null
    private String view;
    private BigDecimal contractPrice;
    private Boolean isActive;

    // NEW: Unit detail fields
    private BigDecimal floorArea;
    private String floorNumber;
    private String unitNumber;
}

