package vn.agis.crm.base.jpa.dto.resp;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

/**
 * Response DTO for single notification deletion
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationDeleteResponse {
    
    /**
     * ID of the deleted notification
     */
    private Long id;
    
    /**
     * Whether the deletion was successful
     */
    private boolean success;
    
    /**
     * Message describing the result
     */
    private String message;
    
    /**
     * HTTP status code
     */
    private int statusCode;
    
    /**
     * Constructor for successful deletion
     */
    public NotificationDeleteResponse(Long id) {
        this.id = id;
        this.success = true;
        this.message = "Notification deleted successfully";
        this.statusCode = 200;
    }
    
    /**
     * Constructor for failed deletion
     */
    public NotificationDeleteResponse(Long id, String errorMessage, int statusCode) {
        this.id = id;
        this.success = false;
        this.message = errorMessage;
        this.statusCode = statusCode;
    }
}
