package vn.agis.crm.base.jpa.constants;


public interface JpaConstants {
    interface Method {
        String CREATE = "create";
        String CREATE_ONE = "createOne";
        String CREATE_MANY = "createMany";
        String UPDATE = "update";
        String UPDATE_ONE = "updateOne";
        String UPDATE_MANY = "updateMany";
        String DELETE = "delete";
        String DELETE_ONE = "deleteOne";
        String DELETE_MANY = "deleteMany";
        String GET_ONE = "getOne";
        String GET_MANY = "getMany";
        String GET_ALL = "getAll";
        String SEARCH = "search";
        String SEARCH_BY_RSQL = "searchByRsql";
        String SEARCH_AND_FILTER = "searchAndFilter";
        String CHECK_EXITS = "count";
        String GET_AREA_HIERARCHY = "getAreaHierarchy";
        String CHECK_EXITS_CODE = "countByCode";
        String ACTIVE = "active";
        String ASSIGN = "assign";
        String PENDING = "pending";
        String GET_BY_KEY = "getByKey";
        String GET_FIRST = "getFirst";
    }
}
