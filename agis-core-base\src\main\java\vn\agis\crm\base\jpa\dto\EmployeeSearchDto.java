package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class EmployeeSearchDto {
    private String employeeCode; // LIKE
    private String fullName;     // LIKE
    private String phone;        // LIKE
    private String email;        // LIKE

    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "createdAt,desc";

    public EmployeeSearchDto() {}

    public EmployeeSearchDto(String employeeCode, String fullName, String phone, String email,
                             Integer page, Integer size, String sortBy) {
        this.employeeCode = employeeCode;
        this.fullName = fullName;
        this.phone = phone;
        this.email = email;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

