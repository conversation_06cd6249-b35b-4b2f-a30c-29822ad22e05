package vn.agis.crm.util;

import vn.agis.crm.base.jpa.dto.ImportErrorDto;

import java.util.*;
import java.util.function.Function;
import java.util.regex.Pattern;

/**
 * Configurable validation rules for import processing
 */
public class ImportValidationRules {
    
    // Validation rule definitions
    private static final Map<String, ValidationRule> VALIDATION_RULES = new HashMap<>();
    
    static {
        // Phone validation rules
        VALIDATION_RULES.put("phone.required", new ValidationRule(
            "PHONE", ImportErrorType.MISSING_REQUIRED_FIELD, ImportSeverity.ERROR,
            value -> value != null && !value.trim().isEmpty(),
            "Phone number is required"
        ));
        
        VALIDATION_RULES.put("phone.format", new ValidationRule(
            "PHONE", ImportErrorType.INVALID_PHONE_FORMAT, ImportSeverity.ERROR,
            value -> value == null || value.trim().isEmpty() || ImportDataParser.isValidPhone(value),
            "Phone number format is invalid"
        ));
        
        // Name validation rules
        VALIDATION_RULES.put("name.required", new ValidationRule(
            "HỌ VÀ TÊN KHÁCH HÀNG", ImportErrorType.MISSING_REQUIRED_FIELD, ImportSeverity.ERROR,
            value -> value != null && !value.trim().isEmpty(),
            "Customer name is required"
        ));
        
        VALIDATION_RULES.put("name.length", new ValidationRule(
            "HỌ VÀ TÊN KHÁCH HÀNG", ImportErrorType.INVALID_FORMAT, ImportSeverity.WARNING,
            value -> value == null || value.trim().length() <= 255,
            "Customer name is too long (max 255 characters)"
        ));
        
        // Email validation rules
        VALIDATION_RULES.put("email.format", new ValidationRule(
            "EMAIL", ImportErrorType.INVALID_EMAIL_FORMAT, ImportSeverity.WARNING,
            value -> value == null || value.trim().isEmpty() || ImportDataParser.isValidEmail(value),
            "Email format is invalid"
        ));
        
        // Date validation rules
        VALIDATION_RULES.put("birth_date.format", new ValidationRule(
            "NGÀY THÁNG NĂM SINH", ImportErrorType.INVALID_DATE_FORMAT, ImportSeverity.WARNING,
            value -> value == null || value.trim().isEmpty() || ImportDataValidator.parseDate(value) != null,
            "Birth date format is invalid"
        ));
        
        // Number validation rules
        VALIDATION_RULES.put("price.format", new ValidationRule(
            "GIÁ GỐC TRÊN HỢP ĐỒNG", ImportErrorType.INVALID_NUMBER_FORMAT, ImportSeverity.WARNING,
            value -> value == null || value.trim().isEmpty() || ImportDataValidator.parseNumber(value) != null,
            "Price format is invalid"
        ));
    }
    
    /**
     * Get validation rules for a specific field
     */
    public static List<ValidationRule> getRulesForField(String fieldName) {
        List<ValidationRule> rules = new ArrayList<>();
        
        for (Map.Entry<String, ValidationRule> entry : VALIDATION_RULES.entrySet()) {
            if (entry.getValue().getFieldName().equals(fieldName)) {
                rules.add(entry.getValue());
            }
        }
        
        return rules;
    }
    
    /**
     * Get all validation rules
     */
    public static Map<String, ValidationRule> getAllRules() {
        return new HashMap<>(VALIDATION_RULES);
    }
    
    /**
     * Add or update a validation rule
     */
    public static void addRule(String ruleId, ValidationRule rule) {
        VALIDATION_RULES.put(ruleId, rule);
    }
    
    /**
     * Remove a validation rule
     */
    public static void removeRule(String ruleId) {
        VALIDATION_RULES.remove(ruleId);
    }
    
    /**
     * Check if a rule is enabled (based on configuration)
     */
    public static boolean isRuleEnabled(String ruleId) {
        // Check configuration to see if rule is enabled
        String configKey = "import.validation.rule." + ruleId + ".enabled";
        Boolean enabled = ImportConfigurationManager.getBooleanConfig(configKey);
        return enabled != null ? enabled : true; // Default to enabled
    }
    
    /**
     * Get rule severity from configuration (allows overriding default severity)
     */
    public static ImportSeverity getRuleSeverity(String ruleId, ImportSeverity defaultSeverity) {
        String configKey = "import.validation.rule." + ruleId + ".severity";
        String severityStr = ImportConfigurationManager.getStringConfig(configKey);
        
        if (severityStr != null) {
            try {
                return ImportSeverity.valueOf(severityStr.toUpperCase());
            } catch (IllegalArgumentException e) {
                // Invalid severity in config, use default
            }
        }
        
        return defaultSeverity;
    }
    
    /**
     * Validation rule definition
     */
    public static class ValidationRule {
        private final String fieldName;
        private final ImportErrorType errorType;
        private final ImportSeverity severity;
        private final Function<String, Boolean> validator;
        private final String errorMessage;
        
        public ValidationRule(String fieldName, ImportErrorType errorType, ImportSeverity severity,
                            Function<String, Boolean> validator, String errorMessage) {
            this.fieldName = fieldName;
            this.errorType = errorType;
            this.severity = severity;
            this.validator = validator;
            this.errorMessage = errorMessage;
        }
        
        public String getFieldName() {
            return fieldName;
        }
        
        public ImportErrorType getErrorType() {
            return errorType;
        }
        
        public ImportSeverity getSeverity() {
            return severity;
        }
        
        public Function<String, Boolean> getValidator() {
            return validator;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        /**
         * Validate a value using this rule
         */
        public ImportErrorDto validate(String value, Long jobId, Integer rowNumber) {
            if (!validator.apply(value)) {
                return new ImportErrorDto(jobId, rowNumber, fieldName, value,
                    errorType.getCode(), errorMessage, severity.getCode());
            }
            return null;
        }
    }
    
    /**
     * Business rule validation
     */
    public static class BusinessRules {
        
        /**
         * Validate that project and unit combination is valid
         */
        public static ImportErrorDto validateProjectUnit(Map<String, String> rowData, Long jobId, Integer rowNumber) {
            String projectName = rowData.get("TÊN DỰ ÁN");
            String unitCode = rowData.get("MÃ CĂN");
            
            // If both are provided, they should be valid together
            if (projectName != null && !projectName.trim().isEmpty() && 
                unitCode != null && !unitCode.trim().isEmpty()) {
                
                // TODO: Implement actual project/unit lookup
                // For now, just check basic format
                if (unitCode.length() > 20) {
                    return new ImportErrorDto(jobId, rowNumber, "MÃ CĂN", unitCode,
                        ImportErrorType.INVALID_FORMAT.getCode(),
                        "Mã căn quá dài (tối đa 20 ký tự)",
                        ImportSeverity.WARNING.getCode());
                }
            }
            
            return null;
        }
        
        /**
         * Validate employee code format
         */
        public static ImportErrorDto validateEmployeeCode(Map<String, String> rowData, Long jobId, Integer rowNumber) {
            String employeeCode = rowData.get("MÃ SỐ NHÂN VIÊN");
            
            if (employeeCode != null && !employeeCode.trim().isEmpty()) {
                // Basic format validation - should be alphanumeric
                if (!Pattern.matches("^[A-Za-z0-9]+$", employeeCode.trim())) {
                    return new ImportErrorDto(jobId, rowNumber, "MÃ SỐ NHÂN VIÊN", employeeCode,
                        ImportErrorType.INVALID_FORMAT.getCode(),
                        "Mã số nhân viên chỉ được chứa chữ cái và số",
                        ImportSeverity.WARNING.getCode());
                }
            }
            
            return null;
        }
        
        /**
         * Validate date consistency (birth date should be reasonable)
         */
        public static ImportErrorDto validateBirthDate(Map<String, String> rowData, Long jobId, Integer rowNumber) {
            String birthDateStr = rowData.get("NGÀY THÁNG NĂM SINH");
            
            if (birthDateStr != null && !birthDateStr.trim().isEmpty()) {
                Date birthDate = ImportDataValidator.parseDate(birthDateStr);
                if (birthDate != null) {
                    Calendar cal = Calendar.getInstance();
                    cal.add(Calendar.YEAR, -120); // 120 years ago
                    Date minDate = cal.getTime();
                    
                    cal = Calendar.getInstance();
                    cal.add(Calendar.YEAR, -10); // 10 years ago (minimum reasonable age)
                    Date maxDate = cal.getTime();
                    
                    if (birthDate.before(minDate) || birthDate.after(maxDate)) {
                        return new ImportErrorDto(jobId, rowNumber, "NGÀY THÁNG NĂM SINH", birthDateStr,
                            ImportErrorType.BUSINESS_RULE_VIOLATION.getCode(),
                            "Ngày sinh không hợp lý (nên từ 10-120 năm trước)",
                            ImportSeverity.WARNING.getCode());
                    }
                }
            }
            
            return null;
        }
    }
}
