//package vn.agis.crm.rmi;
//
//import redis.clients.jedis.Jedis;
//import redis.clients.jedis.ScanParams;
//import redis.clients.jedis.ScanResult;
//
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//import java.util.ArrayList;
//
//public class RedisCommandLine {
//
//    public static void main(String[] args) {
//        if (args.length < 3) {
//            printUsage();
//            return;
//        }
//
//        String host = args[0];
//        int port = Integer.parseInt(args[1]);
//        String command = args[2].toUpperCase(); // Convert command to uppercase for case-insensitive matching
//
//        try (Jedis jedis = new Jedis(host, port)) {
//            switch (command) {
//                case "GET":
//                    if (args.length == 4) {
//                        String key = args[3];
//                        String value = jedis.get(key);
//                        System.out.println("Result: " + value);
//                    } else {
//                        System.out.println("Usage: GET <key>");
//                    }
//                    break;
//                case "SET":
//                    if (args.length == 5) {
//                        String key = args[3];
//                        String value = args[4];
//                        String result = jedis.set(key, value);
//                        System.out.println("Result: " + result);
//                    } else {
//                        System.out.println("Usage: SET <host> <port> SET <key> <value>");
//                    }
//                    break;
//                case "DEL":
//                    if (args.length >= 4) {
//                        String patternOrKey = args[3];
//                        if (patternOrKey.contains("*")) {
//                            // Delete by pattern using SCAN
//                            Set<String> keysToDelete = scanKeys(jedis, patternOrKey);
//                            if (!keysToDelete.isEmpty()) {
//                                Long deletedCount = jedis.del(keysToDelete.toArray(new String[0]));
//                                System.out.println("Deleted " + deletedCount + " keys matching pattern '" + patternOrKey + "'.");
//                            } else {
//                                System.out.println("No keys found matching pattern '" + patternOrKey + "'.");
//                            }
//                        } else {
//                            // Delete specific keys
//                            String[] keys = new String[args.length - 3];
//                            System.arraycopy(args, 3, keys, 0, args.length - 3);
//                            Long result = jedis.del(keys);
//                            System.out.println("Deleted " + result + " keys.");
//                        }
//                    } else {
//                        System.out.println("Usage: DEL <host> <port> DEL <key1> [key2...] or DEL <pattern*>");
//                    }
//                    break;
//                case "SCAN":
//                    if (args.length >= 4) {
//                        String cursor = args[3];
//                        ScanParams scanParams = new ScanParams();
//                        if (args.length == 5) {
//                            scanParams.match(args[4]);
//                        }
//                        ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
//                        System.out.println("Cursor: " + scanResult.getCursor());
//                        System.out.println("Keys: " + scanResult.getResult());
//                    } else {
//                        System.out.println("Usage: SCAN <host> <port> SCAN <cursor> [pattern]");
//                    }
//                    break;
//                default:
//                    System.out.println("Unsupported command: " + command);
//                    printUsage();
//            }
//
//        } catch (Exception e) {
//            System.out.println("Error interacting with Redis: " + e.getMessage());
//            e.printStackTrace(); // Print stack trace for debugging
//        }
//    }
//
//    private static Set<String> scanKeys(Jedis jedis, String pattern) {
//        Set<String> keys = new HashSet<>();
//        String cursor = "0";
//        ScanParams scanParams = new ScanParams().match(pattern);
//        do {
//            ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
//            keys.addAll(scanResult.getResult());
//            cursor = scanResult.getCursor();
//        } while (!cursor.equals("0"));
//        return keys;
//    }
//
//    private static void printUsage() {
//        System.out.println("Usage: RedisCommandLine <host> <port> <command> [args...]");
//        System.out.println("Supported commands:");
//        System.out.println("  GET <key>");
//        System.out.println("  SET <key> <value>");
//        System.out.println("  DEL <key1> [key2...] or DEL <pattern*>");
//        System.out.println("  SCAN <cursor> [pattern]");
//    }
//}
