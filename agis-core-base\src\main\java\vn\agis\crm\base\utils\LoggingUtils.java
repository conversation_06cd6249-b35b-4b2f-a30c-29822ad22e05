package vn.agis.crm.base.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: kiendt
 * Date: 4/6/2021
 * Contact: <EMAIL>
 */
public class LoggingUtils {

    private static Gson gson = new Gson();
    private static Map<String, Logger> mapLogs = new ConcurrentHashMap<>();

    private static final Object lock = new Object();

    public static void info(Class<?> mainClass, Object message) {
        Logger logger = getLogger(mainClass);
        if (logger.isInfoEnabled()) {
            logger.info(toString(message));
        }
    }

    public static void info(Class<?> mainClass, String message) {
        Logger logger = getLogger(mainClass);
        if (logger.isInfoEnabled()) {
            logger.info(message);
        }
    }

    //
    public static void info(Class<?> mainClass, String format, Object param) {
        Logger logger = getLogger(mainClass);
        if (logger.isInfoEnabled()) {
            logger.info(format, toString(param));
        }
    }

    public static void debug(Class<?> mainClass, String message) {
        Logger logger = getLogger(mainClass);
        if (logger.isDebugEnabled()) {
            logger.debug(message);
        }
    }

    public static void debug(Class<?> mainClass, Object message) {
        Logger logger = getLogger(mainClass);
        if (logger.isDebugEnabled()) {
            logger.debug(toString(message));
        }
    }

    //
    public static void debug(Class<?> mainClass, String format, Object param) {
        Logger logger = getLogger(mainClass);
        if (logger.isDebugEnabled()) {
            logger.debug(format, toString(param));
        }
    }

    public static void warn(Class<?> mainClass, String message) {
        Logger logger = getLogger(mainClass);
        if (logger.isWarnEnabled()) {
            logger.warn(message);
        }
    }

    public static void warn(Class<?> mainClass, Object message) {
        Logger logger = getLogger(mainClass);
        if (logger.isWarnEnabled()) {
            logger.warn(toString(message));
        }
    }

    public static void trace(Class<?> mainClass, String message) {
        Logger logger = getLogger(mainClass);
        if (logger.isTraceEnabled()) {
            logger.trace(message);
        }
    }


    public static void trace(Class<?> mainClass, Object message) {
        Logger logger = getLogger(mainClass);
        if (logger.isTraceEnabled()) {
            logger.trace(toString(message));
        }
    }

    public static void trace(Class<?> mainClass, String format, Object param) {
        Logger logger = getLogger(mainClass);
        if (logger.isTraceEnabled()) {
            logger.trace(format, toString(param));
        }
    }


    public static void error(Class<?> mainClass, String message) {
        Logger logger = getLogger(mainClass);
        if (logger.isErrorEnabled()) {
            logger.error(message);
        }
    }

    public static void error(Class<?> mainClass, Object message, Throwable cause) {
        Logger logger = getLogger(mainClass);
        if (logger.isErrorEnabled()) {
            logger.error(toString(message), cause);
        }
    }

    private static Logger getLogger(Class<?> mainClass) {
        Logger logger = mapLogs.get(mainClass.getName());
        if (logger == null) {
            synchronized (lock) {
                logger = LoggerFactory.getLogger(mainClass.getName());
                mapLogs.put(mainClass.getName(), logger);
            }
        }
        return logger;
    }

    public static <T> String toString(T obj) {
        return toStringUsingObjectMapper(obj).replaceAll(",\\\"password\\\":\\\"[^\\\"]*\\\"", "");
    }

//    public static <T> String toStringUsingGson(T obj) {
//        return gson.toJson(obj);
//    }

    public static <T> String toStringUsingObjectMapper(T object) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(object);
        } catch (Exception ex) {
            error(LoggingUtils.class, ex.getMessage(), ex);
            return "";
        }
    }
}
