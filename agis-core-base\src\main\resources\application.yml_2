spring:
  data:
    mongodb:
      host: ************
      port: 27017
      database: resourcedb
  rabbitmq:
    host: ************
    port: 5672
    username: admin
    password: onegate@2020
  redis:
    host: ************
    port: 6379
    password: onegate@2020
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************
    username: onegate
    password: onegate@123
  jpa:
    show-sql: false
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL5Dialect
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: -1
logging:
  level:
    root: ERROR
    org.springframework: INFO
    vn.agis.ocsgw: DEBUG