package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.RuleRun;

@Repository
public interface RuleRunRepository extends JpaRepository<RuleRun, Long> {

    Page<RuleRun> findByJobId(Long jobId, Pageable pageable);

    /**
     * Count rule runs for a specific customer
     */
    @Query("SELECT COUNT(rr) FROM RuleRun rr WHERE rr.customerId = :customerId")
    long countRuleRunsByCustomerId(@Param("customerId") Long customerId);

}

