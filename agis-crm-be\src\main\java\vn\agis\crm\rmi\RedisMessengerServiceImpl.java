package vn.agis.crm.rmi;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.redis.RedisCache;

import jakarta.annotation.PostConstruct; // Import PostConstruct

import java.rmi.RemoteException;
import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;
import java.rmi.server.UnicastRemoteObject;
import java.util.Collection;
import java.util.List;

@Component
public class RedisMessengerServiceImpl extends UnicastRemoteObject implements RedisMessengerService {

    @Autowired
    private RedisCache redisCache;

    public RedisMessengerServiceImpl() throws RemoteException {
        super();
    }

    @Override
    public String get(String key) throws RemoteException {
        try {
            // Assuming cached objects are Strings
            return (String) redisCache.get(key, String.class);
        } catch (Exception e) {
            throw new RemoteException("Error getting key from Red<PERSON>", e);
        }
    }

    @Override
    public String set(String key, String value) throws RemoteException {
        try {
            // Assuming cached objects are Strings
            redisCache.put(key, value, String.class);
            return "OK";
        } catch (Exception e) {
            throw new RemoteException("Error setting key in Redis", e);
        }
    }

    @Override
    public Long del(String... keys) throws RemoteException {
        try {
            // RedisCache.removeAll expects a Collection of keys
            Collection<String> keyCollection = java.util.Arrays.asList(keys);
            redisCache.removeAll(keyCollection, String.class); // Pass String.class as a placeholder type
            // RedisCache.removeAll doesn't return the count, so we'll return a placeholder or modify RedisCache
            // For now, returning the number of keys requested to delete
            return (long) keys.length;
        } catch (Exception e) {
            throw new RemoteException("Error deleting keys from Redis", e);
        }
    }

    @Override
    public List<String> scan(String pattern) throws RemoteException {
        try {
            // RedisCache.scanKey returns a List of full keys
            return redisCache.scanKey(pattern, 1000L); // Using a count of 1000, can be adjusted
        } catch (Exception e) {
            throw new RemoteException("Error scanning keys in Redis", e);
        }
    }

    @PostConstruct
    public void createStubAndBind() throws RemoteException {
        try {
            Registry registry = LocateRegistry.createRegistry(1100); // Use a different port than RmiMessengerService (1099)
            registry.rebind("RedisMessengerService", this);
            System.out.println("RedisMessengerService bound to RMI registry on port 1100");
        } catch (RemoteException e) {
            System.err.println("Error binding RedisMessengerService to RMI registry: " + e.getMessage());
            throw e; // Re-throw the exception to indicate failure
        }
    }
}
