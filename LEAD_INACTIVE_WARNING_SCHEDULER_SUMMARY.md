# Lead Inactive Warning Notification Scheduler

## Overview

A comprehensive scheduled job system for automatically sending lead inactive warning notifications to employees in the AGIS CRM system. The scheduler runs daily at 8:00 AM (Vietnam timezone) and creates notifications for leads that haven't had any interactions for a configurable number of days, calculating from either the last interaction date or the lead creation date.

## ✅ **Implementation Details**

### **Scheduler Configuration**
```java
@Component
@Scheduled(cron = "0 0 8 * * *", zone = "Asia/Ho_Chi_Minh")
public class LeadInactiveWarningNotificationScheduler {
    // Daily execution at 8:00 AM Vietnam time
}
```

### **System Configuration**
- **Config Key**: `"NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS"`
- **Config Value**: Number of days of inactivity to trigger warning (e.g., "7" for 7 days)
- **Config Type**: 1 (single value)
- **Validation**: Must be integer between 1-365

### **Inactivity Calculation Logic**
The scheduler determines lead inactivity using these rules:

1. **With Interactions**: Calculate from last interaction date (`happened_at`)
2. **Without Interactions**: Calculate from lead creation date (`created_at`)
3. **Reference Date**: `max(last_interaction_date, creation_date)` or `creation_date` if no interactions
4. **Inactive Threshold**: `reference_date <= (current_date - inactive_days)`

## ✅ **Core Functionality**

### **1. Configuration Validation**
```java
private Integer getInactiveDaysFromConfig() {
    Config config = configRepository.findOneByConfigKeyIgnoreCase("NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS");
    // Validates existence, format, and range (1-365 days)
}
```

### **2. Active Lead Detection**
```java
private List<Customers> findActiveLeadCustomers() {
    // Uses CustomerRepository.search() with sourceType = "Leads"
    // Filters out deleted customers (deleted_at IS NULL)
}
```

### **3. Last Interaction Finding**
```java
private Date findLastInteractionDate(Customers customer) {
    // Check primary interactions through customer_offers -> interactions_primary
    // Check secondary interactions through customer_properties -> interactions_secondary
    // Returns the most recent interaction date across both types
}
```

### **4. Inactivity Assessment**
```java
private InactiveLeadInfo checkLeadInactivity(Customers customer, Date cutoffDate) {
    Date lastInteractionDate = findLastInteractionDate(customer);
    Date referenceDate = lastInteractionDate != null ? lastInteractionDate : customer.getCreatedAt();
    
    if (referenceDate.before(cutoffDate)) {
        // Lead is inactive - create InactiveLeadInfo
    }
}
```

### **5. Warning Notification Creation**
```java
Notifications notification = notificationService.createNotification(
    employee.getId(),
    4, // LeadInactiveWarning type
    "Cảnh báo lead không hoạt động",
    content,
    customer.getId(),
    null // System-generated
);
```

## ✅ **Notification Content Templates**

### **Lead with Previous Interactions**
```
"Lead [Tên khách hàng] (SĐT: [phone]) đã không có tương tác nào từ ngày [dd/MM/yyyy] ([x] ngày). Hãy liên hệ để duy trì mối quan hệ với khách hàng."
```

### **Lead without Any Interactions**
```
"Lead [Tên khách hàng] (SĐT: [phone]) được tạo từ ngày [dd/MM/yyyy] ([x] ngày) nhưng chưa có tương tác nào. Hãy liên hệ khách hàng để bắt đầu chăm sóc."
```

### **Example Content**
```
"Lead Nguyễn Văn A (SĐT: 0901234567) đã không có tương tác nào từ ngày 15/12/2024 (7 ngày). Hãy liên hệ để duy trì mối quan hệ với khách hàng."
```

## ✅ **Database Integration**

### **Repository Dependencies**
- **CustomerRepository**: Find active lead customers with source_type = "Leads"
- **CustomerOfferRepository**: Get customer offers for primary interaction checking
- **CustomerPropertyRepository**: Get customer properties for secondary interaction checking
- **InteractionsPrimaryRepository**: Find primary interactions by customer offer ID
- **InteractionsSecondaryRepository**: Find secondary interactions by customer property ID
- **EmployeeRepository**: Validate assigned employee status
- **ConfigRepository**: Read inactive warning days configuration
- **NotificationService**: Create Type 4 (LeadInactiveWarning) notifications

### **Interaction Detection Logic**

**Primary Interactions (Customer Offers)**:
```java
List<CustomerOffers> customerOffers = customerOfferRepository.findByCustomerId(customer.getId());
for (CustomerOffers offer : customerOffers) {
    List<InteractionsPrimary> primaryInteractions = interactionsPrimaryRepository.findByCustomerOfferId(offer.getId());
    // Find most recent interaction.happenedAt
}
```

**Secondary Interactions (Customer Properties)**:
```java
List<CustomerProperties> customerProperties = customerPropertyRepository.findByCustomerId(customer.getId());
for (CustomerProperties property : customerProperties) {
    List<InteractionsSecondary> secondaryInteractions = interactionsSecondaryRepository.findByCustomerPropertyId(property.getId());
    // Find most recent interaction.happenedAt
}
```

## ✅ **Employee Assignment Logic**

### **Priority System for Notifications**
```java
private Long findAssignedEmployeeId(Customers customer) {
    // Priority 1: Current Staff (current_staff_id)
    if (customer.getCurrentStaffId() != null) {
        Employee staff = employeeRepository.findById(customer.getCurrentStaffId()).orElse(null);
        if (staff != null && staff.getStatus() == Employee.Status.active && staff.getDeletedAt() == null) {
            return staff.getId();
        }
    }
    
    // Priority 2: Current Manager (current_manager_id)
    if (customer.getCurrentManagerId() != null) {
        Employee manager = employeeRepository.findById(customer.getCurrentManagerId()).orElse(null);
        if (manager != null && manager.getStatus() == Employee.Status.active && manager.getDeletedAt() == null) {
            return manager.getId();
        }
    }
    
    return null; // No active assigned employee found
}
```

**Note**: Unlike the birthday scheduler, this does NOT fall back to admin employees. Only assigned staff/managers receive notifications.

## ✅ **Error Handling & Reliability**

### **Transaction Management**
```java
@Transactional(propagation = Propagation.REQUIRES_NEW)
public boolean processInactiveLeadNotification(InactiveLeadInfo leadInfo, int inactiveDays) {
    // Each notification processed in separate transaction
    // Prevents rollback of entire batch on individual failures
}
```

### **Comprehensive Error Handling**
- **Configuration Errors**: Invalid/missing config gracefully handled
- **Customer Processing Errors**: Individual failures don't stop batch processing
- **Employee Validation Errors**: Inactive/missing employees skipped with logging
- **Interaction Check Errors**: Safe error handling with null return
- **Notification Creation Errors**: Logged but don't crash job

### **Logging Strategy**
```java
// Job-level logging
logger.info("⏰ Starting lead inactive warning notification job");
logger.info("Found {} inactive leads", inactiveLeads.size());
logger.info("⏰ Job completed. Processed: {}, Success: {}, Errors: {}", total, success, errors);

// Individual processing logging
logger.debug("Customer {} is inactive since {}", customer.getId(), referenceDate);
logger.debug("Created inactive lead notification {} for employee {}", notification.getId(), employee.getId());
logger.warn("Employee {} for customer {} is not active, skipping notification", employeeId, customerId);
```

## ✅ **Performance Optimizations**

### **Efficient Database Queries**
- **Single Configuration Query**: One-time config lookup per job run
- **Optimized Customer Search**: Uses existing indexed search with source_type filtering
- **Lazy Interaction Loading**: Only checks interactions for lead customers
- **Minimal N+1 Queries**: Efficient repository usage patterns

### **Memory Management**
- **Streaming Processing**: Processes customers one by one
- **Transaction Boundaries**: Separate transactions prevent memory buildup
- **Efficient Filtering**: Early filtering reduces unnecessary processing

## ✅ **Configuration Examples**

### **Enable 7-Day Inactive Warning**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS', '7', 1, 'Số ngày cảnh báo lead không hoạt động', NOW(), 1);
```

### **Short-Term Warnings (3 days)**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS', '3', 1, 'Cảnh báo lead không hoạt động sau 3 ngày', NOW(), 1);
```

### **Long-Term Warnings (30 days)**
```sql
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS', '30', 1, 'Cảnh báo lead không hoạt động sau 30 ngày', NOW(), 1);
```

### **Disable Warnings**
```sql
-- Option 1: Delete the config
DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Option 2: Set empty value
UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Option 3: Set invalid value
UPDATE configs SET config_value = 'DISABLED' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';
```

## ✅ **Integration Features**

### **Spring Boot Integration**
- Uses existing `@EnableScheduling` from other schedulers
- Scheduler automatically discovered and registered
- Shares timezone configuration with other schedulers

### **Timezone Support**
- **Vietnam Timezone**: All date calculations use `Asia/Ho_Chi_Minh`
- **Consistent Timing**: Job runs at 8:00 AM local time
- **Date Formatting**: Dates formatted according to Vietnamese conventions (dd/MM/yyyy)

### **Existing Service Integration**
- **NotificationService**: Uses existing `createNotification()` method
- **ConfigRepository**: Leverages existing configuration system
- **All Repositories**: Uses existing repository methods and patterns

## ✅ **InactiveLeadInfo Data Structure**

### **Inner Class for Lead Information**
```java
private static class InactiveLeadInfo {
    private final Customers customer;           // Customer entity
    private final Long assignedEmployeeId;     // Employee to notify
    private final Date referenceDate;          // Last interaction or creation date
    private final boolean hasInteractions;     // Whether customer has any interactions
    
    // Used to determine notification content template
}
```

**Benefits**:
- **Encapsulation**: Groups related data together
- **Type Safety**: Prevents parameter confusion
- **Clear Intent**: Makes data flow explicit
- **Content Customization**: Enables different message templates

## ✅ **Monitoring & Observability**

### **Job Execution Metrics**
```java
// Logged metrics per job run:
- Total customers processed
- Successful notifications created
- Error count
- Job start/completion times
- Configuration values used
```

### **Health Indicators**
```java
// Warning conditions logged:
- No inactive leads found
- Configuration disabled/missing
- Customers without assigned employees
- Individual notification creation failures
```

### **Debug Information**
```java
// Debug-level logging includes:
- Cutoff date calculations
- Lead filtering logic
- Interaction detection details
- Individual customer processing
```

## ✅ **Production Considerations**

### **Scalability**
- **Batch Processing**: Processes all lead customers in single job run
- **Future Enhancement**: Consider pagination for large customer bases
- **Database Load**: Optimized queries minimize database impact
- **Memory Usage**: Streaming approach prevents memory issues

### **Reliability**
- **Idempotent**: Safe to run multiple times (notifications have unique constraints)
- **Fault Tolerant**: Individual failures don't affect other customers
- **Recoverable**: Failed customers can be reprocessed manually if needed

### **Maintenance**
- **Configuration Changes**: No code changes needed to adjust timing
- **Customer Changes**: Automatically includes new lead customers
- **Interaction Changes**: Automatically detects new interactions

## ✅ **Testing Recommendations**

### **Unit Tests**
1. Configuration parsing and validation
2. Date calculation logic (timezone handling)
3. Lead filtering logic (source type, deletion status)
4. Last interaction detection logic
5. Inactivity assessment logic
6. Notification content generation
7. Error handling scenarios

### **Integration Tests**
1. End-to-end job execution with test data
2. Database transaction behavior
3. NotificationService integration
4. Configuration change effects
5. Multiple lead scenarios with different interaction patterns

### **Manual Testing**
1. Test with various configuration values (3, 7, 14, 30 days)
2. Test with leads having different interaction patterns
3. Test with leads without assigned employees
4. Test timezone behavior across different server locations

## ✅ **Business Impact**

### **Lead Management Enhancement**
- **Proactive Monitoring**: Automatic detection of inactive leads based on configurable timeframes
- **Intelligent Calculation**: Uses last interaction date or creation date for accurate inactivity assessment
- **Comprehensive Coverage**: Checks both primary and secondary interactions for complete activity tracking
- **Employee Accountability**: Direct notifications to assigned staff/managers with detailed lead information

### **Customer Retention Improvement**
- **Timely Intervention**: Prevents leads from being forgotten or neglected
- **Relationship Maintenance**: Encourages regular customer contact and engagement
- **Conversion Optimization**: Systematic follow-up increases lead conversion rates
- **Service Quality**: Ensures consistent lead care across all employees

### **Operational Efficiency**
- **Automated Monitoring**: Reduces manual lead tracking overhead
- **Configurable Timing**: Adjustable warning periods based on business requirements
- **Targeted Notifications**: Only notifies assigned employees (no spam to admins)
- **Performance Insights**: Provides data for lead management analysis

This implementation provides a robust, configurable, and maintainable solution for automatic lead inactive warning notifications while following AGIS CRM architectural patterns and coding standards.
