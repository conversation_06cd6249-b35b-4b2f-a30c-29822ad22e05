package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Table(name = "REPORT_CONFIG")
@Entity
@Data
public class Report extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "NAME")
    private String name;
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "STATUS")
    private Integer status;
    @Column(name = "ENABLE_PREVIEW")
    private Integer enablePreview;
    @Column(name = "SCHEMA")
    private String schema;
    @Lob
    @Column(name = "QUERY")
    private String query;
    @Column(name = "SCHEDULE")
    private String schedule;
    @Column(name = "FILTER_PARAMS")
    private String filterParams;
    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
    @Column(name = "UPDATED_BY")
    private Long updatedBy;
    @Column(name = "TIME_ONCE")
    private Date timeOnce;
    @Column(name = "SCHEDULE_DESC")
    private String scheduleDesc;
    @Transient
    private List<ReportContent> reportContents;
    @Transient
    private ReportSending reportSending;
}
