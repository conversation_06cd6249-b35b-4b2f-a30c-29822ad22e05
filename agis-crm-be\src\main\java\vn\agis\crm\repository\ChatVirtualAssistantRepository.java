package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import vn.agis.crm.base.jpa.entity.ChatVirtualAssistant;

import java.util.List;

public interface ChatVirtualAssistantRepository extends JpaRepository<ChatVirtualAssistant, Long> {
    List<ChatVirtualAssistant> findAllByCreatedBy(Long userId);
    ChatVirtualAssistant findFirstByCreatedBy(Long userId);
    ChatVirtualAssistant findFirstByIdAndCreatedBy(Long id, Long userId);
}
