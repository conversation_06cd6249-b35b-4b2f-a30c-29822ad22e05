package vn.agis.crm.sync;

import java.util.Map;


public interface AsyncService<T extends AsyncObjectType> {

    /**
     * Executing Asynchronous Tasks
     */
    void executeAsync(T obj);

    /**
     * Executing Asynchronous Tasks
     */
    void executeAsync(T obj, String... args);

    /**
     * Executing Asynchronous Tasks
     */
    void executeAsync(T obj, Object... args);

    /**
     * Executing Asynchronous Tasks
     */
    String executeAsync(T obj, Map<String, String> map);

}