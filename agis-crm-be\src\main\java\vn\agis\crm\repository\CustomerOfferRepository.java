package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.CustomerOffers;

import java.util.List;

@Repository
public interface CustomerOfferRepository extends JpaRepository<CustomerOffers, Long> {
    List<CustomerOffers> findByCustomerId(Long customerId);

    /**
     * Count active customer offers for a project (for dependency validation)
     */
    @Query("SELECT COUNT(co) FROM CustomerOffers co WHERE co.projectId = :projectId AND co.status != 'CANCELLED'")
    long countActiveOffersByProjectId(@Param("projectId") Long projectId);

    /**
     * Check if project has any active customer offers (for dependency validation)
     */
    @Query("SELECT CASE WHEN COUNT(co) > 0 THEN true ELSE false END FROM CustomerOffers co WHERE co.projectId = :projectId AND co.status != 'CANCELLED'")
    boolean hasActiveOffersByProjectId(@Param("projectId") Long projectId);

    /**
     * Find all customer offers for a project (for detailed dependency info)
     */
    List<CustomerOffers> findByProjectId(Long projectId);

    /**
     * Count unique customers who have active offers for a project
     */
//    @Query("SELECT COUNT(DISTINCT co.customerId) FROM CustomerOffers co WHERE co.projectId = :projectId AND co.status != 'CANCELLED'")
    @Query("SELECT COUNT(DISTINCT co.customerId) FROM CustomerOffers co WHERE co.projectId = :projectId")
    long countUniqueCustomersWithActiveOffersByProjectId(@Param("projectId") Long projectId);

    /**
     * Batch count unique customers with active offers for multiple projects (for performance optimization)
     * Returns a map of projectId -> customer count
     */
    @Query("SELECT co.projectId, COUNT(DISTINCT co.customerId) FROM CustomerOffers co WHERE co.projectId IN :projectIds AND co.status != 'CANCELLED' GROUP BY co.projectId")
    List<Object[]> countUniqueCustomersWithActiveOffersByProjectIds(@Param("projectIds") List<Long> projectIds);
}

