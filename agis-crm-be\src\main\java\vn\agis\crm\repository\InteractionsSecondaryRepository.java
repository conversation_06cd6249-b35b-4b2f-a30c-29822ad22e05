package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;

import java.util.List;

@Repository
public interface InteractionsSecondaryRepository extends JpaRepository<InteractionsSecondary, Long> {
    List<InteractionsSecondary> findByCustomerPropertyId(Long customerPropertyId);

    /**
     * Count secondary interactions for a specific unit through customer_properties
     * Used for unit deletion validation
     */
    @Query("SELECT COUNT(is) FROM InteractionsSecondary is " +
           "JOIN CustomerProperties cp ON is.customerPropertyId = cp.id " +
           "WHERE cp.unitId = :unitId")
    long countSecondaryInteractionsByUnitId(@Param("unitId") Long unitId);

    /**
     * Check if unit has any secondary interactions (for dependency validation)
     */
    @Query("SELECT CASE WHEN COUNT(is) > 0 THEN true ELSE false END FROM InteractionsSecondary is " +
           "JOIN CustomerProperties cp ON is.customerPropertyId = cp.id " +
           "WHERE cp.unitId = :unitId")
    boolean hasSecondaryInteractionsByUnitId(@Param("unitId") Long unitId);
}

