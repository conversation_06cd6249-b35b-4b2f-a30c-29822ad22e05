package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

import java.util.Objects;

@Getter
@Setter
public class DeviceTypeSearchDTO {
    private String typeCode;
    private String typeName;
    private String keyType;
    private String modelCode;
    private Integer page;
    private Integer size;
    private String sortBy;

    public DeviceTypeSearchDTO(
            String typeCode,
            String typeName,
            String keyType,
            String modelCode,
            Integer page,
            Integer size,
            String sortBy) {
        this.typeCode = Objects.isNull(typeCode) ? " " : SqlUtils.optimizeSearchLike(typeCode);
        this.typeName = Objects.isNull(typeName) ? " " : SqlUtils.optimizeSearchLike(typeName);
        this.keyType = Objects.isNull(keyType) ? " " : SqlUtils.optimizeSearchLike(keyType);
        this.modelCode = Objects.isNull(modelCode) ? " " : SqlUtils.optimizeSearchLike(modelCode);
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

