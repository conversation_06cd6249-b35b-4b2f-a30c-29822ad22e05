package vn.agis.crm.base.jpa.dto.resp;

import java.util.Date;

public interface SearchReportResponeDTO {
    Long getId();
    String getName();
    String getDescription();
    Integer getStatus();
    Integer getEnablePreview();
    String getSchema();
    String getQuery();
    String getFilterParams();
    Date getUpdatedDate();
    Long getUpdateBy();
    Date getCreatedDate();
    Long getCreatedBy();
}
