package vn.agis.crm.base.redis.apigw;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;

/**
 * Author: kiendt
 * Date: 1/8/2021
 * Contact: <EMAIL>
 */
@SuppressWarnings("unused")
public class LocalCache {
    private static final Logger logger = LoggerFactory.getLogger(LocalCache.class);
    private ConcurrentHashMap<String, Object> map;

    public LocalCache() {
        map = new ConcurrentHashMap<>();
    }

    public Object get(String key, Class<?> type) {
        Object object = map.get(key + "@@@" + type.getSimpleName());
        return object;
    }

    public void put(String key, Object item, Class<?> type) {
        map.put(key + "@@@" + type.getSimpleName(), item);
    }

    public void put(String key, Object item) {
        map.put(key + "@@@" + item.getClass().getSimpleName(), item);
    }

    public void remove(String key, Class<?> type) {
        map.remove(key + "@@@" + type.getSimpleName());
    }

    public void removeAll() {
        map.clear();
    }

    public Long increment(String key, Long aLong) {
        Long value = (Long) map.getOrDefault(key + "@@@" + Long.class.getSimpleName(), 0L);
        value = value + aLong;
        this.put(key, value);
        return value;
    }

    public Long decrement(String key, Long aLong) {
        Long value = (Long) map.getOrDefault(key + "@@@" + Long.class.getSimpleName(), 0L);
        if (value >= aLong) {
            value = value - aLong;
        }
        this.put(key, value);
        return value;
    }
}
