C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Device.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\domain\imports\ImportJobSource.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\Auth2RequestInit.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\repositories\CustomJpaRepository.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchDeviceRespone.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ChangeStatusAlertReqDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ImportDeviceRowItem.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Province.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateDeviceFromIoTRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DataPoolReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\AlertHistoryResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\DetailAlertResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ExcelUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateCommandRequestIoT.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DashboardConfig.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\StringUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\DateUtil.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\RelativeUpsertDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DynamicConfig.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CheckExistEmployeeReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateAlertReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DeviceSearchDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AlertFilter.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\virtualassistant\Chat.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateAlertReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerPropertyDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReqPaymentErrorCacheEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\Constants.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchDeviceTelemetryReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchClientAuthDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Units.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\InteractionPrimaryCreateDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\RoleSearchDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\errors\BaseException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\PageInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\docs\Project.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchWardRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\CustomMessageListenerAdapter.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ImportJob.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\InteractionSecondaryUpdateDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UnitCodeCheckDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\apigw\Uacp.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ObjectImportFile.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\Param.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ParamDashboard.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\NotificationDeleteResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\WaterPricingRules.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\UserManageId.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchHistoryAlertRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\CustomerResDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Customers.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\GenericSerializer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CountHistoryAlertRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ProjectDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Notifications.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\RSQLUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateUserReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\TransactionLogger.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Location.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ExcelSheetDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DashboardConfigCreateDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\FileMetadataDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\EventBus.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceType.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\RuleJobHistory.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\dto\LoginRequestDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\repositories\ImportJobErrorRepository.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\EmployeeSummaryDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\RedisCommandDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\LeadRuleResDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\RolePermissionId.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\DuplicateException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\IdUsageResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CustomerUpsertRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ReportContent.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\JsonArrayToStringDeserializer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\SearchInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\InteractionSecondaryDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateAssignmentEmployeeDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\ResourceNotFoundException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\chatbot\ChatBotQuestionResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ComboboxResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Command.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\BeanUtil.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ObjectMapperUtil.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerRelativeDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\domain\imports\ImportErrorType.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\LoginInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\MySimpleMessageListenerContainer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\PermissionDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\DataInvalidException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchAlertReqDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\JsonMessageConverter.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\AMQPEventListener.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\CustomerProperties.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceWaterSummary.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\OfferUpsertDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ChatVirtualAssistant.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\JobDetailsRequestDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceMeasurementCycle.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Permission.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\FileUploadResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchReportRespone.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\NotFoundException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\KeyValuePair.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchDynamicResponeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Config.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DocumentEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchRoleCreateAccountReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\LoggingUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ImportJobDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DatasetConfigDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\InteractionPrimaryDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\BadRequestException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ProjectSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ProjectDeletionValidationResult.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ValidationResultDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\AMQPEventBus.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DynamicConfigSearchDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\domain\imports\ImportJobMode.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\constants\JpaConstants.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\FilterParam.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchReferNameReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerOfferDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ObjectUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AlertLogs.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\MessageKeyConstant.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\AesCrypt.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\domain\imports\ImportJobStatus.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\DryRunResultDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\UserRole.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ImportTemplateDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ExportReportResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\DeviceAndCustomerDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\GetAssignmentHistoryDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\DataConstrainException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchCommandRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\docs\DTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ValidateUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ReportContentResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\InteractionPrimaryUpdateDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ResetPasswordInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\InteractionPrimarySearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\AlertToSend.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\IdEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\services\CrudService.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\Constants.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\NotificationBatchDeleteRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchReceivingGroupReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ChangeUserForManagerReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\controller\CrudController.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\PropertyUpsertDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceTelemetryRecord.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\EmailGroup.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ValueList.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchAlertResponeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\NotificationDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\QueryInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ReceivingGroupNameResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\chatbot\ChatBotQuestionDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CheckMsisdnDevice.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\InteractionSecondaryCreateDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\EmployeeDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AlertHistory.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\Serializer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\RabbitStreamSubscribers.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\errors\ExceptionTranslator.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateConfigReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\InteractionsSecondary.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ManualAssignmentRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UnitDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ComboboxDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\SearchCriteria.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\docs\DocumentContent.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\UserResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\Auth2Response.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReceivingGroup.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReportSendingGroupId.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Ward.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ProjectDependencyError.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\DashboardConfigResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\IDeviceAndCustomer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\RsqlSearchOperation.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CheckExistGroupCodeReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\Auth2Request.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Alert.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CustomerDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\SecurityUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SendMessageDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\UserAuth2Info.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Role.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\NotificationSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Projects.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\ListenableResultCallBack.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\InteractionSecondarySearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\AlertLog.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DeviceInfoCCBSReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\virtualassistant\Query.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchRoleRespone.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\apigw\LoginInfoCache.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\AssignmentHistoryDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CheckExistUserReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\SpringContextUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\UnitSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Subscription.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\LeadRuleDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\apigw\LocalCache.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchClientAuthResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateRulePriorityRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\DetailReceivingGroupResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\SqlUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\virtualassistant\CreateQueryVirtualAssistant.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SimpleRoleDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DetailGroupSimDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceAssignment.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchDeviceResponeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ConfigSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ReflectionUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerDependencyError.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\errors\CommonException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\RolePermissions.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\ExpiredPasswordException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchDeviceTypeResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\CopyPropertiesUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\CustomRsqlVisitor.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\AreasSearchResDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\AMQPSubscribes.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\MessageHandlerDirectReplyTo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Employee.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\virtualassistant\CreateChatVirtualAssistant.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\Event.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateSimTicketReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ReportConfigDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\DeviceCommandRecord.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ProjectDetailDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\Utils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ImportErrorDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateCommandRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\IterableUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\FileStorageException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\UserManage.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ResponseData.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Report.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\AnnotationProcessor.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\FileEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\FilterParamValueRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\DetailReceivingGroupResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CustomerDeletionValidationResult.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\AssignmentsDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchRoleResponeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\CrmCauseCode.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CheckExistCustomerReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ReceivingGroupNameResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\SupportedFormatsDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AlertList.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\CustomBaseException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\ResponseDataCommon.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AbstractEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\FilterDashboardRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateConfigReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\AuthResponseWithTokenAndErrorCodeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\UnitDeletionValidationResult.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\ApiError.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\AlertFullNameResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\ResponseCode.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UpdateEmployeeReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ImportJobError.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\MessageHandler.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ExportSimReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DeviceInfoReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\User.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\virtualassistant\ParamSearchAdvance.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\RedisCache.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Permissions.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\FileUploadDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\virtualassistant\Answer.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\CustomerRelatives.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\SimpleRole.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\ResponeBase.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\constants\AMQPConstants.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\ForbiddenException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ChangePasswordInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\RuleRun.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DashboardConfigDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\ProjectWithStatsDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\JpaCriteriaQueryVisitor.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\FileUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\AssignmentHistoryResponseDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\DateUtils.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ForgotPasswordInfo.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateEmailGroupReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Areas.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\ReportSearchDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\AlertReceivingGroup.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\UploadDeviceResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\QuerySuggest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\GZip.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\SearchUserRequest.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\SSOLoginDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ImportResultDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\RabbitStreamSubscriber.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\AlertToSendDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchCommandResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\repositories\ImportJobRepository.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\MultipartFileWrapper.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateUserReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchAlertRespone.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\SearchForm.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\GenericRsqlSpecBuilder.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\CustomerOffers.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\AssignmentUpsertDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\res\virtualassistant\Question.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\InternalServerException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\InteractionsPrimary.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\Sme.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchUserResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\rsql\GenericRsqlSpecification.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\AMQPSubscriber.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\configuration\SpringContext.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\AMQPEventPublisher.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReportSendingGroup.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\ResponseStatusConst.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\AreasSearch.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\RolePermission.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\ImportProgressDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\IAlertHistoryResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\redis\ObjectCache.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\ResponseDataConfiguration.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\domain\imports\ImportSeverity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\CreateEmployeeReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\LeadRule.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\CommonResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\EmployeeSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\DeviceTypeSearchDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchDashboardConfigResponseDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\AMQPService.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\NotificationBatchDeleteResponse.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ProjectEntity.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\utils\UnZip.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\errors\RemoveHadUsedEntityException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\req\UserReq.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\event\amqp\AMQPAbstractConfiguration.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\CustomerAssignments.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\QueryVirtualAssistant.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReportContent.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\errors\RemoveSystemEntityException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\resp\SearchReportResponeDTO.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\constants\CycleTypeEnum.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\exception\type\MaxRowExcelException.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\dto\SimpleRoleSearchDto.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\jpa\entity\ReportSending.java
C:\Users\<USER>\OneDrive\Desktop\CRM\agis-core-base\src\main\java\vn\agis\crm\base\core\filters\UserPrincipal.java
