package vn.agis.crm.base.jpa.repositories;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.ImportJobError;

@Repository
public interface ImportJobErrorRepository extends JpaRepository<ImportJobError, Long> {

    /**
     * Find errors by import job ID with pagination
     */
    Page<ImportJobError> findByImportJobIdOrderByRowNumAsc(Long importJobId, Pageable pageable);

    /**
     * Count errors by import job ID
     */
    long countByImportJobId(Long importJobId);

    /**
     * Delete all errors for a specific import job
     */
    void deleteByImportJobId(Long importJobId);
}
