package vn.agis.crm.endpoint;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.service.PermissionService;

@Component
public class PermissionEndpoint {
    private static Logger logger = LoggerFactory.getLogger(PermissionEndpoint.class);
    private PermissionService permissionService;
    protected EventBus eventBus;
    public PermissionEndpoint(PermissionService permissionService, EventBus eventBus) {
        this.permissionService = permissionService;
        this.eventBus = eventBus;
    }
    public Event process(Event event) {
        return this.permissionService.process(event);
    }
}
