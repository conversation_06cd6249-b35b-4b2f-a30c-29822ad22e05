# Dùng image Java ch<PERSON>h thức
FROM eclipse-temurin:21-jre-alpine

# T<PERSON><PERSON> thư mục chứa ứng dụng
WORKDIR /app

# Copy source test trig
COPY config/ /app/config/

# Tạo thư mục uploads (nơi ứng dụng ghi file ảnh)
RUN mkdir -p /app/uploads

# Copy file jar vào container
COPY target/crm-http-api-1.0.jar app.jar
# COPY opentelemetry-javaagent.jar opentelemetry-javaagent.jar
# C<PERSON><PERSON> hình cổng (tu<PERSON> theo app, ví dụ 8083)
EXPOSE 8081

# Lệnh khởi động
ENTRYPOINT ["java", "-jar", "app.jar"]