package vn.agis.crm.base.utils;

import org.dhatim.fastexcel.reader.ReadableWorkbook;
import org.dhatim.fastexcel.reader.Row;
import org.dhatim.fastexcel.reader.Sheet;

import java.io.IOException;
import java.util.stream.Stream;

public class ExcelUtils {
    public static Long countNonEmptyCells(ReadableWorkbook excelFile) throws IOException {
        Long nonEmptyCount = 0L;
        int columnIndex = 2; // Cột C (index = 2, vì tính từ 0)\
        Sheet sheet = excelFile.getFirstSheet();

        try (Stream<Row> rows = sheet.openStream()) {
            nonEmptyCount = (Long) rows
                    .map(row -> row.getCellText(columnIndex)) // Lấy giá trị cột C
                    .filter(text -> text != null && !text.trim().isEmpty()) // Lọc giá trị không rỗng
                    .count();
        }
        return nonEmptyCount - 1;
    }

    public static int countNonEmptyCellsInRow(Row row) {
        int nonEmptyCount = 0;
        for (int i = 0; i < row.getCellCount(); i++) {
            String cellValue = row.getCellText(i);
            if (cellValue != null && !cellValue.trim().isEmpty()) { // Chỉ đếm nếu có dữ liệu thực sự
                nonEmptyCount++;
            }
        }
        return nonEmptyCount;
    }
}
