# Permission Search API Testing Guide

## Overview

This guide provides comprehensive testing instructions for the enhanced `getAllPermissions` API endpoint with name-based filtering functionality.

## API Endpoint

```
GET /permissions?name={searchTerm}
```

## Test Scenarios

### 1. Get All Permissions (Backward Compatibility)

**Scenario**: Retrieve all permissions without any filtering

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Content-Type**: `application/json`
- **Body**: Array of all permissions
```json
[
  {
    "id": 1,
    "name": "createUser",
    "description": "Create user permission",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  {
    "id": 2,
    "name": "updateUser", 
    "description": "Update user permission",
    "createdAt": "2024-01-15T10:31:00.000Z"
  },
  {
    "id": 3,
    "name": "deleteProject",
    "description": "Delete project permission", 
    "createdAt": "2024-01-15T10:32:00.000Z"
  }
]
```

### 2. Search by Exact Name Match

**Scenario**: Search for permissions with exact name match

**Setup**:
```sql
-- Ensure test data exists
INSERT INTO permissions (name, description, created_at) VALUES
('createUser', 'Create user permission', NOW()),
('updateUser', 'Update user permission', NOW()),
('deleteProject', 'Delete project permission', NOW());
```

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=createUser" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Array with single matching permission
```json
[
  {
    "id": 1,
    "name": "createUser",
    "description": "Create user permission",
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
]
```

### 3. Search by Partial Name Match

**Scenario**: Search for permissions containing "user" in the name

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=user" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Array with permissions containing "user"
```json
[
  {
    "id": 1,
    "name": "createUser",
    "description": "Create user permission",
    "createdAt": "2024-01-15T10:30:00.000Z"
  },
  {
    "id": 2,
    "name": "updateUser",
    "description": "Update user permission", 
    "createdAt": "2024-01-15T10:31:00.000Z"
  }
]
```

### 4. Case-Insensitive Search

**Scenario**: Search with different case variations

**Test Requests**:
```bash
# Uppercase
curl -X GET "http://localhost:8080/permissions?name=USER" \
  -H "Content-Type: application/json" \
  -v

# Mixed case
curl -X GET "http://localhost:8080/permissions?name=User" \
  -H "Content-Type: application/json" \
  -v

# Lowercase
curl -X GET "http://localhost:8080/permissions?name=user" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**: All should return the same results (permissions containing "user")

### 5. No Results Found

**Scenario**: Search for non-existent permission name

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=nonexistent" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Empty array
```json
[]
```

### 6. Empty Name Parameter

**Scenario**: Search with empty name parameter (should return all permissions)

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Array of all permissions (same as without name parameter)

### 7. Whitespace Handling

**Scenario**: Search with whitespace in name parameter

**Test Requests**:
```bash
# Leading/trailing whitespace (should be trimmed)
curl -X GET "http://localhost:8080/permissions?name=%20user%20" \
  -H "Content-Type: application/json" \
  -v

# Only whitespace (should return all permissions)
curl -X GET "http://localhost:8080/permissions?name=%20%20%20" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**: 
- First request should return permissions containing "user"
- Second request should return all permissions

### 8. Special Characters

**Scenario**: Search with special characters

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=create-user" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Empty array (unless permissions with special characters exist)

### 9. Single Character Search

**Scenario**: Search with single character

**Test Request**:
```bash
curl -X GET "http://localhost:8080/permissions?name=e" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Array of permissions containing the letter "e"

## Automated Testing Script

Create a bash script to run all test scenarios:

```bash
#!/bin/bash

BASE_URL="http://localhost:8080"
ENDPOINT="/permissions"

echo "=== Permission Search API Testing ==="
echo

# Test 1: Get all permissions (backward compatibility)
echo "Test 1: Get all permissions (no name parameter)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: All permissions retrieved successfully"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 2: Exact match search
echo "Test 2: Exact match search (name=createUser)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=createUser")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Exact match search successful"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 3: Partial match search
echo "Test 3: Partial match search (name=user)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=user")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Partial match search successful"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 4: Case-insensitive search
echo "Test 4: Case-insensitive search (name=USER)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=USER")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Case-insensitive search successful"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 5: No results
echo "Test 5: No results (name=nonexistent)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=nonexistent")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ] && [ "$body" = "[]" ]; then
    echo "✅ PASS: No results handled correctly"
else
    echo "❌ FAIL: Expected 200 with empty array, got $http_code with $body"
fi
echo

# Test 6: Empty parameter
echo "Test 6: Empty parameter (name=)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Empty parameter handled correctly"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 7: Whitespace parameter
echo "Test 7: Whitespace parameter (name=%20%20%20)"
response=$(curl -s -w "%{http_code}" -X GET "$BASE_URL$ENDPOINT?name=%20%20%20")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Whitespace parameter handled correctly"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi

echo
echo "=== Testing Complete ==="
```

## Database Verification Queries

After testing, verify the database state and search functionality:

```sql
-- Check all permissions
SELECT id, name, description, created_at FROM permissions ORDER BY name;

-- Test case-insensitive search manually
SELECT id, name, description 
FROM permissions 
WHERE LOWER(name) LIKE LOWER('%user%');

-- Test exact match
SELECT id, name, description 
FROM permissions 
WHERE LOWER(name) LIKE LOWER('%createUser%');

-- Count total permissions
SELECT COUNT(*) as total_permissions FROM permissions;

-- Test partial match with different cases
SELECT id, name, description 
FROM permissions 
WHERE LOWER(name) LIKE LOWER('%USER%');
```

## Performance Testing

Test API performance with multiple concurrent requests:

```bash
# Install Apache Bench (ab) if not available
# Ubuntu: sudo apt-get install apache2-utils
# macOS: brew install httpd

# Test with 10 concurrent requests, 100 total requests
ab -n 100 -c 10 "http://localhost:8080/permissions?name=user"

# Test without name parameter
ab -n 100 -c 10 "http://localhost:8080/permissions"

# Test with no results
ab -n 100 -c 10 "http://localhost:8080/permissions?name=nonexistent"
```

## Integration Testing Checklist

- [ ] API endpoint responds correctly to all test scenarios
- [ ] Database queries execute without errors
- [ ] AMQP messaging works between agis-http-api and agis-crm-be
- [ ] Case-insensitive search works as expected
- [ ] Partial matching works correctly
- [ ] Empty and null parameters handled properly
- [ ] Whitespace trimming works correctly
- [ ] Response JSON structure is consistent
- [ ] HTTP status codes are correct for each scenario
- [ ] Unit tests pass for all layers (controller, service, repository)
- [ ] Performance is acceptable under load
- [ ] Backward compatibility maintained
- [ ] Logging captures search operations

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check application logs for exceptions
   - Verify database connection
   - Ensure AMQP messaging is working

2. **Empty results when data exists**
   - Check database query syntax
   - Verify case-insensitive search implementation
   - Test repository method directly

3. **AMQP Communication Issues**
   - Check RabbitMQ server status
   - Verify queue configurations
   - Test message routing

### Debug Commands

```bash
# Check application logs
tail -f logs/application.log

# Test database connection
mysql -h localhost -u username -p database_name

# Check RabbitMQ status
sudo systemctl status rabbitmq-server

# Test API health
curl http://localhost:8080/actuator/health
```

## Expected Results Summary

| Test Scenario | HTTP Status | Response Type | Expected Behavior |
|---------------|-------------|---------------|-------------------|
| No name parameter | 200 OK | Array of all permissions | Backward compatibility |
| Exact match | 200 OK | Array with matching permission | Single result |
| Partial match | 200 OK | Array with matching permissions | Multiple results |
| Case-insensitive | 200 OK | Array with matching permissions | Same as lowercase |
| No results | 200 OK | Empty array | No matches found |
| Empty parameter | 200 OK | Array of all permissions | Same as no parameter |
| Whitespace parameter | 200 OK | Array of all permissions | Trimmed to empty |
| Special characters | 200 OK | Array (likely empty) | Literal search |

All tests should pass to confirm the permission search API is working correctly with proper name-based filtering and maintained backward compatibility.
