# Project Deletion API Testing Guide

This document provides test scenarios for the enhanced project deletion API with dependency validation.

## API Endpoints

### 1. Validate Project Deletion
**GET** `/project-mgmt/validate-deletion/{id}`

Validates if a project can be deleted by checking for dependencies.

### 2. Delete Project (Enhanced)
**DELETE** `/project-mgmt/delete/{id}`

Deletes a project after validating dependencies. Returns error if dependencies exist.

## Test Scenarios

### Scenario 1: Project Can Be Deleted (No Dependencies)

**Request:**
```bash
curl -X GET "http://localhost:8080/project-mgmt/validate-deletion/1"
```

**Expected Response (200 OK):**
```json
{
  "canDelete": true,
  "dependencies": [],
  "message": "Dự án có thể xóa an toàn.",
  "projectId": 1,
  "projectName": "Test Project"
}
```

**Delete Request:**
```bash
curl -X DELETE "http://localhost:8080/project-mgmt/delete/1"
```

**Expected Response:** 200 OK (empty body)

### Scenario 2: Project Has Units Dependency

**Request:**
```bash
curl -X GET "http://localhost:8080/project-mgmt/validate-deletion/2"
```

**Expected Response (200 OK):**
```json
{
  "canDelete": false,
  "dependencies": [
    {
      "dependencyType": "UNITS",
      "count": 5,
      "message": "Không thể xóa dự án. Dự án có 5 căn hộ đang hoạt động.",
      "details": null
    }
  ],
  "message": "Không thể xóa dự án. Dự án có 5 căn hộ đang hoạt động.",
  "projectId": 2,
  "projectName": "Project with Units"
}
```

**Delete Request:**
```bash
curl -X DELETE "http://localhost:8080/project-mgmt/delete/2"
```

**Expected Response (400 Bad Request):**
```json
{
  "canDelete": false,
  "dependencies": [
    {
      "dependencyType": "UNITS",
      "count": 5,
      "message": "Không thể xóa dự án. Dự án có 5 căn hộ đang hoạt động.",
      "details": null
    }
  ],
  "message": "Không thể xóa dự án. Dự án có 5 căn hộ đang hoạt động.",
  "projectId": 2,
  "projectName": "Project with Units"
}
```

### Scenario 3: Project Has Multiple Dependencies

**Request:**
```bash
curl -X GET "http://localhost:8080/project-mgmt/validate-deletion/3"
```

**Expected Response (200 OK):**
```json
{
  "canDelete": false,
  "dependencies": [
    {
      "dependencyType": "UNITS",
      "count": 10,
      "message": "Không thể xóa dự án. Dự án có 10 căn hộ đang hoạt động.",
      "details": null
    },
    {
      "dependencyType": "CUSTOMER_OFFERS",
      "count": 5,
      "message": "Không thể xóa dự án. Dự án có 5 chào bán khách hàng đang hoạt động.",
      "details": null
    },
    {
      "dependencyType": "CUSTOMER_PROPERTIES",
      "count": 3,
      "message": "Không thể xóa dự án. Dự án có 3 bất động sản khách hàng đã sở hữu.",
      "details": null
    }
  ],
  "message": "Không thể xóa dự án do có nhiều phụ thuộc: 10 căn hộ, 5 chào bán khách hàng và 3 bất động sản khách hàng.",
  "projectId": 3,
  "projectName": "Project with Multiple Dependencies"
}
```

### Scenario 4: Project Not Found

**Request:**
```bash
curl -X GET "http://localhost:8080/project-mgmt/validate-deletion/999"
```

**Expected Response (400 Bad Request):**
```json
{
  "canDelete": false,
  "dependencies": [],
  "message": "Không tìm thấy dự án",
  "projectId": 999,
  "projectName": null
}
```

## Database Setup for Testing

To test the scenarios above, you'll need to set up test data:

### Create Test Projects
```sql
INSERT INTO projects (id, name, description, is_active, created_at) VALUES
(1, 'Test Project', 'Project with no dependencies', 1, NOW()),
(2, 'Project with Units', 'Project that has units', 1, NOW()),
(3, 'Project with Multiple Dependencies', 'Project with units, offers, and properties', 1, NOW());
```

### Create Test Units (for Scenario 2 & 3)
```sql
INSERT INTO units (project_id, code, area, contract_price, is_active, created_at) VALUES
(2, 'UNIT-001', 100.00, 1000000.00, 1, NOW()),
(2, 'UNIT-002', 120.00, 1200000.00, 1, NOW()),
(2, 'UNIT-003', 110.00, 1100000.00, 1, NOW()),
(2, 'UNIT-004', 130.00, 1300000.00, 1, NOW()),
(2, 'UNIT-005', 105.00, 1050000.00, 1, NOW()),
(3, 'UNIT-101', 100.00, 1000000.00, 1, NOW()),
(3, 'UNIT-102', 120.00, 1200000.00, 1, NOW()),
-- Add 8 more units for project 3 to reach 10 total
(3, 'UNIT-103', 110.00, 1100000.00, 1, NOW()),
(3, 'UNIT-104', 130.00, 1300000.00, 1, NOW()),
(3, 'UNIT-105', 105.00, 1050000.00, 1, NOW()),
(3, 'UNIT-106', 115.00, 1150000.00, 1, NOW()),
(3, 'UNIT-107', 125.00, 1250000.00, 1, NOW()),
(3, 'UNIT-108', 135.00, 1350000.00, 1, NOW()),
(3, 'UNIT-109', 140.00, 1400000.00, 1, NOW()),
(3, 'UNIT-110', 145.00, 1450000.00, 1, NOW());
```

### Create Test Customer Offers (for Scenario 3)
```sql
INSERT INTO customer_offers (customer_id, project_id, status, created_at) VALUES
(1, 3, 'OPEN', NOW()),
(2, 3, 'OPEN', NOW()),
(3, 3, 'OPEN', NOW()),
(4, 3, 'OPEN', NOW()),
(5, 3, 'OPEN', NOW());
```

### Create Test Customer Properties (for Scenario 3)
```sql
INSERT INTO customer_properties (customer_id, project_id, unit_id, transaction_date, contract_price, created_at) VALUES
(1, 3, 1, '2024-01-01', 1000000.00, NOW()),
(2, 3, 2, '2024-01-02', 1200000.00, NOW()),
(3, 3, 3, '2024-01-03', 1100000.00, NOW());
```

## Expected Behavior Summary

1. **Validation Endpoint**: Always returns 200 OK with validation result
2. **Delete Endpoint**: Returns 200 OK if deletion succeeds, 400 Bad Request if dependencies exist
3. **Dependencies Checked**: Units (active), Customer Offers (non-cancelled), Customer Properties (all)
4. **Error Messages**: User-friendly messages indicating specific dependency counts
5. **Multiple Dependencies**: Combined message listing all dependency types
