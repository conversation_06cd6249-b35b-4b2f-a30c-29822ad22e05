package vn.agis.crm.constant;

public class SQL {
    public static final String SEARCH_ROLE =
            """     
                    SELECT s.ID, s.NAME, s.TYPE, s.STATUS, s.DESCRIPTION, s.CREATEd_DATE as createdDate, s.CREATED_BY as createdBy FROM role  s
                    WHERE
                    (
                        ( :type = 1)
                        OR ( :type = 2 AND s.type in (0,3))
                        OR ( :type = 3 AND s.type in (0,3) AND (s.ID in (SELECT ur.ROLE_ID from user_role ur where ur.USER_ID in (:userIdList)) or s.created_by in (:userIdList)) )
                    )
                    AND
                        ( :#{#search.name} = ' '  OR UPPER(s.NAME) LIKE CONCAT('%', UPPER(:#{#search.name}), '%'))
                        AND ( -1 = :#{#search.type} OR s.TYPE = :#{#search.type} )
                    """;
    public static final String COUNT_SEARCH_ROLE = " SELECT count(1) FROM (" + SEARCH_ROLE + ") tmp";



    public static final String GET_PERMISSION_KEY_BY_ROLE_IDS =
            """
                    SELECT DISTINCT
                    ( p.name ) 
                    FROM
                    roles r
                    JOIN role_permissions rp ON r.id = rp.role_id
                    JOIN permissions p ON p.ID = rp.permission_id 
                    WHERE r.id = :ids
                    """;
    public static final String GET_LIST_PERMISSION_BY_USER =
        """
            SELECT
            	*
            FROM
            	permission
            WHERE
             id IN (
            		SELECT DISTINCT
            			rp.PERMISSION_ID
            		FROM
            			user_role ur
            			JOIN role ro ON ur.ROLE_ID = ro.ID
            			JOIN role_permission rp ON rp.ROLE_ID = ro.id
            		WHERE
            			ur.USER_ID = :userId and ro.status = 1)
              """;


    public static final String GET_PERMISSION_REPORT_BY_USER =
            """
                SELECT
                    p.PERMISSION_KEY
                FROM
                    permission p
                WHERE
                 (p.ID IN (
                        SELECT DISTINCT
                            rp.PERMISSION_ID
                        FROM
                            user_role ur
                            JOIN role ro ON ur.ROLE_ID = ro.ID
                            JOIN role_permission rp ON rp.ROLE_ID = ro.id
                        WHERE
                            ur.USER_ID = :userId ))
                 AND (p.PERMISSION_KEY LIKE 'getReport_%')
                  """;

    public static final String GET_PERMISSION_CHART_BY_USER =
            """
                SELECT
                    p.PERMISSION_KEY
                FROM
                    permission p
                WHERE
                 (p.ID IN (
                        SELECT DISTINCT
                            rp.PERMISSION_ID
                        FROM
                            user_role ur
                            JOIN role ro ON ur.ROLE_ID = ro.ID
                            JOIN role_permission rp ON rp.ROLE_ID = ro.id
                        WHERE
                            ur.USER_ID = :userId ))
                 AND (p.PERMISSION_KEY LIKE 'dynamicChart_%')
                  """;
}
