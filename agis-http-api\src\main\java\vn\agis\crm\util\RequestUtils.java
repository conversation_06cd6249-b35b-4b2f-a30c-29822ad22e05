package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.utils.BeanUtil;
import vn.agis.crm.base.utils.SecurityUtils;

import java.util.Date;
import java.util.Objects;


public class RequestUtils {
    private static Logger logger = LoggerFactory.getLogger(RequestUtils.class);

    private static int timeout = 30000;
    private static int timeout2 = 180000;
    private RequestUtils() {

    }

    public static Event amqp(String method, String category, Object payload, String routingKey) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqpWithLongTimeout(String method, String category, Object payload, String routingKey) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout2);
        return eventResp;
    }
    public static Event amqp(String method, String category, Object payload, String routingKey, Integer timeout) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqpWithTimeout(String method, String category, Object payload, String routingKey, int timeout) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqpWithoutTimeout(String method,String category, Object payload, String routingKey) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publish(exchange, routingKey, event);
        return eventResp;
    }

    public static String getExchangeFromRoutingKey(String routingKey) {
        return AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE;
    }

}
