{"info": {"name": "CRM - Customer Import", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "f1d2c3b4-a5e6-7890-1234-abcdef123456"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}, {"key": "fileId", "value": ""}, {"key": "jobId", "value": ""}], "item": [{"name": "Step 1: Upload CSV File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "file", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/import/upload", "host": ["{{baseUrl}}"], "path": ["api", "import", "upload"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () { pm.response.to.have.status(200); });", "pm.test(\"Response has fileId\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.fileId).to.not.be.empty;", "    pm.collectionVariables.set(\"fileId\", jsonData.fileId);", "});"], "type": "text/javascript"}}]}, {"name": "Step 2: Dry Run Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"conflictResolution\": \"upsert\"}"}, "url": {"raw": "{{baseUrl}}/api/import/{{fileId}}/dry-run", "host": ["{{baseUrl}}"], "path": ["api", "import", "{{fileId}}", "dry-run"]}}}, {"name": "Step 3: Execute Import", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\"conflictResolution\": \"upsert\", \"runOnlyValid\": \"true\"}"}, "url": {"raw": "{{baseUrl}}/api/import/{{fileId}}/execute", "host": ["{{baseUrl}}"], "path": ["api", "import", "{{fileId}}", "execute"]}}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () { pm.response.to.have.status(200); });", "pm.test(\"Response has jobId\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.jobId).to.be.a('number');", "    pm.collectionVariables.set(\"jobId\", jsonData.jobId);", "});"], "type": "text/javascript"}}]}, {"name": "Step 4: Check Job Status", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/import/jobs/{{jobId}}", "host": ["{{baseUrl}}"], "path": ["api", "import", "jobs", "{{jobId}}"]}}}]}