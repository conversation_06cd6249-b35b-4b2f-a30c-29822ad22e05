package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import java.math.BigDecimal;

/**
 * Search DTO for filtering secondary interactions
 * Used for GET /interactions-secondary/search endpoint
 */
@Data
public class InteractionSecondarySearchDto {
    
    private Long customerPropertyId;
    private String result;
    private BigDecimal expectedSellPriceFrom;
    private BigDecimal expectedSellPriceTo;
    private BigDecimal expectedRentPriceFrom;
    private BigDecimal expectedRentPriceTo;
    private String happenedAtFrom; // dd/MM/yyyy format
    private String happenedAtTo;   // dd/MM/yyyy format
    private Long createdBy;
    private String createdAtFrom;  // dd/MM/yyyy format
    private String createdAtTo;    // dd/MM/yyyy format
    
    // Pagination parameters
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "happenedAt,desc";
    
    public InteractionSecondarySearchDto() {}
    
    public InteractionSecondarySearchDto(Long customerPropertyId, String result,
                                       BigDecimal expectedSellPriceFrom, BigDecimal expectedSellPriceTo,
                                       BigDecimal expectedRentPriceFrom, BigDecimal expectedRentPriceTo,
                                       String happenedAtFrom, String happenedAtTo,
                                       Long createdBy, String createdAtFrom, String createdAtTo,
                                       Integer page, Integer size, String sortBy) {
        this.customerPropertyId = customerPropertyId;
        this.result = result;
        this.expectedSellPriceFrom = expectedSellPriceFrom;
        this.expectedSellPriceTo = expectedSellPriceTo;
        this.expectedRentPriceFrom = expectedRentPriceFrom;
        this.expectedRentPriceTo = expectedRentPriceTo;
        this.happenedAtFrom = happenedAtFrom;
        this.happenedAtTo = happenedAtTo;
        this.createdBy = createdBy;
        this.createdAtFrom = createdAtFrom;
        this.createdAtTo = createdAtTo;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
