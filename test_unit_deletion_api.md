# Unit Deletion API Testing Guide

## Overview

This guide provides comprehensive testing instructions for the enhanced `deleteUnit` API endpoint with dependency validation.

## API Endpoint

```
DELETE /unit-mgmt/delete/{id}
```

## Test Scenarios

### 1. Successful Unit Deletion

**Scenario**: Delete a unit that has no secondary interactions

**Setup**:
```sql
-- Create a unit without any dependencies
INSERT INTO units (id, project_id, code, area, contract_price, is_active, created_at) 
VALUES (100, 1, 'TEST_CLEAN', 75.5, 3500000000, 1, NOW());
```

**Test Request**:
```bash
curl -X DELETE "http://localhost:8080/unit-mgmt/delete/100" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `200 OK`
- **Body**: Empty
- **Headers**: `Content-Length: 0`

### 2. Failed Deletion - Has Secondary Interactions

**Scenario**: Delete a unit that has secondary interactions (should fail)

**Setup**:
```sql
-- Create unit with dependencies
INSERT INTO units (id, project_id, code, area, contract_price, is_active, created_at) 
VALUES (101, 1, 'TEST_USED', 75.5, 3500000000, 1, NOW());

-- Create customer property for the unit
INSERT INTO customer_properties (id, customer_id, project_id, unit_id, transaction_date, contract_price, created_at) 
VALUES (101, 1, 1, 101, '2024-01-15', 3500000000, NOW());

-- Create secondary interaction (this prevents deletion)
INSERT INTO interactions_secondary (customer_property_id, result, happened_at, created_at) 
VALUES (101, 'Tư vấn chuyển nhượng', NOW(), NOW());
```

**Test Request**:
```bash
curl -X DELETE "http://localhost:8080/unit-mgmt/delete/101" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `400 Bad Request`
- **Content-Type**: `application/json`
- **Body**:
```json
{
  "canDelete": false,
  "message": "Căn hộ đang được sử dụng, không thể xóa.",
  "secondaryInteractionsCount": 1
}
```

### 3. Failed Deletion - Unit Not Found

**Scenario**: Delete a non-existent unit

**Test Request**:
```bash
curl -X DELETE "http://localhost:8080/unit-mgmt/delete/999999" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `400 Bad Request`
- **Content-Type**: `application/json`
- **Body**:
```json
{
  "canDelete": false,
  "message": "Không tìm thấy căn hộ",
  "secondaryInteractionsCount": 0
}
```

### 4. Invalid ID Parameter

**Scenario**: Delete with invalid ID format

**Test Request**:
```bash
curl -X DELETE "http://localhost:8080/unit-mgmt/delete/invalid" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `400 Bad Request`
- **Body**: Spring validation error message

### 5. Multiple Secondary Interactions

**Scenario**: Delete a unit with multiple secondary interactions

**Setup**:
```sql
-- Create unit with multiple dependencies
INSERT INTO units (id, project_id, code, area, contract_price, is_active, created_at) 
VALUES (102, 1, 'TEST_MULTI', 75.5, 3500000000, 1, NOW());

-- Create customer property
INSERT INTO customer_properties (id, customer_id, project_id, unit_id, transaction_date, contract_price, created_at) 
VALUES (102, 1, 1, 102, '2024-01-15', 3500000000, NOW());

-- Create multiple secondary interactions
INSERT INTO interactions_secondary (customer_property_id, result, happened_at, created_at) VALUES
(102, 'Tư vấn chuyển nhượng', NOW(), NOW()),
(102, 'Tư vấn cho thuê', NOW(), NOW()),
(102, 'Khảo sát thị trường', NOW(), NOW());
```

**Test Request**:
```bash
curl -X DELETE "http://localhost:8080/unit-mgmt/delete/102" \
  -H "Content-Type: application/json" \
  -v
```

**Expected Response**:
- **Status Code**: `400 Bad Request`
- **Body**:
```json
{
  "canDelete": false,
  "message": "Căn hộ đang được sử dụng, không thể xóa.",
  "secondaryInteractionsCount": 3
}
```

## Automated Testing Script

Create a bash script to run all test scenarios:

```bash
#!/bin/bash

BASE_URL="http://localhost:8080"
ENDPOINT="/unit-mgmt/delete"

echo "=== Unit Deletion API Testing ==="
echo

# Test 1: Successful deletion
echo "Test 1: Successful deletion (Unit ID: 100)"
response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL$ENDPOINT/100")
http_code="${response: -3}"
echo "HTTP Status: $http_code"
if [ "$http_code" = "200" ]; then
    echo "✅ PASS: Unit deleted successfully"
else
    echo "❌ FAIL: Expected 200, got $http_code"
fi
echo

# Test 2: Failed deletion - has dependencies
echo "Test 2: Failed deletion - has secondary interactions (Unit ID: 101)"
response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL$ENDPOINT/101" -H "Content-Type: application/json")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "400" ]; then
    echo "✅ PASS: Deletion blocked due to dependencies"
else
    echo "❌ FAIL: Expected 400, got $http_code"
fi
echo

# Test 3: Unit not found
echo "Test 3: Unit not found (Unit ID: 999999)"
response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL$ENDPOINT/999999" -H "Content-Type: application/json")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "400" ]; then
    echo "✅ PASS: Unit not found handled correctly"
else
    echo "❌ FAIL: Expected 400, got $http_code"
fi
echo

# Test 4: Invalid ID
echo "Test 4: Invalid ID format"
response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL$ENDPOINT/invalid")
http_code="${response: -3}"
echo "HTTP Status: $http_code"
if [ "$http_code" = "400" ]; then
    echo "✅ PASS: Invalid ID handled correctly"
else
    echo "❌ FAIL: Expected 400, got $http_code"
fi
echo

# Test 5: Multiple dependencies
echo "Test 5: Multiple secondary interactions (Unit ID: 102)"
response=$(curl -s -w "%{http_code}" -X DELETE "$BASE_URL$ENDPOINT/102" -H "Content-Type: application/json")
http_code="${response: -3}"
body="${response%???}"
echo "HTTP Status: $http_code"
echo "Response Body: $body"
if [ "$http_code" = "400" ]; then
    echo "✅ PASS: Multiple dependencies handled correctly"
else
    echo "❌ FAIL: Expected 400, got $http_code"
fi

echo
echo "=== Testing Complete ==="
```

## Database Verification Queries

After testing, verify the database state:

```sql
-- Check if units were actually deleted
SELECT id, code, is_active FROM units WHERE id IN (100, 101, 102);

-- Check secondary interactions count for remaining units
SELECT 
    u.id,
    u.code,
    COUNT(is.id) as secondary_interactions_count
FROM units u
LEFT JOIN customer_properties cp ON u.id = cp.unit_id
LEFT JOIN interactions_secondary is ON cp.id = is.customer_property_id
WHERE u.id IN (101, 102)
GROUP BY u.id, u.code;

-- Verify dependency validation query
SELECT 
    u.id as unit_id,
    u.code,
    COUNT(is.id) as secondary_interactions
FROM units u
LEFT JOIN customer_properties cp ON u.id = cp.unit_id
LEFT JOIN interactions_secondary is ON cp.id = is.customer_property_id
WHERE u.id = 101
GROUP BY u.id, u.code;
```

## Performance Testing

Test API performance with multiple concurrent requests:

```bash
# Install Apache Bench (ab) if not available
# Ubuntu: sudo apt-get install apache2-utils
# macOS: brew install httpd

# Test with 10 concurrent requests, 100 total requests
ab -n 100 -c 10 -m DELETE http://localhost:8080/unit-mgmt/delete/999999
```

## Integration Testing Checklist

- [ ] API endpoint responds correctly to all test scenarios
- [ ] Database queries execute without errors
- [ ] AMQP messaging works between agis-http-api and agis-crm-be
- [ ] Error messages are in Vietnamese as expected
- [ ] Response JSON structure matches specification
- [ ] HTTP status codes are correct for each scenario
- [ ] Unit tests pass for both controller and service layers
- [ ] Performance is acceptable under load
- [ ] Logging captures all important events
- [ ] Exception handling works for edge cases

## Troubleshooting

### Common Issues

1. **500 Internal Server Error**
   - Check application logs for exceptions
   - Verify database connection
   - Ensure AMQP messaging is working

2. **404 Not Found**
   - Verify the endpoint URL is correct
   - Check if the application is running
   - Confirm the controller mapping

3. **Database Connection Issues**
   - Check database server status
   - Verify connection credentials
   - Test database queries manually

4. **AMQP Communication Issues**
   - Check RabbitMQ server status
   - Verify queue configurations
   - Test message routing

### Debug Commands

```bash
# Check application logs
tail -f logs/application.log

# Test database connection
mysql -h localhost -u username -p database_name

# Check RabbitMQ status
sudo systemctl status rabbitmq-server

# Test API health
curl http://localhost:8080/actuator/health
```

## Expected Results Summary

| Test Scenario | HTTP Status | Response Body | Database Change |
|---------------|-------------|---------------|-----------------|
| Successful deletion | 200 OK | Empty | Unit deleted |
| Has dependencies | 400 Bad Request | Validation error JSON | No change |
| Unit not found | 400 Bad Request | Not found error JSON | No change |
| Invalid ID | 400 Bad Request | Validation error | No change |
| Multiple dependencies | 400 Bad Request | Validation error JSON | No change |

All tests should pass to confirm the unit deletion API is working correctly with proper dependency validation and Vietnamese error messages.
