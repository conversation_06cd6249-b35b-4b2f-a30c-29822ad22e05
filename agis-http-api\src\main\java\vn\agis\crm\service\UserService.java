package vn.agis.crm.service;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.MessageKeyConstant.Validation;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants.RoutingKey;
import vn.agis.crm.base.exception.type.*;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.SearchUserResponseDTO;
import vn.agis.crm.base.jpa.dto.resp.UserResponseDTO;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

@Service
public class UserService extends CrudService<User, Long> {

    private Logger logger = LoggerFactory.getLogger(UserService.class);
    private static final String objectKey = "User";
    private static final String routingKeyLog = RoutingKey.ROUTING_KEY_LOGGING;

    @Autowired
    private MessageSource messageSource;


    public UserService() {
        super(User.class);
        this.routingKey = RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.USER;
    }

    public Page<SearchUserResponseDTO> getPage(SearchUserRequest searchUserRequest, Pageable pageable) {
        List<SearchUserResponseDTO> response = new ArrayList<>();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Method.SEARCH_USER, category, searchUserRequest, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchUserResponseDTO.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
            if (searchUserRequest.isLoggable()) {
//                TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                        ObjectMapperUtil.toJsonString(searchUserRequest), ObjectMapperUtil.toJsonString(response), null, null, event);
            }
        }
    }




    public Page<SearchUserResponseDTO> getNoOneManagedCustomerAccounts(SearchUserRequest searchUserRequest, Pageable pageable) {
        List<SearchUserResponseDTO> response = new ArrayList<>();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Method.SEARCH_USER_CUSTOMER_NO_ONE_MANAGE, category, searchUserRequest, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchUserResponseDTO.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(searchUserRequest), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public UserResponseDTO getOne(Long id) {
        UserResponseDTO response = new UserResponseDTO();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {
            event = RequestUtils.amqpWithTimeout(Method.GET_ONE_USER, category, id, routingKey, 120000);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (UserResponseDTO) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(category, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(id, response.getFullName(), objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(id), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public void changeStatusUser(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        User user = new User();
        try {
            event = RequestUtils.amqp(Method.CHANGE_STATUS_USER, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;
//                Event event1 = RequestUtils.amqp(Method.GET_LIST_TOKEN_3RD, category, id, routingKey);
//                List<AuthorizationInfo> list = (List<AuthorizationInfo>) event1.payload;
//                if (list != null && list.size() > 0) {
//                    for (AuthorizationInfo info : list) {
//                        redisCache.remove(info.getAuthorizationCode(), User.class);
//                    }
//                }
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(category, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, new ArrayList<>(), MessageKeyConstant.FORBIDDEN);
            }
        } finally {
//            TransactionLogger.writeLogITrans(id, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(id), ObjectMapperUtil.toJsonString(user), null, null, event);
        }

    }

    public void changeUserForManager(ChangeUserForManagerReq changeUserForManagerReq) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Method.CHANGE_USER_FOR_MANAGER, category, changeUserForManagerReq, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {

            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(category, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, new ArrayList<>(), MessageKeyConstant.FORBIDDEN);
            }
        } finally {
//            TransactionLogger.writeLogITrans(changeUserForManagerReq.getOldUserId(), null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(changeUserForManagerReq), null, null, null, event);
        }

    }

    public void deleteUser(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        User user = new User();
        try {
            event = RequestUtils.amqp(Method.DELETE_USER, category, id, routingKey);

//            Event event1 = RequestUtils.amqp(Method.GET_LIST_TOKEN_3RD, category, id, routingKey);
//            List<AuthorizationInfo> list = (List<AuthorizationInfo>) event1.payload;
//            if (list != null && list.size() > 0) {
//                for (AuthorizationInfo info : list) {
//                    redisCache.remove(info.getAuthorizationCode(), User.class);
//                }
//            }
//            // nếu thay đổi SecretKey thì vô hiệu token
//            if (list != null && list.size() > 0) {
//                for (AuthorizationInfo info : list) {
//                    redisCache.remove("token" + info.getAuthorizationCode(), String.class);
//                }
//            }
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(category, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
        } finally {
//            TransactionLogger.writeLogITrans(id, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(id), ObjectMapperUtil.toJsonString(user), null, null, event);
        }

    }


    public User createUser(CreateUserReq createUserReq) {
        User user = new User();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Method.CREATE_USER, category, createUserReq, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;
                return user;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(category, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                throw throwDuplicateException(Validation.EXISTS, category, (List<String>) event.payload, null);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(createUserReq), ObjectMapperUtil.toJsonString(user), null, null, event);
        }
    }

    public User updateUser(UpdateUserReq updateUserReq) {
        User user = new User();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        try {

            event = RequestUtils.amqpWithTimeout(Method.UPDATE_USER, category, updateUserReq, routingKey, 180000);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;

//                Event event1 = RequestUtils.amqp(Method.GET_LIST_TOKEN_3RD, category, updateUserReq.getId(), routingKey);
//                List<AuthorizationInfo> list = (List<AuthorizationInfo>) event1.payload;
//                if (list != null && list.size() > 0) {
//                    for (AuthorizationInfo info : list) {
//                        redisCache.remove(info.getAuthorizationCode(), User.class);
//                    }
//                }
//                if (updateUserReq.getIsChangeSecretKey()) {
//                    // nếu thay đổi SecretKey thì vô hiệu token
//                    if (list != null && list.size() > 0) {
//                        for (AuthorizationInfo info : list) {
//                            redisCache.remove("token" + info.getAuthorizationCode(), String.class);
//                        }
//                    }
//                }
                return user;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw throwResourceNotFoundException(event.respErrorDesc, (List<String>) event.payload, null);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN || event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new ForbiddenException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                throw throwDuplicateException(Validation.EXISTS, category, (List<String>) event.payload, null);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(updateUserReq.getId(), updateUserReq.getFullName(), objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(updateUserReq), ObjectMapperUtil.toJsonString(user), null, null, event);
        }
    }

    public Integer checkExistCreateUser(CheckExistUserReq checkExistUserReq) {
        Event event = RequestUtils.amqp(Method.EXIST_BY_EMAIL_OR_USERNAME, category, checkExistUserReq, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (Integer) event.payload;
        }
        return null;
    }


    public List<Role> getListRole(SearchRoleCreateAccountReq type) {
        Event event = RequestUtils.amqp(Method.GET_LIST_ROLE, category, type, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (List<Role>) event.payload;
        }
        return null;
    }

    public void forgotPasswordInit(String email) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        ForgotPasswordInfo forgotPasswordInfo = new ForgotPasswordInfo();
        forgotPasswordInfo.setEmail(email);
        try {
            event = RequestUtils.amqp(Method.FORGOT_PASSWORD_INIT, category, forgotPasswordInfo, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw throwBadRequestException((String) event.payload, category, new ArrayList<>(), null);
            }
        } finally {
//            TransactionLogger.writeLogITrans(null, email, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(forgotPasswordInfo), null, null, null, event);
        }

    }

    public void forgotPasswordFinish(ResetPasswordInfo resetPasswordInfo) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.FORGOT_PASSWORD_FINISH, category, resetPasswordInfo, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw throwBadRequestException((String) event.payload, category, new ArrayList<>(), null);
            }
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(resetPasswordInfo), null, null, null, event);
        }
    }

    public void validateTokenMail(ResetPasswordInfo resetPasswordInfo) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.VALIDATE_TOKEN_MAIL, category, resetPasswordInfo, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw throwBadRequestException((String) event.payload, category, new ArrayList<>(), null);
            }
        } finally {
        }
    }

    public void changePassword(ChangePasswordInfo changePasswordInfo) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Method.CHANGE_PASSWORD_USER, category, changePasswordInfo, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw throwBadRequestException((String) event.payload, category, new ArrayList<>(), null);
            }
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(changePasswordInfo), null, null, null, event);
        }
    }

    public User updateProfile(UpdateUserReq updateUserReq) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        User user = null;
        try {
            event = RequestUtils.amqp(Method.UPDATE_PROFILE, category, updateUserReq, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;
//                Event event1 = RequestUtils.amqp(Method.GET_LIST_TOKEN_3RD, category, SecurityUtils.getUserId(), routingKey);
//                List<AuthorizationInfo> list = (List<AuthorizationInfo>) event1.payload;
//                if (list != null && list.size() > 0) {
//                    for (AuthorizationInfo info : list) {
//                        redisCache.remove(info.getAuthorizationCode(), User.class);
//                    }
//                }
//                if (updateUserReq.getIsChangeSecretKey()) {
//                    // nếu thay đổi SecretKey thì vô hiệu token
//                    if (list != null && list.size() > 0) {
//                        for (AuthorizationInfo info : list) {
//                            redisCache.remove("token" + info.getAuthorizationCode(), String.class);
//                        }
//                    }
//                }
                return user;
            } else if (event.respStatusCode == ResponseCode.CONFLICT) {
                throw new DuplicateException((String) event.payload, category, new ArrayList<>(), Validation.EXISTS);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(MessageKeyConstant.NOT_FOUND, category, new ArrayList<>(), MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(updateUserReq.getId(), updateUserReq.getFullName(), objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(updateUserReq), ObjectMapperUtil.toJsonString(user), null, null, event);
        }
    }

    public UserResponseDTO viewProfile() {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        UserResponseDTO userResponseDTO = null;
        try {
            event = RequestUtils.amqp(Method.VIEW_PROFILE, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                userResponseDTO = (UserResponseDTO) event.payload;
                return userResponseDTO;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw throwBadRequestException((String) event.payload, category, new ArrayList<>(), null);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(MessageKeyConstant.NOT_FOUND, category, new ArrayList<>(), MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(event.userId, userResponseDTO != null ? userResponseDTO.getFullName() : null, objectKey, null, timeRequest, timeResponse,
//                    ObjectMapperUtil.toJsonString(event.userId), ObjectMapperUtil.toJsonString(userResponseDTO), null, null, event);
        }
    }

    public User currentUser(String token) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        User user = null;
        try {
            event = RequestUtils.amqp(Method.CURRENT_USER, category, token, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                user = (User) event.payload;
                return user;
            } else {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, new ArrayList<>(), MessageKeyConstant.FORBIDDEN);
            }
        } finally {
        }
    }


    public List<Long> getListActivatedAccount(List<Long> listAccountId) {
        try {
            Event event = RequestUtils.amqp(Method.GET_LIST_ACTIVATED_ACCOUNT, category, listAccountId, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                List<Long> list = (List<Long>) event.payload;
                return list;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public List<Province> getProvince() {
        try {
            Event event = RequestUtils.amqp(Method.GET_PROVINCE, category, null, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                List<Province> list = ObjectMapperUtil.listMapper((String) event.payload, Province.class);
                return list;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    public List<Ward> searchWard(SearchWardRequest searchWardRequest) {
        try {
            Event event = RequestUtils.amqp(Method.SEARCH_WARD, category, searchWardRequest, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                List<Ward> list = ObjectMapperUtil.listMapper((String) event.payload, Ward.class);
                return list;
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }






    /**
     * throw BadRequestException
     */
    private BadRequestException throwBadRequestException(String message, String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(message, values, LocaleContextHolder.getLocale());
        return new BadRequestException(messageNotFound, entity, field, message);
    }

    private ResourceNotFoundException throwResourceNotFoundException(String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(MessageKeyConstant.NOT_FOUND, values, LocaleContextHolder.getLocale());
        return new ResourceNotFoundException(messageNotFound, entity, field, MessageKeyConstant.NOT_FOUND);
    }

    private DuplicateException throwDuplicateException(String message, String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(message, values, LocaleContextHolder.getLocale());
        return new DuplicateException(messageNotFound, entity, field, message);
    }


}
