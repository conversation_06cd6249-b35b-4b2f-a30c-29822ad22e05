package vn.agis.crm.repository;

import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import vn.agis.crm.base.jpa.entity.Permission;
import vn.agis.crm.constant.SQL;


@org.springframework.stereotype.Repository
public interface PermissionRepository extends JpaRepository<Permission, Long> {

    List<Permission> findAll();

    void deletePermissionByPermissionKey(String permistionKey);

    Permission getPermissionByPermissionKey(String permistionKey);

    @Query(nativeQuery = true, value = SQL.GET_PERMISSION_KEY_BY_ROLE_IDS)
    List<String> getListPermissionKeyByRoleIds(List<Long> ids);

    @Query(nativeQuery = true, value = SQL.GET_LIST_PERMISSION_BY_USER)
    List<Permission> getListPermissionByUserId(Long userId);


    @Query(nativeQuery = true,value =SQL.GET_PERMISSION_REPORT_BY_USER)
    List<String> getPermisstionKeyReportByUser(Long userId);


    @Query(nativeQuery = true,value =SQL.GET_PERMISSION_CHART_BY_USER)
    List<String> getPermisstionKeyChartByUser(Long userId);
}
