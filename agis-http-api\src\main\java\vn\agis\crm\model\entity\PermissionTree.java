package vn.agis.crm.model.entity;

import vn.agis.crm.base.jpa.entity.Permission;

import java.util.ArrayList;
import java.util.List;

public class PermissionTree {
    private String label;
    private String key;
    private Permission data;
    private List<PermissionTree> children;

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Permission getData() {
        return data;
    }

    public void setData(Permission data) {
        this.data = data;
    }

    public List<PermissionTree> getChildren() {
        return children;
    }

    public void setChildren(List<PermissionTree> children) {
        this.children = children;
    }

    public PermissionTree(String label, String key) {
        this.key = key;
        this.label = label;
        this.children = new ArrayList<>();
    }

    public PermissionTree(String label, String key, Permission data) {
        this.label = label;
        this.key = key;
        this.data = data;
    }


}
