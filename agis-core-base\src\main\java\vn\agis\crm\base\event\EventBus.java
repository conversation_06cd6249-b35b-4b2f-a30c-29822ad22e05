package vn.agis.crm.base.event;

import org.springframework.amqp.rabbit.RabbitMessageFuture;
import vn.agis.crm.base.event.amqp.AMQPEventPublisher;
import vn.agis.crm.base.event.amqp.ListenableResultCallBack;

import java.util.concurrent.Future;

/**
 * Created by tiemnd on 12/14/19.
 */
public interface EventBus {
    void registerSubscriber(AMQPSubscriber subscriber);

    void registerStreamSubscriber(RabbitStreamSubscriber subscriber);

    void publishSuperStream(byte[] data, String streamName);

    void publishStream(byte[] data, String streamName);

    Event publish(String routingKey, Event event);

    Event publish(String topicExchange, String routingKey, Event event);

    Event publishWithDelay(String xDelayedMessageExchange, String routingKey, Event event, Integer delayedInterval);

    Future<RabbitMessageFuture> publishAndReceive(String topicExchange, String routingKey, Object event);

    Future<RabbitMessageFuture> publishAndReceive(String topicExchange, String routingKey, Object event, Integer receivedTimeout);

    Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event);

    Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event, Integer receivedTimeout);

    void publishAndReceive(String topicExchange, String routingKey, Event event, Integer receivedTimeout, ListenableResultCallBack callBack);

    Event publishAndReceiveSynch(String var1, String var2, Event var3, Integer var5);

    AMQPEventPublisher registerPublisher(String topicExchange);

    String getExchange(String routingKey);

}
