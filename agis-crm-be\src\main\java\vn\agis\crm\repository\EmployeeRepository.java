package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.repositories.CustomJpaRepository;

import java.util.List;

@Repository
public interface EmployeeRepository extends CustomJpaRepository<Employee, Long> {

    Employee findOneByEmailIgnoreCase(String email);

    Employee findOneByEmployeeCodeIgnoreCase(String employeeCode);

    List<Employee> findByEmailOrEmployeeCode(String email, String employeeCode);

    List<Employee> findByEmailAndIdNot(String email, Long id);

    Employee findOneByPhone(String phone);

    List<Employee> findByPhoneAndIdNot(String phone, Long id);

    @Query("SELECT e FROM Employee e WHERE e.status = :status")
    List<Employee> findByStatus(@Param("status") Employee.Status status);

    @Query("SELECT e FROM Employee e WHERE e.deletedAt IS NULL AND "+
            "(:employeeCode IS NULL OR UPPER(e.employeeCode) LIKE CONCAT('%', UPPER(:employeeCode), '%')) AND "+
            "(:fullName IS NULL OR UPPER(e.fullName) LIKE CONCAT('%', UPPER(:fullName), '%')) AND "+
            "(:phone IS NULL OR UPPER(e.phone) LIKE CONCAT('%', UPPER(:phone), '%')) AND "+
            "(:email IS NULL OR UPPER(e.email) LIKE CONCAT('%', UPPER(:email), '%'))")
    Page<Employee> searchEmployees(@Param("employeeCode") String employeeCode,
                                   @Param("fullName") String fullName,
                                   @Param("phone") String phone,
                                   @Param("email") String email,
                                   Pageable pageable);

    /**
     * Count active employees by role ID for SimpleRole statistics
     */
    @Query("SELECT COUNT(e) FROM Employee e WHERE e.roleId = :roleId AND e.deletedAt IS NULL AND e.status = 'active'")
    long countActiveEmployeesByRoleId(@Param("roleId") Integer roleId);

    /**
     * Count active employees by multiple role IDs for batch processing
     */
    @Query("SELECT e.roleId, COUNT(e) FROM Employee e WHERE e.roleId IN :roleIds AND e.deletedAt IS NULL AND e.status = 'active' GROUP BY e.roleId")
    List<Object[]> countActiveEmployeesByRoleIds(@Param("roleIds") List<Integer> roleIds);
}
