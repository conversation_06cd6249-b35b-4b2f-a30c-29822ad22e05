package vn.agis.crm.endpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.service.NotificationService;

@Component
public class NotificationEndpoint {

    @Autowired
    private NotificationService notificationService;

    public Event process(Event event) {
        return notificationService.process(event);
    }
}
