package vn.agis.crm.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableGlobalMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import vn.agis.crm.core.filters.JwtFilter;
//import vn.agis.crm.core.filters.PublicApiFilter;

import java.util.Collections;

@Configuration
@EnableWebSecurity
@EnableGlobalMethodSecurity(prePostEnabled = true, securedEnabled = true)
public class SecurityConfig {

    private final CorsFilter corsFilter;
    private final JwtFilter jwtFilter;
//    private final PublicApiFilter publicApiFilter;

//    public SecurityConfig(CorsFilter corsFilter, JwtFilter jwtFilter, PublicApiFilter publicApiFilter) {
//        this.corsFilter = corsFilter;
//        this.jwtFilter = jwtFilter;
//        this.publicApiFilter = publicApiFilter;
//    }

    public SecurityConfig(CorsFilter corsFilter, JwtFilter jwtFilter) {
        this.corsFilter = corsFilter;
        this.jwtFilter = jwtFilter;
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .addFilterBefore(jwtFilter, UsernamePasswordAuthenticationFilter.class)
                .addFilterBefore(corsFilter, UsernamePasswordAuthenticationFilter.class);

        http.cors(corsConfigurer -> corsConfigurer.configurationSource(corsConfiguration()))
                .csrf(AbstractHttpConfigurer::disable);
        http.authorizeHttpRequests(authorizeRequests -> {
                    authorizeRequests.requestMatchers("/auth/token").permitAll()
                            .requestMatchers("/auth/login").permitAll()
                            .requestMatchers("/api/client-authentication/oauth2/token").permitAll()
                            .requestMatchers("/user-mgmt/forgot-password/**").permitAll()
                            .requestMatchers("/user-mgmt/validate-token-mail").permitAll()
                            .requestMatchers("/client-authentication/oauth2/**").permitAll()
                            .requestMatchers("/user-mgmt/current").permitAll()
                            .requestMatchers("/actuator/**").permitAll()
                            .requestMatchers("/auth/sso/token").permitAll()
                            .requestMatchers("/api/msimapi/**").permitAll()
                            .anyRequest().authenticated();
                })
                .httpBasic(Customizer.withDefaults());
        return http.build();
    }

    @Bean
    public CorsConfigurationSource corsConfiguration() {
        return request -> {
            org.springframework.web.cors.CorsConfiguration config =
                    new org.springframework.web.cors.CorsConfiguration();
            config.setAllowedHeaders(Collections.singletonList("*"));
            config.setAllowedMethods(Collections.singletonList("*"));
            config.setAllowedOriginPatterns(Collections.singletonList("*"));
            config.setExposedHeaders(Collections.singletonList("*"));
            config.setAllowCredentials(true);
            return config;
        };
    }

}
