package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

import java.util.Objects;

@Getter
@Setter
public class RoleSearchDTO {
    private String name;
    private Integer type;
    private Integer page;
    private Integer size;
    private String sortBy;

    public RoleSearchDTO(
            String name,
            Integer type,
            Integer page,
            Integer size,
            String sortBy) {
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.type = Objects.isNull(type) ? -1 : type;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }

}
