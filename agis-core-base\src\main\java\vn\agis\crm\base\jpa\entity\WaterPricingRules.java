package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import javax.persistence.criteria.CriteriaBuilder;
import java.util.Date;

@Entity
@Table(name = "water_pricing_rules")
@Data
public class WaterPricingRules {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "province_id")
    private Integer provinceId;

    @Column(name = "region")
    private Integer region;

    @Column(name = "usage_type")
    private Integer usageType;

    @Column(name = "tier1_limit")
    private Integer tier1Limit;

    @Column(name = "tier2_limit")
    private Integer tier2Limit;

    @Column(name = "tier3_limit")
    private Integer tier3Limit;

    @Column(name = "price_tier1")
    private Integer priceTier1;

    @Column(name = "price_tier2")
    private Integer priceTier2;

    @Column(name = "price_tier3")
    private Integer priceTier3;

    @Column(name = "price_tier4")
    private Integer priceTier4;

    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "updated_at")
    private Date updatedAt;
}
