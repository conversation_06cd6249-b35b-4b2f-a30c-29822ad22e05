package vn.agis.crm.base.jpa.dto.resp;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class DetailAlertResponse {
    private Long id;
    private String name;
    private String emailList;
    private String emailSubject;
    private String emailContent;
    private String msisdnsNotify;
    private String smsContent;
    private String zaloContent;
    private String zaloNotify;

    private String url;
    private String description;
    private Integer severity;
    private Integer status;

    private String rule;
    private Integer eventType;
    private Integer actionType;
    private String sendingMethod;

    private Long deviceTypeId;
    private Long userEnterpriseId;
    private Long userCustomerId;
    private Long deviceId;
    private Long createdBy;
}
