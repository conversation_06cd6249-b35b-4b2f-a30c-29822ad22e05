package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryUpdateDto;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;
import vn.agis.crm.service.InteractionsSecondaryApiService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * REST Controller for Secondary Interactions CRUD operations
 * Provides comprehensive API endpoints for managing secondary interactions
 */
@RestController
@RequestMapping("/interactions-secondary")
@Tag(name = "Secondary Interactions", description = "API endpoints for managing secondary interactions")
public class InteractionsSecondaryController extends CrudController<InteractionsSecondary, Long> {

    private final InteractionsSecondaryApiService interactionsSecondaryApiService;

    @Autowired
    public InteractionsSecondaryController(InteractionsSecondaryApiService service) {
        super(service);
        this.interactionsSecondaryApiService = service;
        this.baseUrl = "/interactions-secondary";
    }

    /**
     * Create new secondary interaction
     */
    @PostMapping
    @Operation(summary = "Create secondary interaction", 
               description = "Create a new secondary interaction for a customer property")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Secondary interaction created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Customer property not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<InteractionSecondaryDto> createInteraction(
            @Valid @RequestBody InteractionSecondaryCreateDto createDto,
            HttpServletRequest request) {
        
        InteractionSecondaryDto created = interactionsSecondaryApiService.createInteraction(createDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    /**
     * Update existing secondary interaction
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update secondary interaction", 
               description = "Update an existing secondary interaction by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Secondary interaction updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Secondary interaction or customer property not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<InteractionSecondaryDto> updateInteraction(
            @Parameter(description = "Secondary interaction ID") @PathVariable Long id,
            @Valid @RequestBody InteractionSecondaryUpdateDto updateDto,
            HttpServletRequest request) {
        
        // Ensure ID consistency
        updateDto.setId(id);
        
        InteractionSecondaryDto updated = interactionsSecondaryApiService.updateInteraction(updateDto);
        return ResponseEntity.ok(updated);
    }

    /**
     * Get secondary interaction by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get secondary interaction by ID", 
               description = "Retrieve a specific secondary interaction by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Secondary interaction found"),
        @ApiResponse(responseCode = "404", description = "Secondary interaction not found")
    })
    public ResponseEntity<InteractionSecondaryDto> getInteractionById(
            @Parameter(description = "Secondary interaction ID") @PathVariable Long id,
            HttpServletRequest request) {
        
        InteractionSecondaryDto interaction = interactionsSecondaryApiService.getInteractionById(id);
        return ResponseEntity.ok(interaction);
    }

    /**
     * Delete secondary interaction by ID
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete secondary interaction", 
               description = "Delete a secondary interaction by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Secondary interaction deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Secondary interaction not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<Void> deleteInteraction(
            @Parameter(description = "Secondary interaction ID") @PathVariable Long id,
            HttpServletRequest request) {
        
        interactionsSecondaryApiService.deleteInteraction(id);
        return ResponseEntity.ok().build();
    }

    /**
     * Search secondary interactions with filters and pagination
     */
    @PostMapping("/search")
    @Operation(summary = "Search secondary interactions", 
               description = "Search secondary interactions with filters, sorting, and pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    public ResponseEntity<Page<InteractionSecondaryDto>> searchInteractions(
            @RequestBody InteractionSecondarySearchDto searchDto,
            HttpServletRequest request) {
        
        Page<InteractionSecondaryDto> results = interactionsSecondaryApiService.searchInteractions(searchDto);
        return ResponseEntity.ok(results);
    }

    /**
     * Get secondary interactions by customer property ID
     */
    @GetMapping("/customer-property/{customerPropertyId}")
    @Operation(summary = "Get interactions by customer property ID", 
               description = "Retrieve all secondary interactions for a specific customer property")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Secondary interactions retrieved successfully")
    })
    public ResponseEntity<List<InteractionSecondaryDto>> getInteractionsByCustomerPropertyId(
            @Parameter(description = "Customer property ID") @PathVariable Long customerPropertyId,
            HttpServletRequest request) {
        
        List<InteractionSecondaryDto> interactions = interactionsSecondaryApiService
            .getInteractionsByCustomerPropertyId(customerPropertyId);
        return ResponseEntity.ok(interactions);
    }

    /**
     * Count secondary interactions by customer property ID
     */
    @GetMapping("/customer-property/{customerPropertyId}/count")
    @Operation(summary = "Count interactions by customer property ID", 
               description = "Count the number of secondary interactions for a specific customer property")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Count retrieved successfully")
    })
    public ResponseEntity<Long> countInteractionsByCustomerPropertyId(
            @Parameter(description = "Customer property ID") @PathVariable Long customerPropertyId,
            HttpServletRequest request) {
        
        Long count = interactionsSecondaryApiService.countInteractionsByCustomerPropertyId(customerPropertyId);
        return ResponseEntity.ok(count);
    }

    /**
     * Count secondary interactions by unit ID (through customer properties)
     */
    @GetMapping("/unit/{unitId}/count")
    @Operation(summary = "Count interactions by unit ID", 
               description = "Count the number of secondary interactions for a specific unit through customer properties")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Count retrieved successfully")
    })
    public ResponseEntity<Long> countInteractionsByUnitId(
            @Parameter(description = "Unit ID") @PathVariable Long unitId,
            HttpServletRequest request) {
        
        Long count = interactionsSecondaryApiService.countInteractionsByUnitId(unitId);
        return ResponseEntity.ok(count);
    }

    /**
     * Get all secondary interactions (with pagination)
     */
    @GetMapping
    @Operation(summary = "Get all secondary interactions", 
               description = "Retrieve all secondary interactions with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Secondary interactions retrieved successfully")
    })
    public ResponseEntity<Page<InteractionSecondaryDto>> getAllInteractions(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort criteria (e.g., 'happenedAt,desc')") @RequestParam(defaultValue = "happenedAt,desc") String sort,
            HttpServletRequest request) {
        
        InteractionSecondarySearchDto searchDto = new InteractionSecondarySearchDto();
        searchDto.setPage(page);
        searchDto.setSize(size);
        searchDto.setSortBy(sort);
        
        Page<InteractionSecondaryDto> results = interactionsSecondaryApiService.searchInteractions(searchDto);
        return ResponseEntity.ok(results);
    }
}
