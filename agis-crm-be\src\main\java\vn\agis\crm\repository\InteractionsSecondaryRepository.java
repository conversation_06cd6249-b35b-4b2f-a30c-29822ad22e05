package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Repository
public interface InteractionsSecondaryRepository extends JpaRepository<InteractionsSecondary, Long> {

    List<InteractionsSecondary> findByCustomerPropertyId(Long customerPropertyId);

    /**
     * Find secondary interactions by customer property ID with pagination
     */
    Page<InteractionsSecondary> findByCustomerPropertyId(Long customerPropertyId, Pageable pageable);

    /**
     * Find secondary interactions by result containing text (case-insensitive)
     */
    Page<InteractionsSecondary> findByResultContainingIgnoreCase(String result, Pageable pageable);

    /**
     * Find secondary interactions by date range
     */
    @Query("SELECT is FROM InteractionsSecondary is WHERE is.happenedAt BETWEEN :startDate AND :endDate")
    Page<InteractionsSecondary> findByHappenedAtBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate, Pageable pageable);

    /**
     * Find secondary interactions by customer property ID and date range
     */
    @Query("SELECT is FROM InteractionsSecondary is WHERE is.customerPropertyId = :customerPropertyId AND is.happenedAt BETWEEN :startDate AND :endDate")
    Page<InteractionsSecondary> findByCustomerPropertyIdAndHappenedAtBetween(@Param("customerPropertyId") Long customerPropertyId,
                                                                             @Param("startDate") Date startDate,
                                                                             @Param("endDate") Date endDate,
                                                                             Pageable pageable);

    /**
     * Find secondary interactions by expected sell price range
     */
    @Query("SELECT is FROM InteractionsSecondary is WHERE is.expectedSellPrice BETWEEN :minPrice AND :maxPrice")
    Page<InteractionsSecondary> findByExpectedSellPriceBetween(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, Pageable pageable);

    /**
     * Find secondary interactions by expected rent price range
     */
    @Query("SELECT is FROM InteractionsSecondary is WHERE is.expectedRentPrice BETWEEN :minPrice AND :maxPrice")
    Page<InteractionsSecondary> findByExpectedRentPriceBetween(@Param("minPrice") BigDecimal minPrice, @Param("maxPrice") BigDecimal maxPrice, Pageable pageable);

    /**
     * Find secondary interactions by created by user
     */
    Page<InteractionsSecondary> findByCreatedBy(Long createdBy, Pageable pageable);

    /**
     * Count secondary interactions by customer property ID
     */
    long countByCustomerPropertyId(Long customerPropertyId);

    /**
     * Check if customer property has any secondary interactions (for dependency validation)
     */
    boolean existsByCustomerPropertyId(Long customerPropertyId);

    /**
     * Count secondary interactions for a specific unit through customer_properties
     * Used for unit deletion validation
     */
    @Query("SELECT COUNT(is) FROM InteractionsSecondary is " +
           "JOIN CustomerProperties cp ON is.customerPropertyId = cp.id " +
           "WHERE cp.unitId = :unitId")
    long countSecondaryInteractionsByUnitId(@Param("unitId") Long unitId);

    /**
     * Check if unit has any secondary interactions (for dependency validation)
     */
    @Query("SELECT CASE WHEN COUNT(is) > 0 THEN true ELSE false END FROM InteractionsSecondary is " +
           "JOIN CustomerProperties cp ON is.customerPropertyId = cp.id " +
           "WHERE cp.unitId = :unitId")
    boolean hasSecondaryInteractionsByUnitId(@Param("unitId") Long unitId);
}

