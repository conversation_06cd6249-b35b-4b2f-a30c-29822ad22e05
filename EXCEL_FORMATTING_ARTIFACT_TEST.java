// Excel Formatting Artifact Removal Test
// Comprehensive test suite for phone number Excel formatting artifact handling

package vn.agis.crm.test;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.util.ImportDataValidator;
import vn.agis.crm.util.ImportDataParser;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test suite for Excel formatting artifact removal in phone number processing
 * Tests all scenarios where Excel adds leading single quotes to phone numbers
 */
@SpringBootTest
@Transactional
public class ExcelFormattingArtifactTest {

    @Autowired
    private CustomerRepository customerRepository;

    private Long testJobId = 1L;
    private Set<String> existingPhones;
    private Set<String> filePhones;

    @BeforeEach
    void setUp() {
        // Clean up test data
        customerRepository.deleteAll();
        
        // Initialize test sets
        existingPhones = new HashSet<>();
        filePhones = new HashSet<>();
    }

    @Test
    void testPhoneValidationWithLeadingSingleQuote() {
        System.out.println("🔍 Test: Phone Validation with Leading Single Quote");
        
        // Test data with Excel formatting artifacts
        Map<String, String> testPhones = new LinkedHashMap<>();
        testPhones.put("Normal phone", "0901234567");
        testPhones.put("Excel formatted phone", "'0901234567");
        testPhones.put("Excel formatted with +84", "'+84901234567");
        testPhones.put("Excel formatted with 84", "'84901234567");
        testPhones.put("Excel formatted with spaces", "'************");
        testPhones.put("Excel formatted with dashes", "'************");
        testPhones.put("Multiple quotes", "''0901234567");
        
        for (Map.Entry<String, String> entry : testPhones.entrySet()) {
            System.out.println("\n📱 Testing: " + entry.getKey() + " - " + entry.getValue());
            
            // Create row data
            Map<String, String> rowData = new LinkedHashMap<>();
            rowData.put("__ROW_NUMBER__", "1");
            rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
            rowData.put("PHONE", entry.getValue());
            
            // Reset file phones for each test
            Set<String> testFilePhones = new HashSet<>();
            
            // Validate the phone
            ValidationResultDto result = ImportDataValidator.validateRow(rowData, testJobId, existingPhones, testFilePhones);
            
            // Check validation result
            boolean isValid = result.isValid() || (result.getErrors() != null && 
                result.getErrors().stream().noneMatch(error -> "INVALID_PHONE_FORMAT".equals(error.getErrorType())));
            
            System.out.println("   ✅ Validation Result: " + (isValid ? "VALID" : "INVALID"));
            
            if (result.getErrors() != null && !result.getErrors().isEmpty()) {
                result.getErrors().forEach(error -> 
                    System.out.println("   ⚠️ Error: " + error.getErrorType() + " - " + error.getErrorDescription()));
            }
            
            // For phones that should be valid (Vietnamese format), assert they pass validation
            if (entry.getValue().contains("0901234567") || entry.getValue().contains("84901234567")) {
                assertTrue(isValid, "Phone " + entry.getValue() + " should be valid after Excel artifact removal");
            }
        }
        
        System.out.println("\n✅ Excel formatting artifact removal test completed");
    }

    @Test
    void testPhoneNormalizationWithExcelArtifacts() {
        System.out.println("🔍 Test: Phone Normalization with Excel Artifacts");
        
        // Test normalization consistency
        Map<String, String> normalizationTests = new LinkedHashMap<>();
        normalizationTests.put("0901234567", "+84901234567");
        normalizationTests.put("'0901234567", "+84901234567");
        normalizationTests.put("'+84901234567", "+84901234567");
        normalizationTests.put("'84901234567", "+84901234567");
        normalizationTests.put("'************", "+84901234567");
        normalizationTests.put("'************", "+84901234567");
        
        for (Map.Entry<String, String> test : normalizationTests.entrySet()) {
            String input = test.getKey();
            String expected = test.getValue();
            
            // Test ImportDataValidator normalization
            String normalized = normalizeVietnamesePhoneForTest(input);
            
            System.out.println("📱 Input: '" + input + "' → Normalized: '" + normalized + "' (Expected: '" + expected + "')");
            
            assertEquals(expected, normalized, 
                "Phone normalization failed for input: " + input);
        }
        
        System.out.println("✅ Phone normalization with Excel artifacts works correctly");
    }

    @Test
    void testPhoneParserWithExcelArtifacts() {
        System.out.println("🔍 Test: ImportDataParser Phone Handling with Excel Artifacts");
        
        // Test ImportDataParser methods
        Map<String, Boolean> validationTests = new LinkedHashMap<>();
        validationTests.put("0901234567", true);
        validationTests.put("'0901234567", true);
        validationTests.put("'+84901234567", true);
        validationTests.put("'84901234567", true);
        validationTests.put("'invalid", false);
        validationTests.put("'123", false);
        
        for (Map.Entry<String, Boolean> test : validationTests.entrySet()) {
            String input = test.getKey();
            Boolean expectedValid = test.getValue();
            
            boolean isValid = ImportDataParser.isValidPhone(input);
            String normalized = ImportDataParser.normalizePhone(input);
            
            System.out.println("📱 Input: '" + input + "' → Valid: " + isValid + ", Normalized: '" + normalized + "'");
            
            if (expectedValid) {
                assertTrue(isValid, "Phone " + input + " should be valid after Excel artifact removal");
                assertNotNull(normalized, "Phone " + input + " should have valid normalization");
            }
        }
        
        System.out.println("✅ ImportDataParser Excel artifact handling works correctly");
    }

    @Test
    void testCustomerIdentificationWithExcelArtifacts() {
        System.out.println("🔍 Test: Customer Identification with Excel Artifacts");
        
        // Create existing customer with normal phone
        Customers existingCustomer = new Customers();
        existingCustomer.setPhone("0901234567");
        existingCustomer.setFullName("Existing Customer");
        existingCustomer.setCreatedBy(1L);
        existingCustomer.setCreatedAt(new Date());
        customerRepository.save(existingCustomer);
        
        // Test identification with Excel formatted phone
        String excelFormattedPhone = "'0901234567";
        
        // This would be tested through the customer identification logic
        // For now, we test the normalization that would be used
        String normalizedExcelPhone = normalizePhoneForCustomerIdentification(excelFormattedPhone);
        String normalizedExistingPhone = normalizePhoneForCustomerIdentification(existingCustomer.getPhone());
        
        System.out.println("📱 Excel formatted phone: '" + excelFormattedPhone + "' → Normalized: '" + normalizedExcelPhone + "'");
        System.out.println("📱 Existing phone: '" + existingCustomer.getPhone() + "' → Normalized: '" + normalizedExistingPhone + "'");
        
        assertEquals(normalizedExistingPhone, normalizedExcelPhone, 
            "Excel formatted phone should normalize to same value as existing phone for customer identification");
        
        System.out.println("✅ Customer identification with Excel artifacts works correctly");
    }

    @Test
    void testDuplicateDetectionWithExcelArtifacts() {
        System.out.println("🔍 Test: Duplicate Detection with Excel Artifacts");
        
        // Add existing phone to system
        existingPhones.add("+84901234567"); // Normalized format
        
        // Test duplicate detection with Excel formatted phone
        Map<String, String> rowData1 = new LinkedHashMap<>();
        rowData1.put("__ROW_NUMBER__", "1");
        rowData1.put("HỌ VÀ TÊN KHÁCH HÀNG", "Customer 1");
        rowData1.put("PHONE", "0901234567"); // Normal format
        
        Map<String, String> rowData2 = new LinkedHashMap<>();
        rowData2.put("__ROW_NUMBER__", "2");
        rowData2.put("HỌ VÀ TÊN KHÁCH HÀNG", "Customer 2");
        rowData2.put("PHONE", "'0901234567"); // Excel formatted
        
        // Validate first row (should detect system duplicate)
        ValidationResultDto result1 = ImportDataValidator.validateRow(rowData1, testJobId, existingPhones, filePhones);
        
        // Validate second row (should detect file duplicate)
        ValidationResultDto result2 = ImportDataValidator.validateRow(rowData2, testJobId, existingPhones, filePhones);
        
        // Check for duplicate detection
        boolean hasSystemDuplicate = result1.getErrors() != null && 
            result1.getErrors().stream().anyMatch(error -> "DUPLICATE_IN_SYSTEM".equals(error.getErrorType()));
        
        boolean hasFileDuplicate = result2.getErrors() != null && 
            result2.getErrors().stream().anyMatch(error -> "DUPLICATE_IN_FILE".equals(error.getErrorType()));
        
        System.out.println("📱 Normal phone duplicate in system: " + hasSystemDuplicate);
        System.out.println("📱 Excel formatted phone duplicate in file: " + hasFileDuplicate);
        
        assertTrue(hasSystemDuplicate, "Normal phone should be detected as duplicate in system");
        assertTrue(hasFileDuplicate, "Excel formatted phone should be detected as duplicate in file");
        
        System.out.println("✅ Duplicate detection with Excel artifacts works correctly");
    }

    @Test
    void testEdgeCasesWithExcelArtifacts() {
        System.out.println("🔍 Test: Edge Cases with Excel Artifacts");
        
        // Test edge cases
        Map<String, String> edgeCases = new LinkedHashMap<>();
        edgeCases.put("Empty string", "");
        edgeCases.put("Only quote", "'");
        edgeCases.put("Multiple quotes", "'''0901234567");
        edgeCases.put("Quote in middle", "090'1234567");
        edgeCases.put("Quote at end", "0901234567'");
        edgeCases.put("Null value", null);
        edgeCases.put("Whitespace with quote", "  '0901234567  ");
        
        for (Map.Entry<String, String> test : edgeCases.entrySet()) {
            String description = test.getKey();
            String input = test.getValue();
            
            System.out.println("\n📱 Testing edge case: " + description + " - '" + input + "'");
            
            try {
                // Test artifact removal
                String cleaned = removeExcelFormattingArtifactsForTest(input);
                System.out.println("   Cleaned: '" + cleaned + "'");
                
                // Test validation (should not throw exceptions)
                if (input != null) {
                    Map<String, String> rowData = new LinkedHashMap<>();
                    rowData.put("__ROW_NUMBER__", "1");
                    rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
                    rowData.put("PHONE", input);
                    
                    ValidationResultDto result = ImportDataValidator.validateRow(rowData, testJobId, new HashSet<>(), new HashSet<>());
                    System.out.println("   Validation completed without exceptions");
                }
                
            } catch (Exception e) {
                System.out.println("   ❌ Exception: " + e.getMessage());
                fail("Edge case should not throw exception: " + description);
            }
        }
        
        System.out.println("\n✅ Edge cases with Excel artifacts handled correctly");
    }

    // Helper methods for testing
    private String normalizeVietnamesePhoneForTest(String phone) {
        // Simulate the normalization logic from ImportDataValidator
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }

        String cleanPhone = removeExcelFormattingArtifactsForTest(phone);
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");

        // Convert to +84 format
        if (cleanPhone.startsWith("0")) {
            cleanPhone = "+84" + cleanPhone.substring(1);
        } else if (cleanPhone.startsWith("84") && !cleanPhone.startsWith("+84")) {
            cleanPhone = "+" + cleanPhone;
        } else if (!cleanPhone.startsWith("+")) {
            cleanPhone = "+84" + cleanPhone;
        }

        return cleanPhone;
    }

    private String normalizePhoneForCustomerIdentification(String phone) {
        // Simulate the normalization logic from ImportExecutionProcessor
        if (phone == null) return null;
        
        String normalized = removeExcelFormattingArtifactsForTest(phone.trim());
        normalized = normalized.replaceAll("\\s+", "");
        if (normalized.isEmpty()) return null;
        
        // Convert +84 to 0
        if (normalized.startsWith("+84")) {
            normalized = "0" + normalized.substring(3);
        } else if (normalized.startsWith("84") && normalized.length() > 10) {
            normalized = "0" + normalized.substring(2);
        }
        
        return normalized;
    }

    private String removeExcelFormattingArtifactsForTest(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }
        
        String cleaned = phone.trim();
        
        // Remove leading single quotes that Excel adds to force text interpretation
        if (cleaned.startsWith("'")) {
            cleaned = cleaned.substring(1);
        }
        
        return cleaned;
    }
}

/**
 * Manual Testing Instructions
 * ===========================
 * 
 * 1. **Run the Test Suite:**
 *    ```bash
 *    mvn test -Dtest=ExcelFormattingArtifactTest
 *    ```
 * 
 * 2. **Create Test Excel File:**
 *    Create an Excel file with phone numbers that have leading single quotes:
 *    - '0901234567
 *    - '+84901234567
 *    - '84901234567
 *    - '************
 *    - '************
 * 
 * 3. **Test Import Process:**
 *    - Upload the test Excel file through the import API
 *    - Run dry-run first to verify validation
 *    - Execute import and verify customer creation
 *    - Check that phone numbers are stored without leading quotes
 * 
 * 4. **Verification Steps:**
 *    - All Excel formatted phones should pass validation
 *    - Normalization should produce consistent results
 *    - Duplicate detection should work across formats
 *    - Customer identification should work with Excel formatted phones
 *    - No leading quotes should be stored in the database
 * 
 * 5. **Database Verification:**
 *    ```sql
 *    -- Check that no phones have leading quotes
 *    SELECT * FROM customers WHERE phone LIKE "'%";
 *    
 *    -- Check normalization consistency
 *    SELECT phone, additional_phones FROM customers;
 *    ```
 * 
 * 6. **API Testing:**
 *    ```bash
 *    # Test import with Excel formatted phones
 *    curl -X POST "http://localhost:8080/imports" \
 *      -F "file=@excel_formatted_phones.xlsx" \
 *      -F "options={\"mode\":\"DRY_RUN\"}"
 *    ```
 * 
 * 7. **Performance Testing:**
 *    - Test with large files containing many Excel formatted phones
 *    - Verify that artifact removal doesn't impact performance
 *    - Check memory usage during processing
 */
