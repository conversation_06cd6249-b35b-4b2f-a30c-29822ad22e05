package vn.agis.crm.endpoint;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.event.AMQPSubscribes;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.constants.AMQPConstants;

import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Component
public class RootEndpoint {
    private static final Logger logger = LoggerFactory.getLogger(RootEndpoint.class);
    protected EventBus eventBus;
    private RoleEndpoint roleEndpoint;
    private PermissionEndpoint permissionEndpoint;
    private ExecutorService executorService = Executors.newVirtualThreadPerTaskExecutor();

    @Autowired
    private ProjectEndpoint projectEndpoint;

    @Autowired
    private UserEndpoint userEndpoint;

    @Autowired
    private EmployeeEndpoint employeeEndpoint;

    @Autowired
    private SubscriptionEndpoint subscriptionEndpoint;
    @Autowired
    private AreasEndpoint areasEndpoint;
    @Autowired
    private ConfigEndpoint configEndpoint;

    @Autowired
    private UnitEndpoint unitEndpoint;

    @Autowired
    private CustomerEndpoint customerEndpoint;

    @Autowired
    private SimpleRoleEndpoint simpleRoleEndpoint;

    @Autowired
    private SimplePermissionEndpoint simplePermissionEndpoint;
    @Autowired
    private AssignmentEndpoint assignmentEndpoint;

    @Autowired
    private AssignmentRuleEndpoint assignmentRuleEndpoint;

    @Autowired
    private AssignmentJobEndpoint assignmentJobEndpoint;

    @Autowired
    private ImportJobEndpoint importJobEndpoint;

    @Autowired
    private ImportTemplateEndpoint importTemplateEndpoint;

    @Autowired
    private ChatVirtualAssistantEndpoint chatVirtualAssistantEndpoint;
    @Autowired
    private QueryVirtualAssistantEndpoint queryVirtualAssistantEndpoint;
    @Autowired
    private QuerySuggestEndpoint querySuggestEndpoint;

    @Autowired
    private NotificationEndpoint notificationEndpoint;

    @Autowired
    private ChatBotEndpoint chatBotEndpoint;

    public RootEndpoint(
            EventBus eventBus, RoleEndpoint roleEndpoint, PermissionEndpoint permissionEndpoint, SubscriptionEndpoint subscriptionEndpoint

    ) {
        this.eventBus = eventBus;
        this.roleEndpoint = roleEndpoint;
        this.permissionEndpoint = permissionEndpoint;
        this.subscriptionEndpoint = subscriptionEndpoint;
    }

    @AMQPSubscribes(exchange = AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE, queue = AMQPConstants.Queue.QUEUE_KEY_CORE_MANAGEMENT,
            routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT, concurrency = 8)
    public void processExternal(Event event) {
        executorService.submit(() -> {
            switch (event.category) {
                case Category.USER -> sendResponse(event, userEndpoint.process(event));
                case Category.EMPLOYEE -> sendResponse(event, employeeEndpoint.process(event));
                case Constants.Category.ROLE -> sendResponse(event, roleEndpoint.process(event));
                case Constants.Category.PERMISSION -> sendResponse(event, permissionEndpoint.process(event));
                case Category.SUBSCRIPTION ->  sendResponse(event, subscriptionEndpoint.process(event));
                case Category.ASSIGNMENT_JOB -> sendResponse(event, assignmentJobEndpoint.process(event));
                case Category.AREAS -> sendResponse(event, areasEndpoint.process(event));
                case Category.PROJECT -> sendResponse(event, projectEndpoint.process(event));
                case Category.ASSIGNMENT_RULE -> sendResponse(event, assignmentRuleEndpoint.process(event));
                case Category.UNIT -> sendResponse(event, unitEndpoint.process(event));
                case Category.CUSTOMER -> sendResponse(event, customerEndpoint.process(event));
                case Category.ASSIGNMENT -> sendResponse(event, assignmentEndpoint.process(event));
                case Category.ROLE_SIMPLE -> sendResponse(event, simpleRoleEndpoint.process(event));
                case Category.PERMISSION_SIMPLE -> sendResponse(event, simplePermissionEndpoint.process(event));
                case Category.CONFIG -> sendResponse(event, configEndpoint.process(event));
                case Category.IMPORT_JOB -> sendResponse(event, importJobEndpoint.process(event));
                case Category.IMPORT_TEMPLATE -> sendResponse(event, importTemplateEndpoint.process(event));
                case Category.CHAT_VIRTUAL_ASSISTANT -> sendResponse(event, chatVirtualAssistantEndpoint.process(event));
                case Category.QUERY_VIRTUAL_ASSISTANT -> sendResponse(event, queryVirtualAssistantEndpoint.process(event));
                case Category.QUERY_SUGGEST -> sendResponse(event, querySuggestEndpoint.process(event));
                case Category.NOTIFICATION -> sendResponse(event, notificationEndpoint.process(event));
                case Category.CHAT_BOT -> sendResponse(event, chatBotEndpoint.process(event));
                //do something
                default -> System.out.println("default");
                //do something else
            }
        });
    }

    protected Event sendResponse(Event in, Event out) {
        if(Objects.isNull(out)) return null ;
        if (in.replyQueue != null) {
            eventBus.publish(in.replyQueue, out);
            logger.info("Send response via replyQueue#" + in.replyQueue + "#" + out.toString());
            return null;
        }
        if (in.respRoutingKey != null) {
            eventBus.publish(AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE, in.respRoutingKey, out);
            logger.info("Send response via resRoutingKey to exchange #" + AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE +
                    " with routingkey #" + in.respRoutingKey + " #" + out.toString());
        }
        return null;
    }
}
