package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.ProjectDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.ProjectDependencyError;
import vn.agis.crm.base.jpa.dto.ProjectSearchDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDetailDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.dto.res.ProjectWithStatsDto;
import vn.agis.crm.base.jpa.entity.Projects;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.utils.ObjectUtils;
import vn.agis.crm.model.mapper.ProjectMapper;
import vn.agis.crm.repository.CustomerOfferRepository;
import vn.agis.crm.repository.CustomerPropertyRepository;
import vn.agis.crm.repository.ProjectRepository;
import vn.agis.crm.repository.UnitRepository;
import vn.agis.crm.util.BaseController;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.HashSet;
import java.util.Set;


@Service
@Transactional
public class ProjectService {

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private ProjectMapper projectMapper;

    @Autowired
    private UnitRepository unitRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Method.CREATE:
                return create(event);
            case Method.UPDATE:
                return update(event);
            case Method.DELETE:
                return delete(event);
            case Method.FIND_BY_ID:
                return findById(event);
            case Method.SEARCH:
                return search(event);
            case Method.CHECK_EXIST_PROJECT_NAME:
                return checkExistName(event);
            case Method.VALIDATE_PROJECT_DELETION:
                return validateProjectDeletion(event);
            case Method.GET_BOUGHT_PROJECT:
                return getBoughtProjects(event);
            case Method.GET_GREETING_PROJECT:
                return getGreetingProjects(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event checkExistName(Event event) {
        String name = (String) event.payload;
        if (name == null || name.trim().isEmpty()) {
            return event.createResponse(false, 200, "Success");
        }
        boolean exists = projectRepository.findFirstByName(name) != null;
        return event.createResponse(exists, 200, "Success");
    }

    private Event create(Event event) {
        ProjectDto projectDto = (ProjectDto) event.payload;
        Projects projectEntity = projectMapper.toEntity(projectDto);
        projectEntity.setCreatedBy(event.userId);
        projectEntity.setCreatedAt(new Date());
//        ProjectDetailDto projectDetailDto = projectMapper.toDetailDto(projectRepository.save(projectEntity));
        projectEntity = projectRepository.save(projectEntity);
        return event.createResponse(projectEntity, 201, "Created");
    }

    private Event update(Event event) {
        ProjectDto projectDto = (ProjectDto) event.payload;
        return projectRepository.findById(projectDto.getId()).map(existingProject -> {
            projectMapper.updateEntityFromDto(existingProject, projectDto);
            existingProject.setUpdatedAt(new Date());
            existingProject.setUpdatedBy(event.userId);
            Projects projects = projectRepository.save(existingProject);
            return event.createResponse(projects, 200, "Success");
        }).orElse(event.createResponse(null, 404, "Không tìm thấy dự án"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;

        // First validate if project can be deleted
        ProjectDeletionValidationResult validationResult = validateProjectDeletionInternal(id);

        if (!validationResult.isCanDelete()) {
            return event.createResponse(validationResult, ResponseCode.BAD_REQUEST,
                MessageKeyConstant.ProjectDeletion.PROJECT_HAS_DEPENDENCIES);
        }

        // If validation passes, proceed with deletion
        projectRepository.deleteById(id);
        return event.createResponse(null, ResponseCode.OK, MessageKeyConstant.ProjectDeletion.PROJECT_DELETION_SUCCESS);
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        return projectRepository.findById(id)
                .map(project -> event.createResponse(project, 200, "Success"))
                .orElse(event.createResponse(null, 404, "Không tìm thấy dự án"));
    }

    private Event search(Event event) {
        ProjectSearchDto searchDto = (ProjectSearchDto) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchDto.getSize(),
                searchDto.getPage(), searchDto.getSortBy());
        String name = searchDto.getName() == null ? "" : searchDto.getName();
        Page<Projects> projectsPage = projectRepository.findByNameContainingIgnoreCase(name, listRequest.getPageable());

        // Enhance projects with customer statistics using batch processing
        List<ProjectWithStatsDto> projectsWithStats = batchEnhanceProjectsWithStats(projectsPage.getContent());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setData(ObjectMapperUtil.toJsonString(projectsWithStats));
        pageInfo.setTotalCount(projectsPage.getTotalElements());
        return event.createResponse(pageInfo, 200, "Success");
    }

    /**
     * Validates if a project can be deleted by checking for dependencies
     */
    private Event validateProjectDeletion(Event event) {
        Long id = (Long) event.payload;
        ProjectDeletionValidationResult validationResult = validateProjectDeletionInternal(id);

        if (validationResult.isCanDelete()) {
            return event.createResponse(validationResult, ResponseCode.OK, "Dự án có thể xóa được");
        } else {
            return event.createResponse(validationResult, ResponseCode.BAD_REQUEST,
                MessageKeyConstant.ProjectDeletion.PROJECT_DEPENDENCY_VALIDATION_FAILED);
        }
    }

    /**
     * Internal method to validate project deletion dependencies
     */
    private ProjectDeletionValidationResult validateProjectDeletionInternal(Long projectId) {
        // Check if project exists
        Optional<Projects> projectOpt = projectRepository.findById(projectId);
        if (!projectOpt.isPresent()) {
            ProjectDeletionValidationResult result = new ProjectDeletionValidationResult();
            result.setCanDelete(false);
            result.setProjectId(projectId);
            result.setMessage("Không tìm thấy dự án");
            result.setDependencies(new ArrayList<>());
            return result;
        }

        Projects project = projectOpt.get();
        List<ProjectDependencyError> dependencies = new ArrayList<>();

        // Check for active units
        long unitCount = unitRepository.countActiveUnitsByProjectId(projectId);
        if (unitCount > 0) {
            dependencies.add(new ProjectDependencyError(
                ProjectDependencyError.DependencyType.UNITS, unitCount));
        }

        // Check for active customer offers
        long offerCount = customerOfferRepository.countActiveOffersByProjectId(projectId);
        if (offerCount > 0) {
            dependencies.add(new ProjectDependencyError(
                ProjectDependencyError.DependencyType.CUSTOMER_OFFERS, offerCount));
        }

        // Check for customer properties
        long propertyCount = customerPropertyRepository.countPropertiesByProjectId(projectId);
        if (propertyCount > 0) {
            dependencies.add(new ProjectDependencyError(
                ProjectDependencyError.DependencyType.CUSTOMER_PROPERTIES, propertyCount));
        }

        // Create result
        if (dependencies.isEmpty()) {
            return ProjectDeletionValidationResult.success(projectId, project.getName());
        } else {
            return ProjectDeletionValidationResult.failure(projectId, project.getName(), dependencies);
        }
    }

    /**
     * Enhance a project with customer statistics
     */
    private ProjectWithStatsDto enhanceProjectWithStats(Projects project) {
        Long totalCustomersPurchased = customerPropertyRepository.countUniqueCustomersByProjectId(project.getId());
        Long totalCustomersWithOffers = customerOfferRepository.countUniqueCustomersWithActiveOffersByProjectId(project.getId());

        return ProjectWithStatsDto.fromProjectWithStats(project, totalCustomersPurchased, totalCustomersWithOffers);
    }

    /**
     * Batch enhance multiple projects with customer statistics for better performance
     */
    private List<ProjectWithStatsDto> batchEnhanceProjectsWithStats(List<Projects> projects) {
        if (projects.isEmpty()) {
            return new ArrayList<>();
        }

        List<ProjectWithStatsDto> result = new ArrayList<>();

        // Use batch queries for better performance with large datasets
        if (projects.size() > 5) { // Use batch processing for more than 5 projects
            List<Long> projectIds = projects.stream().map(Projects::getId).collect(Collectors.toList());

            // Batch query for customer properties
            Map<Long, Long> purchasedCounts = new HashMap<>();
            List<Object[]> purchasedResults = customerPropertyRepository.countUniqueCustomersByProjectIds(projectIds);
            for (Object[] row : purchasedResults) {
                purchasedCounts.put((Long) row[0], (Long) row[1]);
            }

            // Batch query for customer offers
            Map<Long, Long> offerCounts = new HashMap<>();
            List<Object[]> offerResults = customerOfferRepository.countUniqueCustomersWithActiveOffersByProjectIds(projectIds);
            for (Object[] row : offerResults) {
                offerCounts.put((Long) row[0], (Long) row[1]);
            }

            // Create DTOs with batch-loaded statistics
            for (Projects project : projects) {
                Long purchasedCount = purchasedCounts.getOrDefault(project.getId(), 0L);
                Long offerCount = offerCounts.getOrDefault(project.getId(), 0L);
                result.add(ProjectWithStatsDto.fromProjectWithStats(project, purchasedCount, offerCount));
            }
        } else {
            // Use individual queries for small datasets
            for (Projects project : projects) {
                result.add(enhanceProjectWithStats(project));
            }
        }

        return result;
    }

    private Event getGreetingProjects(Event event) {
        Set<Long> projectIds = projectRepository.getGreetingProjectIds();
        if(!ObjectUtils.empty(projectIds)) {
            return event.createResponse(projectRepository.findAllById(projectIds), 200, "Success");
        }else{
            return event.createResponse(new ArrayList<>(), 200, "Success");
        }
    }

    private Event getBoughtProjects(Event event) {
        Set<Long> projectIds = projectRepository.getBoughtProjectIds();
        if(!ObjectUtils.empty(projectIds)) {
            return event.createResponse(projectRepository.findAllById(projectIds), 200, "Success");
        }else{
            return event.createResponse(new ArrayList<>(), 200, "Success");
        }
    }
}

