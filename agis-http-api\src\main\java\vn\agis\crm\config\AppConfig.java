package vn.agis.crm.config;

import com.google.common.base.Strings;
import io.github.jhipster.config.JHipsterProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.concurrent.ConcurrentMapCache;
import org.springframework.cache.support.SimpleCacheManager;
import org.springframework.context.ApplicationContext;
import org.springframework.context.MessageSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.ReloadableResourceBundleMessageSource;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.MediaType;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import redis.clients.jedis.*;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.AMQPService;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.amqp.AMQPEventBus;
import vn.agis.crm.base.redis.RedisCache;
import vn.agis.crm.core.cache.MessageSubscriber;
import vn.agis.crm.core.filters.JwtFilter;

import java.time.Duration;
import java.util.Arrays;

@Configuration
@EnableAsync
@EnableAspectJAutoProxy
@EnableTransactionManagement
@EnableAutoConfiguration
@EnableCaching
public class AppConfig {

    private final Logger log = LoggerFactory.getLogger(AppConfig.class);

    @Autowired
    public Environment env;
    private final JHipsterProperties jHipsterProperties;


    public AppConfig(JHipsterProperties jHipsterProperties) {
        this.jHipsterProperties = jHipsterProperties;
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

    @Bean
    public JwtFilter jwtFilter() {
        return new JwtFilter();
    }

    /* ========================= RABBIT MQ ============================ */
    @Bean
    @ConfigurationProperties(prefix = "spring.rabbitmq", ignoreUnknownFields = true)
    public CachingConnectionFactory connectionFactory() {
        SpringContext.setServiceName(Constants.CRMService.FE_API);
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setConnectionNameStrategy(f -> "SimManagement01");
        connectionFactory.getRabbitConnectionFactory().setMaxInboundMessageBodySize(Integer.MAX_VALUE);
        return connectionFactory;
    }

    @Bean
    public EventBus eventBus(CachingConnectionFactory connectionFactory, ApplicationContext ctx) {
        return new AMQPEventBus(connectionFactory, ctx);
    }

    @Bean
    public AMQPService amqpService(ApplicationContext ctx) {
        return new AMQPService(ctx);
    }

    @Bean
    @Primary
    public MessageSource messageSource() {
        ReloadableResourceBundleMessageSource messageSource = new ReloadableResourceBundleMessageSource();
        messageSource.setBasenames("classpath:messages", "classpath:notification_template", "classpath:email_template");
        messageSource.setDefaultEncoding("UTF-8");
        return messageSource;
    }

    /* ========================= Redis ============================ */
    private boolean useSentinelConfigs() {
        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        return master != null && nodes != null && !master.isEmpty() && !nodes.isEmpty();
    }

    //Standalone, Sentinel, Cluster
    @Bean
    @ConfigurationProperties(prefix = "spring.redis")
    public RedisConnectionFactory redisConnectionFactory() {
        String modeConnection = env.getProperty("spring.redis.mode");
        final JedisPoolConfig jedisPoolConfig = jedisPoolConfig();
        if(modeConnection == null){
            return new JedisConnectionFactory();
        }else if(modeConnection.equals("Standalone")){
            return new JedisConnectionFactory(getStandaloneConfig());
        }else if(modeConnection.equals("Sentinel")){
            return new JedisConnectionFactory(getSentinelConfig(), jedisPoolConfig);
        }else if(modeConnection.equals("Cluster")){
            return new JedisConnectionFactory(getClusterConfig(), jedisPoolConfig);
        }
        return new JedisConnectionFactory();
    }



    private JedisPoolConfig jedisPoolConfig() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxIdle(10);
        config.setMinIdle(0);
        config.setMaxWait(Duration.ofMillis(-1));
        return config;
    }
    private RedisClusterConfiguration getClusterConfig() {
        String nodes = env.getProperty("spring.redis.cluster.nodes");
        String username = env.getProperty("spring.redis.cluster.username");
        String password = env.getProperty("spring.redis.cluster.password");
        RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration();

        if(!Strings.isNullOrEmpty(password)){
            redisClusterConfiguration.setPassword(password);
        }
        if(!Strings.isNullOrEmpty(username)){
            redisClusterConfiguration.setUsername(Strings.nullToEmpty(username));
        }
        for(String nodeInfo : nodes.split(",")){
            redisClusterConfiguration.addClusterNode(new RedisClusterNode(nodeInfo.split(":")[0].trim(), Integer.parseInt(nodeInfo.split(":")[1].trim())));
        }
        return redisClusterConfiguration;
    }

    private RedisStandaloneConfiguration getStandaloneConfig() {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
        String host = env.getProperty("spring.redis.standalone.host");
        String port = env.getProperty("spring.redis.standalone.port");
        String username = env.getProperty("spring.redis.standalone.username");
        String password = env.getProperty("spring.redis.standalone.password");
        redisStandaloneConfiguration.setHostName(host.trim());
        redisStandaloneConfiguration.setPort(Integer.parseInt(port.trim()));
        if(!Strings.isNullOrEmpty(password)){
            redisStandaloneConfiguration.setPassword(password);
        }
        if(!Strings.isNullOrEmpty(username)){
            redisStandaloneConfiguration.setUsername(Strings.nullToEmpty(username));
        }
        return redisStandaloneConfiguration;
    }

    // Setup Sentinel config
    public RedisSentinelConfiguration getSentinelConfig() {
        RedisSentinelConfiguration configuration = new RedisSentinelConfiguration();
        if (!useSentinelConfigs()) return configuration;
        String password = env.getProperty("spring.redis.sentinel.password");
        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        configuration.master(master);
        if(!Strings.isNullOrEmpty(password)){
            configuration.setPassword(password);
        }
        for (String node : nodes.split(",")) {
            String split[] = node.split(":");
            configuration.sentinel(split[0].trim(), Integer.parseInt(split[1].trim()));
        }
        return configuration;
    }
//
//    @Bean
//    @ConditionalOnMissingBean(name = "redisTemplate")
//    public RedisTemplate<Object, Object> redisTemplateObj(RedisConnectionFactory redisConnectionFactory) {
//        RedisTemplate<Object, Object> template = new RedisTemplate<>();
//        template.setConnectionFactory(redisConnectionFactory);
//        return template;
//    }


    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory jedisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<String, Object>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return redisTemplate;
    }

    @Bean
    public RedisCache systemCache(RedisTemplate redisTemplate) throws NoSuchFieldException, IllegalAccessException {
        return new RedisCache(redisTemplate);
    }

    @Bean
    MessageListenerAdapter messageListener() {
        return new MessageListenerAdapter(new MessageSubscriber());
    }

    @Bean
    RedisMessageListenerContainer redisContainer(MessageListenerAdapter messageListenerAdapter, RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container
                = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        container.addMessageListener(messageListenerAdapter, new PatternTopic("__keyevent@*__:set"));
        return container;
    }

    @Bean
    public CorsFilter corsFilter() {
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        CorsConfiguration config = jHipsterProperties.getCors();
        if (config.getAllowedOrigins() != null && !config.getAllowedOrigins().isEmpty()) {
            config.addAllowedHeader("*");
            config.addAllowedMethod("*");
            config.addAllowedOrigin("*");
            config.addExposedHeader("x-total-count, Authorization");
            config.setAllowCredentials(true);
            source.registerCorsConfiguration("/api/**", config);
            source.registerCorsConfiguration("/v2/api-docs", config);
        }
        return new CorsFilter(source);
    }
    @Bean
    public CacheManager cacheManager() {
        SimpleCacheManager cacheManager = new SimpleCacheManager();
        cacheManager.setCaches(Arrays.asList(
                new ConcurrentMapCache("objAPN"),
                new ConcurrentMapCache("device")));
        return cacheManager;
    }
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        RestTemplate restTemplate = builder.build();
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(
                Arrays.asList(
                        MediaType.APPLICATION_JSON,
                        new MediaType("application", "*+json"),
                        MediaType.APPLICATION_OCTET_STREAM));
        restTemplate.getMessageConverters().add(converter);
        return restTemplate;
    }

}
