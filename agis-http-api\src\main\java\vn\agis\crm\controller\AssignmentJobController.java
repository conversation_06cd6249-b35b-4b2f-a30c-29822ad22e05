package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.entity.RuleJobHistory;
import vn.agis.crm.base.jpa.entity.RuleRun;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.AssignmentJobApiService;

@RestController
@RequestMapping("/assignment-jobs")
public class AssignmentJobController {

    private final AssignmentJobApiService jobService;

    @Autowired
    public AssignmentJobController(AssignmentJobApiService jobService) {
        this.jobService = jobService;
    }

    @GetMapping("/history")
    @Operation(description = "Get the history of assignment job runs")
    public ResponseEntity<Page<RuleJobHistory>> getJobHistory(
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "jobStartedAt,desc") String sortBy) {

        String[] sortParams = sortBy.split(",");
        Sort sort = Sort.by(Sort.Direction.fromString(sortParams[1]), sortParams[0]);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<RuleJobHistory> historyPage = jobService.getJobHistory(pageable);
        return ResponseEntity.ok(historyPage);
    }

    @GetMapping("/{jobId}/details")
    @Operation(description = "Get the details (which customers were assigned) of a specific job run")
    public ResponseEntity<Page<RuleRun>> getJobDetails(
            @PathVariable Long jobId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "executedAt,desc") String sortBy) {

        String[] sortParams = sortBy.split(",");
        Sort sort = Sort.by(Sort.Direction.fromString(sortParams[1]), sortParams[0]);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<RuleRun> detailsPage = jobService.getJobDetails(jobId, pageable);
        return ResponseEntity.ok(detailsPage);
    }
}

