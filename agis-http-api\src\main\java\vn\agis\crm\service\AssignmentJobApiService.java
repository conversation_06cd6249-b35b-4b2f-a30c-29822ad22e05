package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.entity.RuleJobHistory;
import vn.agis.crm.base.jpa.dto.req.JobDetailsRequestDto;
import vn.agis.crm.base.jpa.entity.RuleRun;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.Collections;
import java.util.List;

@Service
public class AssignmentJobApiService extends CrudService<RuleJobHistory, Long> {

    private static final Logger logger = LoggerFactory.getLogger(AssignmentJobApiService.class);

    public AssignmentJobApiService() {
        super(RuleJobHistory.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.ASSIGNMENT_JOB;
    }

    public Page<RuleJobHistory> getJobHistory(Pageable pageable) {
        Event event = RequestUtils.amqp(Constants.Method.GET_JOB_HISTORY, category, pageable, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            PageInfo pageInfo = (PageInfo) event.payload;
            List<RuleJobHistory> jobs = ObjectMapperUtil.listMapper(pageInfo.getData(), RuleJobHistory.class);
            return new PageImpl<>(jobs, pageable, pageInfo.getTotalCount());
        }
        logger.error("Failed to get job history, status: {}, message: {}", event.respStatusCode, event.respErrorDesc);
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }

    public Page<RuleRun> getJobDetails(Long jobId, Pageable pageable) {
        JobDetailsRequestDto requestDto = new JobDetailsRequestDto(jobId, pageable);
        Event event = RequestUtils.amqp(Constants.Method.GET_JOB_DETAILS, category, requestDto, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            PageInfo pageInfo = (PageInfo) event.payload;
            List<RuleRun> runs = ObjectMapperUtil.listMapper(pageInfo.getData(), RuleRun.class);
            return new PageImpl<>(runs, pageable, pageInfo.getTotalCount());
        }
        logger.error("Failed to get job details for job ID {}, status: {}, message: {}", jobId, event.respStatusCode, event.respErrorDesc);
        return new PageImpl<>(Collections.emptyList(), pageable, 0);
    }
}

