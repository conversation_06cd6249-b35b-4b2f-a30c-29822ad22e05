package vn.agis.crm.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.UnitDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.UnitSearchDto;
import vn.agis.crm.base.jpa.dto.req.UnitDto;
import vn.agis.crm.base.jpa.entity.Units;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.mapper.UnitMapper;
import vn.agis.crm.repository.InteractionsSecondaryRepository;
import vn.agis.crm.repository.UnitRepository;
import vn.agis.crm.repository.spec.UnitSpecs;
import vn.agis.crm.util.BaseController;

@Service
@Transactional
public class UnitService {

    @Autowired
    private UnitRepository unitRepository;

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    public Event process(Event event) {
        switch (event.method) {
            case Method.CREATE:
                return create(event);
            case Method.UPDATE:
                return update(event);
            case Method.DELETE:
                return delete(event);
            case Method.FIND_BY_ID:
                return findById(event);
            case Method.SEARCH:
                return search(event);
            case vn.agis.crm.base.constants.Constants.Method.CHECK_EXIST_UNIT_CODE:
                return checkExistCode(event);
            case "VALIDATE_UNIT_DELETION":
                return validateUnitDeletion(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event checkExistCode(Event event) {
        vn.agis.crm.base.jpa.dto.req.UnitCodeCheckDto dto = (vn.agis.crm.base.jpa.dto.req.UnitCodeCheckDto) event.payload;
        if (dto == null || dto.getProjectId() == null || dto.getCode() == null || dto.getCode().trim().isEmpty()) {
            return event.createResponse(false, 200, "Success");
        }
        boolean exists = unitRepository.existsByProjectIdAndCode(dto.getProjectId(), dto.getCode());
        return event.createResponse(exists, 200, "Success");
    }

    private Event create(Event event) {
        UnitDto dto = (UnitDto) event.payload;
        // validate duplicate by (projectId, code)
        if (dto.getProjectId() == null || dto.getCode() == null || dto.getArea() == null) {
            return event.createResponse(null, 400, "Missing required fields (projectId, code, area)");
        }
        if (unitRepository.existsByProjectIdAndCode(dto.getProjectId(), dto.getCode())) {
            return event.createResponse(null, 409, "Duplicate unit code in project");
        }
        Units entity = unitMapper.toEntity(dto);
        // defaults
        if (entity.getDoorDirection() == null) entity.setDoorDirection("Khác");
        if (entity.getContractPrice() == null) entity.setContractPrice(BigDecimal.ZERO);
        if (entity.getIsActive() == null) entity.setIsActive(Boolean.TRUE);

        entity.setCreatedBy(event.userId);
        entity.setCreatedAt(new Date());
        entity = unitRepository.save(entity);
        return event.createResponse(entity, 201, "Created");
    }

    private Event update(Event event) {
        UnitDto dto = (UnitDto) event.payload;
        if (dto.getId() == null) {
            return event.createResponse(null, 400, "Missing id");
        }
        return unitRepository.findById(dto.getId()).map(existing -> {
            // check duplication when projectId/code change (or even when same, ensure unique constraint)
            Long targetProjectId = dto.getProjectId() != null ? dto.getProjectId() : existing.getProjectId();
            String targetCode = dto.getCode() != null ? dto.getCode() : existing.getCode();
            Units dup = unitRepository.findFirstByProjectIdAndCode(targetProjectId, targetCode);
            if (dup != null && !dup.getId().equals(existing.getId())) {
                return event.createResponse(null, 409, "Duplicate unit code in project");
            }
            unitMapper.updateEntityFromDto(existing, dto);
            existing.setUpdatedAt(new Date());
            existing.setUpdatedBy(event.userId);
            Units saved = unitRepository.save(existing);
            return event.createResponse(saved, 200, "Success");
        }).orElse(event.createResponse(null, 404, "Unit not found"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }

        // Validate unit deletion before proceeding
        UnitDeletionValidationResult validationResult = performUnitDeletionValidation(id);
        if (!validationResult.isCanDelete()) {
            return event.createResponse(validationResult, ResponseCode.BAD_REQUEST, validationResult.getMessage());
        }

        // If validation passes, proceed with deletion
        unitRepository.deleteById(id);
        return event.createResponse(null, 200, "Success");
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }
        return unitRepository.findById(id)
                .map(unit -> event.createResponse(unit, 200, "Success"))
                .orElse(event.createResponse(null, 404, "Unit not found"));
    }

    private Event search(Event event) {
        UnitSearchDto searchDto = (UnitSearchDto) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(
                searchDto.getSize(), searchDto.getPage(), searchDto.getSortBy());

        Specification<Units> spec = Specification.where(UnitSpecs.hasProjectId(searchDto.getProjectId()))
                .and(UnitSpecs.codeContains(searchDto.getCode()))
                .and(UnitSpecs.productTypeContains(searchDto.getProductType()));

        Page<Units> page = unitRepository.findAll(spec, listRequest.getPageable());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setData(ObjectMapperUtil.toJsonString(page.getContent()));
        pageInfo.setTotalCount(page.getTotalElements());
        return event.createResponse(pageInfo, 200, "Success");
    }

    /**
     * Validate unit deletion endpoint
     */
    private Event validateUnitDeletion(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }

        UnitDeletionValidationResult validationResult = performUnitDeletionValidation(id);
        return event.createResponse(validationResult, ResponseCode.OK, "Validation completed");
    }

    /**
     * Perform unit deletion validation
     * Checks for secondary interactions that would prevent deletion
     */
    private UnitDeletionValidationResult performUnitDeletionValidation(Long unitId) {
        // Check if unit exists
        Optional<Units> unitOpt = unitRepository.findById(unitId);
        if (!unitOpt.isPresent()) {
            return UnitDeletionValidationResult.unitNotFound();
        }

        // Check for secondary interactions
        long secondaryInteractionsCount = interactionsSecondaryRepository.countSecondaryInteractionsByUnitId(unitId);

        if (secondaryInteractionsCount > 0) {
            return UnitDeletionValidationResult.failedWithSecondaryInteractions(secondaryInteractionsCount);
        }

        return UnitDeletionValidationResult.success();
    }
}

