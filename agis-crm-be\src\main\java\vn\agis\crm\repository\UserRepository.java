package vn.agis.crm.repository;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.dto.req.SearchUserRequest;
import vn.agis.crm.base.jpa.entity.User;
import vn.agis.crm.base.jpa.repositories.CustomJpaRepository;
import vn.agis.crm.constant.sql.SQLUser;
import vn.agis.crm.model.dto.IGetUserDTO;
import vn.agis.crm.model.dto.ISearchUserDTO;
import vn.agis.crm.model.dto.UserIdNameDTO;

@Repository
public interface UserRepository extends CustomJpaRepository<User, Long> {

    @Query(nativeQuery = true, value = SQLUser.GET_PAGE_USER, countQuery = SQLUser.COUNT_GET_PAGE_USER)
    Page<ISearchUserDTO> getPageUser(@Param("search") SearchUserRequest search, Integer type, Long userId, Pageable pageable);

    List<User> findByEmailOrUsername(String email, String username);

    List<User> findByEmailAndIdNot(String email, Long id);

    User findOneByEmailIgnoreCase(String email);

    User findOneByUsernameIgnoreCase(String userName);

    @Query(nativeQuery = true, value = SQLUser.GET_PAGE_USER_CUSTOMER_NO_ONE_MANAGED, countQuery = SQLUser.COUNT_GET_PAGE_USER_CUSTOMER_NO_ONE_MANAGED)
    Page<ISearchUserDTO> getPageUserNoOneManaged(@Param("search") SearchUserRequest search, int type, String provinceCode, Pageable pageable);

    @Query(nativeQuery = true, value = SQLUser.GET_LST_USER_BY_USER_MANAGE)
    List<IGetUserDTO> findAllByUserManageOrUser(Long userId);


    @Query("select u.id from User u where u.id in (:listAccountId) and u.status = :status")
    List<Long> getListActivatedAccount(List<Long> listAccountId, Integer status);


    @Query(nativeQuery = true, value = SQLUser.GET_ONE_USER)
    IGetUserDTO getOneUser(Long id);

    @Query(nativeQuery = true, value = "SELECT u.id, u.name FROM users u WHERE u.id IN (:ids)")
    List<UserIdNameDTO> findUserIdsAndNamesByIdIn(@Param("ids") List<Long> ids);
}
