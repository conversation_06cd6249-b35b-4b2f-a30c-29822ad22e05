package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

/**
 * 409
 * Whren throw new this class, the error returned will contain an error code is 409
 */

public class DuplicateException extends BaseException {
    /**
	 *
	 */
	private static final long serialVersionUID = 5355439102807051567L;

	public DuplicateException(String title, String entityName, String field, String errorCode) {
		super(title, entityName, Collections.singletonList(field), errorCode);
	}

	public DuplicateException(String title, String entityName, List<String> field, String errorCode) {
		super(title, entityName, field, errorCode);
	}
}
