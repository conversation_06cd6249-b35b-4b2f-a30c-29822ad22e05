package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

public class NotFoundException extends BaseException {

    public NotFoundException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, Collections.singletonList(field), errorCode);
    }

    public NotFoundException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }
}
