package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
public class CreateEmployeeReq {
    @NotBlank
    @Size(max = 50)
    private String employeeCode;

    @NotBlank
    @Size(max = 255)
    private String fullName;

    @Size(max = 32)
    private String phone;

    @Email
    @Size(max = 255)
    private String email;

    @NotBlank
    @Size(min = 8, max = 255)
    private String password; // raw password; BE sẽ hash

    @NotNull
    private Integer roleId;
}

