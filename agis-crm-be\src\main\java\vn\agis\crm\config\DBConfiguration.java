//package vn.agis.crm.config;
//
//import com.zaxxer.hikari.HikariConfig;
//import com.zaxxer.hikari.HikariDataSource;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.boot.jdbc.DataSourceBuilder;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//
//import javax.sql.DataSource;
//
//@Configuration
//public class DBConfiguration {
//    @Bean({"dsCore-Config"})
//    @ConditionalOnProperty(prefix = "spring", name = {"datasource-core.jdbc-url"})
//    @ConfigurationProperties("spring.datasource-core")
//    public HikariConfig coreDBConfig() {
//        return new HikariConfig();
//    }
//
//    @Bean({"coremgmt"})
//    public HikariDataSource coreDBDataSource(@Qualifier("dsCore-Config") HikariConfig dsLogConfig) throws InterruptedException {
//        HikariDataSource ds = new HikariDataSource(dsLogConfig);
//        return ds;
//    }
//
//
//    @Bean({"dsReport-Config"})
//    @ConditionalOnProperty(prefix = "spring", name = {"datasource-report.jdbc-url"})
//    @ConfigurationProperties("spring.datasource-report")
//    public HikariConfig reportDBConfig() {
//        return new HikariConfig();
//    }
//
//    @Bean({"reportmgmt"})
//    public HikariDataSource reportDBDataSource(@Qualifier("dsReport-Config") HikariConfig dsLogConfig) throws InterruptedException {
//        HikariDataSource ds = new HikariDataSource(dsLogConfig);
//        return ds;
//    }
//
//
//    @Bean
//    @ConfigurationProperties(prefix = "spring.datasource")
//    @Primary
//    public DataSource dataSource() {
//        return DataSourceBuilder.create().build();
//    }
//}
