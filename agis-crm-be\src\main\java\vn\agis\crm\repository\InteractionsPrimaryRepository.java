package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;

import java.util.Date;
import java.util.List;

@Repository
public interface InteractionsPrimaryRepository extends JpaRepository<InteractionsPrimary, Long>, JpaSpecificationExecutor<InteractionsPrimary> {

    List<InteractionsPrimary> findByCustomerOfferId(Long customerOfferId);

    /**
     * Find primary interactions by customer offer ID with pagination
     */
    Page<InteractionsPrimary> findByCustomerOfferId(Long customerOfferId, Pageable pageable);

    /**
     * Find primary interactions by result containing text (case-insensitive)
     */
    Page<InteractionsPrimary> findByResultContainingIgnoreCase(String result, Pageable pageable);

    /**
     * Find primary interactions by date range
     */
    @Query("SELECT ip FROM InteractionsPrimary ip WHERE ip.happenedAt BETWEEN :startDate AND :endDate")
    Page<InteractionsPrimary> findByHappenedAtBetween(@Param("startDate") Date startDate, @Param("endDate") Date endDate, Pageable pageable);

    /**
     * Find primary interactions by customer offer ID and date range
     */
    @Query("SELECT ip FROM InteractionsPrimary ip WHERE ip.customerOfferId = :customerOfferId AND ip.happenedAt BETWEEN :startDate AND :endDate")
    Page<InteractionsPrimary> findByCustomerOfferIdAndHappenedAtBetween(@Param("customerOfferId") Long customerOfferId,
                                                                        @Param("startDate") Date startDate,
                                                                        @Param("endDate") Date endDate,
                                                                        Pageable pageable);

    /**
     * Find primary interactions by created by user
     */
    Page<InteractionsPrimary> findByCreatedBy(Long createdBy, Pageable pageable);

    /**
     * Count primary interactions by customer offer ID
     */
    long countByCustomerOfferId(Long customerOfferId);

    /**
     * Check if customer offer has any primary interactions (for dependency validation)
     */
    boolean existsByCustomerOfferId(Long customerOfferId);
}

