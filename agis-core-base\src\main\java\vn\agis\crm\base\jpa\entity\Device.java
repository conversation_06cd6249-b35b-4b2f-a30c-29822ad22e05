package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Table(name = "DEVICE")
@Entity
@Data
public class Device extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "DEVICE_NAME")
    private String deviceName;
    @Column(name = "SERIAL_NUMBER")
    private String serialNumber;
    @Column(name = "MANUFACTURE")
    private String manufacture;
    @Column(name = "MANUFACTURE_DATE")
    private Date manufactureDate;
    @Column(name = "DEVICE_TYPE_ID")
    private Integer deviceTypeId;
    @Column(name = "PROTOCOL")
    private String protocol;
    @Column(name = "IMEI")
    private String imei;
    @Column(name = "UPDATED_BY")
    private Integer updatedBy;
    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
    @Column(name = "USER_ENTERPRISE_ID")
    private Long userEnterpriseId;
    @Column(name = "USER_CUSTOMER_ID")
    private Long userCustomerId;
    @Column(name = "CONNECTION_STATUS")
    private Integer connectionStatus;
    @Column(name = "PREVIOUS_STATUS")
    private Integer previousStatus;
    @Column(name = "LAST_STATUS_UPDATED_AT")
    private Long lastStatusUpdatedAt;
    @Column(name = "IOT_DEVICE_AE_ID")
    private String iotDeviceAeId;
    @Column(name = "IOT_DEVICE_NAME")
    private String iotDeviceName;
    @Column(name = "IOT_DEVICE_ACCESS_TOKEN")
    private String iotDeviceAccessToken;
    @Column(name = "IOT_DEVICE_REFRESH_TOKEN")
    private String iotDeviceRefreshToken;
    @Column(name = "DESCRIPTION")
    private String description;
    @Column(name = "IMAGE_ID")
    private Long imageId;
    @Column(name = "CYCLE")
    private Long cycle;
    @Column(name = "REAL_STATUS")
    private Integer realStatus;
    @Transient
    private Long imsi;
    @Transient
    private String latitude;
    @Transient
    private String longitude;
    @Transient
    private String fullAddress;
    @Transient
    private String typeCode;
    @Transient
    private String modelCode;

    @Transient
    private Float currentVolume;
    @Transient
    private Long estimatedCost;
}
