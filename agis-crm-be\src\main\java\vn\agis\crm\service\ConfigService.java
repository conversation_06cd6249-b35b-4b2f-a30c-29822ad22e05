package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.ConfigSearchDto;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CreateConfigReq;
import vn.agis.crm.base.jpa.dto.req.UpdateConfigReq;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.repository.ConfigRepository;
import vn.agis.crm.util.BaseController;

import java.util.Date;

@Service
@Transactional
public class ConfigService {

    @Autowired
    private ConfigRepository configRepository;

    public Event process(Event event) {
        switch (event.method) {
            case JpaConstants.Method.SEARCH:
                return processSearch(event);
            case JpaConstants.Method.CREATE:
                return processCreate(event);
            case JpaConstants.Method.UPDATE:
                return processUpdate(event);
            case JpaConstants.Method.DELETE:
                return processDelete(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
            case JpaConstants.Method.GET_ALL:
                return processGetAll(event);
            case JpaConstants.Method.CHECK_EXITS:
                return processCheckExists(event);
            case JpaConstants.Method.GET_BY_KEY:
                return processGetByKey(event);
        }
        return event;
    }

    private Event processSearch(Event event) {
        ConfigSearchDto dto = (ConfigSearchDto) event.payload;
        BaseController.ListRequest lr = new BaseController.ListRequest(dto.getSize(), dto.getPage(), dto.getSortBy());
        Page<Config> page = configRepository.search(dto.getConfigKey(), dto.getConfigType(), dto.getDescription(), lr.getPageable());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(page.getTotalElements());
        pageInfo.setData(vn.agis.crm.base.utils.ObjectMapperUtil.toJsonString(page.getContent()));
        return event.createResponse(pageInfo, ResponseCode.OK, null);
    }

    private Event processCreate(Event event) {
        CreateConfigReq req = (CreateConfigReq) event.payload;
        if (configRepository.findOneByConfigKeyIgnoreCase(req.getConfigKey()) != null) {
            return event.createResponse("configKey", ResponseCode.CONFLICT, "Config key exists");
        }
        Config c = new Config();
        c.setConfigKey(req.getConfigKey());
        c.setConfigType(req.getConfigType());
        c.setConfigValue(req.getConfigValue());
        c.setDescription(req.getDescription());
        c.setCreatedBy(event.userId);
        c.setCreatedAt(new Date());
        c = configRepository.save(c);
        return event.createResponse(c, 201, "Created");
    }

    private Event processUpdate(Event event) {
        UpdateConfigReq req = (UpdateConfigReq) event.payload;
        var opt = configRepository.findById(req.getId());
        if (opt.isEmpty()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Config c = opt.get();
        if (req.getConfigKey() != null && !req.getConfigKey().equalsIgnoreCase(c.getConfigKey())
            && configRepository.findOneByConfigKeyIgnoreCase(req.getConfigKey()) != null) {
            return event.createResponse("configKey", ResponseCode.CONFLICT, "Config key exists");
        }
        if (req.getConfigKey() != null) c.setConfigKey(req.getConfigKey());
        if (req.getConfigType() != null) c.setConfigType(req.getConfigType());
        if (req.getConfigValue() != null) c.setConfigValue(req.getConfigValue());
        if (req.getDescription() != null) c.setDescription(req.getDescription());
        c.setUpdatedBy(event.userId);
        c.setUpdatedAt(new Date());
        c = configRepository.save(c);
        return event.createResponse(c, ResponseCode.OK, "OK");
    }

    private Event processDelete(Event event) {
        Long id = (Long) event.payload;
        if (configRepository.existsById(id)) {
            configRepository.deleteById(id);
            return event.createResponse(null, ResponseCode.OK, "Deleted");
        }
        return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
    }

    private Event processGetOne(Event event) {
        Long id = (Long) event.payload;
        return configRepository.findById(id)
                .map(c -> event.createResponse(c, ResponseCode.OK, null))
                .orElse(event.createResponse(null, ResponseCode.NOT_FOUND, null));
    }

    private Event processGetAll(Event event) {
        return event.createResponse(configRepository.findAll(), ResponseCode.OK, null);
    }

    private Event processCheckExists(Event event) {
        String key = (String) event.payload;
        boolean exists = configRepository.findOneByConfigKeyIgnoreCase(key) != null;
        return event.createResponse(exists, ResponseCode.OK, null);
    }

    private Event processGetByKey(Event event) {
        String key = (String) event.payload;
        Config c = configRepository.findOneByConfigKeyIgnoreCase(key);
        if (c != null) {
            return event.createResponse(c, ResponseCode.OK, null);
        }
        return event.createResponse(null, ResponseCode.NOT_FOUND, "Not Found");
    }
}

