package vn.agis.crm.service.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;
import vn.agis.crm.base.jpa.dto.CustomerRelativeDto;
import vn.agis.crm.base.jpa.dto.CustomerPropertyDto;
import vn.agis.crm.base.jpa.dto.CustomerOfferDto;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.CustomerRelatives;
import vn.agis.crm.base.jpa.entity.CustomerProperties;
import vn.agis.crm.base.jpa.entity.CustomerOffers;
import vn.agis.crm.repository.*;
import vn.agis.crm.model.mapper.UnitMapper;
import vn.agis.crm.model.mapper.ProjectMapper;
import vn.agis.crm.base.jpa.entity.Units;
import vn.agis.crm.base.jpa.entity.Projects;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Component
public class CustomerResponseMapper {

    private static final Logger logger = LoggerFactory.getLogger(CustomerResponseMapper.class);

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private CustomerRelativeRepository customerRelativeRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;

    @Autowired
    private UnitRepository unitRepository;

    @Autowired
    private ProjectRepository projectRepository;

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private ProjectMapper projectMapper;

    public CustomerResDto toResDto(Customers c) {
        CustomerResDto dto = new CustomerResDto();
        dto.setId(c.getId()); dto.setFullName(c.getFullName()); dto.setPhone(c.getPhone()); dto.setEmail(c.getEmail()); dto.setCccd(c.getCccd());
        dto.setBirthDate(c.getBirthDate()); dto.setAddressContact(c.getAddressContact()); dto.setAddressPermanent(c.getAddressPermanent());
        dto.setNationality(c.getNationality()); dto.setMaritalStatus(c.getMaritalStatus()); dto.setTotalAsset(c.getTotalAsset());
        dto.setBusinessField(c.getBusinessField()); dto.setAvatarUrl(c.getAvatarUrl()); dto.setZaloStatus(c.getZaloStatus());
        dto.setFacebookLink(c.getFacebookLink()); dto.setSourceType(c.getSourceType()); dto.setSourceDetail(c.getSourceDetail());
        dto.setNotes(c.getNotes());

        // Map multiple contact information fields
        dto.setAdditionalPhones(c.getAdditionalPhones());
        dto.setAdditionalEmails(c.getAdditionalEmails());
        dto.setAdditionalCccds(c.getAdditionalCccds());

        // Map interests field
        dto.setInterests(c.getInterests());
        if (c.getCurrentManagerId() != null) {
            employeeRepository.findById(c.getCurrentManagerId()).ifPresent(e -> dto.setCurrentManager(toEmployeeSummaryWithAssignment(e, c.getId(), 1)));
        }
        if (c.getCurrentStaffId() != null) {
            employeeRepository.findById(c.getCurrentStaffId()).ifPresent(e -> dto.setCurrentStaff(toEmployeeSummaryWithAssignment(e, c.getId(), 2)));
        }
        // load relatives
        List<CustomerRelatives> rels = customerRelativeRepository.findByCustomerId(c.getId());
        dto.setRelatives(rels.stream().map(this::toRelativeDto).collect(Collectors.toList()));

        // properties - optimized to avoid N+1 queries
        List<CustomerProperties> props = customerPropertyRepository.findByCustomerId(c.getId());
        dto.setCustomerProperties(this.toPropertyDtoListOptimized(props));

        // offers
        List<CustomerOffers> offers = customerOfferRepository.findByCustomerId(c.getId());
        dto.setCustomerOffers(offers.stream().map(o -> {
            CustomerOfferDto dtoO = this.toOfferDto(o);
            java.util.List<vn.agis.crm.base.jpa.entity.InteractionsPrimary> list = interactionsPrimaryRepository.findByCustomerOfferId(o.getId());
            if (list != null) {
                dtoO.setInteractionsPrimary(list.stream().map(this::toInteractionPrimaryDto).collect(Collectors.toList()));
            }
            return dtoO;
        }).collect(Collectors.toList()));
        return dto;
    }

    private CustomerRelativeDto toRelativeDto(CustomerRelatives r) {
        CustomerRelativeDto dto = new CustomerRelativeDto();
        dto.setId(r.getId());
        dto.setRelationType(r.getRelationType());
        dto.setFullName(r.getFullName());
        dto.setYearOfBirth(r.getYearOfBirth());
        dto.setPhone(r.getPhone());
        dto.setNotes(r.getNotes());
        return dto;
    }

    private EmployeeSummaryDto toEmployeeSummary(Employee e) {
        EmployeeSummaryDto s = new EmployeeSummaryDto();
        s.setId(e.getId()); s.setEmployeeCode(e.getEmployeeCode()); s.setFullName(e.getFullName());
        s.setPhone(e.getPhone()); s.setEmail(e.getEmail());
        return s;
    }

    private EmployeeSummaryDto toEmployeeSummaryWithAssignment(Employee e, Long customerId, Integer roleType) {
        EmployeeSummaryDto s = new EmployeeSummaryDto();
        s.setId(e.getId()); s.setEmployeeCode(e.getEmployeeCode()); s.setFullName(e.getFullName());
        s.setPhone(e.getPhone()); s.setEmail(e.getEmail());

        // Fetch the most recent active assignment for this employee, customer, and role type
        java.util.List<vn.agis.crm.base.jpa.entity.CustomerAssignments> assignments =
            customerAssignmentRepository.findByCustomerIdAndEmployeeIdAndRoleTypeOrderByAssignedFromDesc(customerId, e.getId(), roleType);

        if (!assignments.isEmpty()) {
            vn.agis.crm.base.jpa.entity.CustomerAssignments latestAssignment = assignments.get(0);
            s.setAssignmentId(latestAssignment.getId()); // Bổ sung assignmentId
            s.setAssignedFrom(latestAssignment.getAssignedFrom());
            s.setAssignedTo(latestAssignment.getAssignedTo());
        }

        return s;
    }

    private CustomerPropertyDto toPropertyDto(CustomerProperties cp) {
        CustomerPropertyDto dto = new CustomerPropertyDto();
        dto.setId(cp.getId());
        dto.setProjectId(cp.getProjectId());
        dto.setUnitId(cp.getUnitId());
        dto.setTransactionDate(cp.getTransactionDate());
        dto.setContractPrice(cp.getContractPrice());
        dto.setExternalAgencyName(cp.getExternalAgencyName());
        dto.setExternalSaleName(cp.getExternalSaleName());
        dto.setExternalSalePhone(cp.getExternalSalePhone());
        dto.setExternalSaleEmail(cp.getExternalSaleEmail());
        dto.setEmployeeId(cp.getEmployeeId());
        dto.setNotes(cp.getNotes());
        dto.setLegalStatus(cp.getLegalStatus()); // NEW: Map legal status
        dto.setFirstInteraction(cp.getFirstInteraction());
        dto.setLastInteraction(cp.getLastInteraction());

        return dto;
    }

    /**
     * Optimized method to convert list of CustomerProperties to CustomerPropertyDto list
     * Fetches all required units and projects in batch to avoid N+1 queries
     */
    private List<CustomerPropertyDto> toPropertyDtoListOptimized(List<CustomerProperties> properties) {
        if (properties == null || properties.isEmpty()) {
            return List.of();
        }

        // Collect all unique unit and project IDs
        Set<Long> unitIds = properties.stream()
            .map(CustomerProperties::getUnitId)
            .filter(id -> id != null)
            .collect(Collectors.toSet());

        Set<Long> projectIds = properties.stream()
            .map(CustomerProperties::getProjectId)
            .filter(id -> id != null)
            .collect(Collectors.toSet());

        // Fetch all units and projects in batch
        Map<Long, Units> unitsMap = Map.of();
        Map<Long, Projects> projectsMap = Map.of();

        try {
            if (!unitIds.isEmpty()) {
                unitsMap = unitRepository.findAllById(unitIds).stream()
                    .collect(Collectors.toMap(Units::getId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("Error fetching units in batch: {}", e.getMessage(), e);
        }

        try {
            if (!projectIds.isEmpty()) {
                projectsMap = projectRepository.findAllById(projectIds).stream()
                    .collect(Collectors.toMap(Projects::getId, Function.identity()));
            }
        } catch (Exception e) {
            logger.error("Error fetching projects in batch: {}", e.getMessage(), e);
        }

        // Convert properties to DTOs with populated unit and project information
        final Map<Long, Units> finalUnitsMap = unitsMap;
        final Map<Long, Projects> finalProjectsMap = projectsMap;

        return properties.stream().map(p -> {
            CustomerPropertyDto dtoP = this.toPropertyDtoWithMaps(p, finalUnitsMap, finalProjectsMap);

            // Load secondary interactions
            try {
                java.util.List<vn.agis.crm.base.jpa.entity.InteractionsSecondary> list =
                    interactionsSecondaryRepository.findByCustomerPropertyId(p.getId());
                if (list != null) {
                    dtoP.setInteractionsSecondary(list.stream()
                        .map(this::toInteractionSecondaryDto)
                        .collect(Collectors.toList()));
                }
            } catch (Exception e) {
                logger.error("Error fetching secondary interactions for property ID {}: {}",
                    p.getId(), e.getMessage(), e);
            }

            return dtoP;
        }).collect(Collectors.toList());
    }

    /**
     * Convert CustomerProperties to CustomerPropertyDto using pre-fetched maps
     * This avoids individual database queries for each property
     */
    private CustomerPropertyDto toPropertyDtoWithMaps(CustomerProperties cp,
                                                     Map<Long, Units> unitsMap,
                                                     Map<Long, Projects> projectsMap) {
        CustomerPropertyDto dto = new CustomerPropertyDto();
        dto.setId(cp.getId());
        dto.setProjectId(cp.getProjectId());
        dto.setUnitId(cp.getUnitId());
        dto.setTransactionDate(cp.getTransactionDate());
        dto.setContractPrice(cp.getContractPrice());
        dto.setExternalAgencyName(cp.getExternalAgencyName());
        dto.setExternalSaleName(cp.getExternalSaleName());
        dto.setExternalSalePhone(cp.getExternalSalePhone());
        dto.setExternalSaleEmail(cp.getExternalSaleEmail());
        dto.setEmployeeId(cp.getEmployeeId());
        dto.setNotes(cp.getNotes());
        dto.setLegalStatus(cp.getLegalStatus());
        dto.setFirstInteraction(cp.getFirstInteraction());
        dto.setLastInteraction(cp.getLastInteraction());

        // Populate UnitDto from pre-fetched map
        if (cp.getUnitId() != null) {
            Units unit = unitsMap.get(cp.getUnitId());
            if (unit != null) {
                try {
                    dto.setUnit(unitMapper.toDto(unit));
                } catch (Exception e) {
                    logger.error("Error mapping unit to DTO for unit ID {}: {}",
                        cp.getUnitId(), e.getMessage(), e);
                }
            } else {
                logger.warn("Unit not found for unit ID: {}", cp.getUnitId());
            }
        }

        // Populate ProjectDto from pre-fetched map
        if (cp.getProjectId() != null) {
            Projects project = projectsMap.get(cp.getProjectId());
            if (project != null) {
                try {
                    dto.setProject(projectMapper.toDto(project));
                } catch (Exception e) {
                    logger.error("Error mapping project to DTO for project ID {}: {}",
                        cp.getProjectId(), e.getMessage(), e);
                }
            } else {
                logger.warn("Project not found for project ID: {}", cp.getProjectId());
            }
        }

        return dto;
    }

    private CustomerOfferDto toOfferDto(CustomerOffers co) {
        CustomerOfferDto dto = new CustomerOfferDto();
        dto.setId(co.getId());
        dto.setProjectId(co.getProjectId());
        dto.setFirstInteraction(co.getFirstInteraction());
        dto.setLastInteraction(co.getLastInteraction());
        dto.setStatus(co.getStatus());
        dto.setNotes(co.getNotes());
        return dto;
    }

    private vn.agis.crm.base.jpa.dto.InteractionSecondaryDto toInteractionSecondaryDto(vn.agis.crm.base.jpa.entity.InteractionsSecondary is) {
        vn.agis.crm.base.jpa.dto.InteractionSecondaryDto dto = new vn.agis.crm.base.jpa.dto.InteractionSecondaryDto();
        dto.setId(is.getId());
        dto.setExpectedSellPrice(is.getExpectedSellPrice());
        dto.setExpectedRentPrice(is.getExpectedRentPrice());
        dto.setResult(is.getResult());
        if (is.getHappenedAt() != null) {
            dto.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").format(is.getHappenedAt()));
        }
        dto.setNotes(is.getNotes());

        // NEW: Set audit fields for tracking who created the interaction
        dto.setCreatedBy(is.getCreatedBy());
        if (is.getCreatedBy() != null) {
            Employee employee = employeeRepository.findById(is.getCreatedBy()).orElse(null);
            dto.setCreatedName(employee != null ? employee.getFullName() : null);
        }

        return dto;
    }

    private vn.agis.crm.base.jpa.dto.InteractionPrimaryDto toInteractionPrimaryDto(vn.agis.crm.base.jpa.entity.InteractionsPrimary ip) {
        vn.agis.crm.base.jpa.dto.InteractionPrimaryDto dto = new vn.agis.crm.base.jpa.dto.InteractionPrimaryDto();
        dto.setId(ip.getId());
        dto.setResult(ip.getResult());
        if (ip.getHappenedAt() != null) {
            dto.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").format(ip.getHappenedAt()));
        }
        dto.setNotes(ip.getNotes());

        // NEW: Set audit fields for tracking who created the interaction
        dto.setCreatedBy(ip.getCreatedBy());
        if (ip.getCreatedBy() != null) {
            Employee employee = employeeRepository.findById(ip.getCreatedBy()).orElse(null);
            dto.setCreatedName(employee != null ? employee.getFullName() : null);
        }

        return dto;
    }
}

