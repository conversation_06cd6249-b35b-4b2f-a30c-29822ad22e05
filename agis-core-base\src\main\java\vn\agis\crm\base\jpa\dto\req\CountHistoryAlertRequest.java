package vn.agis.crm.base.jpa.dto.req;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CountHistoryAlertRequest {

    private Long msisdn;
    private Long alertConfigId;
    private int eventType;
    private Long dataThreshold;
    private Long smsThreshold;
}
