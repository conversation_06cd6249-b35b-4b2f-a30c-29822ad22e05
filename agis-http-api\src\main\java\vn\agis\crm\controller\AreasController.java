package vn.agis.crm.controller;

import java.util.List;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import vn.agis.crm.base.jpa.dto.AreasSearchResDTO;
import vn.agis.crm.base.jpa.entity.Areas;
import vn.agis.crm.base.utils.StringUtils;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.model.dto.AreasSearchDTO;
import vn.agis.crm.model.dto.CreateAreasRequest;
import vn.agis.crm.model.dto.UpdateAreasRequest;
import vn.agis.crm.service.AreasService;

@RestController
@RequestMapping("/areas")
public class AreasController extends CrudController<Areas, Long> {

    private final Logger log = LoggerFactory.getLogger(AreasController.class);

    private AreasService areasService;

    @Autowired
    public AreasController(AreasService service) {
        super(service);
        this.areasService = service;
        this.baseUrl = "/areas";
    }

    @GetMapping("/search")
//    @PreAuthorize("hasAnyAuthority('searchAreas')")
    @Operation(description = "Search areas with pagination and filters")
    public ResponseEntity<Page<AreasSearchResDTO>> searchAreas(
        @RequestParam(name = "name", required = false, defaultValue = " ") String name,
        @RequestParam(name = "status", required = false, defaultValue = "-1") Integer status,
        @RequestParam(name = "type", required = false, defaultValue = "-1") Integer type,
        @RequestParam(name = "parentId", required = false, defaultValue = "-1") Long parentId,
        @RequestParam(name = "createdBy", required = false, defaultValue = "-1") Long createdBy,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "createdDate,desc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        AreasSearchDTO searchDTO = new AreasSearchDTO(
            name.trim().equals(" ") ? null : name,
            status == -1 ? null : status,
            type,
            parentId,
            createdBy == -1 ? null : createdBy,
            page,
            size,
            StringUtils.camelToSnake(sortBy)
        );

        Page<AreasSearchResDTO> areasPage = areasService.searchAreas(searchDTO, listRequest.getPageable());
        return ResponseEntity.ok().body(areasPage);
    }

    @GetMapping("/getAreaHierarchy")
//    @PreAuthorize("hasAnyAuthority('searchAreas')")
    @Operation(description = "Search areas with pagination and filters")
    public ResponseEntity<List<Areas>> getAreaHierarchy(
        @RequestParam(name = "parentId", required = false, defaultValue = "-1") Long parentId) {
        List<Areas> areasPage = areasService.getAreaHierarchy(parentId);
        return ResponseEntity.ok().body(areasPage);
    }

    @GetMapping("/{id}")
//    @PreAuthorize("hasAnyAuthority('getAreas')")
    @Operation(description = "Get areas by ID")
    public ResponseEntity<Areas> getAreas(@PathVariable Long id) {
        Areas areas = areasService.get(id);
        return ResponseEntity.ok().body(areas);
    }

    @PostMapping()
//    @PreAuthorize("hasAnyAuthority('createAreas')")
    @Operation(description = "Create new areas")
    public ResponseEntity<Areas> createAreas(@Valid @RequestBody CreateAreasRequest request, HttpServletRequest httpServletRequest) {
        Areas entity = new Areas();
        entity.setName(request.getName());
        entity.setDescription(request.getDescription());
        entity.setParentId(request.getParentId());
        entity.setStatus(request.getStatus());
        entity.setType(request.getType());

        Areas areas = areasService.createAreas(entity);
        return ResponseEntity.ok().body(areas);
    }

    @PutMapping("/{id}")
//    @PreAuthorize("hasAnyAuthority('updateAreas')")
    @Operation(description = "Update areas by ID")
    public ResponseEntity<Areas> updateAreas(@PathVariable Long id, @Valid @RequestBody UpdateAreasRequest request,
        HttpServletRequest httpServletRequest) {
        Areas entity = new Areas();
        entity.setId(id);
        entity.setName(request.getName());
        entity.setDescription(request.getDescription());
        entity.setParentId(request.getParentId());
        entity.setStatus(request.getStatus());

        Areas updatedAreas = areasService.updateAreas(entity);
        return ResponseEntity.ok().body(updatedAreas);
    }

    @PutMapping("/change-status/{id}")
//    @PreAuthorize("hasAnyAuthority('updateAreas')")
    @Operation(description = "Change areas status (active/inactive)")
    public ResponseEntity<Void> changeStatus(@PathVariable Long id) {
        areasService.changeStatus(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
//    @PreAuthorize("hasAnyAuthority('deleteAreas')")
    @Operation(description = "Delete areas by ID")
    public ResponseEntity<Void> deleteAreas(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        areasService.deleteById(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/all")
//    @PreAuthorize("hasAnyAuthority('getAreas')")
    @Operation(description = "Get all areas without pagination")
    public ResponseEntity<List<Areas>> getAllAreas() {
        List<Areas> areasList = areasService.getAllAreas();
        return ResponseEntity.ok().body(areasList);
    }

    @GetMapping("/check-name-exists")
//    @PreAuthorize("hasAnyAuthority('createAreas', 'updateAreas')")
    @Operation(description = "Check if areas name already exists")
    public ResponseEntity<Boolean> checkNameExists(@RequestParam String name) {
        Boolean exists = areasService.checkAreasNameExists(name);
        return ResponseEntity.ok().body(exists);
    }

    @GetMapping("/dropdown")
//    @PreAuthorize("hasAnyAuthority('searchAreas')")
    @Operation(description = "Get areas for dropdown selection")
    public ResponseEntity<Page<AreasSearchResDTO>> getAreasDropdown(
        @RequestParam(name = "name", required = false, defaultValue = " ") String name,
        @RequestParam(name = "status", required = false, defaultValue = "1") Integer status,
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @RequestParam(name = "size", required = false, defaultValue = "50") Integer size,
        @RequestParam(name = "sort", required = false, defaultValue = "name,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        AreasSearchDTO searchDTO = new AreasSearchDTO(
            name.trim().equals(" ") ? null : name,
            status,
            null,
            null,
            null,
            page,
            size,
            StringUtils.camelToSnake(sortBy)
        );

        Page<AreasSearchResDTO> areasPage = areasService.searchAreas(searchDTO, listRequest.getPageable());
        return ResponseEntity.ok().body(areasPage);
    }
}
