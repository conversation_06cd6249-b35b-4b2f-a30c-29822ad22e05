package vn.agis.crm.base.jpa.dto.docs;

import com.google.gson.reflect.TypeToken;
import vn.agis.crm.base.jpa.entity.ProjectEntity;
import vn.agis.crm.base.utils.Utils;

import java.util.List;
import java.util.Map;

public class Project extends DTO{
    private String name;
    private Map<String,String> title;
    private String key;
    private List<String> langSupports;
    private String hostName;
    private String token;
    private String icon;
    private String description;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public List<String> getLangSupports() {
        return langSupports;
    }

    public void setLangSupports(List<String> langSupports) {
        this.langSupports = langSupports;
    }

    public String getHostName() {
        return hostName;
    }

    public void setHostName(String hostName) {
        this.hostName = hostName;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public Map<String, String> getTitle() {
        return title;
    }

    public void setTitle(Map<String, String> title) {
        this.title = title;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Project(){};
    public Project(ProjectEntity entity){
        id = entity.getId();
        name = entity.getName();
        if(entity.getTitle() != null){
            title = Utils.gson.fromJson(entity.getTitle(), new TypeToken<Map<String,String>>(){}.getType());
        }
        key = entity.getKey();
        if(entity.getLangSupports() != null){
            langSupports = Utils.gson.fromJson(entity.getLangSupports(), new TypeToken<List<String>>(){}.getType());
        }
        hostName = entity.getHostName();
        token = entity.getToken();
        icon = entity.getIcon();
        description = entity.getDescription();
    }

    public ProjectEntity convertToEntity(){
        ProjectEntity projectEntity = new ProjectEntity();
        projectEntity.setId(id);
        projectEntity.setName(name);
        projectEntity.setTitle(Utils.gson.toJson(title));
        projectEntity.setKey(key);
        projectEntity.setLangSupports(Utils.gson.toJson(langSupports));
        projectEntity.setHostName(hostName);
        projectEntity.setToken(token);
        projectEntity.setIcon(icon);
        projectEntity.setDescription(description);
        return projectEntity;
    }
}
