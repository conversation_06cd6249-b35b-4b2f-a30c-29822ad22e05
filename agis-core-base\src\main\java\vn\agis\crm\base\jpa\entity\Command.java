package vn.agis.crm.base.jpa.entity;

import lombok.Data;

import jakarta.persistence.*;

import java.util.List;

@Entity
@Data
@Table(name = "command")
public class Command {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "command_id")
    private String commandId;

    @Column(name = "command_name")
    private String commandName;

    @Column(name = "command_type")
    private String commandType;
    @Column(name = "input_schema", columnDefinition = "LONGTEXT")
    private String inputSchema;

    @Column(name = "created_at")
    private Long createdAt;

    @Column(name = "created_by")
    private Long createdBy;

    @Column(name = "device_type_id")
    private Long deviceTypeId;

    @Transient
    private Integer status;

    @Transient
    private String responseData;

}

