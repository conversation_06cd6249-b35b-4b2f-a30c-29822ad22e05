package vn.agis.crm.service;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import jakarta.transaction.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.entity.Permission;
import vn.agis.crm.base.jpa.entity.Role;
import vn.agis.crm.base.jpa.entity.RolePermission;
import vn.agis.crm.repository.PermissionRepository;
import vn.agis.crm.repository.RolePermissionRepository;
import vn.agis.crm.repository.RoleRepository;

import static vn.agis.crm.base.constants.Constants.Method.GET_PERMISSIONKEY_BY_USER;
import static vn.agis.crm.base.constants.Constants.Method.GET_PERMISSIONKEY_DYNAMIC_CONFIG;

@Service
@Transactional
public class PermissionService {

    private PermissionRepository permissionRepository;

    @Autowired
    private RolePermissionRepository rolePermissionRepository;
    @Autowired
    private RoleRepository roleRepository;

    public PermissionService(PermissionRepository permissionRepository) {
        this.permissionRepository = permissionRepository;
    }

    public Event process(Event event) {
        Event response = null;
        switch (event.method) {
            case JpaConstants.Method.GET_ALL -> response = getAllPermission(event);
            case JpaConstants.Method.CREATE -> response = createPermistion(event);
            case JpaConstants.Method.UPDATE -> response = updatePermistion(event);
            case JpaConstants.Method.DELETE -> response = deletePermistion(event);
            case Constants.Method.GET_PERMISSION_BY_USER -> response = getPermissionByUser(event);
            case GET_PERMISSIONKEY_BY_USER -> response = getAllReportIdByPermissionUser(event);
            case GET_PERMISSIONKEY_DYNAMIC_CONFIG -> response = getAllChartIdByPermission(event);
        }
        return response;
    }

    public Event getAllPermission(Event event) {
        List<Permission> permissionList = this.permissionRepository.findAll();
        return event.createResponse(permissionList, 200, "OK");
    }

    public Event createPermistion(Event event) {
        Permission permission = (Permission) event.payload;
            permission = permissionRepository.save(permission);
            Role role = roleRepository.findFirstByName(RoleService.SUPER_ADMIN_ROLENAME);
            if (role != null) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setPermissionId(permission.getId());
                rolePermission.setRoleId(role.getId());
                rolePermissionRepository.save(rolePermission);
            }
            return event.createResponse(permission, 200, "OK");
    }

    public Event deletePermistion(Event event) {
        Long permissionId = (Long) event.payload;
        permissionRepository.deletePermissionByPermissionKey("getReport_" + permissionId);
        rolePermissionRepository.deleteByPermissionId(permissionId);
        return event.createResponse(null, 200, "OK");
    }

    public Event updatePermistion(Event event) {
        Permission permissionRq = (Permission) event.payload;
        Permission permission = permissionRepository.getPermissionByPermissionKey(permissionRq.getPermissionKey());
        permission.setDescription(permissionRq.getDescription());
        return event.createResponse(permissionRepository.save(permission), 200, "OK");
    }

    public Event getPermissionByUser(Event event) {
        // chỉ admin hệ thống hiển thị full
        if (event.userId == 1) {
            return getAllPermission(event);
        } else {
            return event.createResponse(permissionRepository.getListPermissionByUserId(event.userId), ResponseCode.OK, "OK");
        }
    }

    public Event getAllReportIdByPermissionUser(Event event){
        Event request = (Event) event.payload;
        List<String> permissionKey = permissionRepository.getPermisstionKeyReportByUser(request.userId);
        List<Long> reportIds = permissionKey.stream().map(p ->{
            Long id = Long.valueOf(p.substring(10));
            return id;
        }).collect(Collectors.toList());
        return event.createResponse(reportIds,ResponseCode.OK,"OK");
    }
    public Event getAllChartIdByPermission(Event event){
        Event request = (Event) event.payload;
        List<String> permissionKey = permissionRepository.getPermisstionKeyChartByUser(request.userId);
        List<Long> chartIds = permissionKey.stream().map(p ->{
            Long id = Long.valueOf(p.substring(13));
            return id;
        }).collect(Collectors.toList());
        return event.createResponse(chartIds,ResponseCode.OK,"OK");
    }
}
