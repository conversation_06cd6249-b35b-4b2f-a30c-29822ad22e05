package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class CreateUserReq {

    private String username;
    private String description;
    private String email;
    private String password;
    private String phone;
    private String addressContact;
    private Integer type;
    private String representativeName;
    private Long accountRootId;
    private List<Long> roleLst;
    private String taxCode;
    private String addressHeadOffice;
    private String name;
    private Integer provinceCodeOffice;
    private Integer wardCodeOffice;
    private Integer provinceCodeAddress;
    private Integer wardCodeAddress;
    private Integer apartment;

}
