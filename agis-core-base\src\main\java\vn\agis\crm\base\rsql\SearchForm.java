package vn.agis.crm.base.rsql;

import java.io.Serializable;

/**
 * Created by huyvv
 * Date: 12/05/2021
 * Time: 3:33 PM
 * for all issues, contact me: <EMAIL>
 **/
public class SearchForm implements Serializable {
    private Integer pageSize = 20;
    private Integer pageNumber = 0;
    private String sort = "id:DESC";

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        this.pageNumber = pageNumber;
    }

    public String getSort() {
        return sort;
    }

    public void setSort(String sort) {
        this.sort = sort;
    }
}
