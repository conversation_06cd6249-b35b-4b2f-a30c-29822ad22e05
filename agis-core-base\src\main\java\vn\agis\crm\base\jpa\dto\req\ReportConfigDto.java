package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Data
public class ReportConfigDto {
    private Long Id;
    private Long reportContentId;
    private String reportName;
    private Integer status;
    private Integer enablePreview;
    private List<FilterParam> filterParam;
    private String schema;
    private String query;
    private String tableName;
    private String columnDisplay;
    private String columnQueryResult;


    public Map<String, FilterParam> toMap(){
        Map<String, FilterParam> map = new TreeMap<>();
        for (FilterParam param : this.filterParam){
            map.put(param.prKey, param);
        }
        return map;
    }
}
