package vn.agis.crm.base.jpa.dto.res;

import lombok.Data;
import vn.agis.crm.base.jpa.entity.Projects;

import java.util.Date;

/**
 * Enhanced Project DTO that includes customer statistics
 */
@Data
public class ProjectWithStatsDto {
    
    // Basic project information
    private Long id;
    private String name;
    private String description;
    private boolean isActive;
    private Date deletedAt;
    private Date createdAt;
    private Long createdBy;
    private Date updatedAt;
    private Long updatedBy;
    
    // Customer statistics
    private Long totalCustomersPurchased;
    private Long totalCustomersWithOffers;
    
    /**
     * Constructor to create from Projects entity
     */
    public ProjectWithStatsDto(Projects project) {
        this.id = project.getId();
        this.name = project.getName();
        this.description = project.getDescription();
        this.isActive = project.isActive();
        this.deletedAt = project.getDeletedAt();
        this.createdAt = project.getCreatedAt();
        this.createdBy = project.getCreatedBy();
        this.updatedAt = project.getUpdatedAt();
        this.updatedBy = project.getUpdatedBy();
        
        // Initialize statistics to 0
        this.totalCustomersPurchased = 0L;
        this.totalCustomersWithOffers = 0L;
    }
    
    /**
     * Constructor with all fields including statistics
     */
    public ProjectWithStatsDto(Projects project, Long totalCustomersPurchased, Long totalCustomersWithOffers) {
        this(project);
        this.totalCustomersPurchased = totalCustomersPurchased;
        this.totalCustomersWithOffers = totalCustomersWithOffers;
    }
    
    /**
     * Default constructor
     */
    public ProjectWithStatsDto() {
        this.totalCustomersPurchased = 0L;
        this.totalCustomersWithOffers = 0L;
    }
    
    /**
     * Static factory method to create from Projects entity
     */
    public static ProjectWithStatsDto fromProject(Projects project) {
        return new ProjectWithStatsDto(project);
    }
    
    /**
     * Static factory method to create with statistics
     */
    public static ProjectWithStatsDto fromProjectWithStats(Projects project, Long totalCustomersPurchased, Long totalCustomersWithOffers) {
        return new ProjectWithStatsDto(project, totalCustomersPurchased, totalCustomersWithOffers);
    }
}
