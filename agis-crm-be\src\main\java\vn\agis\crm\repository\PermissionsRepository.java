package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Permissions;
import vn.agis.crm.constant.SQL;

import java.util.List;

@Repository
public interface PermissionsRepository extends JpaRepository<Permissions, Long> {

    boolean existsByName(String name);

    Permissions findFirstByName(String name);

    @Query(nativeQuery = true, value = SQL.GET_PERMISSION_KEY_BY_ROLE_IDS)
    List<String> getListPermissionKeyByRoleIds(Long ids);

    /**
     * Find permissions by description with case-insensitive partial matching
     * Uses native query to handle @Lob field properly
     */
    @Query(value = "SELECT * FROM permissions p WHERE LOWER(p.description) LIKE LOWER(CONCAT('%', :description, '%'))",
           nativeQuery = true)
    List<Permissions> findByDescriptionContainingIgnoreCase(@Param("description") String description);
}

