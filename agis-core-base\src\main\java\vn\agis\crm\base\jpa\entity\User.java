package vn.agis.crm.base.jpa.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import lombok.Data;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.List;

@Entity
@Table(name = "USERS")
@Data
public class User extends AbstractEntity {

    private static final BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "USERNAME")
    private String username;

    @Column(name = "EMAIL")
    private String email;

    @Column(name = "PASSWORD")
    @JsonIgnore
    private String password;

    @Column(name = "PHONE")
    private String phone;

    @Column(name = "ADDRESS_CONTACT")
    private String addressContact;

    @Column(name = "TYPE")
    private Integer type;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "DESCRIPTION")
    private String description;

    @Column(name = "NAME")
    private String name;

    @Column(name = "REPRESENTATIVE_NAME")
    private String representativeName;

    @Column(name = "TAX_CODE")
    private String taxCode;

    @Column(name = "ADDRESS_HEAD_OFFICE")
    private String addressHeadOffice;

    @Column(name = "PROVINCE_CODE_OFFICE")
    private Integer provinceCodeOffice;

    @Column(name = "WARD_CODE_OFFICE")
    private Integer wardCodeOffice;

    @Column(name = "PROVINCE_CODE_ADDRESS")
    private Integer provinceCodeAddress;

    @Column(name = "WARD_CODE_ADDRESS")
    private Integer wardCodeAddress;

    @Column(name = "APARTMENT")
    private Integer apartment;

    @Column(name = "USAGE_TYPE")
    private Integer usageType;

    @Transient
    private List<String> roles;

    @Transient
    private List<String> authorities;

    @Transient
    private Integer synchronizeStatus;

    @Transient
    private String tokenOauth2;
    @JsonIgnore
    public void setEncryptedPassword(String password) {
        this.password = passwordEncoder.encode(password);
    }

    public Boolean authenticate(String password) {
        return passwordEncoder.matches(password, this.password);
    }

}
