package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import vn.agis.crm.base.jpa.dto.req.SearchWardRequest;
import vn.agis.crm.base.jpa.entity.Ward;
import vn.agis.crm.constant.sql.SQLWard;

import java.util.List;

public interface WardRepository extends JpaRepository<Ward, Integer> {

    @Query(nativeQuery = true, value = SQLWard.SQL_SEARCH_WARD)
    List<Ward> searchWard(@Param("search")SearchWardRequest search);
}
