package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "customer_properties")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerProperties extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Column(name = "unit_id", nullable = false)
    private Long unitId;

    @Temporal(TemporalType.DATE)
    @Column(name = "transaction_date", nullable = false)
    private Date transactionDate;

    @Column(name = "contract_price", precision = 18, scale = 2, nullable = false)
    private BigDecimal contractPrice;

    @Column(name = "external_agency_name")
    private String externalAgencyName;

    @Column(name = "external_sale_name")
    private String externalSaleName;

    @Column(name = "external_sale_phone")
    private String externalSalePhone;

    @Column(name = "external_sale_email")
    private String externalSaleEmail;

    @Column(name = "employee_id")
    private Long employeeId;

    @Lob
    @Column(name = "notes")
    private String notes;

    @Column(name = "legal_status")
    private String legalStatus;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "first_interaction")
    private Date firstInteraction;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_interaction")
    private Date lastInteraction;
}

