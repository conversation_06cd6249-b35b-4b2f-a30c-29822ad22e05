//package vn.agis.crm.repository.spec;
//
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Objects;
//import org.apache.logging.log4j.util.Strings;
//import org.springframework.data.jpa.domain.Specification;
//import jakarta.persistence.criteria.CriteriaBuilder;
//import jakarta.persistence.criteria.CriteriaQuery;
//import jakarta.persistence.criteria.Predicate;
//import jakarta.persistence.criteria.Root;
//import vn.agis.crm.base.constants.Constants;
//
///**
// * <AUTHOR> HuyNV
// * @version    : 1.0
// * 04/02/2021
// */
//public final class CustomerSpecs {
//    /**
//     * Hàm tìm kiếm động
//     *
//     * @param customerRequest tu khoa tim kiem
//     * @return dieu kien tim kiem
//     */
//    public static Specification<Customer> customerSpec(final SearchCustomerRequest customerRequest) {
//        return (Root<Customer> root, CriteriaQuery<?> query, CriteriaBuilder criteriaBuilder) -> {
//            final List<Predicate> predicates = new ArrayList<>();
//
//            // neu search khac null ad dieu kien where
//            if (Strings.isNotBlank(customerRequest.getCustomerCode())) {
//                Predicate customerCodeContain = criteriaBuilder.like(
//                        criteriaBuilder.lower(root.get("customerCode")),
//                        Constants.Word.PERCENT + customerRequest.getCustomerCode().toLowerCase() + Constants.Word.PERCENT
//                );
//                predicates.add(criteriaBuilder.and(criteriaBuilder.or(
//                    customerCodeContain)));
//            }
//            if (Strings.isNotBlank(customerRequest.getCustomerName())) {
//                Predicate customerNameContain = criteriaBuilder.like(
//                        criteriaBuilder.lower(root.get("customerName")),
//                        Constants.Word.PERCENT + customerRequest.getCustomerName().toLowerCase() + Constants.Word.PERCENT
//                );
//                predicates.add(criteriaBuilder.and(criteriaBuilder.or(
//                    customerNameContain)));
//            }
//            if (Strings.isNotBlank(customerRequest.getPhone())) {
//                Predicate phoneContain = criteriaBuilder.like(
//                        criteriaBuilder.lower(root.get("phone")),
//                        Constants.Word.PERCENT + customerRequest.getPhone().toLowerCase() + Constants.Word.PERCENT
//                );
//                predicates.add(criteriaBuilder.and(criteriaBuilder.or(
//                    phoneContain)));
//            }
//            if (Strings.isNotBlank(customerRequest.getTaxId())) {
//                Predicate taxIdContain = criteriaBuilder.like(
//                        criteriaBuilder.lower(root.get("taxId")),
//                        Constants.Word.PERCENT + customerRequest.getTaxId().toLowerCase() + Constants.Word.PERCENT
//                );
//                predicates.add(criteriaBuilder.and(criteriaBuilder.or(
//                    taxIdContain)));
//            }
//            // neu status khac null ad dieu kien where
//            if (Objects.nonNull(customerRequest.getStatus())) {
//                Predicate statusEqual = criteriaBuilder.equal(root.get("status"), customerRequest.getStatus());
//                predicates.add(statusEqual);
//            }
//
//            if (Objects.nonNull(customerRequest.getCustomerType())) {
//                Predicate customerTypeEqual = criteriaBuilder.equal(root.get("customerType"), customerRequest.getCustomerType());
//                predicates.add(customerTypeEqual);
//            }
//
//            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
//        };
//    }
//}
