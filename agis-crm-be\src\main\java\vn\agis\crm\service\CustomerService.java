package vn.agis.crm.service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import vn.agis.crm.base.jpa.dto.AssignmentHistoryResponseDto;
import vn.agis.crm.base.jpa.dto.GetAssignmentHistoryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.CustomerSearchDto;
import vn.agis.crm.base.jpa.dto.CustomerDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.CustomerDependencyError;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.dto.req.OfferUpsertDto;
import vn.agis.crm.base.jpa.entity.CustomerAssignments;
import vn.agis.crm.base.jpa.entity.CustomerOffers;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.mapper.CustomerMapper;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.util.BaseController;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.repository.EmployeeRepository;


@Service
@Transactional
public class CustomerService {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerMapper customerMapper;
    @Autowired
    private vn.agis.crm.service.mapper.CustomerResponseMapper customerResponseMapper;

    @Autowired
    private vn.agis.crm.repository.CustomerAssignmentRepository customerAssignmentRepository;

    @Autowired
    private vn.agis.crm.repository.CustomerRelativeRepository customerRelativeRepository;

    @Autowired
    private vn.agis.crm.repository.CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private vn.agis.crm.repository.CustomerOfferRepository customerOfferRepository;

    @Autowired
    private vn.agis.crm.repository.RuleRunRepository ruleRunRepository;

    @Autowired
    private vn.agis.crm.repository.InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private vn.agis.crm.repository.InteractionsSecondaryRepository interactionsSecondaryRepository;


    public Event process(Event event) {
        switch (event.method) {
//            case Method.CREATE:
//                return create(event);
//            case Method.UPDATE:
//                return update(event);
            case Method.DELETE:
                return delete(event);
            case Method.FIND_BY_ID:
                return findById(event);
            case Method.SEARCH:
                return search(event);
            case vn.agis.crm.base.constants.Constants.Method.CREATE_CUSTOMER_V2:
                return createV2(event);
            case vn.agis.crm.base.constants.Constants.Method.UPDATE_CUSTOMER_V2:
                return updateV2(event);
            case vn.agis.crm.base.constants.Constants.Method.CHECK_EXISTS:
                return processCheckExists(event);
            case vn.agis.crm.base.constants.Constants.Method.VALIDATE_CUSTOMER_DELETION:
                return validateCustomerDeletion(event);
            case vn.agis.crm.base.constants.Constants.Method.UPDATE_ASSIGNMENT_EMPLOYEE:
                return updateAssignmentEmployeeEvent(event);
            case vn.agis.crm.base.constants.Constants.Method.GET_ASSIGNMENT_HISTORY:
                return getAssignmentHistoryEvent(event);
            case vn.agis.crm.base.constants.Constants.Method.DELETE_ASSIGNMENT:
                return deleteAssignmentEvent(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event create(Event event) {
        CustomerDto dto = (CustomerDto) event.payload;
        if (dto.getFullName() == null || dto.getPhone() == null) {
            return event.createResponse(null, 400, "Missing required fields (fullName, phone)");
        }
        if (customerRepository.existsByPhone(dto.getPhone())) {
            return event.createResponse(null, 409, "Duplicate phone");
        }
        Customers entity = customerMapper.toEntity(dto);
        entity.setCreatedBy(event.userId);
        entity.setCreatedAt(new Date());
        entity = customerRepository.save(entity);
        return event.createResponse(customerResponseMapper.toResDto(entity), 201, "Created");
    }

    private Event update(Event event) {
        CustomerDto dto = (CustomerDto) event.payload;
        if (dto.getId() == null) {
            return event.createResponse(null, 400, "Missing id");
        }
        return customerRepository.findById(dto.getId()).map(existing -> {
            String targetPhone = dto.getPhone() != null ? dto.getPhone() : existing.getPhone();
            Customers dup = customerRepository.findFirstByPhone(targetPhone);
            if (dup != null && !dup.getId().equals(existing.getId())) {
                return event.createResponse(null, 409, "Duplicate phone");
            }
            customerMapper.updateEntityFromDto(existing, dto);
            existing.setUpdatedAt(new Date());
            existing.setUpdatedBy(event.userId);
            Customers saved = customerRepository.save(existing);
            return event.createResponse(customerResponseMapper.toResDto(saved), 200, "Success");
        }).orElse(event.createResponse(null, 404, "Customer not found"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }

        // Validate customer deletion before proceeding
        CustomerDeletionValidationResult validationResult = performCustomerDeletionValidation(id);
        if (!validationResult.isCanDelete()) {
            return event.createResponse(validationResult, ResponseCode.BAD_REQUEST, validationResult.getMessage());
        }

        // If validation passes, proceed with deletion
        customerRepository.deleteById(id);
        return event.createResponse(null, 200, "Success");
    }

    private Event findById(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }

        return customerRepository.findById(id)
                .map(customer -> {
                    // Apply role-based access control
                    if (!hasAccessToCustomer(customer, event)) {
                        return event.createResponse(null, 403, "Access denied: You don't have permission to view this customer");
                    }
                    return event.createResponse(customerResponseMapper.toResDto(customer), 200, "Success");
                })
                .orElse(event.createResponse(null, 404, "Customer not found"));
    }

    private Event search(Event event) {
        CustomerSearchDto searchDto = (CustomerSearchDto) event.payload;

        // Apply role-based access control
        Long filteredEmployeeId = applyRoleBasedFilter(event);
        if (filteredEmployeeId == null && !isAdminRole(event.userType)) {
            // Non-admin user with no valid employee ID - return empty result
            PageInfo pageInfo = new PageInfo();
            pageInfo.setData(ObjectMapperUtil.toJsonString(new java.util.ArrayList<>()));
            pageInfo.setTotalCount(0L);
            return event.createResponse(pageInfo, 200, "Success");
        }

        BaseController.ListRequest listRequest = new BaseController.ListRequest(
                searchDto.getSize(), searchDto.getPage(), searchDto.getSortBy());
        String fullName = (searchDto.getFullName() == null || searchDto.getFullName().isEmpty()) ? null : searchDto.getFullName();
        String phone = (searchDto.getPhone() == null || searchDto.getPhone().isEmpty()) ? null : searchDto.getPhone();
        String email = (searchDto.getEmail() == null || searchDto.getEmail().isEmpty()) ? null : searchDto.getEmail();
        String cccd = (searchDto.getCccd() == null || searchDto.getCccd().isEmpty()) ? null : searchDto.getCccd();
        String address = (searchDto.getAddress() == null || searchDto.getAddress().isEmpty()) ? null : searchDto.getAddress();
        String sourceType = (searchDto.getSourceType() == null || searchDto.getSourceType().isEmpty()) ? null : searchDto.getSourceType();
        String sourceDetail = (searchDto.getSourceDetail() == null || searchDto.getSourceDetail().isEmpty()) ? null : searchDto.getSourceDetail();
        String businessField = (searchDto.getBusinessField() == null || searchDto.getBusinessField().isEmpty()) ? null : searchDto.getBusinessField();
        String interests = (searchDto.getInterests() == null || searchDto.getInterests().isEmpty()) ? null : searchDto.getInterests();
        String relativeName = (searchDto.getRelativeName() == null || searchDto.getRelativeName().isEmpty()) ? null : searchDto.getRelativeName();
        String birthDateFrom = (searchDto.getBirthDateFrom() == null || searchDto.getBirthDateFrom().isEmpty()) ? null : searchDto.getBirthDateFrom();
        String birthDateTo = (searchDto.getBirthDateTo() == null || searchDto.getBirthDateTo().isEmpty()) ? null : searchDto.getBirthDateTo();

        // Parse birthday day/month from birthdayDayMonth parameter
        Integer birthdayDay = null;
        Integer birthdayMonth = null;
        if (searchDto.getBirthdayDayMonth() != null && !searchDto.getBirthdayDayMonth().isEmpty()) {
            String[] dayMonthParts = parseBirthdayDayMonth(searchDto.getBirthdayDayMonth());
            if (dayMonthParts != null) {
                birthdayDay = Integer.parseInt(dayMonthParts[0]);
                birthdayMonth = Integer.parseInt(dayMonthParts[1]);
            }
        }

        Long projectId = searchDto.getProjectId();
        Long purchasedProjectId = searchDto.getPurchasedProjectId();
        Long activeOfferProjectId = searchDto.getActiveOfferProjectId();
        String propertyType = (searchDto.getPropertyType() == null || searchDto.getPropertyType().isEmpty()) ? null : searchDto.getPropertyType();

        // Use role-based filtered employee ID or original search parameter
        Long employeeId = searchDto.getEmployeeId();

        Page<Customers> page = customerRepository.searchWithRoleFilter(fullName, phone, email, cccd, address, sourceType, sourceDetail,
                businessField, interests, relativeName, birthDateFrom, birthDateTo, birthdayDay, birthdayMonth,
                projectId, purchasedProjectId, activeOfferProjectId, propertyType, employeeId,
                event.userType, event.userId, listRequest.getPageable());
        PageInfo pageInfo = new PageInfo();
        java.util.List<CustomerResDto> dtos = new java.util.ArrayList<>();
        for (Customers c : page.getContent()) {
            dtos.add(customerResponseMapper.toResDto(c));
        }
        pageInfo.setData(ObjectMapperUtil.toJsonString(dtos));
        pageInfo.setTotalCount(page.getTotalElements());
        return event.createResponse(pageInfo, 200, "Success");
    }

    /**
     * Parse birthday day/month from string format (DD/MM or MM-DD)
     * @param birthdayDayMonth String in format "DD/MM" or "MM-DD"
     * @return String array [day, month] or null if invalid format
     */
    private String[] parseBirthdayDayMonth(String birthdayDayMonth) {
        if (birthdayDayMonth == null || birthdayDayMonth.trim().isEmpty()) {
            return null;
        }

        try {
            String[] parts;
            if (birthdayDayMonth.contains("/")) {
                // DD/MM format
                parts = birthdayDayMonth.split("/");
            } else if (birthdayDayMonth.contains("-")) {
                // MM-DD format
                parts = birthdayDayMonth.split("-");
            } else {
                return null;
            }

            if (parts.length != 2) {
                return null;
            }

            int day = Integer.parseInt(parts[0].trim());
            int month = Integer.parseInt(parts[1].trim());

            // Validate day and month ranges
            if (day < 1 || day > 31 || month < 1 || month > 12) {
                return null;
            }

            // Handle February 29th and other month-specific validations
            if (month == 2 && day > 29) {
                return null; // February can't have more than 29 days
            }
            if ((month == 4 || month == 6 || month == 9 || month == 11) && day > 30) {
                return null; // April, June, September, November have max 30 days
            }

            return new String[]{String.valueOf(day), String.valueOf(month)};

        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Event createV2(Event event) {
        vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest req = (vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest) event.payload;
        // 1) create customer
        Customers entity = new vn.agis.crm.base.jpa.entity.Customers();
        entity.setFullName(req.getFullName());
        entity.setPhone(req.getPhone());
        entity.setEmail(req.getEmail());
        entity.setCccd(req.getCccd());
        // parse dd/MM/yyyy for birthDate
        if (req.getBirthDate() != null && !req.getBirthDate().isEmpty()) {
            try {
                java.util.Date bd = new java.text.SimpleDateFormat("dd/MM/yyyy").parse(req.getBirthDate());
                entity.setBirthDate(bd);
            } catch (Exception ignored) {}
        }
        entity.setAddressContact(req.getAddressContact());
        entity.setAddressPermanent(req.getAddressPermanent());
        entity.setNationality(req.getNationality());
        entity.setMaritalStatus(req.getMaritalStatus());
        entity.setTotalAsset(req.getTotalAsset());
        entity.setBusinessField(req.getBusinessField());
        entity.setAvatarUrl(req.getAvatarUrl());
        entity.setZaloStatus(req.getZaloStatus());
        entity.setFacebookLink(req.getFacebookLink());
        entity.setSourceType(req.getSourceType());
        entity.setSourceDetail(req.getSourceDetail());
        entity.setNotes(req.getNotes());

        // Set multiple contact information fields
        entity.setAdditionalPhones(req.getAdditionalPhones());
        entity.setAdditionalEmails(req.getAdditionalEmails());
        entity.setAdditionalCccds(req.getAdditionalCccds());

        // Set interests field
        entity.setInterests(req.getInterests());

        entity.setCreatedAt(new Date());
        entity.setCreatedBy(event.userId);
        entity = customerRepository.save(entity);

        Long customerId = entity.getId();
        Date now = new Date();
        // 2) assignments - new logic
        if (req.getCustomerAssignments() != null) {
            processCustomerAssignments(req.getCustomerAssignments(), customerId, event.userId, now);
            // Update current manager/staff references
            updateCurrentAssignmentReferences(entity, req.getCustomerAssignments());
            entity.setUpdatedAt(new Date());
            entity.setUpdatedBy(event.userId);
            customerRepository.save(entity);
        }

        // 3) relatives
        if (req.getCustomerRelatives() != null) {
            for (vn.agis.crm.base.jpa.dto.req.RelativeUpsertDto r : req.getCustomerRelatives()) {
                vn.agis.crm.base.jpa.entity.CustomerRelatives cr = new vn.agis.crm.base.jpa.entity.CustomerRelatives();
                cr.setCustomerId(customerId);
                cr.setRelationType(r.getRelationType());
                cr.setFullName(r.getFullName());
                Integer yob = r.getYearOfBirth() != null ? r.getYearOfBirth() : r.getBirthYear();
                cr.setYearOfBirth(yob);
                cr.setPhone(r.getPhone());
                cr.setNotes(r.getNotes());
                cr.setCreatedAt(now);
                cr.setCreatedBy(event.userId);
                customerRelativeRepository.save(cr);
            }
        }

        // 4) properties
        if (req.getCustomerProperties() != null) {
            for (vn.agis.crm.base.jpa.dto.req.PropertyUpsertDto p : req.getCustomerProperties()) {
                vn.agis.crm.base.jpa.entity.CustomerProperties cp = new vn.agis.crm.base.jpa.entity.CustomerProperties();
                cp.setCustomerId(customerId);
                cp.setProjectId(p.getProjectId());
                cp.setUnitId(p.getUnitId());
                if (p.getTransactionDate() != null && !p.getTransactionDate().isEmpty()) {
                    // accept both yyyy-MM-dd and dd/MM/yyyy
                    try { cp.setTransactionDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(p.getTransactionDate())); } catch (Exception e1) {
                        try { cp.setTransactionDate(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(p.getTransactionDate())); } catch (Exception ignored) {}
                    }
                }
                cp.setContractPrice(p.getContractPrice());
                cp.setEmployeeId(p.getEmployeeId());
                cp.setNotes(p.getNotes());
                cp.setLegalStatus(p.getLegalStatus()); // NEW: Set legal status
                cp.setExternalAgencyName(p.getExternalAgencyName());
                cp.setExternalSaleName(p.getExternalSaleName());
                cp.setExternalSalePhone(p.getExternalSalePhone());
                cp.setExternalSaleEmail(p.getExternalSaleEmail());
//                if (p.getFirstInteraction() != null) cp.setFirstInteraction(p.getFirstInteraction());
                if (p.getFirstInteraction() != null) {
                    try {
                        cp.setFirstInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(p.getFirstInteraction()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
                if (p.getLastInteraction() != null) {
                    try {
                        cp.setLastInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(p.getLastInteraction()));
                    } catch (ParseException e) {
                        throw new RuntimeException(e);
                    }
                }
                cp.setCreatedAt(now);
                cp.setCreatedBy(event.userId);
                cp = customerPropertyRepository.save(cp);
                // persist interactionsSecondary if provided
                if (p.getInteractionsSecondary() != null) {
                    for (vn.agis.crm.base.jpa.dto.InteractionSecondaryDto is : p.getInteractionsSecondary()) {
                        if (is.getId() == null && (is.getDeleted() == null || !is.getDeleted())) {
                            vn.agis.crm.base.jpa.entity.InteractionsSecondary e = new vn.agis.crm.base.jpa.entity.InteractionsSecondary();
                            e.setCustomerPropertyId(cp.getId());
                            e.setExpectedSellPrice(is.getExpectedSellPrice());
                            e.setExpectedRentPrice(is.getExpectedRentPrice());
                            e.setResult(is.getResult());
                            try { if (is.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(is.getHappenedAt())); } catch (Exception ignored) {}
                            e.setNotes(is.getNotes());
                            e.setCreatedAt(new java.util.Date());
                            e.setCreatedBy(event.userId);
                            interactionsSecondaryRepository.save(e);
                        }
                    }
                }
            }
        }

        // 5) offers
        if (req.getCustomerOffers() != null) {
            for (vn.agis.crm.base.jpa.dto.req.OfferUpsertDto o : req.getCustomerOffers()) {
                vn.agis.crm.base.jpa.entity.CustomerOffers co = new vn.agis.crm.base.jpa.entity.CustomerOffers();
                co.setCustomerId(customerId);
                co.setProjectId(o.getProjectId());
                co.setStatus(o.getStatus());
                co.setNotes(o.getNotes());
                // parse dates dd/MM/yyyy
                if (o.getFirstInteraction() != null) {
                    try { co.setFirstInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getFirstInteraction())); } catch (Exception ignored) {}
                }
                if (o.getLastInteraction() != null) {
                    try { co.setLastInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getLastInteraction())); } catch (Exception ignored) {}
                }
                co.setCreatedAt(now);
                co.setCreatedBy(event.userId);
                co = customerOfferRepository.save(co);
                if (o.getInteractionsPrimary() != null) {
                    for (vn.agis.crm.base.jpa.dto.InteractionPrimaryDto ip : o.getInteractionsPrimary()) {
                        if (ip.getId() == null && (ip.getDeleted() == null || !ip.getDeleted())) {
                            vn.agis.crm.base.jpa.entity.InteractionsPrimary e = new vn.agis.crm.base.jpa.entity.InteractionsPrimary();
                            e.setCustomerOfferId(co.getId());
                            e.setResult(ip.getResult());
                            try { if (ip.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(ip.getHappenedAt())); } catch (Exception ignored) {}
                            e.setNotes(ip.getNotes());
                            e.setCreatedAt(new java.util.Date());
                            e.setCreatedBy(event.userId);
                            interactionsPrimaryRepository.save(e);
                        }
                    }
                }
            }
        }

        return event.createResponse(customerResponseMapper.toResDto(entity), 201, "Created");
    }

    private Event updateV2(Event event) {
        vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest req = (vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest) event.payload;
        if (req.getId() == null) return event.createResponse(null, 400, "Missing id");
        return customerRepository.findById(req.getId()).map(entity -> {
            // update basic
            if (req.getFullName() != null) entity.setFullName(req.getFullName());
            if (req.getPhone() != null) entity.setPhone(req.getPhone());
            if (req.getEmail() != null) entity.setEmail(req.getEmail());
            if (req.getCccd() != null) entity.setCccd(req.getCccd());
            if (req.getBirthDate() != null && !req.getBirthDate().isEmpty()) {
                try { entity.setBirthDate(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(req.getBirthDate())); } catch (Exception ignored) {}
            }
            if (req.getAddressContact() != null) entity.setAddressContact(req.getAddressContact());
            if (req.getAddressPermanent() != null) entity.setAddressPermanent(req.getAddressPermanent());
            if (req.getNationality() != null) entity.setNationality(req.getNationality());
            if (req.getMaritalStatus() != null) entity.setMaritalStatus(req.getMaritalStatus());
            if (req.getTotalAsset() != null) entity.setTotalAsset(req.getTotalAsset());
            if (req.getBusinessField() != null) entity.setBusinessField(req.getBusinessField());
            if (req.getAvatarUrl() != null) entity.setAvatarUrl(req.getAvatarUrl());
            if (req.getZaloStatus() != null) entity.setZaloStatus(req.getZaloStatus());
            if (req.getFacebookLink() != null) entity.setFacebookLink(req.getFacebookLink());
            if (req.getSourceType() != null) entity.setSourceType(req.getSourceType());
            if (req.getSourceDetail() != null) entity.setSourceDetail(req.getSourceDetail());
            if (req.getNotes() != null) entity.setNotes(req.getNotes());

            // Update multiple contact information fields
            if (req.getAdditionalPhones() != null) entity.setAdditionalPhones(req.getAdditionalPhones());
            if (req.getAdditionalEmails() != null) entity.setAdditionalEmails(req.getAdditionalEmails());
            if (req.getAdditionalCccds() != null) entity.setAdditionalCccds(req.getAdditionalCccds());

            // Update interests field
            if (req.getInterests() != null) entity.setInterests(req.getInterests());

            entity.setUpdatedAt(new Date());
            entity.setUpdatedBy(event.userId);
            entity = customerRepository.save(entity);

            Date now = new Date();
            // assignments - new logic
            if (req.getCustomerAssignments() != null) {
                processCustomerAssignments(req.getCustomerAssignments(), entity.getId(), event.userId, now);
                // Update current manager/staff references
                updateCurrentAssignmentReferences(entity, req.getCustomerAssignments());
                entity.setUpdatedAt(new Date());
                entity.setUpdatedBy(event.userId);
                customerRepository.save(entity);
            }

            // relatives upsert
            if (req.getCustomerRelatives() != null) {
                for (vn.agis.crm.base.jpa.dto.req.RelativeUpsertDto r : req.getCustomerRelatives()) {
                    if (r.getId() == null && (r.getDeleted() == null || !r.getDeleted())) {
                        vn.agis.crm.base.jpa.entity.CustomerRelatives cr = new vn.agis.crm.base.jpa.entity.CustomerRelatives();
                        cr.setCustomerId(entity.getId());
                        cr.setRelationType(r.getRelationType());
                        cr.setFullName(r.getFullName());
                        cr.setYearOfBirth(r.getYearOfBirth());
                        cr.setPhone(r.getPhone());
                        cr.setNotes(r.getNotes());
                        cr.setCreatedAt(now);
                        cr.setCreatedBy(event.userId);
                        customerRelativeRepository.save(cr);
                    } else if (r.getId() != null && Boolean.TRUE.equals(r.getDeleted())) {
                        customerRelativeRepository.deleteById(r.getId());
                    } else if (r.getId() != null) {
                        customerRelativeRepository.findById(r.getId()).ifPresent(cr -> {
                            if (r.getRelationType() != null) cr.setRelationType(r.getRelationType());
                            if (r.getFullName() != null) cr.setFullName(r.getFullName());
                            if (r.getYearOfBirth() != null) cr.setYearOfBirth(r.getYearOfBirth());
                            if (r.getPhone() != null) cr.setPhone(r.getPhone());
                            if (r.getNotes() != null) cr.setNotes(r.getNotes());
                            // TODO: thêm updatedAt
                            // CustomerRelatives chỉ có createdAt/createdBy từ AbstractEntity, không có updatedAt/updatedBy
                            customerRelativeRepository.save(cr);
                        });
                    }
                }
            }

            // properties upsert
            if (req.getCustomerProperties() != null) {
                for (vn.agis.crm.base.jpa.dto.req.PropertyUpsertDto p : req.getCustomerProperties()) {
                    if (p.getId() == null && (p.getDeleted() == null || !p.getDeleted())) {
                        vn.agis.crm.base.jpa.entity.CustomerProperties cp = new vn.agis.crm.base.jpa.entity.CustomerProperties();
                        cp.setCustomerId(entity.getId());
                        cp.setProjectId(p.getProjectId());
                        cp.setUnitId(p.getUnitId());
                        if (p.getTransactionDate() != null && !p.getTransactionDate().isEmpty()) {
                            try { cp.setTransactionDate(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(p.getTransactionDate())); } catch (Exception e1) {
                                try { cp.setTransactionDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(p.getTransactionDate())); } catch (Exception ignored) {}
                            }
                        }
                        cp.setContractPrice(p.getContractPrice());
                        cp.setEmployeeId(p.getEmployeeId());
                        cp.setNotes(p.getNotes());
                        cp.setLegalStatus(p.getLegalStatus()); // NEW: Set legal status
                        if (p.getExternalAgencyName() != null) cp.setExternalAgencyName(p.getExternalAgencyName());
                        if (p.getExternalSaleName() != null) cp.setExternalSaleName(p.getExternalSaleName());
                        if (p.getExternalSalePhone() != null) cp.setExternalSalePhone(p.getExternalSalePhone());
                        if (p.getExternalSaleEmail() != null) cp.setExternalSaleEmail(p.getExternalSaleEmail());
                        cp.setCreatedAt(now);
                        cp.setCreatedBy(event.userId);
                        cp = customerPropertyRepository.save(cp);
                        // create interactionsSecondary
                        if (p.getInteractionsSecondary() != null) {
                            for (vn.agis.crm.base.jpa.dto.InteractionSecondaryDto is : p.getInteractionsSecondary()) {
                                if (is.getId() == null && (is.getDeleted() == null || !is.getDeleted())) {
                                    vn.agis.crm.base.jpa.entity.InteractionsSecondary e = new vn.agis.crm.base.jpa.entity.InteractionsSecondary();
                                    e.setCustomerPropertyId(cp.getId());
                                    e.setExpectedSellPrice(is.getExpectedSellPrice());
                                    e.setExpectedRentPrice(is.getExpectedRentPrice());
                                    e.setResult(is.getResult());
                                    try { if (is.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(is.getHappenedAt())); } catch (Exception ignored) {}
                                    e.setNotes(is.getNotes());
                                    e.setCreatedAt(new java.util.Date());
                                    e.setCreatedBy(event.userId);
                                    interactionsSecondaryRepository.save(e);
                                }
                            }
                        }
                    } else if (p.getId() != null && Boolean.TRUE.equals(p.getDeleted())) {
                        // delete interactions then property
                        java.util.List<vn.agis.crm.base.jpa.entity.InteractionsSecondary> list = interactionsSecondaryRepository.findByCustomerPropertyId(p.getId());
                        if (list != null) {
                            for (vn.agis.crm.base.jpa.entity.InteractionsSecondary e : list) interactionsSecondaryRepository.deleteById(e.getId());
                        }
                        customerPropertyRepository.deleteById(p.getId());
                    } else if (p.getId() != null) {
                        customerPropertyRepository.findById(p.getId()).ifPresent(cp -> {
                            if (p.getProjectId() != null) cp.setProjectId(p.getProjectId());
                            if (p.getUnitId() != null) cp.setUnitId(p.getUnitId());
                            if (p.getTransactionDate() != null && !p.getTransactionDate().isEmpty()) {
                                try { cp.setTransactionDate(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(p.getTransactionDate())); } catch (Exception e1) {
                                    try { cp.setTransactionDate(new java.text.SimpleDateFormat("yyyy-MM-dd").parse(p.getTransactionDate())); } catch (Exception ignored) {}
                                }
                            }
                            if (p.getContractPrice() != null) cp.setContractPrice(p.getContractPrice());
                            if (p.getEmployeeId() != null) cp.setEmployeeId(p.getEmployeeId());
                            if (p.getNotes() != null) cp.setNotes(p.getNotes());
                            if (p.getLegalStatus() != null) cp.setLegalStatus(p.getLegalStatus()); // NEW: Update legal status
                            if (p.getExternalAgencyName() != null) cp.setExternalAgencyName(p.getExternalAgencyName());
                            if (p.getExternalSaleName() != null) cp.setExternalSaleName(p.getExternalSaleName());
                            if (p.getExternalSalePhone() != null) cp.setExternalSalePhone(p.getExternalSalePhone());
                            if (p.getExternalSaleEmail() != null) cp.setExternalSaleEmail(p.getExternalSaleEmail());
                            // CustomerProperties chỉ có createdAt/createdBy từ AbstractEntity, không có updatedAt/updatedBy
                            customerPropertyRepository.save(cp);
                        });
                        // upsert interactionsSecondary
                        if (p.getInteractionsSecondary() != null) {
                            for (vn.agis.crm.base.jpa.dto.InteractionSecondaryDto is : p.getInteractionsSecondary()) {
                                if (is.getId() == null && (is.getDeleted() == null || !is.getDeleted())) {
                                    vn.agis.crm.base.jpa.entity.InteractionsSecondary e = new vn.agis.crm.base.jpa.entity.InteractionsSecondary();
                                    e.setCustomerPropertyId(p.getId());
                                    e.setExpectedSellPrice(is.getExpectedSellPrice());
                                    e.setExpectedRentPrice(is.getExpectedRentPrice());
                                    e.setResult(is.getResult());
                                    try { if (is.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(is.getHappenedAt())); } catch (Exception ignored) {}
                                    e.setNotes(is.getNotes());
                                    e.setCreatedAt(new java.util.Date());
                                    e.setCreatedBy(event.userId);
                                    interactionsSecondaryRepository.save(e);
                                } else if (is.getId() != null && Boolean.TRUE.equals(is.getDeleted())) {
                                    interactionsSecondaryRepository.deleteById(is.getId());
                                } else if (is.getId() != null) {
                                    interactionsSecondaryRepository.findById(is.getId()).ifPresent(e -> {
                                        if (is.getExpectedSellPrice()!=null) e.setExpectedSellPrice(is.getExpectedSellPrice());
                                        if (is.getExpectedRentPrice()!=null) e.setExpectedRentPrice(is.getExpectedRentPrice());
                                        if (is.getResult()!=null) e.setResult(is.getResult());
                                        try { if (is.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(is.getHappenedAt())); } catch (Exception ignored) {}
                                        if (is.getNotes()!=null) e.setNotes(is.getNotes());
                                        e.setUpdatedAt(new java.util.Date());
                                        e.setUpdatedBy(event.userId);
                                        interactionsSecondaryRepository.save(e);
                                    });
                                }
                            }
                        }
                    }
                }
            }

            // offers upsert
            if (req.getCustomerOffers() != null) {
                for (vn.agis.crm.base.jpa.dto.req.OfferUpsertDto o : req.getCustomerOffers()) {
                    if (o.getId() == null && (o.getDeleted() == null || !o.getDeleted())) {
                        vn.agis.crm.base.jpa.entity.CustomerOffers co = new vn.agis.crm.base.jpa.entity.CustomerOffers();
                        co.setCustomerId(entity.getId());
                        co.setProjectId(o.getProjectId());
                        if (o.getFirstInteraction() != null) {
                            try { co.setFirstInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getFirstInteraction())); } catch (Exception ignored) {}
                        }
                        if (o.getLastInteraction() != null) {
                            try { co.setLastInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getLastInteraction())); } catch (Exception ignored) {}
                        }
                        co.setStatus(o.getStatus());
                        co.setNotes(o.getNotes());
                        co.setCreatedAt(now);
                        co.setCreatedBy(event.userId);
                        co = customerOfferRepository.save(co);
                        // create interactionsPrimary
                        if (o.getInteractionsPrimary() != null) {
                            for (vn.agis.crm.base.jpa.dto.InteractionPrimaryDto ip : o.getInteractionsPrimary()) {
                                if (ip.getId() == null && (ip.getDeleted() == null || !ip.getDeleted())) {
                                    vn.agis.crm.base.jpa.entity.InteractionsPrimary e = new vn.agis.crm.base.jpa.entity.InteractionsPrimary();
                                    e.setCustomerOfferId(co.getId());
                                    e.setResult(ip.getResult());
                                    try { if (ip.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(ip.getHappenedAt())); } catch (Exception ignored) {}
                                    e.setNotes(ip.getNotes());
                                    e.setCreatedAt(new java.util.Date());
                                    e.setCreatedBy(event.userId);
                                    interactionsPrimaryRepository.save(e);
                                }
                            }
                        }
                    } else if (o.getId() != null && Boolean.TRUE.equals(o.getDeleted())) {
                        // delete interactions then offer
                        java.util.List<vn.agis.crm.base.jpa.entity.InteractionsPrimary> list = interactionsPrimaryRepository.findByCustomerOfferId(o.getId());
                        if (list != null) {
                            for (vn.agis.crm.base.jpa.entity.InteractionsPrimary e : list) interactionsPrimaryRepository.deleteById(e.getId());
                        }
                        customerOfferRepository.deleteById(o.getId());
                    } else if (o.getId() != null) {
                        customerOfferRepository.findById(o.getId()).ifPresent(co -> {
                            if (o.getProjectId() != null) co.setProjectId(o.getProjectId());
                            if (o.getFirstInteraction() != null) {
                                try { co.setFirstInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getFirstInteraction())); } catch (Exception ignored) {}
                            }
                            if (o.getLastInteraction() != null) {
                                try { co.setLastInteraction(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(o.getLastInteraction())); } catch (Exception ignored) {}
                            }
                            if (o.getStatus() != null) co.setStatus(o.getStatus());
                            if (o.getNotes() != null) co.setNotes(o.getNotes());
                            // CustomerOffers chỉ có createdAt/createdBy từ AbstractEntity, không có updatedAt/updatedBy
                            customerOfferRepository.save(co);
                        });
                        // upsert interactionsPrimary
                        if (o.getInteractionsPrimary() != null) {
                            for (vn.agis.crm.base.jpa.dto.InteractionPrimaryDto ip : o.getInteractionsPrimary()) {
                                if (ip.getId() == null && (ip.getDeleted() == null || !ip.getDeleted())) {
                                    vn.agis.crm.base.jpa.entity.InteractionsPrimary e = new vn.agis.crm.base.jpa.entity.InteractionsPrimary();
                                    e.setCustomerOfferId(o.getId());
                                    e.setResult(ip.getResult());
                                    try { if (ip.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(ip.getHappenedAt())); } catch (Exception ignored) {}
                                    e.setNotes(ip.getNotes());
                                    e.setCreatedAt(new java.util.Date());
                                    e.setCreatedBy(event.userId);
                                    interactionsPrimaryRepository.save(e);
                                } else if (ip.getId() != null && Boolean.TRUE.equals(ip.getDeleted())) {
                                    interactionsPrimaryRepository.deleteById(ip.getId());
                                } else if (ip.getId() != null) {
                                    interactionsPrimaryRepository.findById(ip.getId()).ifPresent(e -> {
                                        if (ip.getResult()!=null) e.setResult(ip.getResult());
                                        try { if (ip.getHappenedAt()!=null) e.setHappenedAt(new java.text.SimpleDateFormat("dd/MM/yyyy").parse(ip.getHappenedAt())); } catch (Exception ignored) {}
                                        if (ip.getNotes()!=null) e.setNotes(ip.getNotes());
                                        e.setUpdatedAt(new java.util.Date());
                                        e.setUpdatedBy(event.userId);
                                        interactionsPrimaryRepository.save(e);
                                    });
                                }
                            }
                        }
                    }
                }
            }

            return event.createResponse(customerResponseMapper.toResDto(entity), 200, "Success");
        }).orElse(event.createResponse(null, 404, "Customer not found"));
    }

    private Event processCheckExists(Event event) {
        vn.agis.crm.base.jpa.dto.req.CheckExistCustomerReq req = (vn.agis.crm.base.jpa.dto.req.CheckExistCustomerReq) event.payload;
        boolean exists = false;
        if (req != null && req.getKey() != null && req.getValue() != null) {
            String key = req.getKey();
            String value = req.getValue();
            switch (key) {
                case "email":
                    exists = customerRepository.existsByEmail(value);
                    break;
                case "phone":
                    exists = customerRepository.existsByPhone(value);
                    break;
                case "cccd":
                    exists = customerRepository.existsByCccd(value);
                    break;
                default:
                    exists = false;
            }
        }
        return event.createResponse(exists, 200, "Success");
    }

    /**
     * Process customer assignments with new business logic:
     * - assignedFrom is automatically set to current time when creating new assignment
     * - assignedTo is automatically set when a new employee is assigned to the same role
     * - Support updating existing assignments by id
     */
    private void processCustomerAssignments(java.util.List<vn.agis.crm.base.jpa.dto.req.AssignmentUpsertDto> assignments,
                                          Long customerId, Long userId, Date now) {
        for (vn.agis.crm.base.jpa.dto.req.AssignmentUpsertDto a : assignments) {
            if (a.getDeleted() != null && a.getDeleted()) {
                // Handle deletion
                if (a.getId() != null) {
                    customerAssignmentRepository.deleteById(a.getId());
                }
                continue;
            }

            vn.agis.crm.base.jpa.entity.CustomerAssignments ca;

            if (a.getId() != null) {
                // Update existing assignment
                ca = customerAssignmentRepository.findById(a.getId()).orElse(null);
                if (ca == null) {
                    continue; // Skip if not found
                }
                ca.setUpdatedAt(now);
                ca.setUpdatedBy(userId);
            } else {
                // Create new assignment
                // First, deactivate current active assignments for this role
                if (a.getRoleType() != null) {
                    customerAssignmentRepository.deactivateActiveAssignments(
                        java.util.Collections.singletonList(customerId), a.getRoleType(), now, now, userId);
                }

                ca = new vn.agis.crm.base.jpa.entity.CustomerAssignments();
                ca.setCustomerId(customerId);
                ca.setAssignedFrom(now); // Auto-set assignedFrom to current time
                ca.setCreatedAt(now);
                ca.setCreatedBy(userId);
            }

            // Update fields
            if (a.getEmployeeId() != null) {
                ca.setEmployeeId(a.getEmployeeId());
            }
            if (a.getRoleType() != null) {
                ca.setRoleType(a.getRoleType());
            }

            customerAssignmentRepository.save(ca);
        }
    }

    /**
     * Update current manager/staff references in customer entity
     */
    private void updateCurrentAssignmentReferences(vn.agis.crm.base.jpa.entity.Customers entity,
                                                 java.util.List<vn.agis.crm.base.jpa.dto.req.AssignmentUpsertDto> assignments) {
        for (vn.agis.crm.base.jpa.dto.req.AssignmentUpsertDto a : assignments) {
            if (a.getDeleted() != null && a.getDeleted()) {
                continue;
            }

            if (a.getRoleType() != null && a.getEmployeeId() != null) {
                if (a.getRoleType() == 1) {
                    entity.setCurrentManagerId(a.getEmployeeId());
                } else if (a.getRoleType() == 2) {
                    entity.setCurrentStaffId(a.getEmployeeId());
                }
            }
        }
    }

    /**
     * Update employee_id in customer_assignments by id, customer_id and role_type
     */
    @Transactional
    public boolean updateAssignmentEmployee(vn.agis.crm.base.jpa.dto.req.UpdateAssignmentEmployeeDto request, Long userId) {
        // Validate input
        if (request.getId() == null || request.getCustomerId() == null ||
            request.getRoleType() == null || request.getNewEmployeeId() == null) {
            throw new IllegalArgumentException("All fields (id, customerId, roleType, newEmployeeId) are required");
        }

        // Validate roleType
        if (request.getRoleType() != 1 && request.getRoleType() != 2) {
            throw new IllegalArgumentException("RoleType must be 1 (Manager) or 2 (Staff)");
        }

        // Check if assignment exists
        CustomerAssignments assignment = customerAssignmentRepository.findByIdAndCustomerIdAndRoleType(
            request.getId(), request.getCustomerId(), request.getRoleType());

        if (assignment == null) {
            throw new IllegalArgumentException("Customer assignment not found with given criteria");
        }

        // Check if new employee exists
        if (!employeeRepository.existsById(request.getNewEmployeeId())) {
            throw new IllegalArgumentException("Employee with id " + request.getNewEmployeeId() + " not found");
        }

        // Update assignment
        Date now = new Date();
        int updatedRows = customerAssignmentRepository.updateEmployeeIdByIdAndCustomerIdAndRoleType(
            request.getId(), request.getCustomerId(), request.getRoleType(),
            request.getNewEmployeeId(), now, userId);

        if (updatedRows > 0) {
            // Update current manager/staff reference in customer
            vn.agis.crm.base.jpa.entity.Customers customer = customerRepository.findById(request.getCustomerId()).orElse(null);
            if (customer != null) {
                if (request.getRoleType() == 1) {
                    customer.setCurrentManagerId(request.getNewEmployeeId());
                } else if (request.getRoleType() == 2) {
                    customer.setCurrentStaffId(request.getNewEmployeeId());
                }
                customer.setUpdatedAt(now);
                customer.setUpdatedBy(userId);
                customerRepository.save(customer);
            }
            return true;
        }

        return false;
    }

    /**
     * Event handler for update assignment employee
     */
    private Event updateAssignmentEmployeeEvent(Event event) {
        try {
            vn.agis.crm.base.jpa.dto.req.UpdateAssignmentEmployeeDto request =
                (vn.agis.crm.base.jpa.dto.req.UpdateAssignmentEmployeeDto) event.payload;

            boolean success = updateAssignmentEmployee(request, event.userId);

            if (success) {
                return event.createResponse("Assignment employee updated successfully", 200, "Success");
            } else {
                return event.createResponse(null, 400, "Failed to update assignment employee");
            }
        } catch (IllegalArgumentException e) {
            return event.createResponse(null, 400, e.getMessage());
        } catch (Exception e) {
            return event.createResponse(null, 500, "Internal server error: " + e.getMessage());
        }
    }

    /**
     * Get assignment history for a specific customer
     */
    @Transactional(readOnly = true)
    public AssignmentHistoryResponseDto getAssignmentHistory(
            GetAssignmentHistoryDto request) {

        // Validate customer exists
        vn.agis.crm.base.jpa.entity.Customers customer = customerRepository.findById(request.getCustomerId())
                .orElseThrow(() -> new IllegalArgumentException("Customer not found with id: " + request.getCustomerId()));

        // Get assignment history with pagination
        org.springframework.data.domain.Pageable pageable = org.springframework.data.domain.PageRequest.of(
                request.getPage(), request.getSize(),
                org.springframework.data.domain.Sort.by(
                        "DESC".equalsIgnoreCase(request.getSortDirection()) ?
                                org.springframework.data.domain.Sort.Direction.DESC :
                                org.springframework.data.domain.Sort.Direction.ASC,
                        request.getSortBy()
                )
        );

        org.springframework.data.domain.Page<Object[]> assignmentPage =
                customerAssignmentRepository.findAssignmentHistoryByCustomerId(request.getCustomerId(), pageable);

        // Convert to DTOs
        java.util.List<vn.agis.crm.base.jpa.dto.AssignmentHistoryDto> historyList = new java.util.ArrayList<>();
        for (Object[] row : assignmentPage.getContent()) {
            vn.agis.crm.base.jpa.dto.AssignmentHistoryDto dto = new vn.agis.crm.base.jpa.dto.AssignmentHistoryDto();
            dto.setId(((Number) row[0]).longValue());
            dto.setCustomerId(((Number) row[1]).longValue());
            dto.setEmployeeId(((Number) row[2]).longValue());
            dto.setEmployeeName((String) row[3]);
            dto.setEmployeeCode((String) row[4]);
            dto.setEmployeeEmail((String) row[5]);
            dto.setEmployeePhone((String) row[6]);
            dto.setRoleType(((Number) row[7]).intValue());

            if (row[8] != null) {
                dto.setAssignedFrom(((java.sql.Timestamp) row[8]));
            }
            if (row[9] != null) {
                dto.setAssignedTo(((java.sql.Timestamp) row[9]));
            }
            // No createdAt/updatedAt fields in AssignmentHistoryDto now
            dto.setActive(row[9] == null);

            historyList.add(dto);
        }

        // Create response
        AssignmentHistoryResponseDto response =
                new AssignmentHistoryResponseDto();
        response.setCustomerId(customer.getId());
        response.setCustomerName(customer.getFullName());
        response.setCustomerPhone(customer.getPhone());
        response.setCustomerEmail(customer.getEmail());
        response.setAssignmentHistory(historyList);
        response.setTotalRecords(assignmentPage.getTotalElements());
        response.setCurrentPage(request.getPage());
        response.setPageSize(request.getSize());
        response.setTotalPages(assignmentPage.getTotalPages());

        return response;
    }

    /**
     * Event handler for get assignment history
     */
    private Event getAssignmentHistoryEvent(Event event) {
        try {
            GetAssignmentHistoryDto request = (GetAssignmentHistoryDto) event.payload;

            AssignmentHistoryResponseDto response = getAssignmentHistory(request);
            return event.createResponse(response, 200, "Success");
        } catch (IllegalArgumentException e) {
            return event.createResponse(null, 404, e.getMessage());
        } catch (Exception e) {
            return event.createResponse(null, 500, "Internal server error: " + e.getMessage());
        }
    }

    /**
     * Delete customer assignment by id. Default is soft delete (set assignedTo).
     */
    @Transactional
    public boolean deleteAssignment(Long assignmentId, Long userId, boolean soft) {
        if (assignmentId == null || assignmentId <= 0) {
            throw new IllegalArgumentException("Invalid assignmentId");
        }
        // check exists
        vn.agis.crm.base.jpa.entity.CustomerAssignments assignment = customerAssignmentRepository.findById(assignmentId).orElse(null);
        if (assignment == null) {
            throw new IllegalArgumentException("Assignment not found with id: " + assignmentId);
        }
        Date now = new Date();
        if (soft) {
            // only set assignedTo if it's currently active
            if (assignment.getAssignedTo() == null) {
                int updated = customerAssignmentRepository.softDeleteAssignment(assignmentId, now, now, userId);
                return updated > 0;
            } else {
                // already ended; treat as success
                return true;
            }
        } else {
            customerAssignmentRepository.deleteById(assignmentId);
            return true;
        }
    }

    /**
     * Event handler for delete assignment
     */
    private Event deleteAssignmentEvent(Event event) {
        try {
            Long assignmentId = (Long) event.payload;
            boolean success = deleteAssignment(assignmentId, event.userId, false);
            if (success) {
                return event.createResponse(null, 200, "Deleted");
            } else {
                return event.createResponse(null, 400, "Failed to delete assignment");
            }
        } catch (IllegalArgumentException e) {
            return event.createResponse(null, 404, e.getMessage());
        } catch (Exception e) {
            return event.createResponse(null, 500, "Internal server error: " + e.getMessage());
        }
    }

    /**
     * Validates if a customer can be deleted by checking for dependencies
     */
    private Event validateCustomerDeletion(Event event) {
        Long id = (Long) event.payload;
        CustomerDeletionValidationResult validationResult = performCustomerDeletionValidation(id);

        if (validationResult.isCanDelete()) {
            return event.createResponse(validationResult, ResponseCode.OK, "Khách hàng có thể xóa được");
        } else {
            return event.createResponse(validationResult, ResponseCode.BAD_REQUEST,
                MessageKeyConstant.CustomerDeletion.CUSTOMER_DEPENDENCY_VALIDATION_FAILED);
        }
    }

    /**
     * Internal method to validate customer deletion dependencies
     */
    private CustomerDeletionValidationResult performCustomerDeletionValidation(Long customerId) {
        // Check if customer exists
        Optional<Customers> customerOpt = customerRepository.findById(customerId);
        if (!customerOpt.isPresent()) {
            return CustomerDeletionValidationResult.customerNotFound(customerId);
        }

        Customers customer = customerOpt.get();
        List<CustomerDependencyError> dependencies = new ArrayList<>();

        // Check customer assignments
        long assignmentsCount = customerRepository.countCustomerAssignmentsByCustomerId(customerId);
        if (assignmentsCount > 0) {
            dependencies.add(CustomerDependencyError.customerAssignments(assignmentsCount));
        }

        // Check customer offers
        long offersCount = customerRepository.countCustomerOffersByCustomerId(customerId);
        if (offersCount > 0) {
            dependencies.add(CustomerDependencyError.customerOffers(offersCount));
        }

        // Check customer properties
        long propertiesCount = customerRepository.countCustomerPropertiesByCustomerId(customerId);
        if (propertiesCount > 0) {
            dependencies.add(CustomerDependencyError.customerProperties(propertiesCount));
        }

        // Check notifications
        long notificationsCount = customerRepository.countNotificationsByCustomerId(customerId);
        if (notificationsCount > 0) {
            dependencies.add(CustomerDependencyError.notifications(notificationsCount));
        }

        // Check rule runs
        long ruleRunsCount = ruleRunRepository.countRuleRunsByCustomerId(customerId);
        if (ruleRunsCount > 0) {
            dependencies.add(CustomerDependencyError.ruleRuns(ruleRunsCount));
        }

        // Return result based on dependencies found
        if (dependencies.isEmpty()) {
            return CustomerDeletionValidationResult.success(customerId, customer.getFullName());
        } else {
            return CustomerDeletionValidationResult.failure(customerId, customer.getFullName(), dependencies);
        }
    }

    /**
     * Apply role-based filtering to determine which employee ID to use for customer access control
     * @param event The event containing user information
     * @return Employee ID to filter by, or null if admin (no filtering needed)
     */
    private Long applyRoleBasedFilter(Event event) {
        Integer userType = event.userType;
        Long userId = event.userId;

        if (isAdminRole(userType)) {
            return null; // Admin can see all customers
        }

        // For Manager and Staff roles, return the user ID to filter by assignment
        return userId;
    }

    /**
     * Check if the user type is Admin
     * @param userType The user's type integer
     * @return true if admin, false otherwise
     */
    private boolean isAdminRole(Integer userType) {
        return vn.agis.crm.base.constants.Constants.UserType.ADMIN.equals(userType);
    }

    /**
     * Check if the user has access to view a specific customer based on role-based access control
     * @param customer The customer to check access for
     * @param event The event containing user information
     * @return true if user has access, false otherwise
     */
    private boolean hasAccessToCustomer(Customers customer, Event event) {
        Integer userType = event.userType;
        Long userId = event.userId;

        // Admin has access to all customers
        if (isAdminRole(userType)) {
            return true;
        }

        // Manager role: can access customers where they are the current manager
        if (vn.agis.crm.base.constants.Constants.UserType.MANAGER.equals(userType)) {
            return userId.equals(customer.getCurrentManagerId());
        }

        // Staff role: can access customers where they are the current staff
        if (vn.agis.crm.base.constants.Constants.UserType.STAFF.equals(userType)) {
            return userId.equals(customer.getCurrentStaffId());
        }

        // Unknown role or no access
        return false;
    }
}
