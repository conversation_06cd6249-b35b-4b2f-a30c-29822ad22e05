package vn.agis.crm.base.utils;

public class TransactionLogger {
//
//    protected static final Logger logger = LoggerFactory.getLogger(TransactionLogger.class);
//    public static String getISimTransLog(Object objLog) {
////        iSimTransLogger.info(new Gson().toJson(objLog));
//        logger.trace(objLog.toString(), TransactionLogger.class);
//        return new Gson().toJson(objLog);
//    }
//
//    public static String getITransLog(Object objLog) {
////        iTransLogger.info(new Gson().toJson(objLog));
//        logger.trace(objLog.toString(), TransactionLogger.class);
//        return new Gson().toJson(objLog);
//    }
//
//    public static String getOSimTransLog(Object objLog) {
////        oSimTransLogger.info(new Gson().toJson(objLog));
//        logger.trace(objLog.toString(), TransactionLogger.class);
//        return new Gson().toJson(objLog);
//    }
//
//    public static String getLog3rdPartner(Object objLog) {
////        oSimTransLogger.info(new Gson().toJson(objLog));
//        logger.trace(objLog.toString(), TransactionLogger.class);
//        return new Gson().toJson(objLog);
//    }
//
//    public static String getOTransLog(Object objLog) {
////        oTransLogger.info(new Gson().toJson(objLog));
//        logger.trace(objLog.toString(), TransactionLogger.class);
//        return new Gson().toJson(objLog);
//    }
//
//    public static void writeLogITrans(Long objectId, String objectName, String objectKey, String requestId, Long requestTime, Long responseTime,
//        String requestContent, String responseContent, String groupKey, String ipSource, Event event) {
//        // không có event không ghi log
//        if (Objects.isNull(event)) {
//            return;
//        }
//        // ghi log
//        ITrans log = new ITrans();
//        log.setUserName(Objects.isNull(SecurityUtils.getUserPrincipal()) ? null : SecurityUtils.getUserPrincipal().getUsername());
//        log.setUserType(Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType());
//        log.setMethodName(event.method);
//        log.setObjectId(objectId);
//        log.setObjectName(objectName);
//        log.setObjectKey(objectKey);
//        log.setRequestId(requestId);
//        log.setEventId(event.id);
//        log.setRequestTime(requestTime);
//        log.setResponseTime(responseTime);
//        log.setResponseCode(event.respStatusCode);
//        log.setErrorDesc(event.respErrorDesc);
//        log.setRequestContent(requestContent);
//        log.setResponseContent(responseContent);
//        log.setProvinceCode(Objects.isNull(SecurityUtils.getProvinceCode()) ? null : SecurityUtils.getProvinceCode());
//        log.setGroupKey(groupKey);
//        log.setIpSource(ipSource == null ? SecurityUtils.getIpAddress() : null);
//        log.setCreatedAt(new Date());
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        bus.publishStream(TransactionLogger.getITransLog(log).getBytes(StandardCharsets.UTF_8), Constants.RabbitStream.I_TRANS_LOG);
////        return TransactionLogger.getITransLog(log);
//    }
//
//    public static void writeLogISimTrans(Long msisdn, Long imsi, Long oldImsi, String objectKey, String requestId, Long requestTime, Long responseTime, Integer responseCode, String errorDesc,
//        String requestContent, String responseContent, String ratingPlanCode,String ratingPlanName, Date ratingStartDate, Date ratingEndDate,
//        String oldRatingPlanCode, String oldRatingPlanName, String customerCode, String customerName, String provinceCode, String contractCode, String groupKey, String groupName, String ipSource, Event event) {
//        // không có event không ghi log
//        if (Objects.isNull(event)) {
//            return;
//        }
//        // ghi log
//        ISimTrans log = new ISimTrans();
//        log.setMsisdn(msisdn);
//        log.setImsi(imsi);
//        log.setOldImsi(oldImsi);
//        log.setUserName(Objects.isNull(SecurityUtils.getUserPrincipal()) ? null : SecurityUtils.getUserPrincipal().getUsername());
//        log.setUserType(Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType());
//        log.setMethodName(event.method);
//        log.setObjectKey(objectKey);
//        log.setRequestId(requestId);
//        log.setEventId(event.id);
//        log.setRequestTime(requestTime);
//        log.setResponseTime(responseTime);
//        log.setResponseCode(responseCode);
//        log.setErrorDesc(errorDesc);
//        log.setRequestContent(requestContent);
//        log.setResponseContent(responseContent);
//        log.setRatingPlanCode(ratingPlanCode);
//        log.setRatingPlanName(ratingPlanName);
//        log.setRatingStartDate(ratingStartDate);
//        log.setRatingEndDate(ratingEndDate);
//        log.setOldRatingPlanCode(oldRatingPlanCode);
//        log.setOldRatingPlanName(oldRatingPlanName);
//        log.setCustomerCode(customerCode);
//        log.setCustomerName(customerName);
//        log.setProvinceCode(provinceCode);
//        log.setGroupKey(groupKey);
//        log.setGroupName(groupName);
//        log.setContractCode(contractCode);
//        log.setIpSource(ipSource == null ? SecurityUtils.getIpAddress() : null);
//        log.setCreatedAt(new Date());
////        return TransactionLogger.getISimTransLog(log);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        bus.publishStream(TransactionLogger.getISimTransLog(log).getBytes(StandardCharsets.UTF_8), Constants.RabbitStream.I_SIM_TRANS_LOG);
//
//    }
//
//    public static void writeLogITransDetail(Long objectId, String objectName, String objectKey, String requestId, Long requestTime, Long responseTime,
//                                      String requestContent, String responseContent, String groupKey, String ipSource, String username,
//                                            int userType, String provinceCode, String methodName, String eventId, int responseCode, String errorDesc) {
//        // ghi log
//        ITrans log = new ITrans();
//        log.setUserName(username);
//        log.setUserType(userType);
//        log.setMethodName(methodName);
//        log.setObjectId(objectId);
//        log.setObjectName(objectName);
//        log.setObjectKey(objectKey);
//        log.setRequestId(requestId);
//        log.setEventId(eventId);
//        log.setRequestTime(requestTime);
//        log.setResponseTime(responseTime);
//        log.setResponseCode(responseCode);
//        log.setErrorDesc(errorDesc);
//        log.setRequestContent(requestContent);
//        log.setResponseContent(responseContent);
//        log.setProvinceCode(provinceCode);
//        log.setGroupKey(groupKey);
//        log.setIpSource(ipSource == null ? SecurityUtils.getIpAddress() : null);
//        log.setCreatedAt(new Date());
////        return TransactionLogger.getITransLog(log);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        bus.publishStream(TransactionLogger.getITransLog(log).getBytes(StandardCharsets.UTF_8), Constants.RabbitStream.I_TRANS_LOG);
//
//    }
//
//    public static void writeLogOSimTrans(Long msisdn, Long imsi, Long oldImsi, String methodName, String targetName, String requestId, String eventId, String groupEventId, Long requestTime, Long responseTime, Long responseStatus, Long responseCode, String errorDesc, String requestContent, String responseContent, String oldRatingPlanCode, String oldRatingPlanName, String ratingPlanCode, String ratingPlanName, Date ratingStartDate, Date ratingEndDate, String customerCode, String customerName, String provinceCode, String contractCode) {
//        OSimTrans log = new OSimTrans();
//        log.setMsisdn(msisdn);
//        log.setImsi(imsi);
//        log.setOldImsi(oldImsi);
//        log.setMethodName(methodName);
//        log.setTargetName(targetName);
//        log.setRequestId(requestId);
//        log.setEventId(eventId);
//        log.setGroupEventId(groupEventId);
//        log.setRequestTime(requestTime);
//        log.setResponseTime(responseTime);
//        log.setResponseStatus(responseStatus);
//        log.setResponseCode(responseCode);
//        log.setErrorDesc(errorDesc);
//        log.setRequestContent(requestContent);
//        log.setResponseContent(responseContent);
//        log.setOldRatingPlanCode(oldRatingPlanCode);
//        log.setOldRatingPlanName(oldRatingPlanName);
//        log.setRatingPlanCode(ratingPlanCode);
//        log.setRatingPlanName(ratingPlanName);
//        log.setRatingStartDate(ratingStartDate);
//        log.setRatingEndDate(ratingEndDate);
//        log.setCustomerCode(customerCode);
//        log.setCustomerName(customerName);
//        log.setProvinceCode(provinceCode);
//        log.setContractCode(contractCode);
//        log.setCreatedAt(new Date());
////        return TransactionLogger.getOSimTransLog(log);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        bus.publishStream(TransactionLogger.getOSimTransLog(log).getBytes(StandardCharsets.UTF_8), Constants.RabbitStream.O_SIM_TRANS_LOG);
//
//    }
//
//
//
//    public static void writeLog3rdPartner(Long userId, String userName, String provinceCode, String fullName, String email, String methodName, String moduleName, String requestId, Long requestTime, Long responseTime, Integer responseStatus, Integer responseCode, String errorDesc, String requestContent, String responseContent ) {
//        PartnerLogDto log = new PartnerLogDto();
//        log.setId(null);
//        log.setUserId(userId);
//        log.setUserName(userName);
//        log.setProvinceCode(provinceCode);
//        log.setFullName(fullName);
//        log.setEmail(email);
//        log.setMethodName(methodName);
//        log.setModuleName(moduleName);
//        log.setRequestId(requestId);
//        log.setRequestTime(requestTime);
//        log.setResponseTime(responseTime);
//        log.setResponseStatus(responseStatus);
//        log.setResponseCode(responseCode);
//        log.setErrorDesc(errorDesc);
//        log.setRequestContent(requestContent);
//        log.setResponseContent(responseContent);
//        log.setCreatedAt(new Date());
////        return TransactionLogger.getOSimTransLog(log);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        bus.publishStream(TransactionLogger.getLog3rdPartner(log).getBytes(StandardCharsets.UTF_8), Constants.RabbitStream.THIRD_PARTNER);
//
//    }
}
