package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.dto.req.CreateEmployeeReq;
import vn.agis.crm.base.jpa.dto.req.UpdateEmployeeReq;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.jpa.dto.EmployeeSearchDto;
import vn.agis.crm.util.RequestUtils;

import java.util.List;

@Service
public class EmployeeApiService extends CrudService<Employee, Long> {

    private static final Logger logger = LoggerFactory.getLogger(EmployeeApiService.class);

    public EmployeeApiService() {
        super(Employee.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.EMPLOYEE;
    }

    public Page<Employee> search(EmployeeSearchDto searchDTO, Pageable pageable) {
        List<Employee> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), Employee.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            } else {
                throw new ForbiddenException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
        } catch (Exception e) {
            logger.error("Error in search employees: {}", e.getMessage(), e);
            return Page.empty(pageable);
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Employee createEmployee(CreateEmployeeReq req) {
        Employee response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Employee) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                        MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception ex) {
            logger.error("Error in createEmployee: {}", ex.getMessage(), ex);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Employee updateEmployee(UpdateEmployeeReq req) {
        Employee response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Employee) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload,
                        MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception ex) {
            logger.error("Error in updateEmployee: {}", ex.getMessage(), ex);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public void changeStatus(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE_ONE, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
        } catch (Exception e) {
            logger.error("Error in changeStatus: {}", e.getMessage(), e);
        } finally {
            writeLog(timeRequest, timeResponse, id.toString(), null, event);
        }
    }

    public boolean checkExists(String key, String value) {
        Boolean response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            var req = new vn.agis.crm.base.jpa.dto.req.CheckExistEmployeeReq(key, value);
            event = RequestUtils.amqp(JpaConstants.Method.CHECK_EXITS, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Boolean) event.payload;
                return Boolean.TRUE.equals(response);
            }
            return false;
        } catch (Exception e) {
            logger.error("Error in check employee exists: {}", e.getMessage(), e);
            return false;
        } finally {
            writeLog(timeRequest, timeResponse, key+":"+value, response != null ? response.toString() : null, event);
        }
    }

    public List<Employee> getAll() {
        List<Employee> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.GET_ALL, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<Employee>) event.payload;
                return response;
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in getAll employees: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, null, response != null ? response.toString() : null, event);
        }
    }

    public Employee currentEmployee(String token) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        Employee employee = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CURRENT_USER, category, token, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                employee = (Employee) event.payload;
                return employee;
            } else {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, "", MessageKeyConstant.FORBIDDEN);
            }
        } finally {
            writeLog(timeRequest, timeResponse, token, employee != null ? employee.toString() : null, event);
        }
    }

}

