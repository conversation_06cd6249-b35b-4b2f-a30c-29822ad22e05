package vn.agis.crm.base.event.amqp;


import com.google.common.base.Strings;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonPrimitive;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonSerializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Address;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.support.converter.MessageConversionException;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.core.type.filter.RegexPatternTypeFilter;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.utils.LoggingUtils;

import java.lang.reflect.ParameterizedType;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by tiemnd on 12/14/19.
 */
public class JsonMessageConverter implements MessageConverter {

    String applicationId;
    String packageServicePrefix;
    String packageCommonPrefix = "vn.agis.crm.";
    int packageCommonPrefixLength = packageCommonPrefix.length();
    String packageBasePrefix = "vn.agis.crm.base.";
    int packageServicePrefixLength;

    private static final Logger logger = LoggerFactory.getLogger(JsonMessageConverter.class);

    private Gson gson;
    private Map<String, Class<?>> classMap = new HashMap();
    private final List<String> dtoPackages = Arrays.asList("vn.agis.crm.base.jpa.dto");

    public JsonMessageConverter(Class<? extends Object> clazz) {
        this();
    }

    public JsonMessageConverter() {
        this.gson = buildGson();
        getClassMap();
        applicationId = SpringContext.getApplicationId();
        packageServicePrefix = SpringContext.getParentPackagePath();
        if (packageServicePrefix == null) {
            LoggingUtils.error(this.getClass(),
                    "Cannot construct JsonMessageConverter(), let using JsonMessageConverter(String serviceName, String parentPackagePrefix)");
        }
        packageServicePrefixLength = packageServicePrefix.length();
    }

    private Gson buildGson() {
        // Flexible Date adapter: supports ISO8601, yyyy-MM-dd, and US month strings like "May 15, 1985"
        JsonDeserializer<java.util.Date> dateDeserializer = (json, typeOfT, context) -> {
            if (json == null || json.isJsonNull()) return null;
            String val = json.getAsString();
            if (val == null || val.isEmpty()) return null;
            // Try epoch millis
            try {
                long epoch = Long.parseLong(val);
                return new java.util.Date(epoch);
            } catch (NumberFormatException ignore) {}
            // Try known patterns
            String[] patterns = new String[] {
                "yyyy-MM-dd'T'HH:mm:ss.SSSX",
                "yyyy-MM-dd'T'HH:mm:ssX",
                "yyyy-MM-dd HH:mm:ss",
                "yyyy-MM-dd",
                "MMM d, yyyy",
                "MMM d, yyyy h:mm:ss a"
            };
            for (String p : patterns) {
                try {
                    java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat(p, java.util.Locale.US);
                    sdf.setLenient(false);
                    // Assume UTC when timezone not provided
                    sdf.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));
                    return sdf.parse(val);
                } catch (java.text.ParseException ignore) {}
            }
            throw new JsonParseException("Unparseable date: " + val);
        };
        JsonSerializer<java.util.Date> dateSerializer = (src, typeOfSrc, context) -> {
            if (src == null) return null;
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSX", java.util.Locale.US);
            sdf.setTimeZone(java.util.TimeZone.getTimeZone("UTC"));
            String formatted = sdf.format(src);
            return new JsonPrimitive(formatted);
        };
        return new GsonBuilder()
                .registerTypeAdapter(java.util.Date.class, dateDeserializer)
                .registerTypeAdapter(java.util.Date.class, dateSerializer)
                .create();
    }

    private void getClassMap() {

        try {
            // create scanner and disable default filters (that is the 'false' argument)
            final ClassPathScanningCandidateComponentProvider provider = new ClassPathScanningCandidateComponentProvider(false);
            // add include filters which matches all the classes (or use your own)
            provider.addIncludeFilter(new RegexPatternTypeFilter(Pattern.compile(".*")));

            for (String dtoPackage : dtoPackages) {
                // get matching classes defined in the package
                final Set<BeanDefinition> classes = provider.findCandidateComponents(dtoPackage);
                // this is how you can load the class type from BeanDefinition instance
                for (BeanDefinition bean : classes) {
                    Class<?> clazz = Class.forName(bean.getBeanClassName());
                    // ... do your magic with the class ...
                    classMap.put(clazz.getSimpleName(), clazz);
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Override
    public Message toMessage(Object o, MessageProperties messageProperties) throws MessageConversionException {
        if (o instanceof Event) {
            Event event = (Event) o;
            if (event.userId != null) {
                messageProperties.getHeaders().put("userId", event.userId);
            }
            if (event.userType != null) {
                messageProperties.getHeaders().put("userType", event.userType);
            }
            if (StringUtils.isNotBlank(event.provinceCode)) {
                messageProperties.getHeaders().put("provinceCode", event.provinceCode);
            }
            messageProperties.setAppId(event.appId);
            if (event.appId == null) messageProperties.setAppId(applicationId);
            messageProperties.setMessageId(event.id);
            messageProperties.setCorrelationId(event.id);
            if (!Strings.isNullOrEmpty(event.replyQueue)) {
                messageProperties.setReplyToAddress(new Address(event.replyQueue));
            }
            if (!Strings.isNullOrEmpty(event.receiveRoutingKey)) {
                messageProperties.setReceivedRoutingKey(event.receiveRoutingKey);
            }
            messageProperties.setTimestamp(event.timeStamp);
            messageProperties.setType(event.method);
            if (event.category != null)
                messageProperties.getHeaders().put("category", event.category);
            if (event.respRoutingKey != null)
                messageProperties.getHeaders().put("respRoutingKey", event.respRoutingKey);
            if (event.payload != null) {
                String className;
                boolean isList = false;
                // Auto set conversion class if null
                if ((event.payload instanceof List) && ((List<?>) event.payload).size() > 0) {
                    Object actualObj = ((List<?>) event.payload).get(0);
                    className = actualObj.getClass().getName();
                    isList = true;
                } else className = event.payload.getClass().getName();

                if (className.startsWith(packageServicePrefix))
                    className = packageCommonPrefix + className.substring(packageServicePrefixLength);

                if (isList)
                    event.conversionClass = "List<" + className + ">";
                else event.conversionClass = className;
            } else {
                event.conversionClass = com.google.gson.JsonNull.class.getName();
            }
            messageProperties.getHeaders().put("conversionClass", event.conversionClass);
            if (event.respStatusCode != null)
                messageProperties.getHeaders().put("respStatusCode", event.respStatusCode);
            if (event.respErrorDesc != null)
                messageProperties.getHeaders().put("respErrorDesc", event.respErrorDesc);
//            Message message = new Message(this.gson.toJson(event.payload).getBytes(), messageProperties);
            if (event.payload != null && event.payload.getClass().getName().equals(byte[].class.getName())) {
                return new Message((byte[]) event.payload, messageProperties);
            } else {
                return new Message(this.gson.toJson(event.payload).getBytes(), messageProperties);
            }
        } else {
            Message message = new Message(this.gson.toJson(o).getBytes(), messageProperties);
            return message;
        }
    }

    @Override
    public Object fromMessage(Message message) throws MessageConversionException {
        Event event = new Event();
        MessageProperties messageProperties = message.getMessageProperties();
        event.id = messageProperties.getMessageId();
        event.appId = messageProperties.getAppId();
        event.userId = (Long) messageProperties.getHeaders().get("userId");
        event.userType = (Integer) messageProperties.getHeaders().get("userType");
        event.provinceCode = (String) messageProperties.getHeaders().get("provinceCode");
        event.timeStamp = messageProperties.getTimestamp();
        event.category = (String) messageProperties.getHeaders().get("category");
        event.method = messageProperties.getType();
        event.replyQueue = messageProperties.getReplyTo();
        event.respStatusCode = (Integer) messageProperties.getHeaders().get("respStatusCode");
        if (event.respStatusCode != null) {
            Object value = messageProperties.getHeaders().get("respErrorDesc");
            if (value instanceof String)
                event.respErrorDesc = (String) messageProperties.getHeaders().get("respErrorDesc");
            else if (value instanceof byte[]){
                event.respErrorDesc = new String((byte[]) messageProperties.getHeaders().get("respErrorDesc"));
            } else if (value != null){
                event.respErrorDesc = value.toString();
            }
        }
        event.respRoutingKey = (String) messageProperties.getHeaders().get("respRoutingKey");
        String originalClass = (String) messageProperties.getHeaders().get("conversionClass");
        event.conversionClass = originalClass;
        Class<?> baseClass = null;
        boolean isList = false;
        if (event.conversionClass == null)
            event.conversionClass = JsonObject.class.getName();
        else if (!event.conversionClass.equals(com.google.gson.JsonNull.class.getName())) {
            // Get actual class if List
            if (event.conversionClass.startsWith("List<")) {
                event.conversionClass = event.conversionClass.substring(5, event.conversionClass.length() - 1);
                isList = true;
            }
            if (event.conversionClass.startsWith(packageCommonPrefix) && !event.conversionClass.startsWith(packageBasePrefix))
                event.conversionClass = packageServicePrefix + event.conversionClass.substring(packageCommonPrefixLength);
            try {
                if (event.conversionClass.equals(byte[].class.getName())) {
                    event.payload = message.getBody();
                }
                else if (isList) {
                    TypeToken<?> typeToken = TypeToken.get(Class.forName(event.conversionClass));
                    ParameterizedType parameterizedType = (ParameterizedType) TypeToken.getParameterized(List.class, typeToken.getType()).getType();
                    Object payload = this.gson.fromJson(new String(message.getBody()), parameterizedType);
                    event.payload = payload;
                } else {
                    if (message.getBody() != null || message.getBody().length > 0) {
                        Object payload = this.gson.fromJson(new String(message.getBody()), Class.forName(event.conversionClass));
                        event.payload = payload;
                    } else {
                        event.payload = null;
                    }
                }
            } catch (ClassNotFoundException e) {
                baseClass = classMap.get(originalClass);
                if (baseClass == null)
                    throw new MessageConversionException(e.getMessage());
                try {
                    if (isList) {
                        TypeToken<?> typeToken = TypeToken.get(baseClass);
                        ParameterizedType parameterizedType = (ParameterizedType) TypeToken.getParameterized(List.class, typeToken.getType()).getType();
                        Object payload = this.gson.fromJson(new String(message.getBody()), parameterizedType);
                        event.payload = payload;
                    } else {
                        Object payload = this.gson.fromJson(new String(message.getBody()), baseClass);
                        event.payload = payload;
                    }
                } catch (JsonSyntaxException f) {
                    throw new MessageConversionException(e.getMessage());
                }
            }
        } else {
            event.conversionClass = null;
            event.payload = null;
        }

        HashMap<String, Object> incomingMessage = new HashMap<>();
        incomingMessage.put("event", event);
        incomingMessage.put("MessageProperties", message.getMessageProperties());
        return incomingMessage;
    }

    public Event getEventFromMessage(Message message) {
        Object map = fromMessage(message);
        if (map != null) return (Event) ((Map) map).get("event");
        return null;
    }
}
