package vn.agis.crm.base.jpa.entity;
import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Entity
@Table(name = "ALERT_RECEIVING_GROUP")
@Data
public class AlertReceivingGroup {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "ALERT_CONFIG_ID")
    private Long alertConfigId;

    @Column(name = "RECEIVING_GROUP_ID")
    private Long receivingGroupId;
}
