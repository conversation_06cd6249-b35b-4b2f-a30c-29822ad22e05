package vn.agis.crm.base.event.amqp;

import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;

public class MySimpleMessageListenerContainer extends SimpleMessageListenerContainer {

    public MySimpleMessageListenerContainer(ConnectionFactory connectionFactory) {
        super(connectionFactory);
        logger.info("MySimpleMessageListenerContainer is constructor here");
    }
}
