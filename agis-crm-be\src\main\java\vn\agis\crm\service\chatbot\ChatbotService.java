package vn.agis.crm.service.chatbot;

import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.ConfigSearchDto;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.chatbot.ChatBotQuestionDTO;
import vn.agis.crm.base.jpa.dto.res.chatbot.ChatBotQuestionResponseDTO;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.exception.OpenAIKeyNotFoundException;
import vn.agis.crm.exception.ParameterNotFoundException;
import vn.agis.crm.model.dto.ChatbotResponse;
import vn.agis.crm.service.ChatVirtualAssistantService;
import vn.agis.crm.util.BaseController;

import java.util.List;
import java.util.Map;

@Service
public class ChatbotService implements ApplicationRunner {
    private final Logger logger = LoggerFactory.getLogger(ChatbotService.class);

    private final RestTemplate restTemplate;

    @Value("${openai.api.key:123}")
    private String openaiApiKey;

    public ChatbotService() {
        this.restTemplate = new RestTemplate();
    }

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.SEND_QUESTION:
                return processQuestion(event);
        }
        return event;
    }

    private Event processQuestion(Event event) {
        ChatBotQuestionDTO dto = (ChatBotQuestionDTO) event.payload;
        ChatBotQuestionResponseDTO chatBotQuestionResponseDTO = new ChatBotQuestionResponseDTO("SQL NOT FOUND");
        try {
            String sqlResponse = semanticSearchService.buildQuery(dto.getQ());
            chatBotQuestionResponseDTO.setSqlResponse(sqlResponse);
        } catch (OpenAIKeyNotFoundException e) {
            return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, "Open AI Key not found");
        } catch (ParameterNotFoundException e) {
            logger.error("Query By Semantic Error, try ask ", e);
            chatBotQuestionResponseDTO.setSqlResponse(generateSqlFromLLM(dto.getQ()));
        } catch (Exception e) {
            return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, e.getMessage() );
        }
        return event.createResponse(chatBotQuestionResponseDTO, ResponseCode.OK, null);
    }


    public ChatbotResponse processQuery(String question) {
        String sql = generateSqlFromLLM(question);
        return new ChatbotResponse(sql);
    }

    private String generateSqlFromLLM(String question) {
        // 1. Lấy schema từ DB (ở đây đơn giản chỉ lấy danh sách cột từ bảng customer)
        logger.info("generateSqlFromLLM for question {}", question);
        String schema = """
                Bảng: customer
                Cột:
                - id (INT, PK)
                - full_name (VARCHAR)
                - phone (VARCHAR)
                - email (VARCHAR)
                - birth_date (DATE)
                - address_contact (TEXT)
                - address_permanent (TEXT)
                - nationality (VARCHAR)
                - marital_status (VARCHAR)
                - total_asset (DECIMAL)
                - business_field (VARCHAR)
                - avatar_url (VARCHAR)
                - zalo_status (VARCHAR)
                - facebook_link (VARCHAR)
                - source_type (ENUM: Data, Leads, Event, Refer)
                - source_detail (VARCHAR)
                - notes (TEXT)
                - created_at (DATETIME)
                - updated_at (DATETIME)

                Bảng: customer_relatives
                - id (INT, PK)
                - customer_id (INT, FK → customer.id)
                - relation_type (VARCHAR)
                - full_name (VARCHAR)
                - birth_year (INT)
                - phone (VARCHAR)
                - notes (TEXT)
                """;

        String systemPrompt = """
                Bạn là chuyên gia SQL cho MySQL.
                Dựa trên schema sau, hãy viết SQL chính xác để trả lời câu hỏi.
                Yêu cầu:
                - Chỉ trả về câu lệnh SQL hợp lệ.
                - Không thêm giải thích, không bao quanh bởi ```sql hoặc ký tự khác.
                - Ví dụ output hợp lệ:
                  SELECT * FROM customer WHERE email = '<EMAIL>';
                        
                Schema:
                """ + schema;

        // 3. Body gửi tới OpenAI
        String apiUrl = "https://api.openai.com/v1/chat/completions";
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(openaiApiKey);
        headers.setContentType(MediaType.APPLICATION_JSON);

        Map<String, Object> body = Map.of(
                "model", "gpt-4o-mini",
                "messages", List.of(
                        Map.of("role", "system", "content", systemPrompt),
                        Map.of("role", "user", "content", "Câu hỏi: " + question)
                ),
                "temperature", 0
        );

        HttpEntity<Map<String, Object>> request = new HttpEntity<>(body, headers);
        ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, request, Map.class);

        // 4. Parse kết quả
        List<Map<String, Object>> choices = (List<Map<String, Object>>) response.getBody().get("choices");
        Map<String, Object> message = (Map<String, Object>) choices.get(0).get("message");
        String sqlResponse = message.get("content").toString().trim();
        logger.info("SQL Response for questions {} = {}", question, sqlResponse);
        return sqlResponse;
    }

    @Autowired
    SemanticSearchService semanticSearchService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
//        System.out.println(generateSqlFromLLM("lấy ra thông tin khách hàng có email là <EMAIL>"));
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có số phone 0908089883"));
//
//        // 2
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có số CCCD 0808308398"));
//
//        // 3
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có địa chỉ email: <EMAIL>"));
//
//        // 4
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có tên Nguyễn Văn A"));
//
//        // 5
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà: 180 Nguyễn Hữu Cảnh, Phường 22, Bình Thạnh, HCM"));
//
//        // 5.1
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà: 180 Nguyễn Hữu Cảnh"));
//
//        // 5.2
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có địa chỉ nhà ở khu vực Bình Thạnh"));
//
//        // 6
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có người thân tên Nguyễn Văn B"));
//
//        // 7
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm Biệt thự"));
//
//        // 8
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm Penthouse"));
//
//        // 9
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm Duplex"));
//
//        // 10
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm Nhà phố"));
//
//        // 11
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm căn hộ"));
//
//        // 11 (Studio)
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang sở hữu sản phẩm căn hộ Studio"));
//
//        // 12
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có ngày sinh vào 09/09/1985"));
//
//        // 13
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng NAM có ngày sinh vào 09/09/1985"));
//
//        // 14
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng đang kinh doanh trong lĩnh vực xuất nhập khẩu"));
//
//        // 15
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng NỮ đang kinh doanh trong lĩnh vực xuất nhập khẩu"));
//
//        // 16
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng có sở thích chơi golf"));
//
//        // 17
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho tôi thông tin chi tiết của khách hàng NAM có sở thích chơi golf"));
//
//        // 1 - phone
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Tìm giúp tôi khách hàng có số điện thoại là 0908089883"));
//
//        // 2 - CCCD
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Lấy thông tin khách hàng theo số căn cước công dân 0808308398"));
//
//        // 3 - email
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng dù<NAME_EMAIL> là ai?"));
//
//        // 4 - tên
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Tra cứu khách hàng có tên đầy đủ là Nguyễn Văn A"));
//
//        // 5 - địa chỉ đầy đủ
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Ai là khách hàng ở địa chỉ 180 Nguyễn Hữu Cảnh, P22, Bình Thạnh, HCM"));
//
//        // 5.1 - địa chỉ ngắn
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng nào đang sống tại 180 Nguyễn Hữu Cảnh?"));
//
//        // 5.2 - khu vực
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Danh sách khách hàng cư trú tại Bình Thạnh"));
//
//        // 6 - người thân
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Có khách hàng nào có người thân tên Nguyễn Văn B không?"));
//
//        // 7 - sản phẩm Biệt thự
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Liệt kê khách hàng đang sở hữu bất động sản loại Biệt thự"));
//
//        // 8 - Penthouse
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng nào có căn Penthouse hoặc Sky Villa"));
//
//        // 9 - Duplex
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Ai đang sở hữu loại hình Duplex"));
//
//        // 10 - Nhà phố/Shophouse
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Danh sách khách hàng có Shophouse hoặc Nhà phố"));
//
//        // 11 - căn hộ chung chung
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng nào hiện đang ở căn hộ"));
//
//        // 11 (Studio)
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Cho biết khách hàng nào đang sở hữu căn hộ Studio"));
//
//        // 12 - ngày sinh
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Tìm khách hàng sinh ngày 09/09/1985"));
//
//        // 13 - ngày sinh + giới tính
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Có khách hàng nam nào sinh ngày 09/09/1985 không?"));
//
//        // 14 - ngành nghề
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng nào đang hoạt động trong lĩnh vực xuất nhập khẩu"));
//
//        // 15 - ngành nghề + giới tính
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Liệt kê khách hàng nữ làm nghề xuất nhập khẩu"));
//
//        // 16 - sở thích golf
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Ai trong danh sách khách hàng có thú vui chơi golf"));
//
//        // 17 - sở thích golf + giới tính
//        System.out.println(
//                semanticSearchService.buildQuery(
//                        "Khách hàng nam nào có sở thích đánh golf"));
    }
}
