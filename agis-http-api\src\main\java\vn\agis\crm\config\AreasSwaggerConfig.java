package vn.agis.crm.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger configuration for Areas API
 */
@Configuration
@OpenAPIDefinition(
    info = @Info(
        title = "CRM Areas Management API",
        version = "1.0",
        description = "API for managing Areas in CRM system"
    ),
    tags = {
        @Tag(name = "Areas", description = "Areas management operations")
    }
)
public class AreasSwaggerConfig {
    // This class is used for Swagger documentation configuration
    // The actual API documentation is defined in the AreasController using annotations
}
