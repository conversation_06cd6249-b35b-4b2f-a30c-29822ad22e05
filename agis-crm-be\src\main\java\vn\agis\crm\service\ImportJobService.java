package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.domain.imports.ImportJobMode;
import vn.agis.crm.base.domain.imports.ImportJobSource;
import vn.agis.crm.base.domain.imports.ImportJobStatus;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.*;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.entity.ImportJob;
import vn.agis.crm.base.jpa.repositories.ImportJobRepository;
import vn.agis.crm.base.utils.MultipartFileWrapper;
import vn.agis.crm.repository.*;
import vn.agis.crm.util.ImportDryRunProcessor;
import vn.agis.crm.util.ImportExecutionProcessor;
import vn.agis.crm.util.ImportFileUtils;
import vn.agis.crm.util.ImportStatisticsCalculator;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

@Service
@Transactional
public class ImportJobService {

    private static final Logger logger = LoggerFactory.getLogger(ImportJobService.class);

    @Autowired
    private ImportJobRepository importJobRepository;

    @Autowired
    private ConfigRepository configRepository;
    @Autowired
    private ProjectRepository projectRepository;
    @Autowired
    private UnitRepository unitRepository;
    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;
    @Autowired
    private CustomerRepository customerRepository;
    @Autowired
    private CustomerRelativeRepository customerRelativeRepository;
    @Autowired
    private EmployeeRepository employeeRepository;
    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;
    @Autowired
    private CustomerOfferRepository customerOfferRepository;


    @Value("${app.upload.dir:uploads}")
    private String uploadBaseDir;

    /**
     * Process incoming events
     */
    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.CREATE_IMPORT_JOB_WEB_UPLOAD:
                return createFromWebUpload(event);
            case Constants.Method.GET_IMPORT_JOB:
                return getById(event);
            case Constants.Method.GET_EXCEL_SHEETS:
                return getExcelSheets(event);
            case Constants.Method.GET_FILE_METADATA:
                return getFileMetadata(event);
            case Constants.Method.GET_SUPPORTED_FORMATS:
                return getSupportedFormats(event);
            case Constants.Method.START_DRY_RUN:
                return startDryRun(event);
            case Constants.Method.GET_DRY_RUN_RESULT:
                return getDryRunResult(event);
            case Constants.Method.GET_IMPORT_ERRORS:
                return getImportErrors(event);
            case Constants.Method.CONFIRM_IMPORT:
                return confirmImport(event);
            case Constants.Method.GET_IMPORT_PROGRESS:
                return getImportProgress(event);
            case Constants.Method.CANCEL_IMPORT:
                return cancelImport(event);
            case Constants.Method.GET_IMPORT_RESULT:
                return getImportResult(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    /**
     * Create import job from web upload
     */
    private Event createFromWebUpload(Event event) {
        try {
            // Extract parameters from event payload (FileUploadDto format)
            FileUploadDto fileDto = (FileUploadDto) event.payload;

            // Create MultipartFile from DTO for processing
            MultipartFile file = new MultipartFileWrapper(
                fileDto.getContent(),
                fileDto.getOriginalFilename(),
                fileDto.getContentType()
            );
            String options = fileDto.getOptions();

            // Validate file
            validateFile(file);

            // Calculate checksum
            String checksum = ImportFileUtils.calculateChecksum(file);

            // Check for duplicates
            List<ImportJob> duplicates = importJobRepository.findByFileChecksum(checksum);

            // Create job entity
            ImportJob job = new ImportJob();
            job.setFileName(file.getOriginalFilename());
            job.setFileChecksum(checksum);
            job.setSource(ImportJobSource.WEB_UPLOAD);
            job.setMode(ImportJobMode.DRY_RUN);
            job.setStatus(ImportJobStatus.PENDING);
            job.setOptions(options);
            job.setCreatedBy(event.userId);

            // Save job to get ID
            job = importJobRepository.save(job);

            // Save file to secure location
            String filePath = ImportFileUtils.createSecureFilePath(
                uploadBaseDir, job.getId(), checksum, file.getOriginalFilename());

            try {
                file.transferTo(Paths.get(filePath));
            } catch (IOException e) {
                logger.error("Failed to save uploaded file", e);
                return event.createResponse(null, 500, "Failed to save uploaded file");
            }

            // Convert to DTO
            ImportJobDto dto = convertToDto(job);

            // Add warnings if duplicates found
            if (!duplicates.isEmpty()) {
                ImportJobDto.WarningsDto warnings = new ImportJobDto.WarningsDto();
                warnings.setDuplicateFileDetected(true);
                warnings.setRelatedJobIds(duplicates.stream().map(ImportJob::getId).collect(Collectors.toList()));
                dto.setWarnings(warnings);
            }

            return event.createResponse(dto, ResponseCode.OK, "Import job created successfully");

        } catch (IOException | NoSuchAlgorithmException e) {
            logger.error("Error processing file upload", e);
            return event.createResponse(null, 500, "Error processing file upload");
        } catch (Exception e) {
            logger.error("Unexpected error in createFromWebUpload", e);
            return event.createResponse(null, 500, "Unexpected error occurred");
        }
    }



    /**
     * Get import job by ID
     */
    private Event getById(Event event) {
        try {
            Long id = (Long) event.payload;
            Optional<ImportJob> job = importJobRepository.findByIdAndCreatedBy(id, event.userId);

            if (job.isPresent()) {
                return event.createResponse(convertToDto(job.get()), ResponseCode.OK, "Success");
            } else {
                return event.createResponse(null, 404, "Import job not found");
            }
        } catch (Exception e) {
            logger.error("Error getting import job by ID", e);
            return event.createResponse(null, 500, "Error retrieving import job");
        }
    }

    /**
     * Get Excel sheets for import job
     */
    private Event getExcelSheets(Event event) {
        try {
            Long id = (Long) event.payload;
            ImportJob job = importJobRepository.findByIdAndCreatedBy(id, event.userId)
                .orElse(null);

            if (job == null) {
                return event.createResponse(null, 404, "Import job not found");
            }

            if (!ImportFileUtils.isExcelFile(job.getFileName())) {
                return event.createResponse(null, 400, "File is not an Excel file");
            }

            List<ImportFileUtils.ExcelSheetInfo> sheets;

            if (job.getSource() == ImportJobSource.WEB_UPLOAD) {
                String filePath = ImportFileUtils.createSecureFilePath(
                    uploadBaseDir, job.getId(), job.getFileChecksum(), job.getFileName());
                sheets = ImportFileUtils.getExcelSheets(filePath);
            } else {
                return event.createResponse(null, 501, "Google Drive sheet listing not yet implemented");
            }

            ExcelSheetDto dto = new ExcelSheetDto();
            dto.setFileName(job.getFileName());
            dto.setSheets(sheets.stream()
                .map(s -> new ExcelSheetDto.SheetInfo(s.getIndex(), s.getName()))
                .collect(Collectors.toList()));

            return event.createResponse(dto, ResponseCode.OK, "Success");

        } catch (IOException e) {
            logger.error("Error reading Excel sheets", e);
            return event.createResponse(null, 500, "Error reading Excel sheets");
        } catch (Exception e) {
            logger.error("Unexpected error in getExcelSheets", e);
            return event.createResponse(null, 500, "Unexpected error occurred");
        }
    }

    /**
     * Get file metadata for import job
     */
    private Event getFileMetadata(Event event) {
        try {
            Long id = (Long) event.payload;
            ImportJob job = importJobRepository.findByIdAndCreatedBy(id, event.userId)
                .orElse(null);

            if (job == null) {
                return event.createResponse(null, 404, "Import job not found");
            }

            FileMetadataDto dto = new FileMetadataDto();
            dto.setFileName(job.getFileName());
            dto.setSource(job.getSource());
            dto.setSourceLink(job.getSourceLink());
            dto.setFileChecksum(job.getFileChecksum());
            dto.setCreatedAt(job.getCreatedAt());

            // Determine MIME type
            String extension = ImportFileUtils.getFileExtension(job.getFileName()).toLowerCase();
            switch (extension) {
                case "csv":
                    dto.setMimeType("text/csv");
                    break;
                case "xlsx":
                    dto.setMimeType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                    break;
                case "xls":
                    dto.setMimeType("application/vnd.ms-excel");
                    break;
            }

            // Get file size if available
            if (job.getSource() == ImportJobSource.WEB_UPLOAD && job.getFileChecksum() != null) {
                try {
                    String filePath = ImportFileUtils.createSecureFilePath(
                        uploadBaseDir, job.getId(), job.getFileChecksum(), job.getFileName());
                    dto.setSizeBytes(Files.size(Paths.get(filePath)));
                } catch (IOException e) {
                    logger.warn("Could not determine file size", e);
                }
            }

            // Check for duplicates
            if (job.getFileChecksum() != null) {
                List<ImportJob> duplicates = importJobRepository.findByFileChecksum(job.getFileChecksum());
                dto.setDuplicateWarning(duplicates.size() > 1);
            }

            return event.createResponse(dto, ResponseCode.OK, "Success");
        } catch (Exception e) {
            logger.error("Error getting file metadata", e);
            return event.createResponse(null, 500, "Error retrieving file metadata");
        }
    }

    /**
     * Get supported formats information
     */
    private Event getSupportedFormats(Event event) {
        try {
            SupportedFormatsDto dto = new SupportedFormatsDto();
            dto.setFormats(Arrays.asList("csv", "xlsx", "xls"));
            dto.setEncoding(Arrays.asList("utf-8"));

            // Get configuration values
            dto.setMaxFileSizeMb(getConfigIntValue("import.max_file_size_mb", 10));
            dto.setMaxRowsPerFile(getConfigIntValue("import.max_rows_per_file", 10000));

            return event.createResponse(dto, ResponseCode.OK, "Success");
        } catch (Exception e) {
            logger.error("Error getting supported formats", e);
            return event.createResponse(null, 500, "Error retrieving supported formats");
        }
    }

    /**
     * Validate uploaded file
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new IllegalArgumentException("File is empty");
        }

        // Check file type
        if (!ImportFileUtils.isValidFileType(file.getOriginalFilename(), file.getContentType())) {
            throw new IllegalArgumentException("Invalid file type");
        }

        // Check file size
        int maxSizeMb = getConfigIntValue("import.max_file_size_mb", 10);
        long maxSizeBytes = maxSizeMb * 1024L * 1024L;
        
        if (file.getSize() > maxSizeBytes) {
            throw new IllegalArgumentException("File size exceeds maximum allowed size of " + maxSizeMb + "MB");
        }
    }

    /**
     * Convert ImportJob entity to DTO
     */
    private ImportJobDto convertToDto(ImportJob job) {
        ImportJobDto dto = new ImportJobDto();
        dto.setId(job.getId());
        dto.setFileName(job.getFileName());
        dto.setFileChecksum(job.getFileChecksum());
        dto.setSource(job.getSource());
        dto.setSourceLink(job.getSourceLink());
        dto.setMode(job.getMode());
        dto.setTotalRows(job.getTotalRows());
        dto.setValidRows(job.getValidRows());
        dto.setErrorRows(job.getErrorRows());
        dto.setStatus(job.getStatus());
        dto.setOptions(job.getOptions());
        dto.setCreatedAt(job.getCreatedAt());
        dto.setUpdatedAt(job.getUpdatedAt());
        return dto;
    }

    /**
     * Get configuration integer value
     */
    private Integer getConfigIntValue(String key, Integer defaultValue) {
        try {
            Optional<Config> config = configRepository.findByConfigKey(key);
            if (config.isPresent()) {
                return Integer.parseInt(config.get().getConfigValue());
            }
        } catch (Exception e) {
            logger.warn("Error reading config value for key: " + key, e);
        }
        return defaultValue;
    }

    /**
     * Start dry-run validation for import job
     */
    private Event startDryRun(Event event) {
        try {
            Long jobId = (Long) event.payload;

            // Get import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Validate job status
            if (!ImportJobStatus.PENDING.equals(job.getStatus())) {
                return event.createResponse(null, 400, "Job must be in PENDING status to start dry-run");
            }

            if (!ImportJobMode.DRY_RUN.equals(job.getMode())) {
                return event.createResponse(null, 400, "Job must be in DRY_RUN mode");
            }

            // Update job status to RUNNING
            job.setStatus(ImportJobStatus.RUNNING);
            // job.setStartedAt(LocalDateTime.now()); // TODO: Add this field to ImportJob entity
            importJobRepository.save(job);

            // Start dry-run processing asynchronously
            CompletableFuture.runAsync(() -> processDryRunSync(job));

            // Return updated job info
            ImportJobDto result = convertToDto(job);
            return event.createResponse(result, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error starting dry-run", e);
            return event.createResponse(null, 500, "Error starting dry-run: " + e.getMessage());
        }
    }

    /**
     * Get dry-run validation results
     */
    private Event getDryRunResult(Event event) {
        try {
            Long jobId = (Long) event.payload;

            // Get import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Build dry-run result
            DryRunResultDto result = buildDryRunResult(job);

            return event.createResponse(result, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error retrieving dry-run result", e);
            return event.createResponse(null, 500, "Error retrieving dry-run result: " + e.getMessage());
        }
    }

    /**
     * Get import errors with pagination and filtering
     */
    @SuppressWarnings("unchecked")
    private Event getImportErrors(Event event) {
        try {
            Map<String, Object> payload = (Map<String, Object>) event.payload;
            Long jobId = ((Number) payload.get("id")).longValue();
            int page = ((Number) payload.get("page")).intValue();
            int size = ((Number) payload.get("size")).intValue();
            Map<String, Object> filters = (Map<String, Object>) payload.get("filters");

            // Get import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            // For now, return empty list - will implement error repository later
            List<ImportErrorDto> errorDtos = new ArrayList<>();

            // Build paginated response
            Map<String, Object> result = new HashMap<>();
            result.put("data", errorDtos);
            result.put("pagination", Map.of(
                "page", page,
                "size", size,
                "totalElements", 0L,
                "totalPages", 0,
                "hasNext", false,
                "hasPrevious", false
            ));

            return event.createResponse(result, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error retrieving import errors", e);
            return event.createResponse(null, 500, "Error retrieving import errors: " + e.getMessage());
        }
    }

    /**
     * Process dry-run validation synchronously
     */
    private void processDryRunSync(ImportJob job) {
        try {
            logger.info("Starting dry-run processing for job {}", job.getId());

            // Get file path
            String filePath = ImportFileUtils.createSecureFilePath(
                uploadBaseDir, job.getId(), job.getFileChecksum(), job.getFileName());

            // Process dry-run using the processor
            DryRunResultDto result = ImportDryRunProcessor.processDryRun(job, filePath);

            // Store result in job options (temporary solution)
            // In a real implementation, this would be stored in a separate table
            job.setOptions(job.getOptions() + "\n" + ImportStatisticsCalculator.generateSummaryReport(result));

            // Update job status based on result
            if ("SUCCESS".equals(result.getStatus())) {
                job.setStatus(ImportJobStatus.SUCCESS);
            } else {
                job.setStatus(ImportJobStatus.FAILED);
            }

            importJobRepository.save(job);

            logger.info("Completed dry-run processing for job {}: {}",
                       job.getId(), ImportDryRunProcessor.generateProcessingSummary(result));

        } catch (Exception e) {
            logger.error("Error processing dry-run for job " + job.getId(), e);

            // Update job status to FAILED
            job.setStatus(ImportJobStatus.FAILED);
            importJobRepository.save(job);
        }
    }

    /**
     * Build dry-run result DTO
     */
    private DryRunResultDto buildDryRunResult(ImportJob job) {
        // If job is still running, return basic info
        if (ImportJobStatus.RUNNING.equals(job.getStatus())) {
            DryRunResultDto result = new DryRunResultDto();
            result.setJobId(job.getId());
            result.setFileName(job.getFileName());
            result.setStatus("RUNNING");
            return result;
        }

        // If job is completed, try to get results from file processing
        try {
            String filePath = ImportFileUtils.createSecureFilePath(
                uploadBaseDir, job.getId(), job.getFileChecksum(), job.getFileName());

            // Re-process to get results (in real implementation, this would be cached)
            return ImportDryRunProcessor.processDryRun(job, filePath);

        } catch (Exception e) {
            logger.warn("Could not rebuild dry-run result for job {}, returning basic info", job.getId(), e);

            // Return basic result
            DryRunResultDto result = new DryRunResultDto();
            result.setJobId(job.getId());
            result.setFileName(job.getFileName());
            result.setStatus(job.getStatus().name());
            result.setTotalRows(0);
            result.setValidRows(0);
            result.setErrorRows(0);
            result.setWarningRows(0);
            result.setDuplicateRows(0);

            // Create empty estimation
            DryRunResultDto.EstimationDto estimation = new DryRunResultDto.EstimationDto();
            estimation.setCustomersToCreate(0);
            estimation.setCustomersToUpdate(0);
            estimation.setRelativesToCreate(0);
            estimation.setPropertiesToCreate(0);
            estimation.setOffersToCreate(0);
            estimation.setAssignmentsToCreate(0);
            estimation.setEstimatedProcessingTimeMs(0L);
            estimation.setEstimatedProcessingTime("0 seconds");
            result.setEstimation(estimation);

            result.setErrorSummary(new ArrayList<>());
            result.setWarnings(Arrays.asList("Could not retrieve detailed results: " + e.getMessage()));

            return result;
        }
    }

    // ================================
    // STEP 3: CONFIRMATION & EXECUTION
    // ================================

    /**
     * Confirm and start import execution
     */
    private Event confirmImport(Event event) {
        try {
            Map<String, Object> requestData = (Map<String, Object>) event.payload;
            Long jobId = ((Number) requestData.get("jobId")).longValue();
            String options = (String) requestData.get("options");
            Long userId = ((Number) requestData.get("userId")).longValue();

            logger.info("Confirming import execution for job {} by user {}", jobId, userId);

            // Get the import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Validate job state - must be in SUCCESS status from dry-run
            if (!ImportJobStatus.SUCCESS.equals(job.getStatus())) {
                return event.createResponse(null, 409, "Job must be in SUCCESS status from dry-run to proceed with execution");
            }

            if (!ImportJobMode.DRY_RUN.equals(job.getMode())) {
                return event.createResponse(null, 409, "Job must be in DRY_RUN mode to proceed with execution");
            }

            // Update job to RUN mode and PENDING status
            job.setMode(ImportJobMode.RUN);
            job.setStatus(ImportJobStatus.PENDING);
            job.setUpdatedBy(userId);

            // Update options if provided
            if (options != null && !options.trim().isEmpty()) {
                job.setOptions(job.getOptions() + "\n" + options);
            }

            importJobRepository.save(job);

            // Start execution asynchronously
            CompletableFuture.runAsync(() -> processImportExecution(event.userId,job));

            // Create and return progress DTO
            ImportProgressDto progress = new ImportProgressDto(job.getId(), job.getFileName(), "PENDING");
            progress.setTotalRows(job.getTotalRows());
            progress.setStartedAt(new Date());

            return event.createResponse(progress, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error confirming import", e);
            return event.createResponse(null, 500, "Error confirming import: " + e.getMessage());
        }
    }

    /**
     * Get import progress
     */
    private Event getImportProgress(Event event) {
        try {
            Map<String, Object> requestData = (Map<String, Object>) event.payload;
            Long jobId = ((Number) requestData.get("jobId")).longValue();
            Long userId = ((Number) requestData.get("userId")).longValue();

            logger.debug("Getting import progress for job {} by user {}", jobId, userId);

            // Get the import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Build progress DTO
            ImportProgressDto progress = buildImportProgress(job);

            return event.createResponse(progress, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error retrieving import progress", e);
            return event.createResponse(null, 500, "Error retrieving import progress: " + e.getMessage());
        }
    }

    /**
     * Cancel running import
     */
    private Event cancelImport(Event event) {
        try {
            Map<String, Object> requestData = (Map<String, Object>) event.payload;
            Long jobId = ((Number) requestData.get("jobId")).longValue();
            String reason = (String) requestData.get("reason");
            Long userId = ((Number) requestData.get("userId")).longValue();

            logger.info("Cancelling import for job {} by user {} with reason: {}", jobId, userId, reason);

            // Get the import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Validate job can be cancelled
            if (!ImportJobStatus.RUNNING.equals(job.getStatus()) && !ImportJobStatus.PENDING.equals(job.getStatus())) {
                return event.createResponse(null, 409, "Job is not in a cancellable state. Current status: " + job.getStatus());
            }

            // Update job status to CANCELLED
            job.setStatus(ImportJobStatus.CANCELLED);
            job.setUpdatedBy(userId);

            // Add cancellation reason to options
            String cancellationInfo = "CANCELLED by user " + userId + " at " + new Date() +
                                    (reason != null ? " - Reason: " + reason : "");
            job.setOptions(job.getOptions() + "\n" + cancellationInfo);

            importJobRepository.save(job);

            // Build progress DTO with cancellation info
            ImportProgressDto progress = buildImportProgress(job);
            progress.setCancellationRequested(true);
            progress.setCancellationReason(reason);

            return event.createResponse(progress, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error cancelling import", e);
            return event.createResponse(null, 500, "Error cancelling import: " + e.getMessage());
        }
    }

    /**
     * Get import result
     */
    private Event getImportResult(Event event) {
        try {
            Map<String, Object> requestData = (Map<String, Object>) event.payload;
            Long jobId = ((Number) requestData.get("jobId")).longValue();
            Long userId = ((Number) requestData.get("userId")).longValue();

            logger.debug("Getting import result for job {} by user {}", jobId, userId);

            // Get the import job
            Optional<ImportJob> jobOpt = importJobRepository.findById(jobId);
            if (!jobOpt.isPresent()) {
                return event.createResponse(null, 404, "Import job not found");
            }

            ImportJob job = jobOpt.get();

            // Validate job is completed
            if (!ImportJobStatus.SUCCESS.equals(job.getStatus()) &&
                !ImportJobStatus.FAILED.equals(job.getStatus()) &&
                !ImportJobStatus.CANCELLED.equals(job.getStatus())) {
                return event.createResponse(null, 409, "Import job is not yet completed. Current status: " + job.getStatus());
            }

            // Build result DTO
            ImportResultDto result = buildImportResult(job);

            return event.createResponse(result, ResponseCode.OK, null);

        } catch (Exception e) {
            logger.error("Error retrieving import result", e);
            return event.createResponse(null, 500, "Error retrieving import result: " + e.getMessage());
        }
    }

    /**
     * Build import progress DTO from job entity
     */
    private ImportProgressDto buildImportProgress(ImportJob job) {
        ImportProgressDto progress = new ImportProgressDto(job.getId(), job.getFileName(), job.getStatus().name());

        progress.setTotalRows(job.getTotalRows());
        progress.setProcessedRows(job.getValidRows() + job.getErrorRows());
        progress.setSuccessfulRows(job.getValidRows());
        progress.setFailedRows(job.getErrorRows());
        progress.setSkippedRows(0); // TODO: Add skipped rows tracking

        progress.calculateProgress();

        // Set timing information
        progress.setStartedAt(job.getCreatedAt());
        progress.setLastUpdatedAt(job.getUpdatedAt());

        if (job.getCreatedAt() != null) {
            progress.updateProcessingMetrics(job.getCreatedAt());
        }

        // Set current phase based on status
        switch (job.getStatus()) {
            case PENDING:
                progress.setCurrentPhase("PENDING");
                break;
            case RUNNING:
                progress.setCurrentPhase("IMPORTING");
                break;
            case SUCCESS:
            case FAILED:
            case CANCELLED:
                progress.setCurrentPhase("COMPLETED");
                break;
        }

        // Check for cancellation
        if (ImportJobStatus.CANCELLED.equals(job.getStatus())) {
            progress.setCancellationRequested(true);
            progress.setCancellationReason("Import was cancelled");
        }

        return progress;
    }

    /**
     * Build import result DTO from job entity
     */
    private ImportResultDto buildImportResult(ImportJob job) {
        ImportResultDto result = new ImportResultDto(job.getId(), job.getFileName(), job.getStatus().name());

        result.setStartedAt(job.getCreatedAt());
        result.setFinishedAt(job.getUpdatedAt());
        result.calculateProcessingTime();

        // Set statistics
        result.setTotalRows(job.getTotalRows());
        result.setProcessedRows(job.getValidRows() + job.getErrorRows());
        result.setSuccessfulRows(job.getValidRows());
        result.setFailedRows(job.getErrorRows());
        result.setSkippedRows(0); // TODO: Add skipped rows tracking
        result.calculateSuccessRate();

        // Set file information
        result.setFileChecksum(job.getFileChecksum());
        result.setFileFormat(getFileFormat(job.getFileName()));

        // Set cancellation information
        if (ImportJobStatus.CANCELLED.equals(job.getStatus())) {
            result.setWasCancelled(true);
            result.setCancelledAt(job.getUpdatedAt());
            result.setCancellationReason("Import was cancelled by user");
        }

        // Create database operations summary (placeholder)
        ImportResultDto.DatabaseOperationsDto dbOps = new ImportResultDto.DatabaseOperationsDto();
        dbOps.setCustomersCreated(job.getValidRows()); // Simplified - in real implementation, track separately
        dbOps.setCustomersUpdated(0);
        dbOps.setCustomersSkipped(0);
        dbOps.setTransactionsCommitted(job.getValidRows());
        dbOps.setTransactionsRolledBack(job.getErrorRows());
        result.setDatabaseOperations(dbOps);

        // Set error summary (placeholder)
        result.setTotalErrors(job.getErrorRows());
        result.setTotalWarnings(0);

        return result;
    }

    /**
     * Get file format from filename
     */
    private String getFileFormat(String fileName) {
        if (fileName == null) return "UNKNOWN";

        String lowerName = fileName.toLowerCase();
        if (lowerName.endsWith(".csv")) return "CSV";
        if (lowerName.endsWith(".xlsx")) return "XLSX";
        if (lowerName.endsWith(".xls")) return "XLS";
        return "UNKNOWN";
    }

    /**
     * Process import execution asynchronously
     */
    private void processImportExecution(Long userId,ImportJob job) {
        try {
            logger.info("Starting asynchronous import execution for job {}", job.getId());

            // Update job status to RUNNING
            job.setStatus(ImportJobStatus.RUNNING);
            importJobRepository.save(job);

            // Get file path
            String filePath = ImportFileUtils.createSecureFilePath(
                uploadBaseDir, job.getId(), job.getFileChecksum(), job.getFileName());

            // Process import execution using the processor
            ImportResultDto result = ImportExecutionProcessor.processImportExecution(job, filePath,userId, projectRepository,unitRepository,customerPropertyRepository,customerRepository,customerRelativeRepository,employeeRepository,interactionsSecondaryRepository, customerOfferRepository);

            // Update job with final results
            job.setStatus(ImportJobStatus.valueOf(result.getStatus()));
            job.setValidRows(result.getSuccessfulRows());
            job.setErrorRows(result.getFailedRows());
            job.setTotalRows(result.getTotalRows());

            // Store result summary in job options
            String resultSummary = generateResultSummary(result);
            job.setOptions(job.getOptions() + "\n" + resultSummary);

            importJobRepository.save(job);

            logger.info("Completed import execution for job {}. Status: {}, Success: {}, Failed: {}",
                       job.getId(), result.getStatus(), result.getSuccessfulRows(), result.getFailedRows());

        } catch (Exception e) {
            logger.error("Error in import execution for job " + job.getId(), e);

            // Update job status to FAILED
            job.setStatus(ImportJobStatus.FAILED);
            job.setOptions(job.getOptions() + "\nExecution failed: " + e.getMessage());
            importJobRepository.save(job);
        }
    }

    /**
     * Generate result summary for storage
     */
    private String generateResultSummary(ImportResultDto result) {
        StringBuilder summary = new StringBuilder();
        summary.append("IMPORT_EXECUTION_RESULT:\n");
        summary.append("Status: ").append(result.getStatus()).append("\n");
        summary.append("Total Rows: ").append(result.getTotalRows()).append("\n");
        summary.append("Successful: ").append(result.getSuccessfulRows()).append("\n");
        summary.append("Failed: ").append(result.getFailedRows()).append("\n");
        summary.append("Success Rate: ").append(String.format("%.2f%%", result.getSuccessRate())).append("\n");
        summary.append("Processing Time: ").append(result.getTotalProcessingTime()).append("\n");

        if (result.getDatabaseOperations() != null) {
            ImportResultDto.DatabaseOperationsDto dbOps = result.getDatabaseOperations();
            summary.append("Customers Created: ").append(dbOps.getCustomersCreated()).append("\n");
            summary.append("Customers Updated: ").append(dbOps.getCustomersUpdated()).append("\n");
            summary.append("Transactions Committed: ").append(dbOps.getTransactionsCommitted()).append("\n");
            summary.append("Transactions Rolled Back: ").append(dbOps.getTransactionsRolledBack()).append("\n");
        }

        if (result.wasCancelled()) {
            summary.append("Cancellation Reason: ").append(result.getCancellationReason()).append("\n");
        }

        return summary.toString();
    }
}
