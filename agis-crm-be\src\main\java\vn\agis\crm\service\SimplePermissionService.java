package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.req.PermissionDto;
import vn.agis.crm.base.jpa.entity.Permissions;
import vn.agis.crm.repository.PermissionsRepository;

import java.util.Date;
import java.util.List;

@Service
@Transactional
public class SimplePermissionService {

    @Autowired
    private PermissionsRepository repository;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.GET_ALL:
                return getAll(event);
            case "GET_ALL_WITH_DESCRIPTION_FILTER":
                return getAllWithDescriptionFilter(event);
            case Constants.Method.CREATE:
                return create(event);
            case Constants.Method.UPDATE:
                return update(event);
            case Constants.Method.DELETE:
                return delete(event);
            case Constants.Method.CHECK_EXIST_PERMISSION_NAME:
                return checkExistName(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    private Event getAll(Event event) {
        List<Permissions> permissions = repository.findAll();
        return event.createResponse(permissions, 200, "Success");
    }

    /**
     * Get all permissions with optional description filtering
     * Payload should be a String (description filter) or null for all permissions
     */
    private Event getAllWithDescriptionFilter(Event event) {
        String descriptionFilter = (String) event.payload;
        List<Permissions> permissions;

        if (descriptionFilter == null || descriptionFilter.trim().isEmpty()) {
            // If no description filter provided, return all permissions
            permissions = repository.findAll();
        } else {
            // Apply description-based filtering with case-insensitive partial matching
            permissions = repository.findByDescriptionContainingIgnoreCase(descriptionFilter.trim());
        }

        return event.createResponse(permissions, 200, "Success");
    }

    private Event create(Event event) {
        PermissionDto dto = (PermissionDto) event.payload;
        if (dto.getName() == null || dto.getName().trim().isEmpty()) {
            return event.createResponse(null, 400, "Missing name");
        }
        if (repository.existsByName(dto.getName())) {
            return event.createResponse(null, 409, "Duplicate permission name");
        }
        Permissions permission = new Permissions();
        permission.setName(dto.getName());
        permission.setDescription(dto.getDescription());
        permission.setCreatedAt(new Date());
        permission.setCreatedBy(event.userId);
        Permissions saved = repository.save(permission);
        return event.createResponse(saved, 201, "Created");
    }

    private Event update(Event event) {
        PermissionDto dto = (PermissionDto) event.payload;
        if (dto.getId() == null) {
            return event.createResponse(null, 400, "Missing id");
        }
        return repository.findById(dto.getId()).map(existing -> {
            String targetName = dto.getName() != null ? dto.getName() : existing.getName();
            Permissions dup = repository.findFirstByName(targetName);
            if (dup != null && !dup.getId().equals(existing.getId())) {
                return event.createResponse(null, 409, "Duplicate permission name");
            }
            if (dto.getName() != null) existing.setName(dto.getName());
            if (dto.getDescription() != null) existing.setDescription(dto.getDescription());
            Permissions saved = repository.save(existing);
            return event.createResponse(saved, 200, "Success");
        }).orElse(event.createResponse(null, 404, "Permission not found"));
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        if (id == null) {
            return event.createResponse(null, 400, "Missing id");
        }
        repository.deleteById(id);
        return event.createResponse(null, 200, "Success");
    }

    private Event checkExistName(Event event) {
        String name = (String) event.payload;
        boolean exists = (name != null && !name.trim().isEmpty()) && repository.existsByName(name);
        return event.createResponse(exists, 200, "Success");
    }
}

