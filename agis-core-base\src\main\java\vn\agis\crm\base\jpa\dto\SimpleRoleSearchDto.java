package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SimpleRoleSearchDto {
    private String name;      // optional, LIKE
    private Boolean isActive; // optional
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "id,asc";

    public SimpleRoleSearchDto() {}

    public SimpleRoleSearchDto(String name, Boolean isActive, Integer page, Integer size, String sortBy) {
        this.name = name;
        this.isActive = isActive;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

