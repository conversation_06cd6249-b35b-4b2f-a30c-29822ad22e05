package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.errors.BaseException;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.NotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.ReceivingGroupNameResponse;
import vn.agis.crm.base.jpa.dto.resp.SearchReportRespone;
import vn.agis.crm.base.jpa.entity.EmailGroup;
import vn.agis.crm.base.jpa.entity.Report;
import vn.agis.crm.base.jpa.entity.ReportSending;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.util.RequestUtils;

import java.io.ByteArrayInputStream;
import java.util.*;

import static vn.agis.crm.base.constants.Constants.Method.GET_PERMISSIONKEY_BY_USER;

@Service
public class ReportService extends CrudService<Report, Long> {
    private Logger logger = LoggerFactory.getLogger(ReportService.class);
    private String emailGroupCategory = Constants.Category.EMAIL_GROUP;
    private String receivingGroupCategory = Constants.Category.RECEIVING_GROUP;
    private static final String objectKey = "Report";
    private static final String objectKeyEmailGroup = "RptRecvGrp";
    private final int timeoutForPreview = 120000;
    private final int timeoutForExport = 900000;
    @Autowired
    private MessageSource messageSource;

    public ReportService() {
        super(Report.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_REPORT;
        this.category = Constants.Category.REPORT;

    }

    public Page<SearchReportRespone> searchReport(ReportSearchDTO reqDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<SearchReportRespone> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_REPORT, category, reqDTO, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchReportRespone.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), SearchReportRespone.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(reqDTO), ObjectMapperUtil.toJsonString(response), null, null, event);
        }

    }

    public Report changeStatusReport(Report report) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        Report response = new Report();
        try {
            event = RequestUtils.amqp(Constants.Method.CHANGE_STATUS_REPORT, category, report, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Report) event.payload;
                return response;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(report.getId(), report.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(report), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public Report updateReportSchedule(Long id, Report report) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        Report response = new Report();
        try {
            report.setId(id);
            event = RequestUtils.amqp(Constants.Method.UPDATE_REPORT_SCHEDULE, category, report, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Report) event.payload;
                return response;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(id, report.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(report), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public ReportSending updateReportSending(Long id, ReportSending report) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        ReportSending response = new ReportSending();
        try {
            report.setReportConfigId(id);
            event = RequestUtils.amqp(Constants.Method.UPDATE_REPORT_SENDING, category, report, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (ReportSending) event.payload;
                return response;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(id, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(report), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }


    public EmailGroup getEmailGroup(Long id) {
        Long timeRequest = System.currentTimeMillis();
        EmailGroup response = new EmailGroup();
        BaseException exception;
        Event event = RequestUtils.amqp(JpaConstants.Method.GET_ONE, emailGroupCategory, id, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            response = (EmailGroup) event.payload;
//            TransactionLogger.writeLogITrans(id, null, objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    String.valueOf(id), ObjectMapperUtil.toJsonString(response), null, null, event);
            return response;
        } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            exception = new NotFoundException(event.respErrorDesc, emailGroupCategory, (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(id, response.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    String.valueOf(id), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        }
        return null;
    }

    public EmailGroup createEmailGroup(CreateEmailGroupReq request) {
        Long timeRequest = System.currentTimeMillis();
        EmailGroup response = new EmailGroup();
        BaseException exception;
        Event event = RequestUtils.amqp(JpaConstants.Method.CREATE_ONE, emailGroupCategory, request, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            response = (EmailGroup) event.payload;
//            TransactionLogger.writeLogITrans(null, request.getName(), objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(response), null, null, event);
            return response;
        } else if (event.respStatusCode == ResponseCode.CONFLICT) {
            exception = new DuplicateException(event.respErrorDesc, "EmailGroup", (String) event.payload, MessageKeyConstant.Validation.DUPLICATE_NAME);
//            TransactionLogger.writeLogITrans(null, request.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.CONFLICT).getBody()), null, null, event);
            throw exception;
        }
        return null;
    }

    public EmailGroup updateEmailGroup(CreateEmailGroupReq request, Long id) {
        Long timeRequest = System.currentTimeMillis();
        EmailGroup response = new EmailGroup();
        BaseException exception;
        EmailGroup emailGroup = new EmailGroup();
        BeanUtils.copyProperties(request, emailGroup);
        emailGroup.setId(id);
        Event event = RequestUtils.amqp(JpaConstants.Method.UPDATE_ONE, emailGroupCategory, emailGroup, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            response = (EmailGroup) event.payload;
//            TransactionLogger.writeLogITrans(id, request.getName(), objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(emailGroup), ObjectMapperUtil.toJsonString(response), null, null, event);
            return response;
        } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            exception = new NotFoundException(event.respErrorDesc, "EmailGroup", (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(id, request.getName(), objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        } else if (event.respStatusCode == ResponseCode.CONFLICT) {
            exception = new DuplicateException(event.respErrorDesc, "EmailGroup", (String) event.payload, MessageKeyConstant.Validation.DUPLICATE_NAME);
//            TransactionLogger.writeLogITrans(id, request.getName(), objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.CONFLICT).getBody()), null, null, event);
            throw exception;
        }
        return null;
    }

    public ResponseBase deleteEmailGroup(Long id) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;
        ResponseBase responseBase = new ResponseBase();
        EmailGroup emailGroup = null;
        Event event = RequestUtils.amqp(JpaConstants.Method.DELETE_ONE, emailGroupCategory, id, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            emailGroup = (EmailGroup) event.payload;
            responseBase.setErrorCode(event.respStatusCode);
            responseBase.setErrorMsg(event.respErrorDesc);
        } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            exception = new NotFoundException(event.respErrorDesc, "EmailGroup", (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(id, null, objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    String.valueOf(id), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        }
//        TransactionLogger.writeLogITrans(id, emailGroup.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                String.valueOf(id), ObjectMapperUtil.toJsonString(responseBase), null, null, event);
        return responseBase;
    }

    public ResponseBase deleteManyEmailGroup(List<Long> ids) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;
        ResponseBase responseBase = new ResponseBase();
        Event event = RequestUtils.amqp(JpaConstants.Method.DELETE_MANY, emailGroupCategory, ids, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            responseBase.setErrorCode(event.respStatusCode);
            responseBase.setErrorMsg(event.respErrorDesc);
        } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            exception = new NotFoundException(event.respErrorDesc, "EmailGroup", (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(null, null, objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(ids), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        }
//        TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                ObjectMapperUtil.toJsonString(ids), ObjectMapperUtil.toJsonString(responseBase), null, null, event);
        return responseBase;
    }

    public Page<ReceivingGroupNameResponse> searchEmailGroup(SearchReceivingGroupReq searchEmailGroupDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<ReceivingGroupNameResponse> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, emailGroupCategory, searchEmailGroupDTO, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), ReceivingGroupNameResponse.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), ReceivingGroupNameResponse.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, searchEmailGroupDTO.getName(), objectKeyEmailGroup, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(searchEmailGroupDTO), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public List<EmailGroup> listAllEmailGroup() {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<EmailGroup> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.GET_ALL, emailGroupCategory, null, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<EmailGroup>) event.payload;
                return response;
            } else return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public long countEmailName(String query) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        long response = 0;
        try {
            event = RequestUtils.amqp
                    (JpaConstants.Method.CHECK_EXITS, emailGroupCategory, query, routingKey);
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                response = (long) event.payload;
                return response;
            }
            return response;
        } finally {
        }
    }


    public Object previewReport(FilterParamValueRequest filterParamValueRequest) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;

        Event event = RequestUtils.amqpWithTimeout(Constants.Method.PREVIEW_REPORT, category, filterParamValueRequest, routingKey, timeoutForPreview);
        if (event.respStatusCode == ResponseCode.OK) {
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), String.valueOf(event.payload), null, null, event);
            return event.payload;
        } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_NO_ACCESS_PEREVIEW, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        } else if (event.respStatusCode == ResponseCode.UNKNOWN_ERROR) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_QUERY_ERROR, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }else if (event.respStatusCode == ResponseCode.PAYLOAD_TOO_LARGE) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_LIMIT_ROW_ERROR, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }

        else {
            exception = throwBadRequestException(MessageKeyConstant.BAD_REQUEST, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }
    }

    public ByteArrayInputStream exportReport(FilterParamValueRequest filterParamValueRequest) {
//        List<IdNameResponse> list = null;
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;

        Event event = RequestUtils.amqpWithTimeout(Constants.Method.EXPORT_REPORT, category, filterParamValueRequest, routingKey, timeoutForExport);
        if (event.respStatusCode == ResponseCode.OK) {
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), null, null, null, event);
            return new ByteArrayInputStream((byte[]) event.payload);
        } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_NO_ACCESS_PEREVIEW, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        } else if (event.respStatusCode == ResponseCode.UNKNOWN_ERROR) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_QUERY_ERROR, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }else if (event.respStatusCode == ResponseCode.PAYLOAD_TOO_LARGE) {
            exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_LIMIT_1M, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }
        else {
            exception = throwBadRequestException(MessageKeyConstant.BAD_REQUEST, category, new ArrayList<>(), null);
//            TransactionLogger.writeLogITrans(filterParamValueRequest.getReportId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(filterParamValueRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
            throw exception;
        }
    }

    public List<Report> getAllReportByPermission() {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<Report> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(GET_PERMISSIONKEY_BY_USER, category, "", routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<Report>) event.payload;
                return response;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }


    /**
     * throw BadRequestException
     */
    private BadRequestException throwBadRequestException(String message, String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(message, values, LocaleContextHolder.getLocale());
        return new BadRequestException(messageNotFound, entity, field, message);
    }

    @Override
    public ResponseBase deleteById(Long id){
        logger.info("Delete Entity Report with id #{}", id);
        Report response = null;
        Event event = new Event();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.DELETE,category,id,routingKey);
            timeResponse = System.currentTimeMillis();
            ResponseBase responseBase = new ResponseBase();
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                responseBase.setErrorCode(event.respStatusCode);
                responseBase.setErrorMsg(event.respErrorDesc);
                response = (Report) event.payload;
                return responseBase;
            } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND){
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        }finally {
            writeLogDetail(id, response ,timeRequest,timeResponse, String.valueOf(id),ObjectMapperUtil.toJsonString(response),event);
        }

    }
    @Override
    void writeLogDetail(Long id, Report entity, long timeRequest, long timeResponse, String request, String response, Event event) {
//        TransactionLogger.writeLogITrans(id, entity != null ? entity.getName() : null, objectKey, null, timeRequest, timeResponse,
//                request, response, null, null, event);
    }
}
