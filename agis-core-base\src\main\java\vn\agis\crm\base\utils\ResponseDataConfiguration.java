/**
 * <AUTHOR> VienNN
 * @version    : 1.0
 */
package vn.agis.crm.base.utils;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import vn.agis.crm.base.constants.ResponseStatusConst;
import vn.agis.crm.base.exception.ApiError;
import vn.agis.crm.base.exception.ResponseDataCommon;

import java.io.Serializable;

public class ResponseDataConfiguration implements Serializable {

    private static final long serialVersionUID = 1L;

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static <T> ResponseEntity<T> success(T body) {
        ResponseData<T> responseData = new ResponseData<T>(ResponseStatusConst.SUCCESS, body);
        return new ResponseEntity(responseData, HttpStatus.OK);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static <T> ResponseEntity<T> error(String status, ApiError error, HttpStatus httpStatus) {
        ResponseData<T> responseData = new ResponseData<T>(ResponseStatusConst.ERROR, error);
        return new ResponseEntity(responseData, httpStatus);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static <T> ResponseEntity<T> error(ResponseData data, HttpStatus status) {
        return new ResponseEntity(data, status);
    }

    @SuppressWarnings({"rawtypes", "unchecked"})
    public static <T> ResponseEntity<T> errorCommon(String status, ApiError error, HttpStatus httpStatus) {
        ResponseDataCommon<T> responseData = new ResponseDataCommon<>(ResponseStatusConst.ERROR, error);
        return new ResponseEntity(responseData, httpStatus);
    }
}
