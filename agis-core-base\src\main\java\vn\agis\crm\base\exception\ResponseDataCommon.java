package vn.agis.crm.base.exception;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

public class ResponseDataCommon<T> implements Serializable {

    @Getter
    @Setter
    private String status;

    @Getter
    @Setter
    private ApiError error;

    @Getter
    @Setter
    private T body;

    public ResponseDataCommon() {
    }

    public ResponseDataCommon(String status, ApiError error) {
        this.status = status;
        this.error = error;
        this.body = null;
    }

    public ResponseDataCommon(String status, ApiError error, T body) {
        this.status = status;
        this.error = error;
        this.body = body;
    }

    public ResponseDataCommon(String status, T body) {
        this.status = status;
        this.error = null;
        this.body = body;
    }

}
