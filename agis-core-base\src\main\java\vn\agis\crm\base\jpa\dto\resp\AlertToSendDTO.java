package vn.agis.crm.base.jpa.dto.resp;

import java.util.Date;

public interface AlertToSendDTO {
    Long getAlertId();
    Long getAlertListId();

    Long getMsisdn();
    Integer getEventType();
    Long getValue();

    String getEmailSubject();

    String getEmailContent();

    String getSmsContent();

    String getNotificationUrl();

    String getAlertEmails();

    String getAlertMsisdns();

    Integer getActionType();

    Long getSmsThreshold();

    Long getDataThreshold();

    String getReceivingGroupEmails();

    String getReceivingGroupMsisdns();
    String getAlertName();
    Integer getSeverity();
    Integer getStatus();
    Long getCustomerId();
    Long getImsi();
    String getDescription();
    Date getRaisedDate();
    Integer getFlag();
    Integer getSimStatus();
    String getRatingPlanName();
    String getDatapoolPkgCode();
}
