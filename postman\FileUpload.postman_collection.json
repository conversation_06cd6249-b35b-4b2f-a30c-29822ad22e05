{"info": {"name": "CRM - File Upload", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_postman_id": "a1b2c3d4-e5f6-7890-1234-567890abcdef"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8080"}, {"key": "fileName", "value": ""}], "item": [{"name": "Upload File", "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": []}]}, "url": {"raw": "{{baseUrl}}/api/files/upload", "host": ["{{baseUrl}}"], "path": ["api", "files", "upload"]}, "description": "Uploads a file and returns its details, including the download URI."}, "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () { pm.response.to.have.status(200); });", "pm.test(\"Response has file details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.be.an('array').that.is.not.empty;", "    var firstFile = jsonData[0];", "    pm.expect(firstFile.fileName).to.not.be.empty;", "    pm.expect(firstFile.fileDownloadUri).to.not.be.empty;", "    pm.collectionVariables.set(\"fileName\", firstFile.fileName);", "});"], "type": "text/javascript"}}]}, {"name": "Download File", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/files/{{fileName}}", "host": ["{{baseUrl}}"], "path": ["api", "files", "{{fileName}}"]}, "description": "Downloads the file specified by the 'fileName' collection variable. The variable is set automatically from the Upload File request."}, "response": []}]}