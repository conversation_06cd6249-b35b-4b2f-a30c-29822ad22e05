package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Entity
@Table(name = "customer_offers")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerOffers extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "first_interaction")
    private Date firstInteraction;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "last_interaction")
    private Date lastInteraction;

    @Column(name = "status")
    private String status; // OPEN|CLOSED|CANCELLED

    @Lob
    @Column(name = "notes")
    private String notes;
}

