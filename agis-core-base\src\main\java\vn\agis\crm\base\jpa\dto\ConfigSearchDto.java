package vn.agis.crm.base.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigSearchDto {
    private String configKey;
    private Integer configType; // -1 = any
    private String description; // NEW: description search field
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "createdAt,desc";

    // Backward compatibility constructor
    public ConfigSearchDto(String configKey, Integer configType, Integer page, Integer size, String sortBy) {
        this.configKey = configKey;
        this.configType = configType;
        this.description = null;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

