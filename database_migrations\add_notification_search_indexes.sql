-- Migration Script: Add Search Performance Indexes for notifications table
-- Description: Add indexes for better search performance on notification search functionality
-- Date: 2024-01-18
-- Author: AGIS CRM Enhancement

-- Add index for target_employee_id (most common search filter)
CREATE INDEX idx_notifications_target_employee_id ON notifications (target_employee_id);

-- Add index for target_customer_id
CREATE INDEX idx_notifications_target_customer_id ON notifications (target_customer_id);

-- Add index for notification type
CREATE INDEX idx_notifications_type ON notifications (type);

-- Add index for read status
CREATE INDEX idx_notifications_is_read ON notifications (is_read);

-- Add index for read_at timestamp (for date range searches)
CREATE INDEX idx_notifications_read_at ON notifications (read_at);

-- Add index for created_at (for sorting and date filtering)
CREATE INDEX idx_notifications_created_at ON notifications (created_at);

-- Add composite index for common search combinations
CREATE INDEX idx_notifications_employee_read_status ON notifications (target_employee_id, is_read, created_at);

-- Add composite index for employee and type filtering
CREATE INDEX idx_notifications_employee_type ON notifications (target_employee_id, type, created_at);

-- Add full-text index for title and content search (MySQL specific)
-- Note: This requires MyISAM engine or MySQL 5.6+ with InnoDB full-text support
-- ALTER TABLE notifications ADD FULLTEXT(title, content);

-- Verification queries
-- Check if indexes were created successfully
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    TABLE_NAME,
    NON_UNIQUE,
    SEQ_IN_INDEX
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'notifications' 
AND INDEX_NAME LIKE 'idx_notifications_%'
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Performance test queries
-- Test search by employee ID
EXPLAIN SELECT * FROM notifications 
WHERE target_employee_id = 1 
ORDER BY created_at DESC 
LIMIT 10;

-- Test search by employee ID and read status
EXPLAIN SELECT * FROM notifications 
WHERE target_employee_id = 1 
AND is_read = false 
ORDER BY created_at DESC 
LIMIT 10;

-- Test search by type and date range
EXPLAIN SELECT * FROM notifications 
WHERE type = 1 
AND created_at >= '2024-01-01' 
AND created_at <= '2024-12-31'
ORDER BY created_at DESC;

-- Test complex search with multiple filters
EXPLAIN SELECT * FROM notifications 
WHERE target_employee_id = 1 
AND type = 2 
AND is_read = false 
AND title LIKE '%birthday%'
ORDER BY created_at DESC 
LIMIT 20;
