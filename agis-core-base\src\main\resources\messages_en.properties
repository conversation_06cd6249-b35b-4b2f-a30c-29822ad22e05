error.length=less than {0} and great than {1}
error.object.not.found={0} not found
error.field.must.be.not.null={0} must be not null
error.duplicate.name=duplicate name
error.invalid.field=field {0} invalid
error.forbbiden.resource=forbbiden resource
error.data.format = Invalid data format
error.user.not.found={0} user not found.

# Project Deletion Messages
error.project.not.found=Không tìm thấy dự án
error.project.has.dependencies=Không thể xóa dự án do có phụ thuộc
error.project.has.units=Không thể xóa dự án do có căn hộ
error.project.has.customer.offers=Không thể xóa dự án do có chào bán khách hàng
error.project.has.customer.properties=Không thể xóa dự án do có bất động sản khách hàng
success.project.deleted=Xóa dự án thành công
error.project.dependency.validation.failed=Kiểm tra phụ thuộc dự án thất bại

# Unit deletion messages
success.unit.deleted=Căn hộ có thể xóa an toàn.
error.unit.not.found=Không tìm thấy căn hộ
error.unit.has.secondary.interactions=Căn hộ đang được sử dụng, không thể xóa.
error.unit.deletion.system.error=Lỗi hệ thống khi xóa căn hộ

# Customer deletion messages
success.customer.deleted=Khách hàng có thể xóa an toàn.
error.customer.not.found=Không tìm thấy khách hàng
error.customer.has.dependencies=Không thể xóa khách hàng do có phụ thuộc
error.customer.has.assignments=Không thể xóa khách hàng vì còn tồn tại bản ghi phân công nhân viên
error.customer.has.offers=Không thể xóa khách hàng vì còn tồn tại chào bán khách hàng
error.customer.has.properties=Không thể xóa khách hàng vì còn tồn tại giao dịch bất động sản
error.customer.has.notifications=Không thể xóa khách hàng vì còn tồn tại thông báo
error.customer.has.rule.runs=Không thể xóa khách hàng vì còn tồn tại lịch sử chạy quy tắc
error.customer.dependency.validation.failed=Kiểm tra phụ thuộc khách hàng thất bại
error.customer.deletion.system.error=Lỗi hệ thống khi xóa khách hàng



#Validate annotation
error.valid.assert.false=Must be false.
error.valid.assert.true=Must be true.
error.valid.decimal.max=Must be less than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.decimal.min=Must be greater than ${inclusive == true ? 'or equal to ':''}{value}.
error.valid.digits=Numeric value out of bounds (<{integer} digits>.<{fraction} digits> expected).
error.valid.email=Must be a well-formed email address.
error.valid.max=Must be less than or equal to {1}.
error.valid.min=Must be greater than or equal to {1}.
error.valid.negative=Must be less than 0.
error.valid.negative.or.zero=Must be less than or equal to 0.
error.valid.not.blank=Must not be blank.
error.valid.not.empty=Must not be empty.
error.valid.null=Must be null.
error.valid.not.null=Field cannot NULL.
error.valid.range=Must be between {2} and {1}.
error.valid.past=Must be a past date.
error.valid.past.or.present=Must be a date in the past or in the present.
error.valid.pattern=Must match "{1}".
error.valid.phone.pattern=Invalid phone number
error.valid.positive=Must be greater than 0.
error.valid.positive.or.zero=Must be greater than or equal to 0.
error.valid.size=Size must be between {2} and {1}.

error.valid.length=Size must be between {2} and {1}.


#change password user
error.invalid.email=invalid email
invalid.token=invalid token
invalid.phone.number=invalid phone number
error.invalid.password=invalid password
id.must.be.null=id must be null
error.passwords.do.not.match=passwords do not match
invalid.type=invalid type
the.new.password.must.be.different.from.the.old.one=the new password must be different from the old one

error.emailIncorect=Email incorrect
error.passwordIncorect=Password incorrect
error.accountInactive=Account inactive



#\check exist
exists={0} exists

bad.request=Bad request


#register_rate
register.rate.sim.notin.permission=Subscribers are not under management.
register.rate.fail=Register rate Fail

#user
error.cant.not.update.province.customer=Cant not update province customer account

error.report.query=query error
error.report.limit.row=report limit row error
error.report.excel.limit=Export file data exceeds 1 million lines
error.export.excel.limit= export limit row error

