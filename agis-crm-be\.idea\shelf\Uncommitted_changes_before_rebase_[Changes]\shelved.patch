Index: config/application-dev.yml
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>app:\r\n  config:\r\n    max-execute-size: 5\r\n    module-prefix: coremngt\r\n  upload:\r\n    dir: ./uploads\r\nserver:\r\n  port: 8089\r\n  servlet:\r\n    context-path: /crm\r\nspring:\r\n  rabbitmq:\r\n    addresses: localhost\r\n    port: 5672\r\n    username: admin\r\n    password: admin123\r\n  datasource:\r\n    jdbc-url: ****************************************    url: ****************************************    username: root\r\n    password: haiha\r\n\r\n#    jdbc-url: *************************************************\r\n#    url: *************************************************\r\n#    username: agis_crm\r\n#    password: agis@123321\r\n\r\n    driver-class-name: com.mysql.jdbc.Driver\r\n    hikari:\r\n      data-source-properties:\r\n        stringtype: unspecified\r\n  jpa:\r\n    database-platform: org.hibernate.dialect.MariaDBDialect\r\n    use-new-id-generator-mappings: false\r\n    show-sql: true\r\n    hibernate:\r\n      # Drop n create table, good for testing, comment this in production\r\n      ddl-auto: none\r\n  redis:\r\n    mode: Standalone  # Change mode to Standalone\r\n    standalone:\r\n      host: localhost   # \uD83D\uDD39 service name trong docker-compose (or the actual host if not running in Docker)\r\n      port: 6379\r\n      username:  # Add username if your Redis instance requires authentication\r\n      password:  # Add password if your Redis instance requires authentication\r\n    cluster:\r\n      nodes:  # Remove or comment out the cluster configuration\r\n      username:\r\n      password:\r\n    sentinel:\r\n      password: # Remove or comment out the sentinel configuration\r\n      master: mymaster\r\n      nodes: localhost:26379\r\n    jedis:\r\n      pool:\r\n        max-active: 1000\r\n        min-idle: 0\r\n        max-wait: -1\r\n        max-idle: 10\r\n  mail:\r\n    host: mail.vnpt-technology.vn\r\n    port: 25\r\n    username: oneiot\r\n    password: One@2024%^&\r\n    protocol: smtp\r\n    #    properties:\r\n    #      mail.smtp.auth: false\r\n    #      mail.smtp.ssl.enable: false\r\n    #      mail.smtp.starttls.enable: false\r\n  messages:\r\n    basename: i18n/messages\r\n    encoding: UTF-8\r\n    cache-duration: -1\r\njhipster:\r\n  mail: # specific JHipster mail property, for standard properties see MailProperties\r\n    from: <EMAIL>\r\n    base-url: http://**********:9090\r\nccbs:\r\n  url-api:\r\napplication:\r\n  activation:\r\n    expirePeriodActiveMail: 2592000 #seconds - 30days\r\n    expirePeriodResetPassword: 86400 #seconds - 1day\r\n    enableMail: true #true/false\r\n  timeAfterSoftDelete: 2592000\r\n  tokenTime:\r\n    remember: 2592000 #seconds - 30days\r\n    noRemember: 1800 # seconds - 0.5h\r\nbaseUrl: http://localhost:9090\r\n#nodeName: ism-core\r\nfolder:\r\n  storage: ./storage\r\nminio:\r\n  url: http://127.0.0.1:9000\r\n  access-key: minioadmin\r\n  secret-key: minioadmin\r\n  folder-name: /\r\nmonitoringEmailList: <EMAIL>,<EMAIL> #dsach mail nhận ngăn cách bởi dấu phẩy\r\nschedule:\r\n  auto-share: 0 0 8 * * *\r\n#  auto-share: * * * * * *\r\noauth2Url: http://************:8080/oauth2/token # url api oauth2 login của iot\r\noauth2TenantUrl: http://************:8080/api/tenants/cmp # url api create tenant cho cmp của iot\r\noauth2UserUrl: http://************:8080/api/users/oauth2 # url api create/put user xác thực bằng oauth2 của iot\r\nnumberShare: 50\r\ntimeoutShare: 300000 #5 phut\r\nopenai:\r\n  api:\r\n    key: ***********************************************************************************************************************************************************************
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/config/application-dev.yml b/config/application-dev.yml
--- a/config/application-dev.yml	(revision b7c8bd11d91474484993c5face02d3923315b5d8)
+++ b/config/application-dev.yml	(date 1758820425641)
@@ -15,15 +15,15 @@
     username: admin
     password: admin123
   datasource:
-    jdbc-url: ************************************
-    url: ************************************
-    username: root
-    password: haiha
+#    jdbc-url: ************************************
+#    url: ************************************
+#    username: root
+#    password: haiha
 
-#    jdbc-url: *************************************************
-#    url: *************************************************
-#    username: agis_crm
-#    password: agis@123321
+    jdbc-url: *************************************************
+    url: *************************************************
+    username: agis_crm
+    password: agis@123321
 
     driver-class-name: com.mysql.jdbc.Driver
     hikari:
