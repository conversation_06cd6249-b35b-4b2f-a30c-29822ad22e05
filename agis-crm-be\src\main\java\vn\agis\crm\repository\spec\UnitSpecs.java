package vn.agis.crm.repository.spec;

import org.springframework.data.jpa.domain.Specification;
import vn.agis.crm.base.jpa.entity.Units;

public class UnitSpecs {

    public static Specification<Units> hasProjectId(Long projectId) {
        return (root, query, cb) -> projectId == null ? cb.conjunction() : cb.equal(root.get("projectId"), projectId);
    }

    public static Specification<Units> codeContains(String code) {
        return (root, query, cb) -> (code == null || code.trim().isEmpty()) 
            ? cb.conjunction() 
            : cb.like(cb.lower(root.get("code")), "%" + code.toLowerCase() + "%");
    }

    public static Specification<Units> productTypeContains(String productType) {
        return (root, query, cb) -> (productType == null || productType.trim().isEmpty()) 
            ? cb.conjunction() 
            : cb.like(cb.lower(root.get("productType")), "%" + productType.toLowerCase() + "%");
    }
}

