package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class SearchCommandRequest {
    private Long deviceId;
    private String commandId;
    private String commandType;
    private String commandName;
    private String data;
    private Integer status;
    private Long createdAtStart;
    private Long createdAtEnd;
    private Integer page;
    private Integer size;
    private String sortBy;

    public SearchCommandRequest(Long deviceId, String commandId, String commandType, String commandName, String data, Long createdAtStart, Long createdAtEnd, Integer status, Integer page, Integer size, String sortBy) {
        this.deviceId = deviceId;
        this.commandId = commandId;
        this.commandType = commandType;
        this.commandName = commandName;
        this.data = data;
        this.status = status;
        this.createdAtStart = createdAtStart;
        this.createdAtEnd = createdAtEnd;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
