package vn.agis.crm.core.filters;

import com.google.gson.Gson;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.AbstractAuthenticationToken;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.WebAuthenticationDetails;
import org.springframework.web.filter.GenericFilterBean;
import org.springframework.web.util.ContentCachingRequestWrapper;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.CRMService;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.core.filters.UserPrincipal;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.User;
import vn.agis.crm.base.redis.RedisCache;
import vn.agis.crm.util.RequestUtils;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;


/**
 * Created by huyvv
 * Date: 03/02/2020
 * Time: 9:22 PM
 * for all issues, contact me: <EMAIL>
 **/
public class JwtFilter extends GenericFilterBean {
    @SuppressWarnings("unused")
    private static Logger logger = LoggerFactory.getLogger(JwtFilter.class);
    private RedisCache redisCache = null;

    public RedisCache getRedisCache() {
        if (redisCache == null) redisCache = SpringContext.getBean(RedisCache.class);
        return redisCache;
    }

    @SneakyThrows
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
//        if (request.getRequestURI().startsWith("/api/msimapi")) {
//            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }

        String authorization = request.getHeader(CRMService.AUTH_HEADER_STRING);
        if (authorization != null && authorization.startsWith(CRMService.AUTH_TOKEN_PREFIX)) {
            String token = authorization.substring(CRMService.AUTH_TOKEN_PREFIX.length());

            if (!request.getRequestURI().contains("/api/auth/token") && !request.getRequestURI().contains("reset-password") &&
            !request.getRequestURI().contains("/api/client-authentication/oauth2/token")
                    && !request.getRequestURI().contains("/forgot-password")
                    && !request.getRequestURI().contains("/validate-token-mail")
                    && !request.getMethod().equalsIgnoreCase("OPTIONS")
                    && !token.equalsIgnoreCase("undefined") && StringUtils.isNotEmpty(token)
            ) {
                //Kiểm tra token đã expire chưa
                Event event1 = RequestUtils.amqp(Constants.Method.VALIDATE_PERMISSION, Constants.Category.EMPLOYEE, token, AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT);
                if (event1.respStatusCode.equals(ResponseCode.OK)) {

                } else {
                    getRedisCache().remove(token, User.class);
                    ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
                    return;
                }
                // nếu token chưa expire thì lấy thông tin trong redis cache ra xử lý
                Employee employee = (Employee) getRedisCache().get(token, Employee.class);
                if (employee == null) {
                    Event event = RequestUtils.amqp(Constants.Method.VALIDATE_PERMISSION, Constants.Category.EMPLOYEE, token, AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT);
                    logger.info("validate result: " + new Gson().toJson(event));
                    if (event.respStatusCode.equals(ResponseCode.OK)) {
                        //build principal for authorization
                        employee = (Employee) event.payload;
//                        user.setTokenType(1);
                        getRedisCache().put(token, employee, Employee.class);
                    } else {
                        //throw new BadRequestAlertException("Invalid JWT signature.", "auth", "Invalid JWT signature.");
                        // custom error response class used across my project
                        ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
                        return;
                    }
                }
//                if (user.getTokenType() != 1){
//                    ((HttpServletResponse) servletResponse).setStatus(HttpStatus.UNAUTHORIZED.value());
//                    return;
//                }
                employee.setPassword("********");
                initSecurityInfo(employee, token, request);
            }
        }

        ContentCachingRequestWrapper servletWrappedRequest = new ContentCachingRequestWrapper(request);
        filterChain.doFilter(servletWrappedRequest, servletResponse);
    }

    private void initSecurityInfo(Employee employee, String token, HttpServletRequest request) {
        Set<GrantedAuthority> authorities = new HashSet<>();
//        TODO:
        for (String scope : employee.getAuthorities()) {
            authorities.add(new SimpleGrantedAuthority(scope));
        }
        String username = employee.getEmployeeCode() != null ? employee.getEmployeeCode() : employee.getEmail();
        String email = employee.getEmail() != null ? employee.getEmail() : "";
//        String fullName = user.getFullName() != null ? user.getFullName() : "";
        UserPrincipal principal = new UserPrincipal(username, employee.getPassword(), authorities, employee.getId(), employee.getRoleId(), email);
        AbstractAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(principal, token, authorities);
        authentication.setDetails(new WebAuthenticationDetails(request));
        SecurityContextHolder.getContext().setAuthentication(authentication);
    }
}
