package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import vn.agis.crm.base.jpa.dto.req.CheckMsisdnDevice;
import vn.agis.crm.base.jpa.dto.req.DeviceSearchDTO;
import vn.agis.crm.base.jpa.dto.req.SearchDeviceTelemetryReq;
import vn.agis.crm.base.jpa.dto.resp.IdUsageResponse;
import vn.agis.crm.base.jpa.dto.resp.SearchDeviceRespone;
import vn.agis.crm.base.jpa.entity.Device;
import vn.agis.crm.base.jpa.entity.DeviceTelemetryRecord;
import vn.agis.crm.base.jpa.entity.FileEntity;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.DeviceService;

import java.io.IOException;
import java.util.List;
import java.util.Locale;


@RestController
@RequestMapping("/device")
public class DeviceController extends CrudController<Device,Long>{
    private final Logger logger = LoggerFactory.getLogger(DeviceController.class);
    @Autowired
    DeviceService deviceService;


    @Autowired
    public DeviceController(DeviceService service) {
        super(service);
        this.deviceService = service;
        this.baseUrl = "/device";
    }
    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('searchDevice')")
    public ResponseEntity<Page<SearchDeviceRespone>> searchDevice(
            @RequestParam(name = "deviceName", required = false, defaultValue = " ") String deviceName,
            @RequestParam(name = "serialNumber", required = false, defaultValue = " ") String serialNumber,
            @RequestParam(name = "msisdn", required = false, defaultValue = " ") String msisdn,
            @RequestParam(name = "imei", required = false, defaultValue = " ") String imei,
            @RequestParam(name = "deviceType", required = false, defaultValue = " ") String deviceType,
            @RequestParam(name = "model", required = false, defaultValue = " ") String model,
            @RequestParam(name = "enterpriseUserId", required = false, defaultValue = "-1") Long enterpriseUserId,
            @RequestParam(name = "customerUserId", required = false, defaultValue = "-1") Long customerUserId,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "connectionStatus", required = false, defaultValue = "-1") Integer connectionStatus,
            @RequestParam(name = "sort", required = false, defaultValue = "msisdn,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        DeviceSearchDTO deviceSearchDTO = new DeviceSearchDTO(deviceName, serialNumber, imei,msisdn,deviceType, model, enterpriseUserId, customerUserId, connectionStatus, page, size, sortBy);
        Page<SearchDeviceRespone> deviceRespones = deviceService.searchDevice(deviceSearchDTO, listRequest.getPageable());
        logger.info("Search Device End");
        return ResponseEntity.ok().body(deviceRespones);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('getDevice')")
    public ResponseEntity<Device> getDetail(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        Device device = deviceService.get(id);
        return ResponseEntity.ok().body(device);
    }

    @PostMapping("")
    @PreAuthorize("hasAnyAuthority('createDevice')")
    public ResponseEntity<Device> createDevice(@RequestBody Device entity, HttpServletRequest httpServletRequest) {
        Device device = deviceService.create(entity);
        return ResponseEntity.ok().body(device);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('updateDevice')")
    public ResponseEntity<Device> updateDevice(@PathVariable Long id ,@RequestBody Device entity, HttpServletRequest httpServletRequest) {
        entity.setId(id);
        Device device = deviceService.update(id,entity);
        return ResponseEntity.ok().body(device);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('deleteDevice')")
    public ResponseEntity<ResponseBase> deleteDevice(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = deviceService.deleteById(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }



    @GetMapping("/checkExit/{msisdn}")
    public ResponseEntity<CheckMsisdnDevice> checkExitMsisdn(@PathVariable Long msisdn, HttpServletRequest httpServletRequest) {
        CheckMsisdnDevice checkExitMsisdn = deviceService.checkExitMsisdn(msisdn);
        return ResponseEntity.ok().body(checkExitMsisdn);
    }
    @GetMapping("/checkImei/{imei}")
    public ResponseEntity<Long> checkExitImei(@PathVariable String imei, HttpServletRequest httpServletRequest) {
        Long countExits = deviceService.checkExitImei(imei);
        return ResponseEntity.ok().body(countExits);
    }


    @GetMapping("/simIsvalid")
    public ResponseEntity<List<Long>> getListMsisdnIsvalid(@RequestParam(name = "msi",required = false,defaultValue = "-1") Long msi){
        List<Long> list = deviceService.getListMsisdnIsvalid(msi);
        return ResponseEntity.ok().body(list);
    }

    @GetMapping("/getTelemetry")
    public ResponseEntity<Page<DeviceTelemetryRecord>> getTelemetry(
            @RequestParam(name = "deviceId", required = false, defaultValue = "-1") Long deviceId,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "measuredAt,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        SearchDeviceTelemetryReq searchDeviceTelemetryReq = new SearchDeviceTelemetryReq(deviceId, page, size, sortBy);
        Page<DeviceTelemetryRecord> telemetryRecords = deviceService.searchTelemetry(searchDeviceTelemetryReq, listRequest.getPageable());
        logger.info("Search Device End");
        return ResponseEntity.ok().body(telemetryRecords);
    }

    @PostMapping("/getUsageByDeviceId")
    public ResponseEntity <List<IdUsageResponse>> getUsageByDeviceId(@RequestBody List<Long> deviceIds) {
        List<IdUsageResponse> idUsageResponses = deviceService.getUsageByDeviceId(deviceIds);
        return ResponseEntity.ok().body(idUsageResponses);
    }

    @PostMapping("/getUsageByUserId")
    public ResponseEntity <List<IdUsageResponse>> getUsageByUserId(@RequestBody List<Long> deviceIds) {
        List<IdUsageResponse> idUsageResponses = deviceService.getUsageByUserId(deviceIds);
        return ResponseEntity.ok().body(idUsageResponses);
    }
    @PostMapping("/upload/image-device")
    public ResponseEntity<?> uploadImageDevice(@RequestPart("file") MultipartFile file)
            throws IOException {
        if (file.isEmpty()){
            return ResponseEntity.badRequest().body(null);
        }
        String fileName = file.getOriginalFilename().toLowerCase(Locale.ROOT);
        if (fileName.endsWith(".jpg") || fileName.endsWith(".png") || fileName.endsWith(".jpeg")){
            FileEntity fileEntity = deviceService.uploadImageDevice(file);
            if (fileEntity != null){
                return ResponseEntity.ok().body(fileEntity);
            }
        }
        return ResponseEntity.badRequest().body(null);
    }

    @GetMapping("/image-device/{id}")
    public ResponseEntity<?> getImageDevice(@PathVariable Long id)
            throws IOException {
        FileEntity fileEntity = deviceService.getFile(id);

        return ResponseEntity.ok()
//                .contentType(MediaType.parseMediaType("application/octet-stream"))
                .body(fileEntity.getData());
    }

}
