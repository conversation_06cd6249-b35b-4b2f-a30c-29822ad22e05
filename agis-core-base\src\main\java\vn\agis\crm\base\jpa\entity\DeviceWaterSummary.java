package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "device_water_summary")
@Data
public class DeviceWaterSummary {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    private Long id;

    @Column(name = "device_id")
    private Long deviceId;

    @Column(name = "start_billing_date")
    private Date startBillingDate;

    @Column(name = "last_billing_date")
    private Date lastBillingDate;

    @Column(name = "for_month")
    private Integer forMonth;

    @Column(name = "current_volume")
    private Float currentVolume;

    @Column(name = "estimated_cost")
    private Integer estimatedCost;

    @Column(name = "last_updated_at")
    private Date lastUpdatedAt;

    @Column(name = "start_volume")
    private Float startVolume;
}

