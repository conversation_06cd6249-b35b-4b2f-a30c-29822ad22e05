package vn.agis.crm.repository;

import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.dto.AreasSearch;
import vn.agis.crm.base.jpa.entity.Areas;

@Repository
public interface AreasRepository extends JpaRepository<Areas, Long> {

    @Query("SELECT COUNT(a.name) FROM Areas a WHERE a.name = :name")
    long countByName(String name);

    @Query("SELECT a FROM Areas a WHERE a.status = :status")
    List<Areas> findByStatus(Integer status);

    @Query("SELECT a FROM Areas a WHERE a.createdBy = :createdBy")
    List<Areas> findByCreatedBy(Long createdBy);

    @Query("SELECT a FROM Areas a WHERE a.createdBy IN :userIds")
    List<Areas> findByCreatedByIn(List<Long> userIds);

    @Query(value = """
            SELECT
                a.ID as id,
                a.NAME as name,
                a.DESCRIPTION as description,
                a.TYPE as type,
                a.PARENT_ID as parentId,
                a.STATUS as status,
                a.CREATED_DATE as createdDate,
                a.CREATED_BY as createdBy,
                a.UPDATED_AT as updatedAt,
                a.UPDATED_BY as updatedBy,
                CASE
                    WHEN EXISTS (
                        SELECT 1
                        FROM users u
                        WHERE u.PROVINCE_CODE_OFFICE = a.ID
                           OR u.WARD_CODE_OFFICE = a.ID
                           OR u.PROVINCE_CODE_ADDRESS = a.ID
                           OR u.WARD_CODE_ADDRESS = a.ID
                           OR u.APARTMENT = a.ID
                    )
                    THEN 1
                    ELSE 0
                END as isAssigned
            FROM areas a
        WHERE (:name IS NULL OR UPPER(a.name) LIKE CONCAT('%', UPPER(:name), '%'))
        AND (:status = -1 OR a.status = :status)
        AND (:parentId = -1 OR a.PARENT_ID = :parentId)
        AND (a.type IN (:types))
        """, nativeQuery = true)
    Page<AreasSearch> searchAreas(@Param("name") String name,
        @Param("parentId") Long parentId,
        @Param("status") Integer status,
        @Param("types") List<Integer> types,
        Pageable pageable);

    @Query("SELECT a FROM Areas a WHERE UPPER(a.name) LIKE CONCAT('%', UPPER(:keyword), '%') OR UPPER(a.description) LIKE CONCAT('%', UPPER(:keyword), '%')")
    List<Areas> findByKeyword(@Param("keyword") String keyword);

    Areas findFirstByName(String name);

    @Query(value = """
        WITH RECURSIVE area_hierarchy AS (
            SELECT * 
            FROM areas a
            WHERE a.ID = :areaId

            UNION ALL

            SELECT child.* 
            FROM areas child
            INNER JOIN area_hierarchy parent ON child.PARENT_ID = parent.ID
        )
        SELECT * 
        FROM area_hierarchy
        WHERE ID <> :areaId
        """, nativeQuery = true)
    List<Areas> getAreaHierarchy(@Param("areaId") Long areaId);
}
