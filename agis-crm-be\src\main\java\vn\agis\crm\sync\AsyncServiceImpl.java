package vn.agis.crm.sync;


import java.util.Map;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class AsyncServiceImpl implements AsyncService<AsyncObjectType> {
    private final Logger logger = LogManager.getLogger(AsyncServiceImpl.class);

    @Override
    @Async("async-services")
    public void executeAsync(AsyncObjectType cot) {

        logger.debug("start executeAsync");
        try {
            cot.update();
        } catch (Exception e) {
            logger.error("executeAsync Exception: ", e);
        }
        logger.debug("end executeAsync");
    }

    @Override
    @Async("async-services")
    public void executeAsync(AsyncObjectType obj, String... args) {
        try {
            obj.update(args);
        } catch (Exception e) {
            logger.error("executeAsync Exception: ", e);
        }
    }

    @Override
    @Async("async-services")
    public void executeAsync(AsyncObjectType obj, Object... args) {
        try {
            obj.update(args);
        } catch (Exception e) {
            logger.error("executeAsync Exception: ", e);
        }
    }

    @Override
    @Async("async-services")
    public String executeAsync(AsyncObjectType obj, Map<String, String> map) {
        try {
            return obj.update(map);
        } catch (Exception e) {
            logger.error("executeAsync Exception: ", e);
        }
        return "Exception";
    }

}
