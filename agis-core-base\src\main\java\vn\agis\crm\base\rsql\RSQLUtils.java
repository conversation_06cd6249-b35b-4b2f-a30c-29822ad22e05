package vn.agis.crm.base.rsql;

import org.apache.commons.beanutils.BeanUtils;
import vn.agis.crm.base.jpa.dto.SearchInfo;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * Created by huyvv
 * Date: 12/05/2021
 * Time: 2:01 PM
 * for all issues, contact me: <EMAIL>
 **/
@SuppressWarnings("ALL")
public class RSQLUtils {

    private RSQLUtils() {

    }

    private static final String LIST_SYMBOL = "_List";
    private static final String FROM_SYMBOL = "_From";
    private static final String TO_SYMBOL = "_To";
    private static final String PAGE_SIZE = "pageSize";
    private static final String PAGE_NUMBER = "pageNumber"; //start from zero
    private static final String SORT = "sort";

    private static List<Field> getAllFields(List<Field> fields, Class<?> type) {
        fields.addAll(Arrays.asList(type.getDeclaredFields()));

        if (type.getSuperclass() != null) {
            getAllFields(fields, type.getSuperclass());
        }

        return fields;
    }

    /**
     * classType: entity mapping object in DB
     * searchForm: input filter from website
     * output: SearchInfo contains (query RSQL, sort, paging)
     */
    public static <S> SearchInfo buildRSQLFromSearchForm(S searchForm, Class<?> classType) {
        SearchInfo searchInfo = new SearchInfo();
        Set<String> conditions = new LinkedHashSet<>();
        int pageSize = 20;
        int pageNumber = 0;

        try {
            List<Field> fieldList = new LinkedList<>();
            getAllFields(fieldList, classType);
            Map<String, Field> mapFields = new HashMap<>();
            for (Field field : fieldList) mapFields.put(field.getName(), field);

            Map<String, String> properties = BeanUtils.describe(searchForm);

            //processing Paging
            searchInfo.setPageSize(!properties.containsKey(PAGE_SIZE) ? pageSize : Integer.parseInt(properties.get(PAGE_SIZE)));
            searchInfo.setPageNumber(!properties.containsKey(PAGE_NUMBER) ? pageNumber : Integer.parseInt(properties.get(PAGE_NUMBER)));
            properties.remove(PAGE_SIZE);
            properties.remove(PAGE_NUMBER);

            //processing Order
            if (properties.containsKey(SORT)) {
                searchInfo.setOrders(properties.get(SORT));
            }
            properties.remove(SORT);

            for (String key : mapFields.keySet()) {
                if (properties.get(key) == null || "".equals(properties.get(key))) continue;
                if (key.endsWith(LIST_SYMBOL)) {
                    String attribute = key.replace(LIST_SYMBOL, "");
                    conditions.add(attribute + "=in=(" + properties.get(key) + ")");
                } else if (key.endsWith(FROM_SYMBOL)) {
                    String attribute = key.replace(FROM_SYMBOL, "");
                    conditions.add(attribute + ">=" + properties.get(key));
                } else if (key.endsWith(TO_SYMBOL)) {
                    String attribute = key.replace(TO_SYMBOL, "");
                    conditions.add(attribute + "<=" + properties.get(key));
                } else { //normal search
                    //distinguish between and number
                    /*if (mapFields.get(key).getType().equals(String.class)) {
                        conditions.add(key + "==*" + properties.get(key) + "*");
                    } else {
                        conditions.add(key + "==" + properties.get(key));
                    }*/
                    conditions.add(key + "=='" + properties.get(key) +"'");
                }
            }
        } catch (IllegalAccessException | NoSuchMethodException | InvocationTargetException ex) {
            return searchInfo;
        }

        searchInfo.setQuery(String.join(";", conditions));

        return searchInfo;
    }

}
