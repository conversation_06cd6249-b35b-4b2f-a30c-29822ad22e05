-- Migration Script: Add Multiple Contact Information Fields to Customers Table
-- Date: 2025-01-15
-- Description: Add JSON fields for storing multiple phone numbers, email addresses, and CCCD numbers

-- Add additional phone numbers field (JSON array)
ALTER TABLE customers 
ADD COLUMN additional_phones JSON NULL 
COMMENT 'JSON array storing additional phone numbers for the customer';

-- Add additional email addresses field (JSON array)
ALTER TABLE customers 
ADD COLUMN additional_emails JSON NULL 
COMMENT 'JSON array storing additional email addresses for the customer';

-- Add additional CCCD numbers field (JSON array)
ALTER TABLE customers 
ADD COLUMN additional_cccds JSON NULL 
COMMENT 'JSON array storing additional CCCD (Citizen Identity Card) numbers for the customer';

-- Create indexes for better search performance on JSON fields
-- Note: MySQL 5.7+ supports functional indexes on JSON fields

-- Index for searching in additional_phones JSON array
CREATE INDEX idx_customers_additional_phones 
ON customers ((CAST(additional_phones AS CHAR(1000) ARRAY)));

-- Index for searching in additional_emails JSON array  
CREATE INDEX idx_customers_additional_emails 
ON customers ((CAST(additional_emails AS CHAR(1000) ARRAY)));

-- Index for searching in additional_cccds JSON array
CREATE INDEX idx_customers_additional_cccds 
ON customers ((CAST(additional_cccds AS CHAR(100) ARRAY)));

-- Add comments to document the JSON structure expected
-- Expected JSON structure for additional_phones: ["0123456789", "0987654321"]
-- Expected JSON structure for additional_emails: ["<EMAIL>", "<EMAIL>"]  
-- Expected JSON structure for additional_cccds: ["123456789012", "987654321098"]

-- Verify the changes
DESCRIBE customers;

-- Sample data insertion examples (for testing purposes - commented out)
/*
-- Example: Insert customer with multiple contact information
INSERT INTO customers (
    full_name, phone, email, cccd, 
    additional_phones, additional_emails, additional_cccds,
    created_at, updated_at
) VALUES (
    'Nguyen Van Test', '0123456789', '<EMAIL>', '123456789012',
    JSON_ARRAY('0987654321', '0111222333'), 
    JSON_ARRAY('<EMAIL>', '<EMAIL>'),
    JSON_ARRAY('987654321098', '111222333444'),
    NOW(), NOW()
);

-- Example: Update existing customer with additional contact information
UPDATE customers 
SET 
    additional_phones = JSON_ARRAY('0987654321', '0111222333'),
    additional_emails = JSON_ARRAY('<EMAIL>'),
    additional_cccds = JSON_ARRAY('987654321098'),
    updated_at = NOW()
WHERE id = 1;

-- Example: Search queries for testing
-- Search by primary or additional phone numbers
SELECT * FROM customers 
WHERE phone LIKE '%123%' 
   OR JSON_SEARCH(additional_phones, 'one', '%123%') IS NOT NULL;

-- Search by primary or additional email addresses  
SELECT * FROM customers 
WHERE email LIKE '%test%' 
   OR JSON_SEARCH(additional_emails, 'one', '%test%') IS NOT NULL;

-- Search by primary or additional CCCD numbers
SELECT * FROM customers 
WHERE cccd LIKE '%123%' 
   OR JSON_SEARCH(additional_cccds, 'one', '%123%') IS NOT NULL;
*/

-- Migration completed successfully
-- Next steps: Update application code to handle the new JSON fields
