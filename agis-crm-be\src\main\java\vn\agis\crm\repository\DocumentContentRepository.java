package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.DocumentEntity;

import java.util.List;

@Repository
public interface DocumentContentRepository extends JpaRepository<DocumentEntity, Long> {
    List<DocumentEntity> findByProjectId(Long projectId);
    DocumentEntity findFirstByProjectIdAndPath(Long projectId, String path);
}
