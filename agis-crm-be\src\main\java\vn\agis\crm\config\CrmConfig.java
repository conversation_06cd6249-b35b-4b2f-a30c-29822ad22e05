package vn.agis.crm.config;

import com.google.common.base.Strings;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.*;
import org.springframework.data.redis.connection.jedis.JedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import redis.clients.jedis.JedisPoolConfig;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.AMQPService;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.amqp.AMQPEventBus;
import vn.agis.crm.base.redis.RedisCache;
import vn.agis.crm.core.cache.MessageSubscriber;

import java.time.Duration;

@Configuration
@EnableAsync
@EnableAspectJAutoProxy
@EnableAutoConfiguration
@EntityScan("vn.agis.crm.base")
@EnableTransactionManagement
@EnableScheduling
public class CrmConfig {

    @Autowired
    public Environment env;

    /* ========================= RABBIT MQ ============================ */
    @Bean
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "disable", havingValue = "false", matchIfMissing = true)
    @ConfigurationProperties(prefix = "spring.rabbitmq",  ignoreUnknownFields = true)
    public CachingConnectionFactory connectionFactory() {
        SpringContext.setServiceName(Constants.CRMService.BE_CORE_MGMT);
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory();
        connectionFactory.setConnectionNameStrategy(f->"CoreManagement01");
        return connectionFactory;
    }

    @Bean
    @ConditionalOnBean(CachingConnectionFactory.class)
    public EventBus eventBus(CachingConnectionFactory connectionFactory, ApplicationContext ctx) {
        return new AMQPEventBus(connectionFactory, ctx);
    }

    @Bean
    @ConditionalOnProperty(prefix = "spring.rabbitmq", name = "disable", havingValue = "false", matchIfMissing = true)
    public AMQPService amqpService(ApplicationContext ctx) {
        return new AMQPService(ctx);
    }

    /* ========================= Redis ============================ */
    private boolean useSentinelConfigs() {
        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        return master != null && nodes != null && !master.isEmpty() && !nodes.isEmpty();
    }

    //Standalone, Sentinel, Cluster
    @Bean
    @ConfigurationProperties(prefix = "spring.redis")
    public RedisConnectionFactory redisConnectionFactory() {
        String modeConnection = env.getProperty("spring.redis.mode");
        final JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(10);
        jedisPoolConfig.setMinIdle(0);
        jedisPoolConfig.setMaxWait(Duration.ofMillis(-1));
        if(modeConnection == null){
            return new JedisConnectionFactory();
        }else if(modeConnection.equals("Standalone")){
            return new JedisConnectionFactory(getStandaloneConfig());
        }else if(modeConnection.equals("Sentinel")){
            return new JedisConnectionFactory(getSentinelConfig());
        }else if(modeConnection.equals("Cluster")){
            return new JedisConnectionFactory(getClusterConfig(), jedisPoolConfig);
        }
        return new JedisConnectionFactory();
    }

    private RedisClusterConfiguration getClusterConfig() {
        String nodes = env.getProperty("spring.redis.cluster.nodes");
        String username = env.getProperty("spring.redis.cluster.username");
        String password = env.getProperty("spring.redis.cluster.password");
        RedisClusterConfiguration redisClusterConfiguration = new RedisClusterConfiguration();

        if(!Strings.isNullOrEmpty(password)){
            redisClusterConfiguration.setPassword(password);
        }
        if(!Strings.isNullOrEmpty(username)){
            redisClusterConfiguration.setUsername(Strings.nullToEmpty(username));
        }
        for(String nodeInfo : nodes.split(",")){
            redisClusterConfiguration.addClusterNode(new RedisClusterNode(nodeInfo.split(":")[0].trim(), Integer.parseInt(nodeInfo.split(":")[1].trim())));
        }
        return redisClusterConfiguration;
    }

    private RedisStandaloneConfiguration getStandaloneConfig() {
        RedisStandaloneConfiguration redisStandaloneConfiguration = new RedisStandaloneConfiguration();
         String host = env.getProperty("spring.redis.standalone.host");
        String port = env.getProperty("spring.redis.standalone.port");
        String username = env.getProperty("spring.redis.standalone.username");
        String password = env.getProperty("spring.redis.standalone.password");
        redisStandaloneConfiguration.setHostName(host.trim());
        redisStandaloneConfiguration.setPort(Integer.parseInt(port.trim()));
        if(!Strings.isNullOrEmpty(password)){
            redisStandaloneConfiguration.setPassword(password);
        }
        if(!Strings.isNullOrEmpty(username)){
            redisStandaloneConfiguration.setUsername(Strings.nullToEmpty(username));
        }
        return redisStandaloneConfiguration;
    }

    // Setup Sentinel config
    public RedisSentinelConfiguration getSentinelConfig() {
        RedisSentinelConfiguration configuration = new RedisSentinelConfiguration();
        if (!useSentinelConfigs()) return configuration;
        String password = env.getProperty("spring.redis.sentinel.password");
        String master = env.getProperty("spring.redis.sentinel.master");
        String nodes = env.getProperty("spring.redis.sentinel.nodes");
        configuration.master(master);
        if(!Strings.isNullOrEmpty(password)){
            configuration.setPassword(password);
        }
        for (String node : nodes.split(",")) {
            String split[] = node.split(":");
            configuration.sentinel(split[0].trim(), Integer.parseInt(split[1].trim()));
        }
        return configuration;
    }

    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<Object, Object> redisTemplateObj(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<Object, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }


    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory jedisConnectionFactory) {
        RedisTemplate<String, Object> redisTemplate = new RedisTemplate<String, Object>();
        redisTemplate.setConnectionFactory(jedisConnectionFactory);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setValueSerializer(new StringRedisSerializer());
        return redisTemplate;
    }

    @Bean
    public RedisCache systemCache(RedisTemplate redisTemplate) throws NoSuchFieldException, IllegalAccessException {
        RedisCache redisCache =  new RedisCache(redisTemplate);
//        try {
//            redisCache.put("test", "test");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        return redisCache;
    }

    @Bean
    MessageListenerAdapter messageListener() {
        return new MessageListenerAdapter(new MessageSubscriber());
    }

//    @Bean
    RedisMessageListenerContainer redisContainer(MessageListenerAdapter messageListenerAdapter, RedisConnectionFactory redisConnectionFactory) {
        RedisMessageListenerContainer container
            = new RedisMessageListenerContainer();
        container.setConnectionFactory(redisConnectionFactory);
        container.addMessageListener(messageListenerAdapter, new PatternTopic("__keyevent@*__:set"));
        return container;
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }

}
