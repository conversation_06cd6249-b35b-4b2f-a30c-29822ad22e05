package vn.agis.crm.model.mapper;

import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.req.ProjectDetailDto;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.entity.Projects;

@Component
public class ProjectMapper {

    public ProjectDetailDto toDetailDto(Projects entity) {
        if (entity == null) {
            return null;
        }

        ProjectDetailDto dto = new ProjectDetailDto();
        dto.setId(entity.getId());
        dto.setName(entity.getName());
        dto.setDescription(entity.getDescription());
        dto.setActive(entity.isActive());
        dto.setCreatedAt(
                entity.getCreatedAt() != null ? entity.getCreatedAt() : null);
        dto.setUpdatedAt(entity.getUpdatedAt());

        return dto;
    }

    public Projects toEntity(ProjectDto dto) {
        if (dto == null) {
            return null;
        }

        Projects entity = new Projects();
        entity.setName(dto.getName());
        entity.setDescription(dto.getDescription());
        if (dto.getIsActive() != null) {
            entity.setActive(dto.getIsActive());
        }

        return entity;
    }

    public void updateEntityFromDto(Projects entity, ProjectDto dto) {
        if (dto == null || entity == null) {
            return;
        }

        if (dto.getName() != null) {
            entity.setName(dto.getName());
        }
        if (dto.getDescription() != null) {
            entity.setDescription(dto.getDescription());
        }
        if (dto.getIsActive() != null) {
            entity.setActive(dto.getIsActive());
        }
    }
}

