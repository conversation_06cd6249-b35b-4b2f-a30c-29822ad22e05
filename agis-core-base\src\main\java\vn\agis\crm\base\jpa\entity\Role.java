package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import lombok.Data;
import vn.agis.crm.base.constants.MessageKeyConstant;

import java.util.List;

@Entity
@Table(name = "ROLE")
@Data
public class Role extends AbstractEntity{

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "NAME")
    @NotNull (message = MessageKeyConstant.Validation.FIELD_MUST_BE_NOT_NULL)
    @NotBlank (message = MessageKeyConstant.Validation.NOT_BLANK)
    @NotEmpty (message = MessageKeyConstant.Validation.NOT_EMPTY)
    @Size(max = 255)
    @Pattern(regexp = "^[a-zA-Z0-9- _aAàÀảẢãÃáÁạẠăĂằẰẳẲẵẴắẮặẶâÂầẦẩẨẫẪấẤậẬbBcCdDđĐeEèÈẻẺẽẼéÉẹẸêÊềỀểỂễỄếẾệỆfFgGhHiIìÌỉỈĩĨíÍịỊjJkKlLmMnNoOòÒỏỎõÕóÓọỌôÔồỒổỔỗỖốỐộỘơƠờỜởỞỡỠớỚợỢpPqQrRsStTuUùÙủỦũŨúÚụỤưƯừỪửỬữỮứỨựỰvVwWxXyYỳỲỷỶỹỸýÝỵỴzZ]*$", message = MessageKeyConstant.Validation.DATA_FORMAT)
    private String name;

    @Column(name = "DESCRIPTION")
    private String description;

    @NotNull (message = MessageKeyConstant.Validation.FIELD_MUST_BE_NOT_NULL)
    @Column(name = "TYPE")
    private Integer type;

    @NotNull (message = MessageKeyConstant.Validation.FIELD_MUST_BE_NOT_NULL)
    @Column(name = "STATUS")
    private Integer status;

    @Transient
    private List<Long> permissionIds;
//    @ManyToMany(fetch = FetchType.LAZY)
//    @JoinTable(name = "ROLE_PERMISSION",
//            joinColumns = @JoinColumn(name = "ROLE_ID"),
//            inverseJoinColumns = @JoinColumn(name = "PERMISSION_ID"))
//    List<Permission> permissions;
}
