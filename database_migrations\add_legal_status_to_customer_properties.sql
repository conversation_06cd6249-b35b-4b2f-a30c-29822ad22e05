-- Migration Script: Add Legal Status Field to Customer Properties Table
-- Date: 2025-01-15
-- Description: Add legal_status field to track legal status/compliance information for customer properties

-- Add legal_status field to customer_properties table
ALTER TABLE customer_properties 
ADD COLUMN legal_status VARCHAR(255) NULL 
COMMENT 'Legal status or compliance information for the customer property (e.g., "Approved", "Pending Review", "Compliant", "Under Investigation")';

-- Create index for better search performance on legal_status field
CREATE INDEX idx_customer_properties_legal_status 
ON customer_properties (legal_status);

-- Verify the changes
DESCRIBE customer_properties;

-- Sample data examples (for testing purposes - commented out)
/*
-- Example: Update existing records with sample legal status values
UPDATE customer_properties 
SET legal_status = 'Approved'
WHERE id IN (1, 2, 3);

UPDATE customer_properties 
SET legal_status = 'Pending Review'
WHERE id IN (4, 5);

UPDATE customer_properties 
SET legal_status = 'Compliant'
WHERE id IN (6, 7, 8);

-- Example: Insert new record with legal status
INSERT INTO customer_properties (
    customer_id, project_id, unit_id, transaction_date, contract_price,
    legal_status, employee_id, notes, created_at, updated_at
) VALUES (
    1, 1, 1, '2024-01-15', 5000000000.00,
    'Approved', 1, 'Property with approved legal status', NOW(), NOW()
);

-- Example: Search queries for testing
-- Find properties by legal status
SELECT * FROM customer_properties 
WHERE legal_status = 'Approved';

-- Find properties with pending legal status
SELECT * FROM customer_properties 
WHERE legal_status LIKE '%Pending%';

-- Count properties by legal status
SELECT legal_status, COUNT(*) as count 
FROM customer_properties 
WHERE legal_status IS NOT NULL 
GROUP BY legal_status;
*/

-- Migration completed successfully
-- Next steps: Update application code to handle the new legal_status field
