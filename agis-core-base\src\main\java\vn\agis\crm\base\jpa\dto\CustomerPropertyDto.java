package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import vn.agis.crm.base.jpa.dto.req.ProjectDto;
import vn.agis.crm.base.jpa.dto.req.UnitDto;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CustomerPropertyDto {
    private Long id;
    private Long projectId;
    private Long unitId;
    private Date transactionDate;
    private BigDecimal contractPrice;

    private String externalAgencyName;
    private String externalSaleName;
    private String externalSalePhone;
    private String externalSaleEmail;

    private Long employeeId;
    private String notes;
    private String legalStatus;

    private Date firstInteraction;
    private Date lastInteraction;

    // new: include detailed interactionsSecondary in response as requested
    private java.util.List<vn.agis.crm.base.jpa.dto.InteractionSecondaryDto> interactionsSecondary;
    private UnitDto unit;
    private ProjectDto project;
}

