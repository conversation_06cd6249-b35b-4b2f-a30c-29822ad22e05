// Enhanced Import Validation Test Examples
// This demonstrates the new validation rules and error summary format

package vn.agis.crm.util;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import vn.agis.crm.base.jpa.dto.DryRunResultDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;

import java.util.*;

@SpringBootTest
public class EnhancedImportValidationTestExamples {

    @Test
    public void testEnhancedPhoneValidation() {
        System.out.println("=== Enhanced Phone Validation Test ===");
        
        // Test data with various phone formats
        Map<String, String> testPhones = Map.of(
            "Valid Vietnamese mobile", "0901234567",
            "Valid with +84", "+84901234567", 
            "Valid with 84", "84901234567",
            "Invalid format", "123456",
            "Invalid prefix", "0801234567",
            "Empty phone", ""
        );
        
        Set<String> existingPhones = Set.of("+84901234567"); // Simulate existing phone
        Set<String> filePhones = new HashSet<>();
        
        for (Map.Entry<String, String> entry : testPhones.entrySet()) {
            LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
            rowData.put("__ROW_NUMBER__", "1");
            rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
            rowData.put("PHONE", entry.getValue());
            
            ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, existingPhones, filePhones);
            
            System.out.println(String.format("Phone: %s (%s) - Valid: %s", 
                entry.getValue(), entry.getKey(), result.isValid()));
            
            if (result.getErrors() != null) {
                result.getErrors().forEach(error -> 
                    System.out.println("  Error: " + error.getErrorDescription()));
            }
        }
    }

    @Test
    public void testEnhancedEmailValidation() {
        System.out.println("\n=== Enhanced Email Validation Test ===");
        
        Map<String, String> testEmails = Map.of(
            "Valid email", "<EMAIL>",
            "Valid with subdomain", "<EMAIL>",
            "Invalid format", "invalid-email",
            "Missing @", "testexample.com",
            "Empty email", ""
        );
        
        for (Map.Entry<String, String> entry : testEmails.entrySet()) {
            LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
            rowData.put("__ROW_NUMBER__", "1");
            rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
            rowData.put("PHONE", "0901234567");
            rowData.put("EMAIL", entry.getValue());
            
            ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, new HashSet<>(), new HashSet<>());
            
            System.out.println(String.format("Email: %s (%s) - Valid: %s", 
                entry.getValue(), entry.getKey(), result.isValid()));
            
            if (result.getErrors() != null) {
                result.getErrors().forEach(error -> 
                    System.out.println("  Error: " + error.getErrorDescription()));
            }
        }
    }

    @Test
    public void testBirthDateValidation() {
        System.out.println("\n=== Birth Date Validation Test ===");
        
        Map<String, String> testDates = Map.of(
            "Valid Vietnamese format", "25/12/1990",
            "Valid short format", "1/1/1985",
            "Valid ISO format", "1990-12-25",
            "Future date (invalid)", "25/12/2030",
            "Very old date (warning)", "01/01/1850",
            "Invalid format", "25-13-1990",
            "Empty date", ""
        );
        
        for (Map.Entry<String, String> entry : testDates.entrySet()) {
            LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
            rowData.put("__ROW_NUMBER__", "1");
            rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
            rowData.put("PHONE", "0901234567");
            rowData.put("NGÀY SINH", entry.getValue());
            
            ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, new HashSet<>(), new HashSet<>());
            
            System.out.println(String.format("Birth Date: %s (%s) - Valid: %s", 
                entry.getValue(), entry.getKey(), result.isValid()));
            
            if (result.getErrors() != null) {
                result.getErrors().forEach(error -> 
                    System.out.println("  Error: " + error.getErrorDescription()));
            }
            
            if (result.getWarnings() != null) {
                result.getWarnings().forEach(warning -> 
                    System.out.println("  Warning: " + warning.getErrorDescription()));
            }
        }
    }

    @Test
    public void testMaritalStatusValidation() {
        System.out.println("\n=== Marital Status Validation Test ===");
        
        Map<String, String> testStatuses = Map.of(
            "Valid Vietnamese", "ĐỘC THÂN",
            "Valid English", "married",
            "Valid uppercase", "ĐÃ LẬP GIA ĐÌNH",
            "Common typo", "DOC THAN",
            "Invalid value", "UNKNOWN_STATUS",
            "Empty status", ""
        );
        
        for (Map.Entry<String, String> entry : testStatuses.entrySet()) {
            LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
            rowData.put("__ROW_NUMBER__", "1");
            rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", "Test Customer");
            rowData.put("PHONE", "0901234567");
            rowData.put("TÌNH TRẠNG HÔN NHÂN", entry.getValue());
            
            ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, new HashSet<>(), new HashSet<>());
            
            System.out.println(String.format("Marital Status: %s (%s) - Valid: %s", 
                entry.getValue(), entry.getKey(), result.isValid()));
            
            if (result.getErrors() != null) {
                result.getErrors().forEach(error -> 
                    System.out.println("  Error: " + error.getErrorDescription()));
            }
        }
    }

    @Test
    public void testDuplicatePhoneDetection() {
        System.out.println("\n=== Duplicate Phone Detection Test ===");
        
        Set<String> existingPhones = Set.of("+84901234567"); // Existing in system
        Set<String> filePhones = new HashSet<>();
        
        // Test duplicate in system
        LinkedHashMap<String, String> row1 = new LinkedHashMap<>();
        row1.put("__ROW_NUMBER__", "1");
        row1.put("HỌ VÀ TÊN KHÁCH HÀNG", "Customer 1");
        row1.put("PHONE", "0901234567"); // Same as existing
        
        ValidationResultDto result1 = ImportDataValidator.validateRow(row1, 1L, existingPhones, filePhones);
        System.out.println("Row 1 (duplicate in system): " + result1.isValid());
        if (result1.getErrors() != null) {
            result1.getErrors().forEach(error -> 
                System.out.println("  Error: " + error.getErrorDescription()));
        }
        
        // Test duplicate in file
        LinkedHashMap<String, String> row2 = new LinkedHashMap<>();
        row2.put("__ROW_NUMBER__", "2");
        row2.put("HỌ VÀ TÊN KHÁCH HÀNG", "Customer 2");
        row2.put("PHONE", "0909876543"); // New phone
        
        LinkedHashMap<String, String> row3 = new LinkedHashMap<>();
        row3.put("__ROW_NUMBER__", "3");
        row3.put("HỌ VÀ TÊN KHÁCH HÀNG", "Customer 3");
        row3.put("PHONE", "0909876543"); // Same as row 2
        
        ValidationResultDto result2 = ImportDataValidator.validateRow(row2, 1L, existingPhones, filePhones);
        ValidationResultDto result3 = ImportDataValidator.validateRow(row3, 1L, existingPhones, filePhones);
        
        System.out.println("Row 2 (first occurrence): " + result2.isValid());
        System.out.println("Row 3 (duplicate in file): " + result3.isValid());
        
        if (result3.getErrors() != null) {
            result3.getErrors().forEach(error -> 
                System.out.println("  Error: " + error.getErrorDescription()));
        }
    }

    @Test
    public void testNewErrorSummaryFormat() {
        System.out.println("\n=== New Error Summary Format Test ===");
        
        // Create sample validation results with errors
        List<ValidationResultDto> validationResults = new ArrayList<>();
        
        // Row with phone format error
        ValidationResultDto result1 = new ValidationResultDto(4);
        result1.addError(new ImportErrorDto(1L, 4, "email", "invalid-email", 
            "INVALID_FORMAT", "Email không đúng định dạng", "ERROR"));
        validationResults.add(result1);
        
        // Row with duplicate phone
        ValidationResultDto result2 = new ValidationResultDto(5);
        result2.addError(new ImportErrorDto(1L, 5, "phone", "0901234567", 
            "DUPLICATE_IN_FILE", "Số điện thoại bị trùng lặp trong file", "ERROR"));
        validationResults.add(result2);
        
        // Row with system duplicate (warning)
        ValidationResultDto result3 = new ValidationResultDto(6);
        result3.addError(new ImportErrorDto(1L, 6, "phone", "0909876543", 
            "DUPLICATE_IN_SYSTEM", "Số điện thoại đã tồn tại trong hệ thống", "WARNING"));
        validationResults.add(result3);
        
        // Calculate statistics with new format
        DryRunResultDto dryRunResult = ImportStatisticsCalculator.calculateStatistics(
            1L, "template2_ERR.xlsx", validationResults, new Date(), new Date());
        
        System.out.println("=== New Error Summary Format ===");
        System.out.println("Total Rows: " + dryRunResult.getTotalRows());
        System.out.println("Valid Rows: " + dryRunResult.getValidRows());
        System.out.println("Error Rows: " + dryRunResult.getErrorRows());
        
        System.out.println("\nError Summary (New Format):");
        if (dryRunResult.getErrorSummary() != null) {
            for (DryRunResultDto.ErrorSummaryDto error : dryRunResult.getErrorSummary()) {
                System.out.println(String.format(
                    "Row %d, Column: %s, Type: %s, Description: %s, Severity: %s",
                    error.getRow(), error.getColumn(), error.getErrType(), 
                    error.getDescription(), error.getSeverity()));
            }
        }
        
        // Expected JSON format
        System.out.println("\n=== Expected JSON Response ===");
        System.out.println("""
            {
              "jobId": 18,
              "fileName": "template2_ERR.xlsx",
              "status": "SUCCESS",
              "totalRows": 3,
              "validRows": 0,
              "errorRows": 3,
              "errorSummary": [
                {
                  "row": 4,
                  "column": "email",
                  "errType": "INVALID_FORMAT",
                  "description": "Email không đúng định dạng",
                  "severity": "ERROR"
                },
                {
                  "row": 5,
                  "column": "phone",
                  "errType": "DUPLICATE_IN_FILE",
                  "description": "Số điện thoại bị trùng lặp trong file",
                  "severity": "ERROR"
                },
                {
                  "row": 6,
                  "column": "phone",
                  "errType": "DUPLICATE_IN_SYSTEM",
                  "description": "Số điện thoại đã tồn tại trong hệ thống",
                  "severity": "WARNING"
                }
              ]
            }
            """);
    }

    @Test
    public void testComprehensiveValidation() {
        System.out.println("\n=== Comprehensive Validation Test ===");
        
        // Test row with multiple errors
        LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
        rowData.put("__ROW_NUMBER__", "10");
        rowData.put("HỌ VÀ TÊN KHÁCH HÀNG", ""); // Missing required field
        rowData.put("PHONE", "123456"); // Invalid format
        rowData.put("EMAIL", "invalid-email"); // Invalid format
        rowData.put("NGÀY SINH", "32/13/1990"); // Invalid date
        rowData.put("TÌNH TRẠNG HÔN NHÂN", "INVALID_STATUS"); // Invalid enum
        
        ValidationResultDto result = ImportDataValidator.validateRow(rowData, 1L, new HashSet<>(), new HashSet<>());
        
        System.out.println("Row with multiple errors - Valid: " + result.isValid());
        System.out.println("Number of errors: " + (result.getErrors() != null ? result.getErrors().size() : 0));
        
        if (result.getErrors() != null) {
            result.getErrors().forEach(error -> 
                System.out.println(String.format("  %s (%s): %s", 
                    error.getColumnName(), error.getErrorType(), error.getErrorDescription())));
        }
    }
}

// Manual testing scenarios for different validation rules
class EnhancedValidationManualTestScenarios {
    
    public static void main(String[] args) {
        System.out.println("🔍 Enhanced Import Validation - Manual Test Scenarios");
        
        System.out.println("\n📋 Scenario 1: Vietnamese Phone Number Validation");
        System.out.println("Valid formats: 0901234567, +84901234567, 84901234567");
        System.out.println("Invalid formats: 123456, 0801234567, +1234567890");
        
        System.out.println("\n📋 Scenario 2: Email Validation (Optional Field)");
        System.out.println("Valid: <EMAIL>, <EMAIL>, empty field");
        System.out.println("Invalid: invalid-email, test@, @example.com");
        
        System.out.println("\n📋 Scenario 3: Birth Date Validation");
        System.out.println("Valid formats: 25/12/1990, 1/1/1985, 1990-12-25");
        System.out.println("Invalid: future dates, very old dates (>150 years), invalid formats");
        
        System.out.println("\n📋 Scenario 4: Marital Status Validation");
        System.out.println("Valid values: ĐỘC THÂN, ĐÃ LẬP GIA ĐÌNH, LY THÂN, GÓA, KHÁC");
        System.out.println("Also accepts: single, married, divorced, widowed, other");
        
        System.out.println("\n📋 Scenario 5: Duplicate Phone Detection");
        System.out.println("- Checks against existing database records");
        System.out.println("- Detects duplicates within the same import file");
        System.out.println("- Normalizes phone numbers for accurate comparison");
        
        System.out.println("\n📋 Scenario 6: New Error Summary Format");
        System.out.println("- Individual error entries for each validation failure");
        System.out.println("- Row-level error details with column, type, description, severity");
        System.out.println("- Supports both ERROR and WARNING severity levels");
        
        System.out.println("\n🚀 Testing Commands:");
        System.out.println("mvn test -Dtest=EnhancedImportValidationTestExamples");
        System.out.println("mvn test -Dtest=EnhancedImportValidationTestExamples#testNewErrorSummaryFormat");
    }
}
