package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class ImportProgressDto {
    private Long jobId;
    private String fileName;
    private String status; // PENDING, RUNNING, SUCCESS, FAILED, CANCELLED
    private Date startedAt;
    private Date lastUpdatedAt;
    
    // Progress tracking
    private Integer totalRows;
    private Integer processedRows;
    private Integer successfulRows;
    private Integer failedRows;
    private Integer skippedRows;
    private Double progressPercentage;
    
    // Performance metrics
    private Long processingTimeMs;
    private Double averageRowProcessingTimeMs;
    private Long estimatedRemainingTimeMs;
    private String estimatedRemainingTime; // Human readable format
    
    // Current batch information
    private Integer currentBatchNumber;
    private Integer totalBatches;
    private Integer batchSize;
    private String currentPhase; // "PARSING", "VALIDATING", "IMPORTING", "FINALIZING"
    
    // Error summary during execution
    private Integer errorCount;
    private Integer warningCount;
    private List<String> recentErrors; // Last 5 errors for quick feedback
    
    // Cancellation support
    private Boolean cancellationRequested;
    private String cancellationReason;
    
    // Resource usage (optional)
    private Long memoryUsageMB;
    private Double cpuUsagePercent;
    
    public ImportProgressDto() {}
    
    public ImportProgressDto(Long jobId, String fileName, String status) {
        this.jobId = jobId;
        this.fileName = fileName;
        this.status = status;
        this.lastUpdatedAt = new Date();
        this.progressPercentage = 0.0;
        this.processedRows = 0;
        this.successfulRows = 0;
        this.failedRows = 0;
        this.skippedRows = 0;
        this.errorCount = 0;
        this.warningCount = 0;
        this.cancellationRequested = false;
    }
    
    /**
     * Calculate progress percentage based on processed rows
     */
    public void calculateProgress() {
        if (totalRows != null && totalRows > 0 && processedRows != null) {
            this.progressPercentage = (processedRows.doubleValue() / totalRows.doubleValue()) * 100.0;
        } else {
            this.progressPercentage = 0.0;
        }
    }
    
    /**
     * Update processing metrics
     */
    public void updateProcessingMetrics(Date startTime) {
        if (startTime != null) {
            this.processingTimeMs = new Date().getTime() - startTime.getTime();
            
            if (processedRows != null && processedRows > 0) {
                this.averageRowProcessingTimeMs = processingTimeMs.doubleValue() / processedRows.doubleValue();
                
                // Estimate remaining time
                Integer remainingRows = (totalRows != null) ? totalRows - processedRows : 0;
                if (remainingRows > 0 && averageRowProcessingTimeMs > 0) {
                    this.estimatedRemainingTimeMs = Math.round(remainingRows * averageRowProcessingTimeMs);
                    this.estimatedRemainingTime = formatDuration(estimatedRemainingTimeMs);
                }
            }
        }
        this.lastUpdatedAt = new Date();
    }
    
    /**
     * Format duration in milliseconds to human readable format
     */
    private String formatDuration(Long durationMs) {
        if (durationMs == null || durationMs <= 0) {
            return "Unknown";
        }
        
        long seconds = durationMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        
        if (hours > 0) {
            return String.format("%d hours %d minutes", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d minutes %d seconds", minutes, seconds % 60);
        } else {
            return String.format("%d seconds", seconds);
        }
    }
    
    /**
     * Check if import is in a terminal state
     */
    public boolean isCompleted() {
        return "SUCCESS".equals(status) || "FAILED".equals(status) || "CANCELLED".equals(status);
    }
    
    /**
     * Check if import can be cancelled
     */
    public boolean isCancellable() {
        return "RUNNING".equals(status) && !Boolean.TRUE.equals(cancellationRequested);
    }
}
