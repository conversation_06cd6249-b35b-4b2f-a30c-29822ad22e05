package vn.agis.crm.base.core.filters;

import org.springframework.security.core.GrantedAuthority;

import java.util.Collection;

/**
 * Created by huyvv
 * Date: 12/02/2020
 * Time: 3:14 PM
 * for all issues, contact me: <EMAIL>
 **/
public class UserPrincipal extends org.springframework.security.core.userdetails.User {
    //add new property for principal
    private Long userId;
    private Integer userType;

//    private String provinceCode;
//    private String fullName;
    private String email;

    public UserPrincipal(String username, String password, Collection<? extends GrantedAuthority> authorities, Long userId, Integer userType, String email) {
        super(username, password, authorities);
        this.userId = userId;
        this.userType = userType;
//        this.provinceCode = provinceCode;
//        this.fullName = fullName;
        this.email = email;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getUserType() {
        return userType;
    }

    public void setUserType(Integer userType) {
        this.userType = userType;
    }

//    public String getProvinceCode() {
//        return provinceCode;
//    }
//
//    public void setProvinceCode(String provinceCode) {
//        this.provinceCode = provinceCode;
//    }
//
//    public String getFullName() {
//        return fullName;
//    }
//
//    public void setFullName(String fullName) {
//        this.fullName = fullName;
//    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
}
