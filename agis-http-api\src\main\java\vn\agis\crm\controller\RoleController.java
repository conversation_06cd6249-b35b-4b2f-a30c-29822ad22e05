package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.RoleSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchRoleRespone;
import vn.agis.crm.base.jpa.entity.Permission;
import vn.agis.crm.base.jpa.entity.Role;
import vn.agis.crm.model.entity.PermissionTree;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.PermissionService;
import vn.agis.crm.service.RoleService;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/role")
public class RoleController extends CrudController<Role, Long> {

    private RoleService roleService;
    private PermissionService permissionService;

    public RoleController(RoleService roleService, PermissionService permissionService) {
        super(roleService);
        this.roleService = roleService;
        this.permissionService = permissionService;
        this.baseUrl = "/role";
    }

    @PostMapping()
    @PreAuthorize("hasAnyAuthority('createRole')")
    public ResponseEntity<Role> createRole(@Valid @RequestBody Role entity, HttpServletRequest httpServletRequest) {
        Role role = roleService.createRole(entity);

        return ResponseEntity.ok().body(role);
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('searchRole')")
    public ResponseEntity<Page<SearchRoleRespone>> searchRole(@RequestParam(name = "name", required = false, defaultValue = " ") String name,
                                                              @RequestParam(name = "type", required = false, defaultValue = "-1") Integer type,
                                                              @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
                                                              @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
                                                              @RequestParam(name = "sort", required = false, defaultValue = "name,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        RoleSearchDTO roleSearchDTO = new RoleSearchDTO(name, type, page, size, sortBy);
        Page<SearchRoleRespone> roleRespones = roleService.searchRole(roleSearchDTO, listRequest.getPageable());
        return ResponseEntity.ok().body(roleRespones);
    }

    @GetMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('getRole')")
    public ResponseEntity<Role> getRoleDetail(@PathVariable Long roleId, HttpServletRequest httpServletRequest) {
        Role role = roleService.get(roleId);
        return ResponseEntity.ok().body(role);
    }

    @PutMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('updateRole')")
    public ResponseEntity<Role> updateRoleDetail(@PathVariable Long roleId, @Valid @RequestBody Role role, HttpServletRequest httpServletRequest) {
        role.setId(roleId);
        Role updatedRole = roleService.updateRoleDetail(role);
        return ResponseEntity.ok().body(updatedRole);
    }

    @PutMapping("/update-status/{roleId}")
    @PreAuthorize("hasAnyAuthority('updateRole')")
    public ResponseEntity<ResponseBase> updateRoleStatus(@PathVariable Long roleId) {
        ResponseBase result = new ResponseBase();
        result = roleService.updateRoleStatus(roleId);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @GetMapping("/get-all-permission")
    public ResponseEntity<List<PermissionTree>> getAllPermission(HttpServletRequest httpServletRequest) {
        List<Permission> permissions = permissionService.getAllPermission();
        List<PermissionTree> permissionTreeList = new ArrayList<PermissionTree>();
        for (Permission permission : permissions) {
            PermissionTree permissionTree = new PermissionTree(permission.getObjectKey(), permission.getObjectKey());
            int index = checkIfKeyExist(permissionTreeList, permission.getObjectKey());
            if (index == -1) {
                permissionTreeList.add(permissionTree);
                permissionTreeList.getLast().getChildren().add(new PermissionTree(permission.getPermissionKey(), permission.getPermissionKey(), permission));
            } else {
                permissionTreeList.get(index).getChildren().add(new PermissionTree(permission.getPermissionKey(), permission.getPermissionKey(), permission));
            }
        }
        return ResponseEntity.ok().body(permissionTreeList);
    }

    @DeleteMapping("/{roleId}")
    @PreAuthorize("hasAnyAuthority('deleteRole')")
    public ResponseEntity<ResponseBase> deleteRole(@PathVariable Long roleId, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = roleService.deleteById(roleId);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    public int checkIfKeyExist(List<PermissionTree> permissionTreeList, String key) {
        for (int i = 0; i < permissionTreeList.size(); i++) {
            if (permissionTreeList.get(i).getKey().equals(key)) {
                return i;
            }
        }
        return -1;
    }
}
