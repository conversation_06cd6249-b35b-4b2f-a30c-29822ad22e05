package vn.agis.crm.util;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ImportFileUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportFileUtils.class);
    
    public static final List<String> SUPPORTED_EXTENSIONS = Arrays.asList("csv", "xlsx", "xls");
    public static final List<String> SUPPORTED_MIME_TYPES = Arrays.asList(
        "text/csv",
        "application/csv",
        "application/vnd.ms-excel",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    );
    
    /**
     * Validate file extension and MIME type
     */
    public static boolean isValidFileType(String fileName, String mimeType) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String extension = getFileExtension(fileName).toLowerCase();
        boolean validExtension = SUPPORTED_EXTENSIONS.contains(extension);
        boolean validMimeType = mimeType == null || SUPPORTED_MIME_TYPES.contains(mimeType);
        
        return validExtension && validMimeType;
    }
    
    /**
     * Get file extension from filename
     */
    public static String getFileExtension(String fileName) {
        if (fileName == null || fileName.lastIndexOf('.') == -1) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf('.') + 1);
    }
    
    /**
     * Check if file is Excel format
     */
    public static boolean isExcelFile(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return "xlsx".equals(extension) || "xls".equals(extension);
    }
    
    /**
     * Calculate SHA-256 checksum for MultipartFile
     */
    public static String calculateChecksum(MultipartFile file) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] fileBytes = file.getBytes();
        byte[] hashBytes = digest.digest(fileBytes);
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return "sha256:" + hexString.toString();
    }
    
    /**
     * Calculate SHA-256 checksum for file path
     */
    public static String calculateChecksum(String filePath) throws IOException, NoSuchAlgorithmException {
        MessageDigest digest = MessageDigest.getInstance("SHA-256");
        byte[] fileBytes = Files.readAllBytes(Paths.get(filePath));
        byte[] hashBytes = digest.digest(fileBytes);
        
        StringBuilder hexString = new StringBuilder();
        for (byte b : hashBytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        
        return "sha256:" + hexString.toString();
    }
    
    /**
     * Create secure file path for storing uploaded files
     */
    public static String createSecureFilePath(String baseDir, Long jobId, String checksum, String originalFileName) {
        String sanitizedFileName = sanitizeFileName(originalFileName);
        String directory = baseDir + File.separator + jobId + File.separator + checksum.substring(7, 15);
        
        try {
            Files.createDirectories(Paths.get(directory));
        } catch (IOException e) {
            logger.error("Failed to create directory: " + directory, e);
            throw new RuntimeException("Failed to create upload directory", e);
        }
        
        return directory + File.separator + sanitizedFileName;
    }
    
    /**
     * Sanitize filename to prevent path traversal attacks
     */
    public static String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unknown";
        }
        
        // Remove path separators and other dangerous characters
        String sanitized = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // Limit length
        if (sanitized.length() > 255) {
            String extension = getFileExtension(sanitized);
            String name = sanitized.substring(0, 255 - extension.length() - 1);
            sanitized = name + "." + extension;
        }
        
        return sanitized;
    }
    
    /**
     * Get sheet names from Excel file
     */
    public static List<ExcelSheetInfo> getExcelSheets(String filePath) throws IOException {
        List<ExcelSheetInfo> sheets = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath)) {
            Workbook workbook;
            
            if (filePath.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(fis);
            } else if (filePath.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(fis);
            } else {
                throw new IllegalArgumentException("Unsupported file format");
            }
            
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                String sheetName = workbook.getSheetName(i);
                sheets.add(new ExcelSheetInfo(i, sheetName));
            }
            
            workbook.close();
        }
        
        return sheets;
    }
    
    /**
     * Get sheet names from Excel MultipartFile
     */
    public static List<ExcelSheetInfo> getExcelSheets(MultipartFile file) throws IOException {
        List<ExcelSheetInfo> sheets = new ArrayList<>();
        
        Workbook workbook;
        String fileName = file.getOriginalFilename();
        
        if (fileName != null && fileName.toLowerCase().endsWith(".xlsx")) {
            workbook = new XSSFWorkbook(file.getInputStream());
        } else if (fileName != null && fileName.toLowerCase().endsWith(".xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            throw new IllegalArgumentException("Unsupported file format");
        }
        
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            String sheetName = workbook.getSheetName(i);
            sheets.add(new ExcelSheetInfo(i, sheetName));
        }
        
        workbook.close();
        return sheets;
    }
    
    /**
     * Excel sheet information
     */
    public static class ExcelSheetInfo {
        private final Integer index;
        private final String name;
        
        public ExcelSheetInfo(Integer index, String name) {
            this.index = index;
            this.name = name;
        }
        
        public Integer getIndex() {
            return index;
        }
        
        public String getName() {
            return name;
        }
    }
}
