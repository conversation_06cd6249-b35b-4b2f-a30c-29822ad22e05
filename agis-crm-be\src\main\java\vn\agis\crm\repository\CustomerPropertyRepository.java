package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.CustomerProperties;

import java.util.List;

@Repository
public interface CustomerPropertyRepository extends JpaRepository<CustomerProperties, Long> {
    List<CustomerProperties> findByCustomerId(Long customerId);

    /**
     * Count customer properties for a project (for dependency validation)
     */
    @Query("SELECT COUNT(cp) FROM CustomerProperties cp WHERE cp.projectId = :projectId")
    long countPropertiesByProjectId(@Param("projectId") Long projectId);

    /**
     * Check if project has any customer properties (for dependency validation)
     */
    @Query("SELECT CASE WHEN COUNT(cp) > 0 THEN true ELSE false END FROM CustomerProperties cp WHERE cp.projectId = :projectId")
    boolean hasPropertiesByProjectId(@Param("projectId") Long projectId);

    /**
     * Find all customer properties for a project (for detailed dependency info)
     */
    List<CustomerProperties> findByProjectId(Long projectId);

    /**
     * Count unique customers who have purchased properties in a project
     */
    @Query("SELECT COUNT(DISTINCT cp.customerId) FROM CustomerProperties cp WHERE cp.projectId = :projectId")
    long countUniqueCustomersByProjectId(@Param("projectId") Long projectId);

    /**
     * Batch count unique customers for multiple projects (for performance optimization)
     * Returns a map of projectId -> customer count
     */
    @Query("SELECT cp.projectId, COUNT(DISTINCT cp.customerId) FROM CustomerProperties cp WHERE cp.projectId IN :projectIds GROUP BY cp.projectId")
    List<Object[]> countUniqueCustomersByProjectIds(@Param("projectIds") List<Long> projectIds);
}

