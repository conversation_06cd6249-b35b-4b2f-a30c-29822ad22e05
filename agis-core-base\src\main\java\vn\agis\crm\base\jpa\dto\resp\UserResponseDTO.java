package vn.agis.crm.base.jpa.dto.resp;

import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Getter
@Setter
public class UserResponseDTO {
    private Long id;
    private String username;
    private String name;
    private Integer type;
    private String email;
    private Integer status;
    private List<RoleDTO> roles = new ArrayList<>();
    private String addressContact;
    private String addressHeadOffice;
    private String representativeName;
    private String taxCode;
//    private List<UserManageDTO> userManages = new ArrayList<>();
    private UserManageDTO manager;
    private String phone;
//    private Boolean isHasChild;
    private Boolean isRootCustomer;
    private String parentId; // áp dụng cho tk KH
    private String description;
    private String provinceAddressName;
    private String wardAddressName;
    private Integer provinceCodeAddress;
    private Integer wardCodeAddress;
    private String provinceOfficeName;
    private String wardOfficeName;
    private Integer provinceCodeOffice;
    private Integer wardCodeOffice;
    private Integer apartment;
    //Tài khoản root đã chọn khi tạo tài khoản
//    private UserManageDTO rootAccount;

    // trạng thái và danh sách api đã chọn
    @Data
    @AllArgsConstructor
    public static class RoleDTO{
        private String roleName;
        private Long roleId;
    }
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class UserManageDTO{
        private Long id;
        private String username;
        private String name;
    }
}
