package vn.agis.crm.constant.sql;

public class SQLUser {
    public static final String GET_PAGE_USER = """
            SELECT DISTINCT
               u.id,
               u.username as username,
               u.name,
               u.type as type,
               u.email as email,
               u.status as status,
               u.phone,
               p.name as provinceAddressName,
               w.name as wardAddressName,
               TO_CHAR(u.created_date, 'dd-mm-yyyy') as createdDate,
               CASE
               	    WHEN u.type = 2 and u.id IN (SELECT DISTINCT user_manage_id FROM user_manage) then 'true'
               	    ELSE 'false'
               END AS isRootCustomer
               FROM users u LEFT JOIN provinces p ON u.province_code_address = p.code
               LEFT JOIN wards w ON u.ward_code_address = w.code
               WHERE
            	(
            	    --- ADMIN
            	    ( :type = 1 AND u.type != 1)
            		OR ( :type = 2 AND u.TYPE not in (1,2) AND (u.id in (select um.user_id from user_manage um where um.user_manage_id = :userId ) ))
            	)
                AND ( :#{#search.username} = ' '  OR UPPER(u.username) LIKE CONCAT('%', UPPER(:#{#search.username}), '%') COLLATE utf8mb4_general_ci)
                AND ( :#{#search.name} = ' '  OR UPPER(u.name) LIKE CONCAT('%', UPPER(:#{#search.name}), '%') COLLATE utf8mb4_general_ci)
                AND (-1 = :#{#search.type} OR u.type = :#{#search.type})
                AND ( :#{#search.email} = ' '  OR UPPER(u.email) LIKE CONCAT('%', UPPER(:#{#search.email}), '%') )
                and u.status != -1 and u.id != :userId and u.created_by != 0
                AND ( :#{#search.status} is NULL  OR u.status = :#{#search.status} )
                AND ( :#{#search.phone} = ' '  OR u.phone LIKE CONCAT('%', :#{#search.phone}, '%') )
                AND ( :#{#search.managerId} = -1 or u.id IN (select um.user_id from user_manage um where um.user_manage_id = :#{#search.managerId}))
                AND ( :#{#search.customerId} = -1  OR u.id IN (SELECT um.user_manage_id FROM user_manage um WHERE um.user_id = :#{#search.customerId}))
                AND ( :#{#search.nameOrUsername} = ' '  OR (UPPER(u.username) LIKE CONCAT('%', UPPER(:#{#search.nameOrUsername}), '%') COLLATE utf8mb4_general_ci ) OR (UPPER(u.name) LIKE CONCAT('%', UPPER(:#{#search.nameOrUsername}), '%') COLLATE utf8mb4_general_ci ))
               AND ( :#{#search.provinceCodeAddress} = -1 OR u.province_code_address = :#{#search.provinceCodeAddress} )
               AND ( :#{#search.wardCodeAddress} = -1 OR u.ward_code_address = :#{#search.wardCodeAddress} )
               AND
               ( :#{#search.deviceTypeId} = -1 OR
               (u.id IN (SELECT d.user_customer_id FROM device d WHERE d.device_type_id = :#{#search.deviceTypeId}) AND :#{#search.type} = 3) OR
               (u.id IN (SELECT d.user_enterprise_id FROM device d WHERE d.device_type_id = :#{#search.deviceTypeId}) AND :#{#search.type} = 2)
               )
               AND
               ( :#{#search.forDevice} = 0 OR
               (u.id NOT IN (SELECT DISTINCT d.user_customer_id FROM device d WHERE d.user_customer_id is not null) AND :#{#search.forDevice} = 1) OR
               (u.id = :#{#search.editingUserIdForDevice} AND :#{#search.forDevice} = 1 )
               )
            """;
    public static final String COUNT_GET_PAGE_USER = " SELECT count(1) FROM (" + GET_PAGE_USER + ") tmp";

    public static final String GET_ONE_USER = """
        SELECT
              u.id,
              u.username AS username,
              u.TYPE AS TYPE,
              u.STATUS AS status,
              u.email AS email,
              u.phone,
              tmp.roleNames,
              tmp.roleIds,
              u.description,
              u.address_contact as addressContact,
              u.representative_name as representativeName,
              u.address_head_office as addressHeadOffice,
              u.tax_code as taxCode,
              u.name,
              u.province_code_office AS provinceCodeOffice,
              u.province_code_address AS provinceCodeAddress,
              u.ward_code_address AS wardCodeAddress,
              u.ward_code_office AS wardCodeOffice,
              p.name AS provinceOfficeName,
              p2.name AS provinceAddressName,
              w.name AS wardAddressName,
              u.apartment AS apartment,
              w2.name AS wardOfficeName,
              um.user_manage_id as userManageId
           FROM
              users u left join provinces p on u.province_code_office = p.code
                    left join provinces p2 on u.province_code_address = p2.code
                    left join wards w on u.ward_code_address = w.code
                    left join wards w2 on u.ward_code_office = w2.code
                    left join user_manage um on u.id = um.user_id
              left join
                  ( SELECT
                            users.id,
                            GROUP_CONCAT(role.name ORDER BY role.id SEPARATOR ',') AS roleNames,
                            GROUP_CONCAT(role.id ORDER BY role.id SEPARATOR ',') AS roleIds
                        FROM
                            users
                            LEFT JOIN user_role ON user_role.user_id = users.id
                            LEFT JOIN role ON role.id = user_role.role_id
                        GROUP BY users.id) tmp on tmp.id = u.id
           WHERE
              u.id = :id
        """;




    public static final String GET_PAGE_USER_CUSTOMER_NO_ONE_MANAGED = """
            SELECT
                            u.id,
                            u.username as username,
                            u.type as type,
                            u.email as email,
                            u.province_code as provinceCode,
                            pr.name as provinceName,
                            null as isHasChild,
                            null as isAssign,
                            u.status as status,
                            TO_CHAR(u.created_date, 'dd-mm-yyyy') as createdDate
                         FROM users u
                            left join province pr on u.province_code = pr.code
                         WHERE
                             ( :#{#search.username} = ' '  OR (UPPER(u.username) LIKE '%' || UPPER(:#{#search.username}) || '%' collate binary_ai )  )
                             AND u.status != -1 and u.created_by != 0
                             AND ((-1 != :#{#search.managerId} and (u.id in (SELECT user_id FROM user_manage where user_manage_id = :#{#search.managerId}) OR u.id not in (	select distinct um.user_id from user_manage um) ))
            										or ( -1 = :#{#search.managerId} AND u.id not in (select distinct um.user_id from user_manage um)))
                          
                             AND (
                        	    --- ADMIN
                        	    (  :type = 1  AND (:#{#search.provinceCode} = ' '  OR u.province_code = :#{#search.provinceCode}))
                        		--- Tỉnh thành phố
                        		OR ( :type = 2 AND u.PROVINCE_CODE = :provinceCode)
                        		OR (:type = 3 AND u.PROVINCE_CODE = :provinceCode)
                        	)
                             AND u.type = 7
                             AND length(u.HIERARCHICAL_RELATION) - length(replace(u.HIERARCHICAL_RELATION, '/','')) = 1
             """;
    public static final String COUNT_GET_PAGE_USER_CUSTOMER_NO_ONE_MANAGED = " SELECT count(1) FROM (" + GET_PAGE_USER_CUSTOMER_NO_ONE_MANAGED + ") tmp";

    public static final String GET_LST_USER_BY_USER_MANAGE = """
        SELECT
        	u.id
        FROM
        	users u
        , user_manage um
        WHERE
        	u.status not in (-1)
        	and u.id != :userId and um.user_manage_id = :userId and um.user_id = u.id
        """;

}
