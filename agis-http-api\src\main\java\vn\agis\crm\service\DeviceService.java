package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.*;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.*;
import vn.agis.crm.base.jpa.entity.Device;
import vn.agis.crm.base.jpa.entity.DeviceTelemetryRecord;
import vn.agis.crm.base.jpa.entity.FileEntity;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.io.IOException;
import java.util.*;

@Service
public class DeviceService extends CrudService<Device, Long> {
    private Logger logger = LoggerFactory.getLogger(DeviceService.class);
    private static final String objectKey = "Device";


    @Autowired
    private RestTemplate restTemplate;

    public DeviceService() {
        super(Device.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_DEVICE_MANAGEMENT;
        this.category = Constants.Category.DEVICE;
        this.restTemplate = new RestTemplate();
        MappingJackson2HttpMessageConverter converter = new MappingJackson2HttpMessageConverter();
        converter.setSupportedMediaTypes(
                Arrays.asList(
                        MediaType.APPLICATION_JSON,
                        new MediaType("application", "*+json"),
                        MediaType.APPLICATION_OCTET_STREAM));
        restTemplate.getMessageConverters().add(converter);
    }

    @Value("${google.api-key}")
    private String apiKey;

    public Page<SearchDeviceRespone> searchDevice(DeviceSearchDTO reqDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<SearchDeviceRespone> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DEVICE, category, reqDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchDeviceRespone.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), SearchDeviceRespone.class), pageable, pageInfo.getTotalCount());
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(reqDTO), ObjectMapperUtil.toJsonString(response), event);
        }
    }

    public Page<DeviceTelemetryRecord> searchTelemetry(SearchDeviceTelemetryReq searchDeviceTelemetryReq, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<DeviceTelemetryRecord> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DEVICE_TELEMETRY, category, searchDeviceTelemetryReq, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), DeviceTelemetryRecord.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(searchDeviceTelemetryReq), ObjectMapperUtil.toJsonString(response), event);
        }
    }

    public CheckMsisdnDevice checkExitMsisdn(Long msisdn) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        CheckMsisdnDevice response = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CHECK_MSISDN, category, msisdn, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (CheckMsisdnDevice) event.payload;
                return response;
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        } finally {
        }

    }

    public Long checkExitImei(String imei) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        Long response = 0L;
        try {
            event = RequestUtils.amqp(Constants.Method.CHECK_IMEI, category, imei, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Long) event.payload;
                return response;
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        } finally {
        }

    }

    public List<Long> getListMsisdnIsvalid(Long msi) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<Long> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.MSISDN_ISVALID, category, msi, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<Long>) event.payload;
                return response;
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        } finally {
        }
    }

    public List<IdUsageResponse> getUsageByDeviceId(List<Long> deviceIds) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<IdUsageResponse> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.GET_USAGE_BY_DEVICE_IDS, category, deviceIds, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = ObjectMapperUtil.listMapper((String) event.payload, IdUsageResponse.class);
                return response;
            } else {
            }
        } catch (Exception e) {
            logger.error("Error in getUsageByDeviceId: {}", e.getMessage(), e);
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(deviceIds), ObjectMapperUtil.toJsonString(response), event);
        }
        return null;
    }

    public List<IdUsageResponse> getUsageByUserId(List<Long> deviceIds) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<IdUsageResponse> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.GET_USAGE_BY_USER_IDS, category, deviceIds, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = ObjectMapperUtil.listMapper((String) event.payload, IdUsageResponse.class);
                return response;
            } else {
            }
        } catch (Exception e) {
            logger.error("Error in getUsageByDeviceId: {}", e.getMessage(), e);
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(deviceIds), ObjectMapperUtil.toJsonString(response), event);
        }
        return null;
    }


    public FileEntity uploadImageDevice(MultipartFile file) throws IOException {
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Constants.Method.UPLOAD_IMAGE_DEVICE, category, file.getBytes(), routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                return (FileEntity) event.payload;
            } else {
            }
        } catch (Exception e) {
            logger.error("Error in uploadImageDevice: {}", e.getMessage(), e);
        } finally {
        }
        return null;
    }

    public FileEntity getFile(Long id) {
        Event event = new Event();
        try {
            event = RequestUtils.amqp(Constants.Method.GET_IMAGE_DEVICE, category, id, routingKey);
            return (FileEntity) event.payload;
        } catch (Exception e) {
            logger.error("Error in getFileImageDevice: {}", e.getMessage(), e);
        } finally {
        }
        return null;
    }


    @Override
    void writeLog(long timeRequest, long timeResponse, String request, String response, Event event) {
//        TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                request, response, null, null, event);
    }

    @Override
    void writeLogDetail(Long id, Device entity, long timeRequest, long timeResponse, String request, String response, Event event) {
//        TransactionLogger.writeLogITrans(id, entity != null? entity.getDeviceName() : null, objectKey, null, timeRequest, timeResponse,
//                request, response, null, null, event);
    }


    //    @Override
//    void writeLog(Event event) {
//        ITrans iTrans = new ITrans();
//        iTrans.setUserName(String.valueOf(event.userId));
//        iTrans.setUserType(event.userType);
//        iTrans.setMethodName(event.method);
//        iTrans.setObjectKey(getObjectKeyAPN("getDevice"));
//        iTrans.setEventId(event.id);
//        iTrans.setRequestTime(event.timeStamp.getTime());
//        iTrans.setResponseTime(System.currentTimeMillis());
//        iTrans.setResponseCode(event.respStatusCode);
//        iTrans.setErrorDesc(event.respErrorDesc);
//        iTrans.setProvinceCode(event.provinceCode);
//        iTrans.setCreatedDate(new Date());
//        TransactionLogger.getITransLog(iTrans);
//    }
//
//    @Cacheable(value="device", key="#device")
//    public String getObjectKeyAPN(String device){
//        List<Permission> permissions = permissionService.getAllPermission();
//        for (Permission p : permissions){
//            if (p.getPermissionKey().contains(device)){
//                return p.getObjectKey();
//            }
//        }
//        return null;
//    }
//    public Location getLatLng(Long loc, Long cell, Long mcc, Long mnc) {
//        try {
//            CellTower cellTower = new CellTower(cell, loc, mcc, mnc);
//            List<CellTower> cellTowers = new ArrayList<>();
//            cellTowers.add(cellTower);
//            GoogleLocationRequest googleLocationRequest = new GoogleLocationRequest(cellTowers);
//
//            HttpHeaders headers = new HttpHeaders();
//            headers.setContentType(MediaType.APPLICATION_JSON);
//
//            String body = ObjectMapperUtil.toJsonString(googleLocationRequest);
//
//            HttpEntity<?> requestEntity =
//                    new HttpEntity<>(body, headers);
//            String url = "https://www.googleapis.com/geolocation/v1/geolocate?key=" + apiKey;
//            ResponseEntity<GoogleLocationResponse> response = restTemplate.exchange(url, HttpMethod.POST, requestEntity, GoogleLocationResponse.class);
//            if (response.getStatusCode().is2xxSuccessful()) {
//                GoogleLocationResponse googleLocationResponse = response.getBody();
//                Location location = googleLocationResponse.getLocation();
//                return location;
//            }
//            return null;
//        } catch (Exception e) {
//            logger.error("error googleGetLocation", e);
//            return null;
//        }
//    }
}
