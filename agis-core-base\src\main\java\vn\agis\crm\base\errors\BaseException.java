package vn.agis.crm.base.errors;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false)
public class BaseException extends RuntimeException {
    private final String entityName;

    private final List<String> field;

    private final String title;

    private final String errorCode;

    public BaseException(String title, String entityName,  List<String>  field, String errorCode) {
        super();
        this.entityName = entityName;
        this.field = field;
        this.title = title;
        this.errorCode = errorCode;
    }
}
