package vn.agis.crm.base.jpa.dto.req;

import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Setter
@Getter
public class UpdateUserReq {
    private Long id;
    private String name;
    private String description;
    private String email;
    private String phone;
    private Long idManager;
    private List<Long> roleLst;
    private String taxCode;
    private String addressHeadOffice;
    private String representativeName;
    private String addressContact;
    private Long accountRootId;
    private Integer provinceCodeOffice;
    private Integer wardCodeOffice;
    private Integer provinceCodeAddress;
    private Integer wardCodeAddress;
    private Integer status;
    private String password;
    private Integer apartment;
}
