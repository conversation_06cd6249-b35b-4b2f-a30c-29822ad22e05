package vn.agis.crm.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;
import vn.agis.crm.service.NotificationService;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Scheduled job to automatically send lead inactive warning notifications
 * Runs daily at 8:00 AM Vietnam timezone to notify employees about leads
 * that haven't had any interactions for a configurable number of days
 */
@Component
public class LeadInactiveWarningNotificationScheduler {

    private static final Logger logger = LoggerFactory.getLogger(LeadInactiveWarningNotificationScheduler.class);
    private static final String TIMEZONE_VIETNAM = "Asia/Ho_Chi_Minh";
    private static final String CONFIG_KEY_INACTIVE_DAYS = "NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * Main scheduled method that runs daily at 8:00 AM Vietnam time
     */
    @Scheduled(cron = "0 0 8 * * *", zone = TIMEZONE_VIETNAM)
    public void processLeadInactiveWarningNotifications() {
        logger.info("⏰ Starting lead inactive warning notification job");

        try {
            // Step 1: Get configuration for inactive warning days
            Integer inactiveDays = getInactiveDaysFromConfig();
            if (inactiveDays == null) {
                logger.info("Lead inactive warning notifications disabled (no configuration found)");
                return;
            }

            logger.info("Processing lead inactive warnings for leads inactive for {} days", inactiveDays);

            // Step 2: Find inactive leads
            List<InactiveLeadInfo> inactiveLeads = findInactiveLeads(inactiveDays);
            logger.info("Found {} inactive leads", inactiveLeads.size());

            if (inactiveLeads.isEmpty()) {
                logger.info("No inactive leads found");
                return;
            }

            // Step 3: Process each inactive lead
            int totalProcessed = 0;
            int successCount = 0;
            int errorCount = 0;

            for (InactiveLeadInfo leadInfo : inactiveLeads) {
                totalProcessed++;
                boolean success = processInactiveLeadNotification(leadInfo, inactiveDays);
                if (success) {
                    successCount++;
                } else {
                    errorCount++;
                }
            }

            logger.info("⏰ Lead inactive warning job completed. Processed: {}, Success: {}, Errors: {}",
                       totalProcessed, successCount, errorCount);

        } catch (Exception e) {
            logger.error("Error in lead inactive warning notification job: {}", e.getMessage(), e);
        }
    }

    /**
     * Get inactive warning days configuration from database
     * @return Number of days for inactive warning, or null if disabled/invalid
     */
    private Integer getInactiveDaysFromConfig() {
        try {
            Config config = configRepository.findOneByConfigKeyIgnoreCase(CONFIG_KEY_INACTIVE_DAYS);
            if (config == null || config.getConfigValue() == null || config.getConfigValue().trim().isEmpty()) {
                return null; // Configuration not found or empty
            }

            String configValue = config.getConfigValue().trim();
            int days = Integer.parseInt(configValue);

            // Validate range (1-365 days)
            if (days < 1 || days > 365) {
                logger.warn("Invalid inactive days configuration: {}. Must be between 1-365", days);
                return null;
            }

            logger.debug("Lead inactive warning days configuration: {}", days);
            return days;

        } catch (NumberFormatException e) {
            logger.warn("Invalid inactive days configuration format: {}. Must be a valid integer", 
                       0);
            return null;
        } catch (Exception e) {
            logger.error("Error reading inactive days configuration: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find leads that have been inactive for the specified number of days
     * @param inactiveDays Number of days to consider as inactive
     * @return List of inactive lead information
     */
    private List<InactiveLeadInfo> findInactiveLeads(int inactiveDays) {
        try {
            // Calculate cutoff date (inactiveDays ago)
            LocalDate cutoffLocalDate = LocalDate.now(ZoneId.of(TIMEZONE_VIETNAM)).minusDays(inactiveDays);
            Date cutoffDate = Date.from(cutoffLocalDate.atStartOfDay(ZoneId.of(TIMEZONE_VIETNAM)).toInstant());

            logger.debug("Looking for leads inactive since: {}", cutoffLocalDate.format(DATE_FORMATTER));

            // Find all active lead customers
            List<Customers> leadCustomers = findActiveLeadCustomers();
            logger.debug("Found {} active lead customers", leadCustomers.size());

            List<InactiveLeadInfo> inactiveLeads = new ArrayList<>();

            for (Customers customer : leadCustomers) {
                InactiveLeadInfo leadInfo = checkLeadInactivity(customer, cutoffDate);
                if (leadInfo != null) {
                    inactiveLeads.add(leadInfo);
                }
            }

            return inactiveLeads;

        } catch (Exception e) {
            logger.error("Error finding inactive leads: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Find all active lead customers
     * @return List of active lead customers
     */
    private List<Customers> findActiveLeadCustomers() {
        try {
            // Use existing search method to find leads
            return customerRepository.search(
                null, // fullName
                null, // phone
                null, // email
                null, // cccd
                null, // address
                "Leads", // sourceType - only leads
                null, // sourceDetail
                null, // businessField
                null, // interests
                null, // relativeName
                null, // birthDateFrom
                null, // birthDateTo
                null, // birthdayDay
                null, // birthdayMonth
                null, // projectId
                null, // purchasedProjectId
                null, // activeOfferProjectId
                null, // propertyType
                null, // employeeId
                org.springframework.data.domain.PageRequest.of(0, 10000)
            ).getContent().stream()
            .filter(customer -> customer.getDeletedAt() == null) // Only active customers
            .toList();

        } catch (Exception e) {
            logger.error("Error finding active lead customers: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Check if a lead customer is inactive based on last interaction or creation date
     * @param customer Customer to check
     * @param cutoffDate Cutoff date for inactivity
     * @return InactiveLeadInfo if inactive, null if active
     */
    private InactiveLeadInfo checkLeadInactivity(Customers customer, Date cutoffDate) {
        try {
            // Find the last interaction date for this customer
            Date lastInteractionDate = findLastInteractionDate(customer);
            
            // Determine reference date: last interaction or creation date
            Date referenceDate = lastInteractionDate != null ? lastInteractionDate : customer.getCreatedAt();
            
            if (referenceDate == null) {
                logger.warn("Customer {} has no creation date, skipping", customer.getId());
                return null;
            }

            // Check if reference date is before cutoff (inactive)
            if (referenceDate.before(cutoffDate)) {
                // Find assigned employee for notification
                Long assignedEmployeeId = findAssignedEmployeeId(customer);
                if (assignedEmployeeId == null) {
                    logger.debug("Customer {} has no assigned employee, skipping notification", customer.getId());
                    return null;
                }

                logger.debug("Customer {} is inactive since {}", customer.getId(), referenceDate);
                return new InactiveLeadInfo(customer, assignedEmployeeId, referenceDate, lastInteractionDate != null);
            }

            return null; // Customer is active

        } catch (Exception e) {
            logger.error("Error checking inactivity for customer {}: {}", customer.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find the last interaction date for a customer (primary or secondary)
     * @param customer Customer to check
     * @return Last interaction date, or null if no interactions
     */
    private Date findLastInteractionDate(Customers customer) {
        try {
            Date lastInteractionDate = null;

            // Check primary interactions (through customer_offers)
            List<CustomerOffers> customerOffers = customerOfferRepository.findByCustomerId(customer.getId());
            for (CustomerOffers offer : customerOffers) {
                List<InteractionsPrimary> primaryInteractions = interactionsPrimaryRepository.findByCustomerOfferId(offer.getId());
                for (InteractionsPrimary interaction : primaryInteractions) {
                    if (interaction.getHappenedAt() != null) {
                        if (lastInteractionDate == null || interaction.getHappenedAt().after(lastInteractionDate)) {
                            lastInteractionDate = interaction.getHappenedAt();
                        }
                    }
                }
            }

            // Check secondary interactions (through customer_properties)
            List<CustomerProperties> customerProperties = customerPropertyRepository.findByCustomerId(customer.getId());
            for (CustomerProperties property : customerProperties) {
                List<InteractionsSecondary> secondaryInteractions = interactionsSecondaryRepository.findByCustomerPropertyId(property.getId());
                for (InteractionsSecondary interaction : secondaryInteractions) {
                    if (interaction.getHappenedAt() != null) {
                        if (lastInteractionDate == null || interaction.getHappenedAt().after(lastInteractionDate)) {
                            lastInteractionDate = interaction.getHappenedAt();
                        }
                    }
                }
            }

            return lastInteractionDate;

        } catch (Exception e) {
            logger.error("Error finding last interaction date for customer {}: {}", customer.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find assigned employee ID for a customer (staff or manager)
     * @param customer Customer to check
     * @return Employee ID or null if no assignment
     */
    private Long findAssignedEmployeeId(Customers customer) {
        try {
            // Priority 1: Current Staff
            if (customer.getCurrentStaffId() != null) {
                Employee staff = employeeRepository.findById(customer.getCurrentStaffId()).orElse(null);
                if (staff != null && staff.getStatus() == Employee.Status.active && staff.getDeletedAt() == null) {
                    return staff.getId();
                }
            }

            // Priority 2: Current Manager
            if (customer.getCurrentManagerId() != null) {
                Employee manager = employeeRepository.findById(customer.getCurrentManagerId()).orElse(null);
                if (manager != null && manager.getStatus() == Employee.Status.active && manager.getDeletedAt() == null) {
                    return manager.getId();
                }
            }

            return null; // No active assigned employee found

        } catch (Exception e) {
            logger.error("Error finding assigned employee for customer {}: {}", customer.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Process inactive lead notification for a single lead
     * @param leadInfo Inactive lead information
     * @param inactiveDays Number of inactive days from config
     * @return true if notification was created, false otherwise
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processInactiveLeadNotification(InactiveLeadInfo leadInfo, int inactiveDays) {
        try {
            // Step 1: Validate employee
            Employee employee = employeeRepository.findById(leadInfo.getAssignedEmployeeId()).orElse(null);
            if (employee == null || employee.getStatus() != Employee.Status.active || employee.getDeletedAt() != null) {
                logger.warn("Employee {} for customer {} is not active, skipping notification", 
                           leadInfo.getAssignedEmployeeId(), leadInfo.getCustomer().getId());
                return false;
            }

            // Step 2: Create notification content
            String title = "Cảnh báo lead không hoạt động";
            String content = createInactiveLeadNotificationContent(leadInfo, inactiveDays);

            // Step 3: Create notification
            Notifications notification = notificationService.createNotification(
                employee.getId(),
                4, // LeadInactiveWarning type
                title,
                content,
                leadInfo.getCustomer().getId(),
                null // System-generated notification
            );

            logger.debug("Created inactive lead notification {} for employee {} about customer {} ({})", 
                        notification.getId(), employee.getId(), leadInfo.getCustomer().getId(), leadInfo.getCustomer().getFullName());

            return true;

        } catch (Exception e) {
            logger.error("Error processing inactive lead notification for customer {}: {}", 
                        leadInfo.getCustomer().getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Create notification content for inactive lead warning
     * @param leadInfo Inactive lead information
     * @param inactiveDays Number of inactive days from config
     * @return Formatted notification content
     */
    private String createInactiveLeadNotificationContent(InactiveLeadInfo leadInfo, int inactiveDays) {
        try {
            Customers customer = leadInfo.getCustomer();
            
            // Format reference date
            LocalDate referenceLocalDate = leadInfo.getReferenceDate().toInstant()
                .atZone(ZoneId.of(TIMEZONE_VIETNAM))
                .toLocalDate();
            String formattedDate = referenceLocalDate.format(DATE_FORMATTER);

            // Create content based on whether customer has had interactions
            if (leadInfo.isHasInteractions()) {
                // Customer has interactions - reference last interaction date
                return String.format(
                    "Lead %s (SĐT: %s) đã không có tương tác nào từ ngày %s (%d ngày). ",
                    customer.getFullName() != null ? customer.getFullName() : "N/A",
                    customer.getPhone() != null ? customer.getPhone() : "N/A",
                    formattedDate,
                    inactiveDays
                );
            } else {
                // Customer has no interactions - reference creation date
                return String.format(
                    "Lead %s (SĐT: %s) được tạo từ ngày %s (%d ngày) nhưng chưa có tương tác nào. ",
                    customer.getFullName() != null ? customer.getFullName() : "N/A",
                    customer.getPhone() != null ? customer.getPhone() : "N/A",
                    formattedDate,
                    inactiveDays
                );
            }

        } catch (Exception e) {
            logger.error("Error creating notification content for customer {}: {}", 
                        leadInfo.getCustomer().getId(), e.getMessage(), e);
            return String.format(
                "Lead %s đã không hoạt động trong thời gian dài.",
                leadInfo.getCustomer().getFullName() != null ? leadInfo.getCustomer().getFullName() : "N/A"
            );
        }
    }

    /**
     * Inner class to hold inactive lead information
     */
    private static class InactiveLeadInfo {
        private final Customers customer;
        private final Long assignedEmployeeId;
        private final Date referenceDate;
        private final boolean hasInteractions;

        public InactiveLeadInfo(Customers customer, Long assignedEmployeeId, Date referenceDate, boolean hasInteractions) {
            this.customer = customer;
            this.assignedEmployeeId = assignedEmployeeId;
            this.referenceDate = referenceDate;
            this.hasInteractions = hasInteractions;
        }

        public Customers getCustomer() { return customer; }
        public Long getAssignedEmployeeId() { return assignedEmployeeId; }
        public Date getReferenceDate() { return referenceDate; }
        public boolean isHasInteractions() { return hasInteractions; }
    }
}
