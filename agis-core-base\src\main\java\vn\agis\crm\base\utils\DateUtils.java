package vn.agis.crm.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

/**
 * Created by tiemnd on 12/25/2017.
 */
public class DateUtils {

    public static final String FORM_ISODATE = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    public static final String FORM_DATE_READABLE = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_DATE_DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
    public static final String FORMAT_DATE_DD_MM_YYYY_SLASH = "dd/MM/yyyy";
    public static final String TIME_ZONE = "Asia/Ho_Chi_Minh";
    private static final Logger logger = LoggerFactory.getLogger(DateUtils.class);

    public static Date convertIsoDateToDate(String isoDate) {
        DateFormat df1 = new SimpleDateFormat(FORM_ISODATE);
        DateFormat df2 = new SimpleDateFormat(FORM_DATE_READABLE);
        Date result = null;
        try {
            result = df1.parse(isoDate);
            String dateFormated = df2.format(result);
            return df2.parse(dateFormated);

        } catch (ParseException e) {
            logger.error("{}", e);
        }
        return result;
    }

    public static Date convertDateStringToDate(String date, String formDate) {
        DateFormat dateFormat = new SimpleDateFormat(formDate);
        try {
            return dateFormat.parse(date);
        } catch (ParseException e) {
            logger.error("{}", e);
        }
        return null;
    }

    public static String convertIsoDateToString(String isoDate) {
        DateFormat df1 = new SimpleDateFormat(FORM_ISODATE);
        DateFormat df2 = new SimpleDateFormat(FORM_DATE_READABLE);
        Date result = null;
        try {
            result = df1.parse(isoDate);
            return df2.format(result);
        } catch (ParseException e) {
            logger.error("{}", e);
        }
        return "";
    }

    public static long convertCurrentDateToLong() {
        Calendar cal = Calendar.getInstance();
        return cal.getTimeInMillis();
    }

    public static Date parseDateToIso(String dateStr) throws ParseException {
        SimpleDateFormat dt = new SimpleDateFormat(FORM_ISODATE);
        dt.setTimeZone(TimeZone.getTimeZone("GMT+0"));
        return dt.parse(dateStr);
    }

    public static String getCurrentIsodate() {
        SimpleDateFormat dt = new SimpleDateFormat(FORM_ISODATE);
        Date date = new Date(System.currentTimeMillis());
        return dt.format(date);
    }

    // Khanh Nguyen start

    public static String now() {
        String dateTime = System.getProperty("subsNnotif.ultis.DateUtils.time", "yyyy-MM-dd' T 'HH:mm:ss");
        return new SimpleDateFormat(dateTime).format(new Date()).toString();
    }

    // Khanh Nguyen end
    public static String convertDateToStrIsoDate(Date date) {
        SimpleDateFormat dt = new SimpleDateFormat(FORM_ISODATE);
        return dt.format(date);
    }

    public static Date convertUTCStringToUTCDate(String utcDate) {
        String pattern = "yyyyMMdd'T'HHmmss";
        DateFormat df = new SimpleDateFormat(pattern);
        try {
            return df.parse(utcDate);
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String convertUTCDatetoUTCString(Date date) {
        String pattern = "yyyyMMdd'T'HHmmss";
        DateFormat df = new SimpleDateFormat(pattern);
        String dateAsString = df.format(date);
        return dateAsString;
    }

    public static String convertLongToStrDate(Long time) {
        Date date = new Date(time);
        SimpleDateFormat dt = new SimpleDateFormat(FORM_ISODATE);
        return dt.format(date);
    }

    public static Long convertUTCStrToLong(String isoDate) {
        DateFormat df1 = new SimpleDateFormat(FORM_ISODATE);
        Date result = null;
        try {
            result = df1.parse(isoDate);
        } catch (ParseException e) {
            logger.error("{}", e);
        }
        return result.getTime();
    }

    public static Long nowByLong() {
        return System.currentTimeMillis();
    }

    public static Long startOfCurrentDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTimeInMillis();
    }

    public static Long endOfCurrentDay() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTimeInMillis();
    }

    public static Long startOfCurrentMonth() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTimeInMillis();
    }

    public static Long endOfCurrentMonth() {
        Calendar c = Calendar.getInstance();
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTimeInMillis();
    }


    public static Long startOfDay(Long timeInMillis) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timeInMillis);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTimeInMillis();
    }

    public static Long endOfDay(Long timeInMillis) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timeInMillis);
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTimeInMillis();
    }

    public static Long startOfMonth(Long timeInMillis) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timeInMillis);
        c.set(Calendar.DAY_OF_MONTH, 1);
        c.set(Calendar.HOUR_OF_DAY, 0);
        c.set(Calendar.MINUTE, 0);
        c.set(Calendar.SECOND, 0);
        c.set(Calendar.MILLISECOND, 0);
        return c.getTimeInMillis();
    }

    public static Long endOfMonth(Long timeInMillis) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(timeInMillis);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        c.set(Calendar.HOUR_OF_DAY, 23);
        c.set(Calendar.MINUTE, 59);
        c.set(Calendar.SECOND, 59);
        c.set(Calendar.MILLISECOND, 999);
        return c.getTimeInMillis();
    }
    public static String getYYYYMMDD(int daysToAdd) {
//        LocalDate currentDate = LocalDate.now();
//
//        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
//        String formattedDate = currentDate.format(formatter);
//        return formattedDate;
        // Lấy ngày hiện tại
        Calendar calendar = Calendar.getInstance();

        // Thêm số ngày vào ngày hiện tại
        calendar.add(Calendar.DAY_OF_MONTH, daysToAdd);
        Date newDate = calendar.getTime();

        // Định dạng ngày mới thành chuỗi YYYYMMDD
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        String formattedDate = dateFormat.format(newDate);
        return formattedDate;
    }

    public static String convertToDDMMYYYY(String rawDate, String separator, String separatorResult) {
        String day = "", month = "", year = "";
        if (StringUtils.isEmpty(rawDate)) return rawDate;
        else {
            String[] s = rawDate.split(separator);
            if (s.length == 3) {
                if (s[0].length() == 2) {
                    day = s[0];
                } else if (s[0].length() == 1) {
                    day = "0" + s[0];
                } else return rawDate;

                if (s[1].length() == 2) {
                    month = s[1];
                } else if (s[1].length() == 1) {
                    month = "0" + s[1];
                } else return rawDate;

                if (s[2].length() == 4) {
                    year = s[2];
                } else return rawDate;

                return day + separatorResult + month + separatorResult + year;
            } else return rawDate;
        }
    }
}
