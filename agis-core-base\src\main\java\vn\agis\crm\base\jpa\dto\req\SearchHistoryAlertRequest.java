package vn.agis.crm.base.jpa.dto.req;


import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class SearchHistoryAlertRequest {

    private Integer eventType;
    private Long deviceId;
    private Long userId;
    private String fromDate;
    private String toDate;
    private Integer page;
    private Integer size;
    private String sortBy;

    public SearchHistoryAlertRequest(Long deviceId, Long userId,Integer eventType, Long fromDate, Long toDate, Integer page, Integer size, String sortBy) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        this.eventType = eventType;
        this.deviceId = deviceId;
        this.userId = userId;
        this.fromDate = Objects.isNull(fromDate) ? " " : formatter.format(new Date(fromDate));
        this.toDate = Objects.isNull(toDate) ? " " : formatter.format(new Date(toDate));
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
