app:
  config:
    max-execute-size: 5
    module-prefix: coremngt
  upload:
    dir: ./uploads
server:
  port: 8089
  servlet:
    context-path: /crm
spring:
  rabbitmq:
    addresses: localhost
    port: 5672
    username: admin
    password: admin123
  datasource:
    jdbc-url: ************************************
    url: ************************************
    username: root
    password: haiha

#    jdbc-url: *************************************************
#    url: *************************************************
#    username: agis_crm
#    password: agis@123321

    driver-class-name: com.mysql.jdbc.Driver
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    database-platform: org.hibernate.dialect.MariaDBDialect
    use-new-id-generator-mappings: false
    show-sql: true
    hibernate:
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none
  redis:
    mode: Standalone  # Change mode to Standalone
    standalone:
      host: localhost   # 🔹 service name trong docker-compose (or the actual host if not running in Docker)
      port: 6379
      username:  # Add username if your Redis instance requires authentication
      password:  # Add password if your Redis instance requires authentication
    cluster:
      nodes:  # Remove or comment out the cluster configuration
      username:
      password:
    sentinel:
      password: # Remove or comment out the sentinel configuration
      master: mymaster
      nodes: localhost:26379
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
  mail:
    host: mail.vnpt-technology.vn
    port: 25
    username: oneiot
    password: One@2024%^&
    protocol: smtp
    #    properties:
    #      mail.smtp.auth: false
    #      mail.smtp.ssl.enable: false
    #      mail.smtp.starttls.enable: false
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: -1
jhipster:
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: <EMAIL>
    base-url: http://**********:9090
ccbs:
  url-api:
application:
  activation:
    expirePeriodActiveMail: 2592000 #seconds - 30days
    expirePeriodResetPassword: 86400 #seconds - 1day
    enableMail: true #true/false
  timeAfterSoftDelete: 2592000
  tokenTime:
    remember: 2592000 #seconds - 30days
    noRemember: 1800 # seconds - 0.5h
baseUrl: http://localhost:9090
#nodeName: ism-core
folder:
  storage: ./storage
minio:
  url: http://127.0.0.1:9000
  access-key: minioadmin
  secret-key: minioadmin
  folder-name: /
monitoringEmailList: <EMAIL>,<EMAIL> #dsach mail nhận ngăn cách bởi dấu phẩy
schedule:
  auto-share: 0 0 8 * * *
#  auto-share: * * * * * *
oauth2Url: http://************:8080/oauth2/token # url api oauth2 login của iot
oauth2TenantUrl: http://************:8080/api/tenants/cmp # url api create tenant cho cmp của iot
oauth2UserUrl: http://************:8080/api/users/oauth2 # url api create/put user xác thực bằng oauth2 của iot
numberShare: 50
timeoutShare: 300000 #5 phut
openai:
  api:
    key: ***********************************************************************************************************************************************************************