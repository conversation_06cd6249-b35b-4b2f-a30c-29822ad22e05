package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.ImportTemplateDto;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;

@Service
public class ImportTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ImportTemplateService.class);

    @Value("${app.template.dir:template}")
    private String templateDir;

    private static final String TEMPLATE_FILENAME = "DATA-FORMAT-V2.csv";

    /**
     * Process incoming events
     */
    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.GET_IMPORT_TEMPLATE:
                return getTemplate(event);
            default:
                return event.createResponse(null, 404, "Method not found");
        }
    }

    /**
     * Get the import template file
     */
    private Event getTemplate(Event event) {
        try {
            String templatePath = templateDir + File.separator + TEMPLATE_FILENAME;

            if (!Files.exists(Paths.get(templatePath))) {
                logger.error("Template file not found: " + templatePath);
                return event.createResponse(null, 404, "Template file not found");
            }

            Resource resource = new FileSystemResource(templatePath);

            // Create response with template info
            ImportTemplateDto dto = new ImportTemplateDto();
            dto.setResource(resource);
            dto.setFilename(TEMPLATE_FILENAME);
            dto.setContentType("text/csv");

            return event.createResponse(dto, ResponseCode.OK, "Success");

        } catch (Exception e) {
            logger.error("Error accessing template file", e);
            return event.createResponse(null, 500, "Error accessing template file");
        }
    }
}
