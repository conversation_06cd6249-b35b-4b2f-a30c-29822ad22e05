package vn.agis.crm.base.utils;

import org.apache.commons.lang3.BooleanUtils;
import vn.agis.crm.base.constants.CycleTypeEnum;
import vn.agis.crm.base.exception.type.BadRequestException;

import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class DateUtil {
    public static final String FORMAT_DATE_DD_MM_YYYY_HH_MM = "dd/MM/yyyy HH:mm";
    public static final String FORMAT_DATE_DD_MM_YY_HH_MM_SS = "dd/MM/yy HH:mm:ss";
    public static final String FORMAT_DATE_DD_MM_YYYY_HH_MM_SS = "dd/MM/yyyy HH:mm:ss";
    public static final String FORMAT_DATE_YYYY_MMDD_HHMM = "yyyyMMddHHmm";
    public static final String FORMAT_DATE_YYYY_MMDD_HHMMSS = "yyyyMMddHHmmss";
    public static final String FORMAT_DATE_DD_MM_YYYY_HH_MM_A = "dd/MM/yyyy hh:mma";
    public static final ZoneId ZONE_ID = ZoneId.of("UTC");
    public static final ZoneId ICT_ZONE_ID = ZoneId.of("Asia/Jakarta");
    public static final ZoneId SYS_ZONE_ID = ZoneId.systemDefault();
    public static final Clock offsetClockICT = Clock.offset(Clock.systemUTC(), Duration.ofHours(7L));
    public static final String FORMAT_DATE_DD_MM_YYYY = "ddMMyyyy";
    public static final String FORMAT_DATE_YYYY_MM_DD = "yyyyMMdd";
    public static final String FORMAT_DATE_DD_MM_YYYY_SLASH = "dd/MM/yyyy";
    public static final String FORMAT_DATE_DD_MM_SLASH = "dd/MM";
    public static final String TIME_ZONE = "Asia/Ho_Chi_Minh";
    public static final String FORMAT_DATE_YYYY_MM_DD_T_HH_MM_SS = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String FORMAT_DATE_YYYY_MM_DD_T_HH_MM_SS_SSSZ = "yyyy-MM-dd'T'HH:mm:ss.SSSZ";
    public static final String FORMAT_DATE_FILE_DD_MM_YYYY_HH_MM_SS = "ddMMyyyyHHmmss";
    public static final String FORMAT_DD_MM_YYYY = "dd-MM-yyyy";
    public static final String FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
    public static final String SEARCH_START_DATE_SUFFIX = " 00:00:00";
    public static final String SEARCH_END_DATE_SUFFIX = " 23:59:59";
    public static final int DATE_OF_WEEK = 7;
    public static final String FORMAT_DATE_MM_YYYY = "MM/uuuu";
    public static final String FORMAT_DATE_YYYY_MM_DD_2 = "YYYY/MM/dd";

    public DateUtil() {
    }

    public static LocalDateTime getCurrentDateTime() {
        long timestamp = System.currentTimeMillis();
        return convertLongToLdtUTC(timestamp);
    }

    public static LocalDateTime getCurrentDateTimeICT() {
        long timestamp = System.currentTimeMillis();
        return convertLongToLdtICT(timestamp);
    }

    public static LocalDate getCurrentDateICT() {
        long timestamp = System.currentTimeMillis();
        return convertLongToLdICT(timestamp);
    }

    public static Long getCurrentMillis() {
        return System.currentTimeMillis();
    }

    public static Long convertLdtToLongUTC(LocalDateTime ldt) {
        return ldt == null ? null : ldt.atZone(ZONE_ID).toInstant().toEpochMilli();
    }

    public static Long convertLdtToLongICT(LocalDateTime ldt) {
        return ldt == null ? null : ldt.atZone(ICT_ZONE_ID).toInstant().toEpochMilli();
    }

    public static Long convertLdtToLong(LocalDateTime ldt) {
        return ldt == null ? null : ldt.atZone(SYS_ZONE_ID).toInstant().toEpochMilli();
    }

    public static LocalDateTime convertLongToLdtUTC(Long longDateTime) {
        return longDateTime == null ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(longDateTime), ZONE_ID);
    }

    public static LocalDate convertLongToLdICT(Long epochMilli) {
        return epochMilli == null ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ICT_ZONE_ID).toLocalDate();
    }

    public static LocalDateTime convertLongToLdtICT(Long epochMilli) {
        return epochMilli == null ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), ICT_ZONE_ID);
    }

    public static LocalDateTime convertLongToLdt(Long epochMilli) {
        return epochMilli == null ? null : LocalDateTime.ofInstant(Instant.ofEpochMilli(epochMilli), SYS_ZONE_ID);
    }

    public static boolean isFuture(Long longDateTime) {
        if (longDateTime == null) {
            throw new IllegalArgumentException();
        } else {
            return getCurrentMillis() < longDateTime;
        }
    }

    public static boolean isPast(Long longDateTime) {
        return !isFuture(longDateTime);
    }

    public static boolean isFuture(LocalDateTime datetime) {
        if (datetime == null) {
            throw new IllegalArgumentException();
        } else {
            return datetime.isAfter(getCurrentDateTime());
        }
    }

    public static boolean isPast(LocalDateTime datetime) {
        return !isFuture(datetime);
    }

    public static boolean isFutureInICT(LocalDateTime datetime) {
        if (datetime == null) {
            throw new IllegalArgumentException();
        } else {
            return datetime.isAfter(getCurrentDateTimeICT());
        }
    }

    public static boolean isPastInICT(LocalDateTime datetime) {
        return !isFutureInICT(datetime);
    }

    public static LocalDateTime convertUtcToIct(LocalDateTime utcDateTime) {
        if (utcDateTime == null) {
            throw new IllegalArgumentException();
        } else {
            ZonedDateTime utc = utcDateTime.atZone(ZONE_ID);
            return utc.withZoneSameInstant(ICT_ZONE_ID).toLocalDateTime();
        }
    }

    public static LocalDateTime convertIctToUtc(LocalDateTime ictDateTime) {
        if (ictDateTime == null) {
            throw new IllegalArgumentException();
        } else {
            ZonedDateTime utc = ictDateTime.atZone(ICT_ZONE_ID);
            return utc.withZoneSameInstant(ZONE_ID).toLocalDateTime();
        }
    }

    public static long subDay(Date d1, Date d2) {
        LocalDate day1 = d1.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate day2 = d2.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Duration diff = Duration.between(day1.atStartOfDay(), day2.atStartOfDay());
        return diff.toDays();
    }

    public static LocalDateTime convertUtilDateToLocalDateTime(Date dateToConvert) {
        return LocalDateTime.ofInstant(dateToConvert.toInstant(), ZoneId.systemDefault());
    }

    public static Instant getCurrentInstantUTC() {
        return Instant.now();
    }

    public static Instant getCurrentInstantICT() {
        return Instant.now(offsetClockICT);
    }

    public static Date getFirstDateOfMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getLastMonthLastDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -1);
        int max = calendar.getActualMaximum(5);
        calendar.set(5, max);
        return calendar.getTime();
    }

    public static Date getMonthBeforeFirstDate() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -1);
        int min = calendar.getActualMinimum(5);
        calendar.set(5, min);
        return calendar.getTime();
    }

    public static Date getFinalDayOfMonthDate(Integer month) {
        Calendar calendar = Calendar.getInstance();
        if (-1 != month) {
            calendar.set(2, month - 1);
            int lastDate = calendar.getActualMaximum(5);
            calendar.set(5, lastDate);
        }

        return calendar.getTime();
    }

    public static Date getLastDayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(10, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        calendar.add(5, -1);
        calendar.set(11, 0);
        return calendar.getTime();
    }

    public static Date getLastDayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(11, 23);
        calendar.set(12, 59);
        calendar.set(13, 59);
        calendar.add(5, -1);
        return calendar.getTime();
    }

    public static Date getLastTwoDayStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(10, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        calendar.add(5, -2);
        calendar.set(11, 0);
        return calendar.getTime();
    }

    public static Date getLastTwoDayEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(11, 23);
        calendar.set(12, 59);
        calendar.set(13, 59);
        calendar.add(5, -2);
        return calendar.getTime();
    }

    public static Date getFirstMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -1);
        calendar.set(5, 1);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return calendar.getTime();
    }

    public static Date getLastMonthEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -1);
        int max = calendar.getActualMaximum(5);
        calendar.set(5, max);
        calendar.set(11, 23);
        calendar.set(12, 59);
        calendar.set(13, 59);
        return calendar.getTime();
    }

    public static Date getFirstTwoMonthStart() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -2);
        calendar.set(5, 1);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
        return calendar.getTime();
    }

    public static Date getFirstTwoMonthEnd() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(2, -2);
        int max = calendar.getActualMaximum(5);
        calendar.set(5, max);
        calendar.set(11, 23);
        calendar.set(12, 59);
        calendar.set(13, 59);
        return calendar.getTime();
    }

    public static Date getFirstWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.clear(12);
        cal.clear(13);
        cal.clear(14);
        cal.set(7, cal.getFirstDayOfWeek() + 1);
        cal.add(5, -7);
        return cal.getTime();
    }

    public static Date getFirstWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(7, cal.getFirstDayOfWeek());
        return cal.getTime();
    }

    public static Date getFirstTwoWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.clear(12);
        cal.clear(13);
        cal.clear(14);
        cal.set(7, cal.getFirstDayOfWeek() + 1);
        cal.add(5, -14);
        return cal.getTime();
    }

    public static Date getFirstTwoWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(7, cal.getFirstDayOfWeek());
        cal.add(5, -7);
        return cal.getTime();
    }

    public static void setFirstTimeOfMonth(Calendar calendar) {
        calendar.set(5, 1);
        calendar.set(11, 0);
        calendar.set(12, 0);
        calendar.set(13, 0);
        calendar.set(14, 0);
    }

    public static void setLastTimeOfMonth(Calendar calendar) {
        calendar.set(5, calendar.getActualMaximum(5));
        calendar.set(11, 23);
        calendar.set(12, 59);
        calendar.set(13, 59);
    }

    public static Date getMondayOfWeek() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.clear(12);
        cal.clear(13);
        cal.clear(14);
        cal.set(7, cal.getFirstDayOfWeek() + 1);
        return cal.getTime();
    }

    public static Date getWeekStart() {
        Calendar cal = Calendar.getInstance();
        cal.add(5, -7);
        return cal.getTime();
    }

    public static Date getWeekStartLast() {
        Calendar cal = Calendar.getInstance();
        cal.add(5, -14);
        return cal.getTime();
    }

    public static Date getWeekEnd() {
        Calendar cal = Calendar.getInstance();
        cal.add(13, -1);
        cal.add(5, -7);
        return cal.getTime();
    }

    public static Date getFirstMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(13, -1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstMonthLast() {
        Calendar cal = Calendar.getInstance();
        cal.add(2, -1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstMonthChange() {
        Calendar cal = Calendar.getInstance();
        cal.add(13, -1);
        cal.add(2, -1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstMonthLastChange() {
        Calendar cal = Calendar.getInstance();
        cal.add(2, -2);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getMondayOfLastWeek() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.clear(12);
        cal.clear(13);
        cal.clear(14);
        cal.set(7, cal.getFirstDayOfWeek() + 1);
        cal.roll(5, -7);
        return cal.getTime();
    }

    public static Date getFirstDayOfYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, 1);
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.set(6, 1);
        return cal.getTime();
    }

    public static Date getFirstDayOfLastYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(5, 1);
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(1, -1);
        cal.set(6, 1);
        return cal.getTime();
    }

    public static Date getFirstDayOfMonth() {
        Calendar cal = Calendar.getInstance();
        cal.add(5, -1);
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstDayOfLastMonth() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.clear(12);
        cal.clear(13);
        cal.clear(14);
        cal.add(2, -1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious1() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 4);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious1LastYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 4);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious2() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 5);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious2LastYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 5);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious2() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 7);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious2LastYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 7);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious3() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 8);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious3LastYear() {
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 8);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious3() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 10);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious3LastYear() {
        Date date = new Date();
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 10);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious4() {
        new Date();
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 11);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getFirstDayPrecious4Lastyear() {
        new Date();
        Calendar cal = Calendar.getInstance();
        cal.set(11, 0);
        cal.set(12, 0);
        cal.set(13, 0);
        cal.set(14, 0);
        cal.add(2, 11);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMinimum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious4() {
        new Date();
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 13);
        cal.set(1, cal.get(1) - 1);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static Date getLastDayPrecious4LastYear() {
        new Date();
        Calendar cal = Calendar.getInstance();
        cal.set(11, 23);
        cal.set(12, 59);
        cal.set(13, 59);
        cal.set(14, 0);
        cal.add(2, 13);
        cal.set(1, cal.get(1) - 2);
        cal.set(5, cal.getActualMaximum(5));
        return cal.getTime();
    }

    public static LocalDate calculateCycleDate(Date date, Integer paymentCycle, CycleTypeEnum cycleType, Boolean isEndDate, Integer currentCycle) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        YearMonth yearMonth = YearMonth.of(calendar.get(1), calendar.get(2) + 1);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
        LocalDate localDate = toLocalDate(date);
        if (CycleTypeEnum.DAILY.equals(cycleType)) {
            localDate = localDate.plusDays((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.WEEKLY.equals(cycleType)) {
            localDate = localDate.plusWeeks((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.MONTHLY.equals(cycleType)) {
            localDate = localDate.plusMonths((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.YEARLY.equals(cycleType)) {
            if (localDate.equals(lastDayOfMonth)) {
                localDate = YearMonth.of(calendar.get(1) + paymentCycle * currentCycle, calendar.get(2) + 1).atEndOfMonth();
            } else {
                localDate = localDate.plusYears((long)paymentCycle * (long)currentCycle);
            }
        }

        return BooleanUtils.isTrue(isEndDate) ? localDate.minusDays(1L) : localDate;
    }

    public static LocalDate calculateCycleDatePostPaid(Date date, Integer paymentCycle, CycleTypeEnum cycleType, Boolean isEndDate, Integer currentCycle) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        YearMonth yearMonth = YearMonth.of(calendar.get(1), calendar.get(2) + 1);
        LocalDate lastDayOfMonth = yearMonth.atEndOfMonth();
        LocalDate localDate = toLocalDate(date);
        if (CycleTypeEnum.DAILY.equals(cycleType)) {
            localDate = localDate.minusDays((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.WEEKLY.equals(cycleType)) {
            localDate = localDate.minusWeeks((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.MONTHLY.equals(cycleType)) {
            localDate = localDate.minusMonths((long)paymentCycle * (long)currentCycle);
        } else if (CycleTypeEnum.YEARLY.equals(cycleType)) {
            if (localDate.equals(lastDayOfMonth)) {
                localDate = YearMonth.of(calendar.get(1) + paymentCycle * currentCycle, calendar.get(2) + 1).atEndOfMonth();
            } else {
                localDate = localDate.minusYears((long)paymentCycle * (long)currentCycle);
            }
        }

        return BooleanUtils.isTrue(isEndDate) ? localDate.minusDays(1L) : localDate;
    }

    public static LocalDate convertDateToLocalDate(Date date) {
        return date == null ? null : date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date convertLocalDateToDate(LocalDate localDate) {
        return localDate == null ? null : Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static String convertLocalDateToString(LocalDate localDate, String format) {
        if (localDate == null) {
            return null;
        } else {
            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(format);
            return dateFormat.format(localDate);
        }
    }

    public static String convertLocalDateTimeToString(LocalDateTime time, String format) {
        if (time == null) {
            return null;
        } else {
            DateTimeFormatter dateFormat = DateTimeFormatter.ofPattern(format);
            return dateFormat.format(time);
        }
    }

    public static String convertDateToString(Date date, String format) {
        if (date == null) {
            return null;
        } else {
            DateFormat dateFormat = new SimpleDateFormat(format);
            return dateFormat.format(date);
        }
    }

    public static Date toDate(LocalDate localDate) {
        return localDate == null ? null : Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date toDate(LocalDateTime localDateTime) {
        return localDateTime == null ? null : Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate toLocalDate(String dateString, String pattern) {
        return dateString != null && pattern != null ? LocalDate.parse(dateString, DateTimeFormatter.ofPattern(pattern)) : null;
    }

    public static LocalDate toLocalDate(Date date) {
        if (date == null) {
            return null;
        } else {
            try {
                return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            } catch (UnsupportedOperationException var2) {
                Date safeDate = new Date(date.getTime());
                return safeDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            }
        }
    }

    public static Date toDate(String dateString, String pattern) {
        if (dateString != null && pattern != null) {
            try {
                return (new SimpleDateFormat(pattern)).parse(dateString);
            } catch (ParseException var3) {
                return null;
            }
        } else {
            return null;
        }
    }

    public static LocalDate timeStampToLocalDate(Timestamp timestamp) {
        return timestamp == null ? null : timestamp.toLocalDateTime().toLocalDate();
    }

    public static String convertStringToDate(String string) {
        SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy");
        SimpleDateFormat sdf2 = new SimpleDateFormat("YYYY/MM/dd");

        try {
            string = sdf2.format(sdf.parse(string));
        } catch (ParseException var4) {
            throw new BadRequestException("BadRequestException", "", "", "");
        }

        string = string.replaceAll("(/)", "-");
        return string;
    }

    public static long numDayBetween(Date from, Date to) {
        long diff = to.getTime() - from.getTime();
        TimeUnit time = TimeUnit.DAYS;
        return time.convert(diff, TimeUnit.MILLISECONDS) + 1L;
    }

    public static Date convertLocalDateTimeToDate(LocalDateTime time) {
        return (Date)(time == null ? new Date() : Timestamp.valueOf(time));
    }
}
