-- Migration Script: Add Interests Field to Customers Table
-- Date: 2025-01-15
-- Description: Add JSON field for storing customer interests and preferences

-- Add interests field (JSON array)
ALTER TABLE customers 
ADD COLUMN interests JSON NULL 
COMMENT 'JSON array storing customer interests and preferences (e.g., ["Real Estate Investment", "Luxury Properties", "Commercial Spaces"])';

-- Create index for better search performance on interests JSON field
-- Note: MySQL 5.7+ supports functional indexes on JSON fields
CREATE INDEX idx_customers_interests 
ON customers ((CAST(interests AS CHAR(2000) ARRAY)));

-- Add comment to document the JSON structure expected
-- Expected JSON structure for interests: ["Real Estate Investment", "Luxury Properties", "Commercial Spaces", "Residential Properties"]

-- Verify the changes
DESCRIBE customers;

-- Sample data insertion examples (for testing purposes - commented out)
/*
-- Example: Insert customer with interests
INSERT INTO customers (
    full_name, phone, email, 
    interests,
    created_at, updated_at
) VALUES (
    '<PERSON><PERSON><PERSON>', '0123456789', '<EMAIL>',
    JSON_ARRAY('Real Estate Investment', 'Luxury Properties', 'Commercial Spaces'),
    NOW(), NOW()
);

-- Example: Update existing customer with interests
UPDATE customers 
SET 
    interests = JSON_ARRAY('Residential Properties', 'Investment Opportunities', 'Rental Properties'),
    updated_at = NOW()
WHERE id = 1;

-- Example: Search queries for testing
-- Search by interests
SELECT * FROM customers 
WHERE JSON_SEARCH(interests, 'one', '%Investment%') IS NOT NULL;

-- Search by multiple interests
SELECT * FROM customers 
WHERE JSON_SEARCH(interests, 'one', '%Real Estate%') IS NOT NULL
   OR JSON_SEARCH(interests, 'one', '%Commercial%') IS NOT NULL;
*/

-- Migration completed successfully
-- Next steps: Update application code to handle the new interests JSON field
