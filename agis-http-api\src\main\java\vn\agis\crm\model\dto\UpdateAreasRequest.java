package vn.agis.crm.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
public class UpdateAreasRequest {
    
    private Long id;
    
    @NotBlank(message = "Areas name is required")
    @Size(max = 255, message = "Areas name must not exceed 255 characters")
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    private Long parentId;
    
    @NotNull(message = "Status is required")
    private Integer status;
    
    public UpdateAreasRequest() {
    }
    
    public UpdateAreasRequest(Long id, String name, String description, Long parentId, Integer status) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.parentId = parentId;
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "UpdateAreasRequest{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", parentId=" + parentId +
                ", status=" + status +
                '}';
    }
}
