package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.utils.StringUtils;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.base.jpa.dto.ConfigSearchDto;
import vn.agis.crm.base.jpa.dto.req.CreateConfigReq;
import vn.agis.crm.base.jpa.dto.req.UpdateConfigReq;
import vn.agis.crm.service.ConfigApiService;

@RestController
@RequestMapping("/configs")
public class ConfigController extends CrudController<Config, Long> {

    private final ConfigApiService configService;

    @Autowired
    public ConfigController(ConfigApiService service) {
        super(service);
        this.configService = service;
        this.baseUrl = "/configs";
    }

    @GetMapping("/search")
    @Operation(description = "Search configs by key/type/description with pagination")
    public ResponseEntity<Page<Config>> search(
            @RequestParam(name = "configKey", required = false, defaultValue = "") String configKey,
            @RequestParam(name = "configType", required = false, defaultValue = "-1") Integer configType,
            @RequestParam(name = "description", required = false, defaultValue = "") String description,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "createdAt,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        ConfigSearchDto dto = new ConfigSearchDto(
                configKey.trim().equals(" ") ? null : configKey,
                configType,
                description.trim().equals(" ") ? null : description,
                page, size, sortBy
        );
        Page<Config> pageResult = configService.search(dto, listRequest.getPageable());
        return ResponseEntity.ok().body(pageResult);
    }


    @GetMapping("/by-key/{configKey}")
    @Operation(description = "Get config by configKey")
    public ResponseEntity<Config> getByKey(@PathVariable String configKey) {
        return ResponseEntity.ok(configService.getByKey(configKey));
    }


    @GetMapping("/{id}")
    @Operation(description = "Get config by ID")
    public ResponseEntity<Config> getById(@PathVariable Long id) {
        return ResponseEntity.ok(configService.get(id));
    }

    @PostMapping
    @Operation(description = "Create new config")
    public ResponseEntity<Config> create(@RequestBody CreateConfigReq req) {
        return ResponseEntity.ok(configService.createConfig(req));
    }

    @PutMapping("/{id}")
    @Operation(description = "Update config by ID")
    public ResponseEntity<Config> update(@PathVariable Long id, @RequestBody UpdateConfigReq req) {
        req.setId(id);
        return ResponseEntity.ok(configService.updateConfig(req));
    }

    @DeleteMapping("/{id}")
    @Operation(description = "Delete config by ID")
    public ResponseEntity<Void> delete(@PathVariable Long id) {
        configService.deleteById(id);
        return ResponseEntity.ok().build();
    }

}

