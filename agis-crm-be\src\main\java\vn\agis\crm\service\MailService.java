package vn.agis.crm.service;

import java.util.Locale;
import org.apache.commons.lang3.CharEncoding;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring5.SpringTemplateEngine;
import io.github.jhipster.config.JHipsterProperties;
import jakarta.mail.internet.MimeMessage;
import vn.agis.crm.base.jpa.entity.User;

/**
 * Created by huyvv Date: 03/02/2020 Time: 1:48 PM for all issues, contact me: <EMAIL>
 **/
@Service
public class MailService {

    private final Logger logger = LoggerFactory.getLogger(MailService.class);

    private static final String USER = "userEntity";

    private static final String TICKET = "ticket";

    private static final String LIST_IMSI = "listImsi";
    private static final String SUB_CODE = "SUB_CODE";

    private JavaMailSender javaMailSender;

    private MessageSource messageSource;

    private final SpringTemplateEngine templateEngine;

    private JHipsterProperties jHipsterProperties;

    @Value("${baseUrl}")
    private String baseUrl;

    @Autowired
    public void setJavaMailSender(JavaMailSender javaMailSender) {
        this.javaMailSender = javaMailSender;
    }

    @Autowired
    public void setMessageSource(MessageSource messageSource) {
        this.messageSource = messageSource;
    }

    @Autowired
    public MailService(SpringTemplateEngine templateEngine) {
        this.templateEngine = templateEngine;
    }

    @Autowired
    public void setJHipsterProperties(JHipsterProperties jHipsterProperties) {
        this.jHipsterProperties = jHipsterProperties;
    }

    @Async
    public void sendEmail(String to, String subject, String content, boolean isMultipart, boolean isHtml) {
        if (!isValid(to)) {
            return;
        }
        MimeMessage mimeMessage = javaMailSender.createMimeMessage();
        try {
            MimeMessageHelper message = new MimeMessageHelper(mimeMessage, isMultipart, CharEncoding.UTF_8);
            message.setTo(to);
            message.setFrom(jHipsterProperties.getMail().getFrom());
            message.setSubject(subject);
            message.setText(content, isHtml);
            javaMailSender.send(mimeMessage);
            logger.debug("Sent email to User '{}'", to);
        } catch (Exception e) {
            logger.warn("Email could not be sent to user '{}'", to);
            e.printStackTrace();
        }
    }

    @Async
    public void sendCreationEmail(User userEntity, String rawPassword) {
        logger.debug("Sending creation email to '{}'", userEntity.getEmail());
        Locale locale = getLocale("vi_VN");
        Context context = new Context(locale);
        context.setVariable(USER, userEntity);
        context.setVariable("rawPassword", rawPassword);
        String content;
        String subject;
        content = templateEngine.process("/mails/creationEmail", context);
        subject = messageSource.getMessage("email.activation.title", null, locale);

        sendEmail(userEntity.getEmail(), subject, content, false, true);
    }

    @Async
    public void sendPasswordResetMail(User userEntity, String token) {
        logger.debug("Sending password reset email to '{}'", userEntity.getEmail());
        Locale locale = getLocale("vi_VN");
        Context context = new Context();
        String resetUrl = baseUrl + "/#/reset-password?token=" + token + "&email=" + userEntity.getEmail();
        context.setVariable("RESET_URL", resetUrl);
        context.setVariable(USER, userEntity);
        String content = templateEngine.process("/mails/passwordResetEmail", context);
        String subject = messageSource.getMessage("email.reset.title", null, locale);
        sendEmail(userEntity.getEmail(), subject, content, false, true);
    }
//
//    @Async
//    public void sendMailTestSim(User userEntity, Ticket ticket) {
//        logger.debug("Sending email replace sim to '{}'", userEntity.getEmail());
//        Locale locale = getLocale("vi_VN");
//        Context context = new Context();
//        String resetUrl = baseUrl + "/#/ticket/list";
//        context.setVariable("listTicketUrl", resetUrl);
//        context.setVariable(USER, userEntity);
//        context.setVariable(TICKET, ticket);
//        String content = templateEngine.process("/mails/testSim", context);
//        String subject = "[Hệ thống ONE Meter] Yêu cầu thử nghiệm";
//        sendEmail(userEntity.getEmail(), subject, content, false, true);
//    }

//    @Async
//    public void sendMailReplaceSim(User userEntity, Ticket ticket) {
//        logger.debug("Sending email replace sim to '{}'", userEntity.getEmail());
//        Locale locale = getLocale("vi_VN");
//        Context context = new Context();
//        String resetUrl = baseUrl + "/#/ticket/list";
//        context.setVariable("listTicketUrl", resetUrl);
//        context.setVariable(USER, userEntity);
//        context.setVariable(TICKET, ticket);
//        String content = templateEngine.process("/mails/replaceSim", context);
//        String subject = "[Hệ thống ONE Meter] Yêu cầu thay thế SIM";
//        sendEmail(userEntity.getEmail(), subject, content, false, true);
//    }
//    public void sendMailOrderSim(User userEntity, Ticket ticket) {
//        logger.debug("Sending email order sim to '{}'", userEntity.getEmail());
//        Locale locale = getLocale("vi_VN");
//        Context context = new Context();
//        String resetUrl = baseUrl + "/#/ticket/list";
//        context.setVariable("listTicketUrl", resetUrl);
//        context.setVariable(USER, userEntity);
//        context.setVariable(TICKET, ticket);
//        String content = templateEngine.process("/mails/orderSim", context);
//        String subject = "[Hệ thống ONE Meter] Yêu cầu đặt SIM";
//        sendEmail(userEntity.getEmail(), subject, content, false, true);
//    }
//    public void sendMailActiveSim(User userEntity, Ticket ticket, String listImsi) {
//        logger.debug("Sending email active sim to '{}'", userEntity.getEmail());
//        Locale locale = getLocale("vi_VN");
//        Context context = new Context();
//        String resetUrl = baseUrl + "/#/ticket/list";
//        context.setVariable("listTicketUrl", resetUrl);
//        context.setVariable(USER, userEntity);
//        context.setVariable(TICKET, ticket);
//        context.setVariable(LIST_IMSI, listImsi);
//        String content = templateEngine.process("/mails/activeSim", context);
//        String subject = "[Hệ thống ONE Meter] Yêu cầu kích hoạt SIM";
//        sendEmail(userEntity.getEmail(), subject, content, false, true);
//    }
//    public void sendMailDiagnose(User userEntity, Ticket ticket) {
//        logger.debug("Sending email diagnose sim to '{}'", userEntity.getEmail());
//        Locale locale = getLocale("vi_VN");
//        Context context = new Context();
//        String resetUrl = baseUrl + "/#/ticket/list";
//        context.setVariable("listTicketUrl", resetUrl);
//        context.setVariable(USER, userEntity);
//        context.setVariable(TICKET, ticket);
//        String content = templateEngine.process("/mails/diagnose", context);
//        String subject = "[Hệ thống ONE Meter] Yêu cầu chẩn đoán";
//        sendEmail(userEntity.getEmail(), subject, content, false, true);
//    }

    public void sendMailWallet(User userEntity, String subCode) {
        logger.debug("Sending email wallet to '{}'", userEntity.getEmail());
        Locale locale = getLocale("vi_VN");
        Context context = new Context();
        context.setVariable(USER, userEntity);
        context.setVariable(SUB_CODE, subCode);
        String content = templateEngine.process("/mails/walletEmail", context);
        String subject = "[Hệ thống ONE Meter] Thông báo không thể gia hạn tự động từ ví "+ subCode;
        sendEmail(userEntity.getEmail(), subject, content, false, true);
    }

    public Locale getLocale(String language) {
        if (language != null) {
            return Locale.forLanguageTag(language);
        }
        return Locale.forLanguageTag("en");
    }

    static boolean isValid(String email) {
        if (email.contains(".") && email.contains("@")) {
            return true;
        }
        return false;
    }
}
