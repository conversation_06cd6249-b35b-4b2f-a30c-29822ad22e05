package vn.agis.crm.util;

/**
 * Severity levels for import validation errors and warnings
 */
public enum ImportSeverity {
    CRITICAL("CRITICAL", "Critical error that stops processing"),
    ERROR("ERROR", "Error that prevents row from being processed"),
    WARNING("WARNING", "Warning that allows processing with potential issues"),
    INFO("INFO", "Informational message");
    
    private final String code;
    private final String description;
    
    ImportSeverity(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return code;
    }
}
