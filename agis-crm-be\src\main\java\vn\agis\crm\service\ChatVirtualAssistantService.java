package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Answer;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Chat;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Query;
import vn.agis.crm.base.jpa.dto.res.virtualassistant.Question;
import vn.agis.crm.base.jpa.entity.ChatVirtualAssistant;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.QueryVirtualAssistant;
import vn.agis.crm.base.utils.ObjectUtils;
import vn.agis.crm.base.utils.Utils;
import vn.agis.crm.repository.ChatVirtualAssistantRepository;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.repository.QueryVirtualAssistantRepository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Transactional
public class ChatVirtualAssistantService {
    private final Logger logger = LoggerFactory.getLogger(ChatVirtualAssistantService.class);
    @Autowired
    private ChatVirtualAssistantRepository chatVirtualAssistantRepository;
    @Autowired
    private QueryVirtualAssistantRepository queryVirtualAssistantRepository;
    @Autowired
    private QueryVirtualAssistantService queryVirtualAssistantService;
    @Autowired
    private CustomerRepository customerRepository;

    public Event process(Event event) {
        switch (event.method){
            case JpaConstants.Method.GET_ALL:
                return getAll(event);
            case JpaConstants.Method.CREATE:
                return create(event);
            case JpaConstants.Method.GET_ONE:
                return getOne(event);
            case JpaConstants.Method.UPDATE:
                return update(event);
            case JpaConstants.Method.DELETE:
                return delete(event);
            case Constants.Method.GET_SLICE_CUSTOMER:
                return getSliceCustomer(event);
            case JpaConstants.Method.GET_FIRST:
                return getFirst(event);
        }
        return event;
    }

    private Event getFirst(Event event) {
        ChatVirtualAssistant chatVirtualAssistant = chatVirtualAssistantRepository.findFirstByCreatedBy(event.userId);
        return event.createResponse(chatVirtualAssistant == null ? new ChatVirtualAssistant() : chatVirtualAssistant, ResponseCode.OK, "");
    }

    private Event getAll(Event event) {
        List<ChatVirtualAssistant> chatVirtualAssistants = chatVirtualAssistantRepository.findAllByCreatedBy(event.userId);
        return event.createResponse(chatVirtualAssistants, ResponseCode.OK, "");
    }

    private Event create(Event event) {
        try {
            ChatVirtualAssistant chatVirtualAssistant = (ChatVirtualAssistant) event.payload;
            ChatVirtualAssistant entity = chatVirtualAssistantRepository.findFirstByCreatedBy(event.userId);
            if(entity == null){
                chatVirtualAssistant.setCreatedAt(new Date());
                chatVirtualAssistant.setCreatedBy(event.userId);
                chatVirtualAssistant = chatVirtualAssistantRepository.save(chatVirtualAssistant);
            }else{
                chatVirtualAssistant.setId(entity.getId());
            }
            QueryVirtualAssistant query = chatVirtualAssistant.getQueries().getFirst();
            query.setCreatedAt(new Date());
            query.setCreatedBy(event.userId);
            query.setChatId(chatVirtualAssistant.getId());
            queryVirtualAssistantService.create(query, event);
            return event.createResponse(chatVirtualAssistant, ResponseCode.OK, "");
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, "Loi");
    }

    private Event getOne(Event event) {
        try {
            Long id = (Long) event.payload;
            ChatVirtualAssistant chatVirtualAssistant = chatVirtualAssistantRepository.findFirstByIdAndCreatedBy(id, event.userId);
            if (chatVirtualAssistant == null) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Loi");
            }
            if(!chatVirtualAssistant.getCreatedBy().equals(event.userId)){
                return event.createResponse(null, ResponseCode.FORBIDDEN, "");
            }
            List<QueryVirtualAssistant> queryVirtualAssistants = queryVirtualAssistantRepository.findAllByChatId(chatVirtualAssistant.getId());
            chatVirtualAssistant.setQueries(new ArrayList<>());
            if(!ObjectUtils.empty(queryVirtualAssistants)){
                for (QueryVirtualAssistant queryVirtualAssistant : queryVirtualAssistants) {
                    QueryVirtualAssistant q = new QueryVirtualAssistant();
                    BeanUtils.copyProperties(queryVirtualAssistant, q);
                    q.setQuestionInfo(Utils.gson.fromJson(Utils.decompress(queryVirtualAssistant.getQuestion()), Question.class));
                    if(ObjectUtils.empty(q.getAnswer())){

                    }else{
                        q.setAnswerInfo(Utils.gson.fromJson(Utils.decompress(queryVirtualAssistant.getAnswer()), Answer.class));
                    }
                    q.setQuestion(null);
                    q.setAnswer(null);
                    chatVirtualAssistant.getQueries().add(q);
                }
            }
            return event.createResponse(chatVirtualAssistant, ResponseCode.OK, "");
        }catch (Exception e){
            logger.error(e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, "Loi");
    }

    private Event getSliceCustomer(Event event) {
        Set<Double> ids = (Set<Double>) event.payload;
        List<Long> idss = ids.stream().map(el -> el.longValue()).collect(Collectors.toList());
        List<CustomerDto> customerDtos = queryVirtualAssistantService.getPageCustomerForAnswer(idss, event.userId, event.userType);
        return event.createResponse(customerDtos, ResponseCode.OK, "");
    }

    private Event delete(Event event) {
        Long id = (Long) event.payload;
        ChatVirtualAssistant chatVirtualAssistant = chatVirtualAssistantRepository.findById(id).orElse(null);
        if(chatVirtualAssistant == null){
            return event.createResponse(null, ResponseCode.NOT_FOUND, "Loi");
        }else{
            queryVirtualAssistantRepository.deleteAllByChatId(chatVirtualAssistant.getId());
            chatVirtualAssistantRepository.delete(chatVirtualAssistant);
            return event.createResponse(chatVirtualAssistant, ResponseCode.OK, "");
        }
    }

    private Event update(Event event) {
        ChatVirtualAssistant chatVirtualAssistant = (ChatVirtualAssistant) event.payload;
        ChatVirtualAssistant entity = chatVirtualAssistantRepository.findById(chatVirtualAssistant.getId()).orElse(null);
        if(entity == null){
            return event.createResponse(null, ResponseCode.NOT_FOUND, "Loi");
        }else{
            entity.setName(chatVirtualAssistant.getName());
            chatVirtualAssistantRepository.save(entity);
            return event.createResponse(chatVirtualAssistant, ResponseCode.OK, "");
        }
    }
}
