package vn.agis.crm.repository.spec;

import org.springframework.data.jpa.domain.Specification;
import vn.agis.crm.base.jpa.entity.Notifications;

import jakarta.persistence.criteria.Predicate;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class NotificationSpecs {

    public static Specification<Notifications> hasTargetEmployeeId(Long targetEmployeeId) {
        return (root, query, cb) -> targetEmployeeId == null ? cb.conjunction() : cb.equal(root.get("targetEmployeeId"), targetEmployeeId);
    }

    public static Specification<Notifications> hasTargetCustomerId(Long targetCustomerId) {
        return (root, query, cb) -> targetCustomerId == null ? cb.conjunction() : cb.equal(root.get("targetCustomerId"), targetCustomerId);
    }

    public static Specification<Notifications> hasType(Integer type) {
        return (root, query, cb) -> type == null ? cb.conjunction() : cb.equal(root.get("type"), type);
    }

    public static Specification<Notifications> titleContains(String title) {
        return (root, query, cb) -> (title == null || title.trim().isEmpty()) 
            ? cb.conjunction() 
            : cb.like(cb.lower(root.get("title")), "%" + title.toLowerCase() + "%");
    }

    public static Specification<Notifications> contentContains(String content) {
        return (root, query, cb) -> (content == null || content.trim().isEmpty()) 
            ? cb.conjunction() 
            : cb.like(cb.lower(root.get("content")), "%" + content.toLowerCase() + "%");
    }

    public static Specification<Notifications> hasReadStatus(Boolean isRead) {
        return (root, query, cb) -> isRead == null ? cb.conjunction() : cb.equal(root.get("isRead"), isRead);
    }

    public static Specification<Notifications> readAtInRange(String readAtRange) {
        return (root, query, cb) -> {
            if (readAtRange == null || readAtRange.trim().isEmpty()) {
                return cb.conjunction();
            }

            try {
                // Support different date range formats
                // Format: "yyyy-MM-dd,yyyy-MM-dd" or "yyyy-MM-dd" for single date
                String[] dates = readAtRange.split(",");
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                
                if (dates.length == 1) {
                    // Single date - search for that specific day
                    Date targetDate = sdf.parse(dates[0].trim());
                    Date startOfDay = new Date(targetDate.getTime());
                    Date endOfDay = new Date(targetDate.getTime() + 24 * 60 * 60 * 1000 - 1);
                    
                    return cb.and(
                        cb.greaterThanOrEqualTo(root.get("readAt"), startOfDay),
                        cb.lessThanOrEqualTo(root.get("readAt"), endOfDay)
                    );
                } else if (dates.length == 2) {
                    // Date range
                    Date startDate = sdf.parse(dates[0].trim());
                    Date endDate = sdf.parse(dates[1].trim());
                    Date endOfDay = new Date(endDate.getTime() + 24 * 60 * 60 * 1000 - 1);
                    
                    return cb.and(
                        cb.greaterThanOrEqualTo(root.get("readAt"), startDate),
                        cb.lessThanOrEqualTo(root.get("readAt"), endOfDay)
                    );
                }
            } catch (ParseException e) {
                // Invalid date format, ignore filter
                return cb.conjunction();
            }
            
            return cb.conjunction();
        };
    }

    /**
     * Combined specification for complex searches
     */
    public static Specification<Notifications> buildSearchSpecification(
            Long targetEmployeeId, Long targetCustomerId, Integer type,
            String title, String content, Boolean isRead, String readAtRange) {
        
        return Specification.where(hasTargetEmployeeId(targetEmployeeId))
                .and(hasTargetCustomerId(targetCustomerId))
                .and(hasType(type))
                .and(titleContains(title))
                .and(contentContains(content))
                .and(hasReadStatus(isRead))
                .and(readAtInRange(readAtRange));
    }
}
