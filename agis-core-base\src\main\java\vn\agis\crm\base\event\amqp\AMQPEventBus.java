package vn.agis.crm.base.event.amqp;

import com.rabbitmq.stream.Address;
import com.rabbitmq.stream.Environment;
import com.rabbitmq.stream.EnvironmentBuilder;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.rabbit.RabbitMessageFuture;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.context.ApplicationContext;
import vn.agis.crm.base.event.AMQPSubscriber;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.RabbitStreamSubscriber;
import vn.agis.crm.base.event.constants.AMQPConstants;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;


/**
 * Created by tiemnd on 12/14/19.
 */
public class AMQPEventBus implements EventBus {

    private static final Logger logger = LoggerFactory.getLogger(AMQPEventBus.class);
    private final Object lock = new Object();
    private Map<String, AMQPEventPublisher> publishers;
    private Map<String, AMQPEventListener> subscribers;
    private ApplicationContext ctx;
    private CachingConnectionFactory connectionFactory;
    private Environment streamEnv;

    public Map<String, AMQPEventListener> getSubscribers() {
        return subscribers;
    }

    public AMQPEventBus(CachingConnectionFactory connectionFactory, ApplicationContext ctx) {
        this.connectionFactory = connectionFactory;
        this.ctx = ctx;
        publishers = new HashMap<>();
        subscribers = new HashMap<>();
        List<String> streamUris = ctx.getEnvironment().getProperty("spring.rabbitmq.stream.uris", List.class);
        String host = ctx.getEnvironment().getProperty("spring.rabbitmq.stream.host", String.class);
        Integer port = ctx.getEnvironment().getProperty("spring.rabbitmq.stream.port", Integer.class);
        if (streamUris != null || StringUtils.isNotBlank(host)) {
            EnvironmentBuilder builder = Environment.builder()
                    .id(ctx.getEnvironment().getProperty("nodeName"));
            if (StringUtils.isNotBlank(host)) {
                Address entryPoint = new Address(host, port);
                String username = ctx.getEnvironment().getProperty("spring.rabbitmq.stream.username", String.class);
                String password = ctx.getEnvironment().getProperty("spring.rabbitmq.stream.password", String.class);
                builder.username(StringUtils.isNotBlank(username)? username: connectionFactory.getRabbitConnectionFactory().getUsername())
                        .password(StringUtils.isNotBlank(password)? password: connectionFactory.getRabbitConnectionFactory().getPassword())
                        .host(host)
                        .port(port)
                        .addressResolver(address -> entryPoint);
            } else {
                builder.username(connectionFactory.getRabbitConnectionFactory().getUsername())
                        .password(connectionFactory.getRabbitConnectionFactory().getPassword())
                        .uris(streamUris);
            }
            streamEnv = builder.build();
        }
        publishers.put(AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME, new AMQPEventPublisher(connectionFactory, AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME));
    }

    @Override
    public void registerSubscriber(AMQPSubscriber subscriber) {
        AMQPEventListener listener = new AMQPEventListener(connectionFactory, ctx, subscriber);
        String key = subscriber.getRoutingKey() + "." + subscriber.getQueue() + "." + subscriber.getExchange();
        logger.info("Registering amqp listener with key {}", key);
        synchronized (lock) {
            subscribers.put(key, listener);
        }
    }

    @Override
    public void registerStreamSubscriber(RabbitStreamSubscriber subscriber) {
        AMQPEventListener listener = new AMQPEventListener(connectionFactory, ctx, streamEnv, subscriber);
        String key = "stream." + subscriber.getStreamName();
        logger.info("Registering amqp listener with key {}", key);
        synchronized (lock) {
            subscribers.put(key, listener);
        }
    }

    @Override
    public void publishSuperStream(byte[] data, String streamName) {
        AMQPEventPublisher publisher = publishers.get("stream." + streamName);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, streamName, streamEnv, true);
            publishers.put("stream." + streamName, publisher);
        }
        publisher.publishStream(data);

    }

    @Override
    public void publishStream(byte[] data, String streamName) {
        AMQPEventPublisher publisher = publishers.get("stream." + streamName);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, streamName, streamEnv, false);
            publishers.put("stream." + streamName, publisher);
        }
        publisher.publishStream(data);

    }

    @Override
    public Event publish(String routingKey, Event event) {
        AMQPEventPublisher publisher = publishers.get(AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME);
        return publisher.publish(routingKey, event);
    }

    @Override
    public Event publish(String exchange, String routingKey, Event event) {
        AMQPEventPublisher publisher = publishers.get(exchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, exchange);
            publishers.put(exchange, publisher);
        }
        return publisher.publish(routingKey, event);
    }

    @Override
    public Event publishWithDelay(String xDelayedMessageExchange, String routingKey, Event event, Integer delayedInterval) {
        AMQPEventPublisher publisher = publishers.get(xDelayedMessageExchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, xDelayedMessageExchange, AMQPConstants.ExchangeType.X_DELAY_MESSAGE);
            publishers.put(xDelayedMessageExchange, publisher);
        }
        return publisher.publishWithDelay(routingKey, event, delayedInterval);
    }

    @Override
    public Future<RabbitMessageFuture> publishAndReceive(String xchange, String routingKey, Object event) {
        AMQPEventPublisher publisher = publishers.get(xchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, xchange, AMQPConstants.ExchangeType.DIRECT);
            publishers.put(xchange, publisher);
        }
        return publisher.publishAndReceive(routingKey, event);
    }

    @Override
    public Future<RabbitMessageFuture> publishAndReceive(String xchange, String routingKey, Object event, Integer receivedTimeout) {
        AMQPEventPublisher publisher = publishers.get(xchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, xchange, AMQPConstants.ExchangeType.DIRECT);
            publishers.put(xchange, publisher);
        }
        return publisher.publishAndReceive(routingKey, event, receivedTimeout);
    }

    @Override
    public void publishAndReceive(String xchange, String routingKey, Event event, Integer receivedTimeout, ListenableResultCallBack callBack) {
        AMQPEventPublisher publisher = publishers.get(xchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, xchange, AMQPConstants.ExchangeType.DIRECT);
            publishers.put(xchange, publisher);
        }
        publisher.publishAndReceive(routingKey, event, receivedTimeout, callBack);
    }

    @Override
    public Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event) {
        AMQPEventPublisher publisher = publishers.get(AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME);
        return publisher.publishAndReceive(routingKey, event);
    }

    @Override
    public Future<RabbitMessageFuture> publishAndReceive(String routingKey, Object event, Integer receivedTimeout) {
        AMQPEventPublisher publisher = publishers.get(AMQPAbstractConfiguration.DEFAULT_EXCHANGE_NAME);
        return publisher.publishAndReceive(routingKey, event, receivedTimeout);
    }

    @Override
    public Event publishAndReceiveSynch(String xchange, String routingKey, Event event, Integer receivedTimeout) {
        AMQPEventPublisher publisher = this.publishers.get(xchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(this.connectionFactory, xchange);
            this.publishers.put(xchange, publisher);
        }

        return publisher.publishAndReceiveSynch(routingKey, event, receivedTimeout);
    }

    @Override
    public AMQPEventPublisher registerPublisher(String xchange) {
        AMQPEventPublisher publisher = publishers.get(xchange);
        if (publisher == null) {
            publisher = new AMQPEventPublisher(connectionFactory, xchange);
            publishers.put(xchange, publisher);
        }
        return publisher;
    }

    @Override
    public String getExchange(String routingKey) {
        return AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE;
    }

}
