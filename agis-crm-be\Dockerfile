FROM eclipse-temurin:21-jre-alpine

# <PERSON><PERSON><PERSON> thư mục chứa <PERSON>ng dụng
WORKDIR /app

# Copy source test
COPY config/ /app/config/

# Copy file jar vào container
COPY target/agis-crm-be-1.0.jar app.jar

# T<PERSON>o thư mục uploads (nơi <PERSON>ng dụng ghi file ảnh)
RUN mkdir -p /app/uploads

# <PERSON><PERSON>u hình cổng
EXPOSE 8089

# Lệnh chạy app
CMD ["java", "-jar", "app.jar"]
