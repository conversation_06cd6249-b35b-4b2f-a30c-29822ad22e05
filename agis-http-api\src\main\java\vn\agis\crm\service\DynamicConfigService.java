package vn.agis.crm.service;

import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.errors.BaseException;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.DynamicConfigSearchDTO;
import vn.agis.crm.base.jpa.dto.req.FilterDashboardRequest;
import vn.agis.crm.base.jpa.entity.DynamicConfig;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class DynamicConfigService extends CrudService<DynamicConfig, Long>{


    private static final String objectKey = "DynamicConfig";
    private int timeoutForDashboardContent = 300000;
    public DynamicConfigService() {
        super(DynamicConfig.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_REPORT;
        this.category = Constants.Category.DASHBOARD;
    }


    public long countName(String query) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        long response = 0;
        try {
            event = RequestUtils.amqp
                    (JpaConstants.Method.CHECK_EXITS, category, query, routingKey);
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                response = (long) event.payload;
                return response;
            }
            return response;
        } finally {
        }
    }

    public Page<DynamicConfig> searchDynamicConfig(DynamicConfigSearchDTO reqDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<DynamicConfig> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DYNAMIC_CONFIG, category, reqDTO, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), DynamicConfig.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), DynamicConfig.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(reqDTO), ObjectMapperUtil.toJsonString(response), null, null, event);
        }

    }

    public List<DynamicConfig> getAll() {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<DynamicConfig> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.GET_ALL_DYNAMIC, category, "", routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<DynamicConfig>) event.payload;
            }
            return response;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(""), ObjectMapperUtil.toJsonString(response), null, null, event);
        }

    }

    public Map<String,Object> getContent(FilterDashboardRequest filterDashboardRequest) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;
        Event event = null;
        Map<String,Object> response = new HashMap<>();
        try {
            event = RequestUtils.amqpWithTimeout(Constants.Method.GET_CONTENT_DASHBOARD, category, filterDashboardRequest, routingKey, timeoutForDashboardContent);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Map<String, Object>) event.payload;
                return response;
            }
            else if (event.respStatusCode == ResponseCode.UNKNOWN_ERROR) {
                exception = throwBadRequestException(MessageKeyConstant.Report.REPORT_QUERY_ERROR, category, new ArrayList<>(), null);
//                TransactionLogger.writeLogITrans(filterDashboardRequest.getId(), null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                        ObjectMapperUtil.toJsonString(filterDashboardRequest), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.BAD_REQUEST).getBody()), null, null, event);
                throw exception;
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(""), ObjectMapperUtil.toJsonString(response), null, null, event);
        }

    }

    private BadRequestException throwBadRequestException(String message, String entity, List<String> field, String... values) {
        String messageNotFound = messageSource.getMessage(message, values, LocaleContextHolder.getLocale());
        return new BadRequestException(messageNotFound, entity, field, message);
    }
}
