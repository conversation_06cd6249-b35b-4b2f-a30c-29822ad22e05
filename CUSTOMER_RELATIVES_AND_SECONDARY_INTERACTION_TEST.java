// Customer Relatives and Secondary Interaction Processing Test
// Comprehensive test suite for the enhanced import system TODO sections

package vn.agis.crm.test;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;
import vn.agis.crm.util.ImportExecutionProcessor;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test suite for customer relatives and secondary interaction processing
 * Tests the comprehensive implementation of TODO sections 4 and 5
 */
@SpringBootTest
@Transactional
public class CustomerRelativesAndSecondaryInteractionTest {

    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private CustomerRelativeRepository customerRelativeRepository;
    
    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;
    
    @Autowired
    private ProjectRepository projectRepository;
    
    @Autowired
    private UnitRepository unitRepository;

    private Long testUserId = 1L;
    private SimpleDateFormat sdf = new SimpleDateFormat("E MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

    @BeforeEach
    void setUp() {
        // Clean up test data
        customerRelativeRepository.deleteAll();
        customerPropertyRepository.deleteAll();
        customerRepository.deleteAll();
        unitRepository.deleteAll();
        projectRepository.deleteAll();
    }

    @Test
    void testCustomerRelativesProcessing() {
        System.out.println("🔍 Test: Customer Relatives Processing");
        
        // Create test customer
        Customers testCustomer = createTestCustomer();
        
        // Test data for customer relatives
        Map<String, Object[]> relativesTestData = new LinkedHashMap<>();
        relativesTestData.put("Complete relative info", new Object[]{
            "Vợ", "Nguyễn Thị B", "1985", "'0987654321", "Ghi chú về người thân"
        });
        relativesTestData.put("Minimal relative info", new Object[]{
            "Con", "Nguyễn Văn C", null, null, null
        });
        relativesTestData.put("Excel formatted phone", new Object[]{
            "Anh", "Nguyễn Văn D", "1980", "'0901234567", "Anh trai"
        });
        relativesTestData.put("Invalid year of birth", new Object[]{
            "Em", "Nguyễn Thị E", "invalid", "0912345678", null
        });
        relativesTestData.put("Future year of birth", new Object[]{
            "Con", "Nguyễn Văn F", "2030", "0923456789", null
        });
        
        for (Map.Entry<String, Object[]> testCase : relativesTestData.entrySet()) {
            String description = testCase.getKey();
            Object[] data = testCase.getValue();
            
            System.out.println("\n👥 Testing: " + description);
            
            // Create iterator with test data
            List<String> testValues = Arrays.asList(
                (String) data[0], // relationType
                (String) data[1], // fullName
                (String) data[2], // yearOfBirth
                (String) data[3], // phone
                (String) data[4]  // notes
            );
            Iterator<String> testIterator = testValues.iterator();
            
            // Create validation result
            ValidationResultDto validationResult = new ValidationResultDto(1);
            
            // Test customer relatives processing
            try {
                // Use reflection to call private method for testing
                java.lang.reflect.Method method = ImportExecutionProcessor.class.getDeclaredMethod(
                    "processCustomerRelatives", Iterator.class, Long.class, Long.class,
                    CustomerRelativeRepository.class, ValidationResultDto.class);
                method.setAccessible(true);
                
                method.invoke(null, testIterator, testCustomer.getId(), testUserId, 
                    customerRelativeRepository, validationResult);
                
                // Verify results
                List<CustomerRelatives> relatives = customerRelativeRepository.findByCustomerId(testCustomer.getId());
                
                if (data[0] != null || data[1] != null || data[2] != null || data[3] != null || data[4] != null) {
                    assertFalse(relatives.isEmpty(), "Should create relative record when data is provided");
                    
                    CustomerRelatives relative = relatives.get(0);
                    assertEquals(testCustomer.getId(), relative.getCustomerId());
                    assertEquals(data[0], relative.getRelationType());
                    assertEquals(data[1], relative.getFullName());
                    
                    // Check phone cleaning
                    if (data[3] != null) {
                        String expectedPhone = ((String) data[3]).startsWith("'") ? 
                            ((String) data[3]).substring(1) : (String) data[3];
                        assertEquals(expectedPhone, relative.getPhone());
                    }
                    
                    System.out.println("   ✅ Relative created successfully: " + relative.getFullName());
                } else {
                    assertTrue(relatives.isEmpty(), "Should not create relative when no data provided");
                    System.out.println("   ✅ No relative created (as expected)");
                }
                
                // Check for warnings
                if (validationResult.getWarnings() != null && !validationResult.getWarnings().isEmpty()) {
                    System.out.println("   ⚠️ Warnings generated:");
                    validationResult.getWarnings().forEach(warning -> 
                        System.out.println("     - " + warning.getErrorDescription()));
                }
                
            } catch (Exception e) {
                System.out.println("   ❌ Error: " + e.getMessage());
                fail("Customer relatives processing should not throw exception: " + e.getMessage());
            }
            
            // Clean up for next test
            customerRelativeRepository.deleteAll();
        }
        
        System.out.println("\n✅ Customer relatives processing test completed");
    }

    @Test
    void testSecondaryTransferInteractionProcessing() {
        System.out.println("🔍 Test: Secondary Transfer Interaction Processing");
        
        // Create test data
        Customers testCustomer = createTestCustomer();
        Projects testProject = createTestProject();
        Units testUnit = createTestUnit(testProject.getId());
        Date transactionDate = new Date();
        String legalStatus = "Đã có sổ đỏ";
        
        // Test data for secondary interactions
        Map<String, Object[]> interactionTestData = new LinkedHashMap<>();
        interactionTestData.put("Complete interaction info", new Object[]{
            sdf.format(new Date(System.currentTimeMillis() - 86400000)), // yesterday
            sdf.format(new Date()) // today
        });
        interactionTestData.put("Only first interaction", new Object[]{
            sdf.format(new Date()), null
        });
        interactionTestData.put("Only last interaction", new Object[]{
            null, sdf.format(new Date())
        });
        interactionTestData.put("No interaction info", new Object[]{
            null, null
        });
        interactionTestData.put("Invalid date format", new Object[]{
            "invalid-date", "another-invalid-date"
        });
        
        for (Map.Entry<String, Object[]> testCase : interactionTestData.entrySet()) {
            String description = testCase.getKey();
            Object[] data = testCase.getValue();
            
            System.out.println("\n🔄 Testing: " + description);
            
            // Create iterator with test data
            List<String> testValues = Arrays.asList(
                (String) data[0], // firstInteraction
                (String) data[1]  // lastInteraction
            );
            Iterator<String> testIterator = testValues.iterator();
            
            // Create validation result
            ValidationResultDto validationResult = new ValidationResultDto(1);
            
            // Test secondary interaction processing
            try {
                // Use reflection to call private method for testing
                java.lang.reflect.Method method = ImportExecutionProcessor.class.getDeclaredMethod(
                    "processSecondaryTransferInteraction", Iterator.class, Long.class, Long.class, Long.class,
                    Date.class, String.class, Long.class, CustomerPropertyRepository.class, ValidationResultDto.class);
                method.setAccessible(true);
                
                CustomerProperties result = (CustomerProperties) method.invoke(null, testIterator, 
                    testCustomer.getId(), testProject.getId(), testUnit.getId(), 
                    transactionDate, legalStatus, testUserId, customerPropertyRepository, validationResult);
                
                // Verify results
                assertNotNull(result, "Should always create customer property record");
                assertEquals(testCustomer.getId(), result.getCustomerId());
                assertEquals(testProject.getId(), result.getProjectId());
                assertEquals(testUnit.getId(), result.getUnitId());
                assertEquals(transactionDate, result.getTransactionDate());
                assertEquals(legalStatus, result.getLegalStatus());
                
                // Check interaction dates
                if (data[0] != null && !data[0].equals("invalid-date")) {
                    assertNotNull(result.getFirstInteraction(), "Should set first interaction when valid date provided");
                }
                if (data[1] != null && !data[1].equals("another-invalid-date")) {
                    assertNotNull(result.getLastInteraction(), "Should set last interaction when valid date provided");
                }
                
                System.out.println("   ✅ Customer property created successfully: ID " + result.getId());
                
                // Check for warnings
                if (validationResult.getWarnings() != null && !validationResult.getWarnings().isEmpty()) {
                    System.out.println("   ⚠️ Warnings generated:");
                    validationResult.getWarnings().forEach(warning -> 
                        System.out.println("     - " + warning.getErrorDescription()));
                }
                
            } catch (Exception e) {
                System.out.println("   ❌ Error: " + e.getMessage());
                fail("Secondary interaction processing should not throw exception: " + e.getMessage());
            }
            
            // Clean up for next test
            customerPropertyRepository.deleteAll();
        }
        
        System.out.println("\n✅ Secondary transfer interaction processing test completed");
    }

    @Test
    void testExcelFormattingArtifactRemoval() {
        System.out.println("🔍 Test: Excel Formatting Artifact Removal in Relatives");
        
        Customers testCustomer = createTestCustomer();
        
        // Test phone numbers with Excel formatting artifacts
        String[] testPhones = {
            "'0901234567",
            "'+84901234567", 
            "'84901234567",
            "'************",
            "'************",
            "''0901234567", // Multiple quotes
            "0901234567"    // No quotes
        };
        
        for (String testPhone : testPhones) {
            System.out.println("\n📱 Testing phone: '" + testPhone + "'");
            
            List<String> testValues = Arrays.asList("Anh", "Test Relative", "1980", testPhone, "Test note");
            Iterator<String> testIterator = testValues.iterator();
            ValidationResultDto validationResult = new ValidationResultDto(1);
            
            try {
                java.lang.reflect.Method method = ImportExecutionProcessor.class.getDeclaredMethod(
                    "processCustomerRelatives", Iterator.class, Long.class, Long.class,
                    CustomerRelativeRepository.class, ValidationResultDto.class);
                method.setAccessible(true);
                
                method.invoke(null, testIterator, testCustomer.getId(), testUserId, 
                    customerRelativeRepository, validationResult);
                
                List<CustomerRelatives> relatives = customerRelativeRepository.findByCustomerId(testCustomer.getId());
                assertFalse(relatives.isEmpty(), "Should create relative");
                
                CustomerRelatives relative = relatives.get(0);
                String cleanedPhone = relative.getPhone();
                
                // Verify Excel artifacts are removed
                assertFalse(cleanedPhone.startsWith("'"), "Phone should not start with single quote");
                System.out.println("   ✅ Cleaned phone: '" + cleanedPhone + "'");
                
            } catch (Exception e) {
                fail("Excel artifact removal test failed: " + e.getMessage());
            }
            
            customerRelativeRepository.deleteAll();
        }
        
        System.out.println("\n✅ Excel formatting artifact removal test completed");
    }

    @Test
    void testValidationAndErrorHandling() {
        System.out.println("🔍 Test: Validation and Error Handling");
        
        Customers testCustomer = createTestCustomer();
        Projects testProject = createTestProject();
        Units testUnit = createTestUnit(testProject.getId());
        
        // Test error scenarios
        Map<String, Object[]> errorTestData = new LinkedHashMap<>();
        errorTestData.put("Very long relative name", new Object[]{
            "Vợ", "A".repeat(300), "1985", "0901234567", "Note"
        });
        errorTestData.put("Invalid year format", new Object[]{
            "Con", "Test Name", "not-a-year", "0901234567", "Note"
        });
        errorTestData.put("Year out of range", new Object[]{
            "Em", "Test Name", "1800", "0901234567", "Note"
        });
        
        for (Map.Entry<String, Object[]> testCase : errorTestData.entrySet()) {
            String description = testCase.getKey();
            Object[] data = testCase.getValue();
            
            System.out.println("\n⚠️ Testing error case: " + description);
            
            List<String> testValues = Arrays.asList(
                (String) data[0], (String) data[1], (String) data[2], (String) data[3], (String) data[4]
            );
            Iterator<String> testIterator = testValues.iterator();
            ValidationResultDto validationResult = new ValidationResultDto(1);
            
            try {
                java.lang.reflect.Method method = ImportExecutionProcessor.class.getDeclaredMethod(
                    "processCustomerRelatives", Iterator.class, Long.class, Long.class,
                    CustomerRelativeRepository.class, ValidationResultDto.class);
                method.setAccessible(true);
                
                method.invoke(null, testIterator, testCustomer.getId(), testUserId, 
                    customerRelativeRepository, validationResult);
                
                // Check for warnings
                if (validationResult.getWarnings() != null && !validationResult.getWarnings().isEmpty()) {
                    System.out.println("   ✅ Warnings generated as expected:");
                    validationResult.getWarnings().forEach(warning -> 
                        System.out.println("     - " + warning.getErrorDescription()));
                } else {
                    System.out.println("   ℹ️ No warnings generated");
                }
                
            } catch (Exception e) {
                System.out.println("   ❌ Unexpected error: " + e.getMessage());
            }
            
            customerRelativeRepository.deleteAll();
        }
        
        System.out.println("\n✅ Validation and error handling test completed");
    }

    // Helper methods
    private Customers createTestCustomer() {
        Customers customer = new Customers();
        customer.setFullName("Test Customer");
        customer.setPhone("0901234567");
        customer.setEmail("<EMAIL>");
        customer.setCreatedBy(testUserId);
        customer.setCreatedAt(new Date());
        return customerRepository.save(customer);
    }

    private Projects createTestProject() {
        Projects project = new Projects();
        project.setName("Test Project");
        project.setCreatedBy(testUserId);
        project.setCreatedAt(new Date());
        project.setActive(true);
        return projectRepository.save(project);
    }

    private Units createTestUnit(Long projectId) {
        Units unit = new Units();
        unit.setProjectId(projectId);
        unit.setCode("TEST-001");
        unit.setArea(BigDecimal.valueOf(100));
        unit.setContractPrice(BigDecimal.valueOf(1000000));
        unit.setCreatedBy(testUserId);
        unit.setCreatedAt(new Date());
        unit.setIsActive(true);
        return unitRepository.save(unit);
    }
}

/**
 * Manual Testing Instructions
 * ===========================
 * 
 * 1. **Run the Test Suite:**
 *    ```bash
 *    mvn test -Dtest=CustomerRelativesAndSecondaryInteractionTest
 *    ```
 * 
 * 2. **Create Test Excel File:**
 *    Create an Excel file with customer data including:
 *    - Customer relatives information (relation type, name, year of birth, phone, notes)
 *    - Secondary interaction dates (first interaction, last interaction)
 *    - Excel formatted phone numbers with leading single quotes
 * 
 * 3. **Test Import Process:**
 *    - Upload the test Excel file through the import API
 *    - Run dry-run first to verify validation
 *    - Execute import and verify data creation
 *    - Check that relatives and properties are created correctly
 * 
 * 4. **Database Verification:**
 *    ```sql
 *    -- Check customer relatives
 *    SELECT * FROM customer_relatives WHERE customer_id = ?;
 *    
 *    -- Check customer properties with secondary interactions
 *    SELECT * FROM customer_properties WHERE customer_id = ?;
 *    
 *    -- Verify phone cleaning
 *    SELECT phone FROM customer_relatives WHERE phone NOT LIKE "'%";
 *    ```
 * 
 * 5. **API Testing:**
 *    ```bash
 *    # Test import with relatives and secondary interactions
 *    curl -X POST "http://localhost:8080/imports" \
 *      -F "file=@customer_relatives_test.xlsx" \
 *      -F "options={\"mode\":\"RUN\"}"
 *    ```
 */
