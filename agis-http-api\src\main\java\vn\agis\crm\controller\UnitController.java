package vn.agis.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Parameter;
import vn.agis.crm.base.jpa.dto.UnitDeletionValidationResult;
import vn.agis.crm.base.jpa.dto.UnitSearchDto;
import vn.agis.crm.base.jpa.dto.req.UnitDto;
import vn.agis.crm.base.jpa.entity.Units;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.UnitService;

@RestController
@RequestMapping("/unit-mgmt")
public class UnitController extends CrudController<Units, Long> {

    UnitService unitService;

    @Autowired
    public UnitController(UnitService service) {
        super(service);
        this.unitService = service;
        this.baseUrl = "/unit-mgmt";
    }

    @GetMapping("/search")
    public ResponseEntity<Page<Units>> getPageUnits(
        @RequestParam(name = "projectId", required = false) Long projectId,
        @RequestParam(name = "code", required = false, defaultValue = "") String code,
        @RequestParam(name = "productType", required = false, defaultValue = "") String productType,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "createdAt,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        UnitSearchDto searchDto = new UnitSearchDto(projectId, code, productType, page, size, sortBy);
        Page<Units> list = unitService.search(searchDto, listRequest.getPageable());
        return ResponseEntity.ok().body(list);
    }

    @GetMapping("/{id}")
    public Units getOneUnit(@PathVariable Long id) {
        return unitService.getOne(id);
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<?> deleteUnit(@PathVariable Long id) {
        try {
            // First validate if unit can be deleted
            UnitDeletionValidationResult validationResult = unitService.validateUnitDeletion(id);

            if (!validationResult.isCanDelete()) {
                // Return validation result with dependency information
                return ResponseEntity.badRequest().body(validationResult);
            }

            // If validation passes, proceed with deletion
            unitService.deleteById(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            // Handle unexpected errors
            UnitDeletionValidationResult errorResult = new UnitDeletionValidationResult(
                false, "Lỗi hệ thống khi xóa căn hộ", 0L);
            return ResponseEntity.internalServerError().body(errorResult);
        }
    }

    @PostMapping("/create")
    public Units createUnit(@RequestBody UnitDto createReq) {
        return unitService.createUnit(createReq);
    }

    @PutMapping("/update/{id}")
    public Units updateUnit(@RequestBody UnitDto updateReq, @PathVariable Long id) {
        updateReq.setId(id);
        return unitService.update(updateReq);
    }

    @GetMapping("/checkExistCode")
    public ResponseEntity<Boolean> checkExistCode(@RequestParam("projectId") Long projectId,
                                                  @RequestParam("code") String code) {
        Boolean exists = unitService.checkExistCode(projectId, code);
        return ResponseEntity.ok(exists != null ? exists : Boolean.FALSE);
    }
}
