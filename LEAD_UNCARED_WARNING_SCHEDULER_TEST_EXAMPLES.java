// Test examples and usage scenarios for LeadUncaredWarningNotificationScheduler
// This demonstrates how to test and validate the lead uncared warning functionality

package vn.agis.crm.scheduler;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Transactional
public class LeadUncaredWarningNotificationSchedulerTest {

    @Autowired
    private LeadUncaredWarningNotificationScheduler scheduler;
    
    @Autowired
    private ConfigRepository configRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private NotificationRepository notificationRepository;
    
    @Autowired
    private CustomerOfferRepository customerOfferRepository;
    
    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Test
    public void testUncaredLeadWarningWith3DaysConfig() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer assigned 4 days ago (should trigger warning)
        Customers leadCustomer = createTestLead("Test Lead", "0901234567");
        customerRepository.save(leadCustomer);

        CustomerAssignments assignment = createAssignment(leadCustomer.getId(), employee.getId(), 4); // 4 days ago
        customerAssignmentRepository.save(assignment);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: Warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getType().equals(3); // LeadUncaredWarning
        assert notification.getTitle().equals("Cảnh báo lead chưa được chăm sóc");
        assert notification.getContent().contains("Test Lead");
        assert notification.getContent().contains("0901234567");
        assert notification.getContent().contains("chưa có tương tác nào");
        assert notification.getTargetCustomerId().equals(leadCustomer.getId());

        System.out.println("✅ 3-day uncared lead warning test passed");
    }

    @Test
    public void testNoWarningForRecentAssignment() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer assigned 2 days ago (should NOT trigger warning)
        Customers leadCustomer = createTestLead("Recent Lead", "0901234567");
        customerRepository.save(leadCustomer);

        CustomerAssignments assignment = createAssignment(leadCustomer.getId(), employee.getId(), 2); // 2 days ago
        customerAssignmentRepository.save(assignment);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: No warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ No warning for recent assignment test passed");
    }

    @Test
    public void testNoWarningForNonLeadCustomers() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create non-lead customer assigned 5 days ago (should NOT trigger warning)
        Customers dataCustomer = createTestCustomer("Data Customer", "0901234567", "Data");
        customerRepository.save(dataCustomer);

        CustomerAssignments assignment = createAssignment(dataCustomer.getId(), employee.getId(), 5); // 5 days ago
        customerAssignmentRepository.save(assignment);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: No warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ No warning for non-lead customers test passed");
    }

    @Test
    public void testNoWarningForLeadWithInteractions() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer assigned 5 days ago
        Customers leadCustomer = createTestLead("Interacted Lead", "0901234567");
        customerRepository.save(leadCustomer);

        CustomerAssignments assignment = createAssignment(leadCustomer.getId(), employee.getId(), 5); // 5 days ago
        customerAssignmentRepository.save(assignment);

        // Setup: Create customer offer and primary interaction after assignment
        CustomerOffers offer = createCustomerOffer(leadCustomer.getId(), 1L);
        customerOfferRepository.save(offer);

        InteractionsPrimary interaction = createPrimaryInteraction(offer.getId(), 2); // 2 days ago (after assignment)
        interactionsPrimaryRepository.save(interaction);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: No warning notification was created (lead has interactions)
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ No warning for lead with interactions test passed");
    }

    @Test
    public void testDisabledConfiguration() {
        // Setup: No configuration (disabled)
        // Don't create any config

        // Setup: Create test employee and lead
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        Customers leadCustomer = createTestLead("Test Lead", "0901234567");
        customerRepository.save(leadCustomer);

        CustomerAssignments assignment = createAssignment(leadCustomer.getId(), employee.getId(), 5); // 5 days ago
        customerAssignmentRepository.save(assignment);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: No notifications were created
        List<Notifications> allNotifications = notificationRepository.findAll();
        assert allNotifications.size() == 0;

        System.out.println("✅ Disabled configuration test passed");
    }

    @Test
    public void testInvalidConfiguration() {
        // Setup: Invalid configuration values
        Config invalidConfig1 = createLeadWarningConfig("invalid");
        Config invalidConfig2 = createLeadWarningConfig("-1");
        Config invalidConfig3 = createLeadWarningConfig("999");

        // Test each invalid config
        for (Config config : List.of(invalidConfig1, invalidConfig2, invalidConfig3)) {
            configRepository.deleteAll();
            configRepository.save(config);

            // Setup: Create test employee and lead
            Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
            employeeRepository.save(employee);

            Customers leadCustomer = createTestLead("Test Lead", "0901234567");
            customerRepository.save(leadCustomer);

            CustomerAssignments assignment = createAssignment(leadCustomer.getId(), employee.getId(), 5);
            customerAssignmentRepository.save(assignment);

            // Execute: Run the scheduler job
            scheduler.processLeadUncaredWarningNotifications();

            // Verify: No notifications were created due to invalid config
            List<Notifications> notifications = notificationRepository.findAll();
            assert notifications.size() == 0;

            // Cleanup
            customerRepository.deleteAll();
            customerAssignmentRepository.deleteAll();
            employeeRepository.deleteAll();
            notificationRepository.deleteAll();
        }

        System.out.println("✅ Invalid configuration test passed");
    }

    @Test
    public void testMultipleUncaredLeads() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create multiple employees
        Employee employee1 = createTestEmployee("Employee 1", "<EMAIL>", 2);
        Employee employee2 = createTestEmployee("Employee 2", "<EMAIL>", 2);
        employeeRepository.saveAll(List.of(employee1, employee2));

        // Setup: Create multiple uncared leads
        Customers lead1 = createTestLead("Lead 1", "0901111111");
        Customers lead2 = createTestLead("Lead 2", "0902222222");
        Customers lead3 = createTestLead("Lead 3", "0903333333");
        customerRepository.saveAll(List.of(lead1, lead2, lead3));

        // Setup: Create assignments (all older than 3 days)
        CustomerAssignments assignment1 = createAssignment(lead1.getId(), employee1.getId(), 4);
        CustomerAssignments assignment2 = createAssignment(lead2.getId(), employee1.getId(), 5);
        CustomerAssignments assignment3 = createAssignment(lead3.getId(), employee2.getId(), 6);
        customerAssignmentRepository.saveAll(List.of(assignment1, assignment2, assignment3));

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: All employees got appropriate notifications
        List<Notifications> emp1Notifications = notificationRepository.findByTargetEmployeeId(employee1.getId());
        List<Notifications> emp2Notifications = notificationRepository.findByTargetEmployeeId(employee2.getId());

        assert emp1Notifications.size() == 2; // Lead 1 and Lead 2
        assert emp2Notifications.size() == 1; // Lead 3

        // Verify notification content is lead-specific
        for (Notifications notification : emp1Notifications) {
            assert notification.getContent().contains("Lead 1") || 
                   notification.getContent().contains("Lead 2");
            assert notification.getType().equals(3); // LeadUncaredWarning
        }

        System.out.println("✅ Multiple uncared leads test passed");
    }

    @Test
    public void testInactiveEmployeeHandling() {
        // Setup: Enable 3-day warning notifications
        Config config = createLeadWarningConfig("3");
        configRepository.save(config);

        // Setup: Create inactive employee
        Employee inactiveEmployee = createTestEmployee("Inactive Employee", "<EMAIL>", 2);
        inactiveEmployee.setStatus(Employee.Status.inactive);
        employeeRepository.save(inactiveEmployee);

        // Setup: Create uncared lead assigned to inactive employee
        Customers leadCustomer = createTestLead("Test Lead", "0901234567");
        customerRepository.save(leadCustomer);

        CustomerAssignments assignment = createAssignment(leadCustomer.getId(), inactiveEmployee.getId(), 5);
        customerAssignmentRepository.save(assignment);

        // Execute: Run the scheduler job
        scheduler.processLeadUncaredWarningNotifications();

        // Verify: No notification was created for inactive employee
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(inactiveEmployee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ Inactive employee handling test passed");
    }

    // Helper methods for test setup

    private Config createLeadWarningConfig(String value) {
        Config config = new Config();
        config.setConfigKey("NOTIFICATION_LEAD_UNCARED_WARNING_DAYS");
        config.setConfigValue(value);
        config.setConfigType(1);
        config.setDescription("Test lead uncared warning configuration");
        config.setCreatedAt(new Date());
        config.setCreatedBy(1L);
        return config;
    }

    private Employee createTestEmployee(String name, String email, Integer roleId) {
        Employee employee = new Employee();
        employee.setEmployeeCode("EMP_" + System.currentTimeMillis());
        employee.setFullName(name);
        employee.setEmail(email);
        employee.setPhone("0900000000");
        employee.setPassword("password");
        employee.setRoleId(roleId);
        employee.setStatus(Employee.Status.active);
        employee.setCreatedAt(new Date());
        employee.setCreatedBy(1L);
        return employee;
    }

    private Customers createTestLead(String name, String phone) {
        return createTestCustomer(name, phone, "Leads");
    }

    private Customers createTestCustomer(String name, String phone, String sourceType) {
        Customers customer = new Customers();
        customer.setFullName(name);
        customer.setPhone(phone);
        customer.setEmail("<EMAIL>");
        customer.setSourceType(sourceType);
        customer.setCreatedAt(new Date());
        customer.setCreatedBy(1L);
        return customer;
    }

    private CustomerAssignments createAssignment(Long customerId, Long employeeId, int daysAgo) {
        CustomerAssignments assignment = new CustomerAssignments();
        assignment.setCustomerId(customerId);
        assignment.setEmployeeId(employeeId);
        assignment.setRoleType(2); // Staff
        
        // Set assignment date to daysAgo in the past
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -daysAgo);
        assignment.setAssignedFrom(cal.getTime());
        
        assignment.setCreatedAt(new Date());
        assignment.setCreatedBy(1L);
        return assignment;
    }

    private CustomerOffers createCustomerOffer(Long customerId, Long projectId) {
        CustomerOffers offer = new CustomerOffers();
        offer.setCustomerId(customerId);
        offer.setProjectId(projectId);
        offer.setStatus("OPEN");
        offer.setCreatedAt(new Date());
        offer.setCreatedBy(1L);
        return offer;
    }

    private InteractionsPrimary createPrimaryInteraction(Long customerOfferId, int daysAgo) {
        InteractionsPrimary interaction = new InteractionsPrimary();
        interaction.setCustomerOfferId(customerOfferId);
        interaction.setResult("Called");
        
        // Set interaction date to daysAgo in the past
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -daysAgo);
        interaction.setHappenedAt(cal.getTime());
        
        interaction.setNotes("Test interaction");
        interaction.setCreatedAt(new Date());
        interaction.setCreatedBy(1L);
        return interaction;
    }
}

// Example manual testing and configuration scenarios
class LeadUncaredWarningSchedulerManualTest {
    
    public static void main(String[] args) {
        System.out.println("🚨 Lead Uncared Warning Notification Scheduler - Manual Test Scenarios");
        
        // Scenario 1: Enable 3-day warning notifications
        System.out.println("\n📋 Scenario 1: Enable 3-day warning notifications");
        System.out.println("SQL: INSERT INTO configs (config_key, config_value, config_type, description) VALUES ('NOTIFICATION_LEAD_UNCARED_WARNING_DAYS', '3', 1, 'Send lead uncared warnings after 3 days');");
        System.out.println("Expected: Leads assigned 3+ days ago without interactions will trigger warnings");
        
        // Scenario 2: Same-day warnings
        System.out.println("\n📋 Scenario 2: Same-day warnings");
        System.out.println("SQL: UPDATE configs SET config_value = '1' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';");
        System.out.println("Expected: Leads assigned 1+ days ago without interactions will trigger warnings");
        
        // Scenario 3: Weekly warnings
        System.out.println("\n📋 Scenario 3: Weekly warnings");
        System.out.println("SQL: UPDATE configs SET config_value = '7' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';");
        System.out.println("Expected: Leads assigned 7+ days ago without interactions will trigger warnings");
        
        // Scenario 4: Disable warnings
        System.out.println("\n📋 Scenario 4: Disable warnings");
        System.out.println("SQL: DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';");
        System.out.println("Expected: No lead uncared warnings will be sent");
        
        System.out.println("\n🔍 Monitoring Commands:");
        System.out.println("-- Check recent lead uncared warnings");
        System.out.println("SELECT * FROM notifications WHERE type = 3 AND created_at >= CURDATE() ORDER BY created_at DESC;");
        
        System.out.println("\n-- Check uncared leads (manual query)");
        System.out.println("SELECT c.id, c.full_name, c.phone, ca.assigned_from, e.full_name as employee_name");
        System.out.println("FROM customers c");
        System.out.println("JOIN customer_assignments ca ON c.id = ca.customer_id");
        System.out.println("JOIN employees e ON ca.employee_id = e.id");
        System.out.println("WHERE c.source_type = 'Leads' AND c.deleted_at IS NULL");
        System.out.println("  AND ca.assigned_to IS NULL");
        System.out.println("  AND ca.assigned_from <= DATE_SUB(CURDATE(), INTERVAL 3 DAY)");
        System.out.println("ORDER BY ca.assigned_from ASC;");
        
        System.out.println("\n-- Check configuration");
        System.out.println("SELECT * FROM configs WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';");
        
        System.out.println("\n-- Check warning statistics");
        System.out.println("SELECT DATE(created_at) as date, COUNT(*) as count FROM notifications WHERE type = 3 GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 7;");
    }
}
