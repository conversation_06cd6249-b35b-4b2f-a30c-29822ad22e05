package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.InteractionPrimarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryUpdateDto;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;
import vn.agis.crm.service.InteractionsPrimaryApiService;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;

/**
 * REST Controller for Primary Interactions CRUD operations
 * Provides comprehensive API endpoints for managing primary interactions
 */
@RestController
@RequestMapping("/interactions-primary")
@Tag(name = "Primary Interactions", description = "API endpoints for managing primary interactions")
public class InteractionsPrimaryController extends CrudController<InteractionsPrimary, Long> {

    private final InteractionsPrimaryApiService interactionsPrimaryApiService;

    @Autowired
    public InteractionsPrimaryController(InteractionsPrimaryApiService service) {
        super(service);
        this.interactionsPrimaryApiService = service;
        this.baseUrl = "/interactions-primary";
    }

    /**
     * Create new primary interaction
     */
    @PostMapping
    @Operation(summary = "Create primary interaction", 
               description = "Create a new primary interaction for a customer offer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Primary interaction created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Customer offer not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<InteractionPrimaryDto> createInteraction(
            @Valid @RequestBody InteractionPrimaryCreateDto createDto,
            HttpServletRequest request) {
        
        InteractionPrimaryDto created = interactionsPrimaryApiService.createInteraction(createDto);
        return ResponseEntity.status(HttpStatus.CREATED).body(created);
    }

    /**
     * Update existing primary interaction
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update primary interaction", 
               description = "Update an existing primary interaction by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary interaction updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Primary interaction or customer offer not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<InteractionPrimaryDto> updateInteraction(
            @Parameter(description = "Primary interaction ID") @PathVariable Long id,
            @Valid @RequestBody InteractionPrimaryUpdateDto updateDto) {
        
        // Ensure ID consistency
        updateDto.setId(id);
        
        InteractionPrimaryDto updated = interactionsPrimaryApiService.updateInteraction(updateDto);
        return ResponseEntity.ok(updated);
    }

    /**
     * Get primary interaction by ID
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get primary interaction by ID", 
               description = "Retrieve a specific primary interaction by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary interaction found"),
        @ApiResponse(responseCode = "404", description = "Primary interaction not found")
    })
    public ResponseEntity<InteractionPrimaryDto> getInteractionById(
            @Parameter(description = "Primary interaction ID") @PathVariable Long id,
            HttpServletRequest request) {
        
        InteractionPrimaryDto interaction = interactionsPrimaryApiService.getInteractionById(id);
        return ResponseEntity.ok(interaction);
    }

    /**
     * Delete primary interaction by ID
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete primary interaction", 
               description = "Delete a primary interaction by ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary interaction deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Primary interaction not found"),
        @ApiResponse(responseCode = "403", description = "Access forbidden")
    })
    public ResponseEntity<Void> deleteInteraction(
            @Parameter(description = "Primary interaction ID") @PathVariable Long id,
            HttpServletRequest request) {
        
        interactionsPrimaryApiService.deleteInteraction(id);
        return ResponseEntity.ok().build();
    }

    /**
     * Search primary interactions with filters and pagination
     */
    @PostMapping("/search")
    @Operation(summary = "Search primary interactions", 
               description = "Search primary interactions with filters, sorting, and pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Search completed successfully")
    })
    public ResponseEntity<Page<InteractionPrimaryDto>> searchInteractions(
            @RequestBody InteractionPrimarySearchDto searchDto,
            HttpServletRequest request) {
        
        Page<InteractionPrimaryDto> results = interactionsPrimaryApiService.searchInteractions(searchDto);
        return ResponseEntity.ok(results);
    }

    /**
     * Get primary interactions by customer offer ID
     */
    @GetMapping("/customer-offer/{customerOfferId}")
    @Operation(summary = "Get interactions by customer offer ID", 
               description = "Retrieve all primary interactions for a specific customer offer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary interactions retrieved successfully")
    })
    public ResponseEntity<List<InteractionPrimaryDto>> getInteractionsByCustomerOfferId(
            @Parameter(description = "Customer offer ID") @PathVariable Long customerOfferId,
            HttpServletRequest request) {
        
        List<InteractionPrimaryDto> interactions = interactionsPrimaryApiService
            .getInteractionsByCustomerOfferId(customerOfferId);
        return ResponseEntity.ok(interactions);
    }

    /**
     * Count primary interactions by customer offer ID
     */
    @GetMapping("/customer-offer/{customerOfferId}/count")
    @Operation(summary = "Count interactions by customer offer ID", 
               description = "Count the number of primary interactions for a specific customer offer")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Count retrieved successfully")
    })
    public ResponseEntity<Long> countInteractionsByCustomerOfferId(
            @Parameter(description = "Customer offer ID") @PathVariable Long customerOfferId,
            HttpServletRequest request) {
        
        Long count = interactionsPrimaryApiService.countInteractionsByCustomerOfferId(customerOfferId);
        return ResponseEntity.ok(count);
    }

    /**
     * Get all primary interactions (with pagination)
     */
    @GetMapping
    @Operation(summary = "Get all primary interactions", 
               description = "Retrieve all primary interactions with pagination")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Primary interactions retrieved successfully")
    })
    public ResponseEntity<Page<InteractionPrimaryDto>> getAllInteractions(
            @Parameter(description = "Page number (0-based)") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "Page size") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "Sort criteria (e.g., 'happenedAt,desc')") @RequestParam(defaultValue = "happenedAt,desc") String sort,
            HttpServletRequest request) {
        
        InteractionPrimarySearchDto searchDto = new InteractionPrimarySearchDto();
        searchDto.setPage(page);
        searchDto.setSize(size);
        searchDto.setSortBy(sort);
        
        Page<InteractionPrimaryDto> results = interactionsPrimaryApiService.searchInteractions(searchDto);
        return ResponseEntity.ok(results);
    }
}
