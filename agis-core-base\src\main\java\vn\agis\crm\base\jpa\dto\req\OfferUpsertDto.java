package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

@Data
public class OfferUpsertDto {
    private Long id;
    private Long projectId;
    // accept dd/MM/yyyy (preferred) or yyyy-MM-dd
    private String firstInteraction;
    private String lastInteraction;
    private String status; // OPEN|CLOSED|CANCELLED or free text per payload
    private String notes;
    // new: detailed primary interactions
    private java.util.List<vn.agis.crm.base.jpa.dto.InteractionPrimaryDto> interactionsPrimary;
    private Boolean deleted;
}

