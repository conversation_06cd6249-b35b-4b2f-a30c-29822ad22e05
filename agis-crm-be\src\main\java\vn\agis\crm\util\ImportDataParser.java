package vn.agis.crm.util;


import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Utility class for parsing import files (CSV, Excel)
 */
public class ImportDataParser {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportDataParser.class);
    
    // CSV parsing constants
    private static final String DEFAULT_DELIMITER = ",";
    private static final String DEFAULT_ENCODING = "UTF-8";
    
    // Phone number validation pattern
    private static final Pattern PHONE_PATTERN = Pattern.compile("^[+]?[0-9]{10,15}$");
    
    // Email validation pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    
    /**
     * Parse CSV file and return list of row data
     */
    public static List<LinkedHashMap<String, String>> parseCSV(InputStream inputStream, String encoding) throws IOException {
        List<LinkedHashMap<String, String>> rows = new ArrayList<>();
        
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(inputStream, encoding != null ? encoding : DEFAULT_ENCODING))) {
            
            String headerLine = reader.readLine();
            if (headerLine == null) {
                throw new IOException("File is empty");
            }
            
            String[] headers = parseCSVLine(headerLine);
            String line;
            int rowNumber = 1;

            while ((line = reader.readLine()) != null) {
                rowNumber++;
                if (line.trim().isEmpty()) {
                    continue; // Skip empty lines
                }
                
                String[] values = parseCSVLine(line);
                LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
                
                for (int i = 0; i < headers.length; i++) {
                    String value = i < values.length ? values[i].trim() : "";
                    rowData.put(headers[i].trim(), value);
                }
                
                rowData.put("__ROW_NUMBER__", String.valueOf(rowNumber));
                rows.add(rowData);
            }
        }
        
        return rows;
    }
    
    /**
     * Parse Excel file and return list of row data
     */
    public static List<LinkedHashMap<String, String>> parseExcel(InputStream inputStream, String fileName, String sheetName) throws IOException {
        List<LinkedHashMap<String, String>> rows = new ArrayList<>();
        
        try (Workbook workbook = createWorkbook(inputStream, fileName)) {
            Sheet sheet = getSheet(workbook, sheetName);
            
            if (sheet.getPhysicalNumberOfRows() == 0) {
                throw new IOException("Sheet is empty");
            }
            
            // Get header row
            Row headerRow = sheet.getRow(1);
            if (headerRow == null) {
                throw new IOException("Header row is missing");
            }
            
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(getCellValueAsString(cell).trim());
            }
            
            // Process data rows
            for (int rowIndex = 2; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null || isEmptyRow(row)) {
                    continue;
                }

                LinkedHashMap<String, String> rowData = new LinkedHashMap<>();
                
                for (int colIndex = 1; colIndex < headers.size(); colIndex++) {
                    Cell cell = row.getCell(colIndex);
                    String value = cell != null ? getCellValueAsString(cell).trim() : "";
                    rowData.put(headers.get(colIndex), value);
                }
                
                rowData.put("__ROW_NUMBER__", String.valueOf(rowIndex + 1));
                rows.add(rowData);
            }
        }
        
        return rows;
    }
    
    /**
     * Parse CSV line handling quoted values and commas
     */
    private static String[] parseCSVLine(String line) {
        List<String> values = new ArrayList<>();
        StringBuilder current = new StringBuilder();
        boolean inQuotes = false;
        
        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);
            
            if (c == '"') {
                inQuotes = !inQuotes;
            } else if (c == ',' && !inQuotes) {
                values.add(current.toString());
                current = new StringBuilder();
            } else {
                current.append(c);
            }
        }
        
        values.add(current.toString());
        return values.toArray(new String[0]);
    }


    /**
     * Create appropriate workbook based on file extension
     */
    private static Workbook createWorkbook(InputStream inputStream, String fileName) throws IOException {
        if (fileName.toLowerCase().endsWith(".xlsx")) {
            return new XSSFWorkbook(inputStream);
        } else if (fileName.toLowerCase().endsWith(".xls")) {
            return new HSSFWorkbook(inputStream);
        } else {
            throw new IOException("Unsupported file format: " + fileName);
        }
    }
    
    /**
     * Get sheet by name or default to first sheet
     */
    private static Sheet getSheet(Workbook workbook, String sheetName) throws IOException {
        if (sheetName != null && !sheetName.trim().isEmpty()) {
            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new IOException("Sheet not found: " + sheetName);
            }
            return sheet;
        } else {
            if (workbook.getNumberOfSheets() == 0) {
                throw new IOException("No sheets found in workbook");
            }
            return workbook.getSheetAt(0);
        }
    }
    
    /**
     * Get cell value as string regardless of cell type
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellTypeEnum()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    // Evaluate formula and get the cached result
                    FormulaEvaluator evaluator = cell.getSheet().getWorkbook().getCreationHelper().createFormulaEvaluator();
                    CellValue cellValue = evaluator.evaluate(cell);

                    switch (cellValue.getCellTypeEnum()) {
                        case STRING:
                            return cellValue.getStringValue();
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                return cell.getDateCellValue().toString();
                            } else {
                                double numericValue = cellValue.getNumberValue();
                                if (numericValue == Math.floor(numericValue)) {
                                    return String.valueOf((long) numericValue);
                                } else {
                                    return String.valueOf(numericValue);
                                }
                            }
                        case BOOLEAN:
                            return String.valueOf(cellValue.getBooleanValue());
                        default:
                            return "";
                    }
                } catch (Exception e) {
                    logger.warn("Failed to evaluate formula in cell, returning formula string: {}", e.getMessage());
                    return cell.getCellFormula();
                }
            case BLANK:
            default:
                return "";
        }
    }
    
    /**
     * Check if row is empty
     */
    private static boolean isEmptyRow(Row row) {
        for (Cell cell : row) {
            if (cell != null && !getCellValueAsString(cell).trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Remove Excel formatting artifacts from phone numbers
     */
    public static String removeExcelFormattingArtifacts(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }

        String cleaned = phone.trim();

        // Remove leading single quotes that Excel adds to force text interpretation
        if (cleaned.startsWith("'")) {
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    }

    /**
     * Validate phone number format
     */
    public static boolean isValidPhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }

        // Remove Excel formatting artifacts first
        String cleanPhone = removeExcelFormattingArtifacts(phone);
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");
        return PHONE_PATTERN.matcher(cleanPhone).matches();
    }

    /**
     * Validate email format
     */
    public static boolean isValidEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return true; // Email is optional
        }

        return EMAIL_PATTERN.matcher(email.trim()).matches();
    }

    /**
     * Normalize phone number to E.164 format
     */
    public static String normalizePhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }

        // Remove Excel formatting artifacts first
        String cleanPhone = removeExcelFormattingArtifacts(phone);
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");

        // Add country code if missing (assume Vietnam +84)
        if (cleanPhone.startsWith("0")) {
            cleanPhone = "+84" + cleanPhone.substring(1);
        } else if (!cleanPhone.startsWith("+")) {
            cleanPhone = "+" + cleanPhone;
        }

        return cleanPhone;
    }
}
