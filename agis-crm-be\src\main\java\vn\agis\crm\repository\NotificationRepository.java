package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Notifications;

import java.util.Date;
import java.util.List;

@Repository
public interface NotificationRepository extends JpaRepository<Notifications, Long>, JpaSpecificationExecutor<Notifications> {

    /**
     * Find notifications by target employee ID
     */
    List<Notifications> findByTargetEmployeeIdOrderByCreatedAtDesc(Long targetEmployeeId);

    /**
     * Find unread notifications by target employee ID
     */
    List<Notifications> findByTargetEmployeeIdAndIsReadFalseOrderByCreatedAtDesc(Long targetEmployeeId);

    /**
     * Count unread notifications by target employee ID
     */
    long countByTargetEmployeeIdAndIsReadFalse(Long targetEmployeeId);

    /**
     * Find notifications by target customer ID
     */
    List<Notifications> findByTargetCustomerIdOrderByCreatedAtDesc(Long targetCustomerId);

    /**
     * Find notifications by type
     */
    List<Notifications> findByTypeOrderByCreatedAtDesc(Integer type);

    /**
     * Mark notification as read
     */
    @Modifying
    @Query("UPDATE Notifications n SET n.isRead = true, n.readAt = :readAt WHERE n.id = :id")
    int markAsRead(@Param("id") Long id, @Param("readAt") Date readAt);

    /**
     * Mark notification as unread
     */
    @Modifying
    @Query("UPDATE Notifications n SET n.isRead = false, n.readAt = null WHERE n.id = :id")
    int markAsUnread(@Param("id") Long id);

    /**
     * Mark all notifications as read for a target employee
     */
    @Modifying
    @Query("UPDATE Notifications n SET n.isRead = true, n.readAt = :readAt WHERE n.targetEmployeeId = :targetEmployeeId AND n.isRead = false")
    int markAllAsReadForEmployee(@Param("targetEmployeeId") Long targetEmployeeId, @Param("readAt") Date readAt);

    /**
     * Delete notification by ID and target employee ID (authorization check)
     */
    @Modifying
    @Query("DELETE FROM Notifications n WHERE n.id = :id AND n.targetEmployeeId = :targetEmployeeId")
    int deleteByIdAndTargetEmployeeId(@Param("id") Long id, @Param("targetEmployeeId") Long targetEmployeeId);

    /**
     * Find notification by ID and target employee ID for authorization check
     */
    @Query("SELECT n FROM Notifications n WHERE n.id = :id AND n.targetEmployeeId = :targetEmployeeId")
    Notifications findByIdAndTargetEmployeeId(@Param("id") Long id, @Param("targetEmployeeId") Long targetEmployeeId);

    /**
     * Delete multiple notifications by IDs and target employee ID (authorization check)
     */
    @Modifying
    @Query("DELETE FROM Notifications n WHERE n.id IN :ids AND n.targetEmployeeId = :targetEmployeeId")
    int deleteByIdsAndTargetEmployeeId(@Param("ids") List<Long> ids, @Param("targetEmployeeId") Long targetEmployeeId);

    /**
     * Find notifications by IDs and target employee ID for authorization check
     */
    @Query("SELECT n FROM Notifications n WHERE n.id IN :ids AND n.targetEmployeeId = :targetEmployeeId")
    List<Notifications> findByIdsAndTargetEmployeeId(@Param("ids") List<Long> ids, @Param("targetEmployeeId") Long targetEmployeeId);
}
