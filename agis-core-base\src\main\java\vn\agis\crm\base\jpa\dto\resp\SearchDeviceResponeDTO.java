package vn.agis.crm.base.jpa.dto.resp;

import java.util.Date;

public interface SearchDeviceResponeDTO {
    Long getId();

    String getMsisdn();

    String getDeviceName();

    String getSerialNumber();

    String getModel();

    Long getEnterpriseUserId();
    Long getCustomerUserId();
    Long getLastConnected();
    Integer getConnectionStatus();
    String getDeviceType();
    Long getDeviceTypeId();
    String getEnterpriseName();
    String getCustomerName();
    String getImei();

    Integer getUpdatedBy();

    Date getUpdatedDate();

    Long getImsi();

    Long getExpanse();

    Long getTraffic();

    Float getCurrentVolume();
    Long getEstimatedCost();
}
