package vn.agis.crm.endpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.service.ChatVirtualAssistantService;

@Component
public class ChatVirtualAssistantEndpoint {
    @Autowired
    private ChatVirtualAssistantService service;

    public Event process(Event event) {
        Event response = event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
        try {
            return service.process(event);
        }catch (Exception e) {
            e.printStackTrace();
        }
        return response;
    }
}
