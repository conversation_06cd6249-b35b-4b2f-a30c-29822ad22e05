package vn.agis.crm.service;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.NotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.entity.QueryVirtualAssistant;
import vn.agis.crm.base.jpa.entity.Report;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class QueryVirtualAssistantService extends CrudService<QueryVirtualAssistant, Long>{
    private static final Logger logger = LoggerFactory.getLogger(QueryVirtualAssistantService.class);
    public QueryVirtualAssistantService() {
        super(QueryVirtualAssistant.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.QUERY_VIRTUAL_ASSISTANT;
    }

    public ResponseEntity<List<QueryVirtualAssistant>> search(Long chatId, Integer page, Integer size) {
        Map<String, Object> params = new HashMap<String, Object>();
        params.put("chatId", chatId);
        params.put("page", page);
        params.put("size", size);
        Event event = new Event();

        List<QueryVirtualAssistant> response = new ArrayList<QueryVirtualAssistant>();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH,category,params,routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                response = (List<QueryVirtualAssistant>) event.payload;
                return new ResponseEntity<>(response, HttpStatus.OK);
            } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND){
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode.intValue() == ResponseCode.FORBIDDEN){
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        }finally {
            writeLog(timeRequest, timeResponse,ObjectMapperUtil.toJsonString(params), ObjectMapperUtil.toJsonString(response), event);
        }
    }


}
