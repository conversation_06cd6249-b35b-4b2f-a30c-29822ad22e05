package vn.agis.crm.base.jpa.entity;

import lombok.Data;

import jakarta.persistence.*;

@Entity
@Data
@Table(name = "device_command_record")
public class DeviceCommandRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "command_id")
    private Long commandId;

    @Column(name = "created_at")
    private Long createdAt;

    @Column(name = "device_id")
    private Long deviceId;

    @Column(name = "request_payload", columnDefinition = "LONGTEXT")
    private String requestPayload;

    @Column(name = "response_payload", columnDefinition = "LONGTEXT")
    private String responsePayload;

    @Column(name = "status")
    private Integer status;

    @Column(name = "record_id")
    private String recordId;

    @Column(name = "sent")
    private Integer sent;
}

