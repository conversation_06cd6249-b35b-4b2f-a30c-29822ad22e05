package vn.agis.crm.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;

public class UnZip {
	static final int BUFFER = 2048;
	private final static Logger logger = LoggerFactory.getLogger(UnZip.class);

	public static String unZipFile(String filePath) {
		String fileName = null;
		BufferedOutputStream dest = null;
		BufferedInputStream is = null;
		ZipEntry entry;
		if (!Files.exists(Paths.get(filePath))){
			logger.info("file not exist " +filePath);
		}
		try {
			ZipFile zipfile = new ZipFile(filePath);
			Enumeration<?> e = zipfile.entries();
			while (e.hasMoreElements()) {
				entry = (ZipEntry) e.nextElement();
				logger.info("Extracting: " + entry);
				String sFile = filePath.substring(0, filePath.lastIndexOf('.'));
				try {
					is = new BufferedInputStream(zipfile.getInputStream(entry));
					int count;
					byte data[] = new byte[BUFFER];
					logger.info("store file unzip to " + sFile);
					FileOutputStream fos = new FileOutputStream(sFile);
					dest = new BufferedOutputStream(fos, BUFFER);
					while ((count = is.read(data, 0, BUFFER)) != -1) {
						dest.write(data, 0, count);
					}
					dest.flush();
					dest.close();
					is.close();
				} catch (Exception e2) {
					logger.error(e2.getMessage(), e);
				} finally {
					dest.close();
					is.close();
					zipfile.close();
				}
				fileName = sFile;
				break;
			}
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
		} finally {
			try {
				if (is != null)
					is.close();
				if (dest != null)
					dest.close();
			} catch (Exception e1) {
			}
		}
		return fileName;
	}
}
