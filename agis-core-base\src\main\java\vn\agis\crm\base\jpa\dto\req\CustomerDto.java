package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.jpa.dto.CustomerPropertyDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class CustomerDto {
    private Long id;
    private String fullName;
    private String phone;
    private String email;
    private String cccd;

    // Multiple contact information fields
    private List<String> additionalPhones;
    private List<String> additionalEmails;
    private List<String> additionalCccds;

    // Customer interests and preferences
    private String interests;
    private Date birthDate; // yyyy-MM-dd
    private String addressContact;
    private String addressPermanent;
    private String nationality;
    private String maritalStatus;
    private BigDecimal totalAsset;
    private String businessField;
    private String avatarUrl;
    private String zaloStatus;
    private String facebookLink;
    private String sourceType;   // Data | Leads | Event | Refer
    private String sourceDetail;
    private String notes;
    private Boolean isActive; // optional, not persisted directly but for parity (can be ignored if not needed)
    private List<CustomerPropertyDto> products;
}

