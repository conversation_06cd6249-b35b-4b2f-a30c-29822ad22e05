package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.ArrayList;

@Entity
@Table(name = "customers", uniqueConstraints = {
    @UniqueConstraint(name = "uq_customer_phone", columnNames = {"phone"})
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Customers extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "full_name", nullable = false, length = 255)
    private String fullName;

    @Column(name = "phone", nullable = false, length = 32)
    private String phone;

    @Column(name = "email", length = 255)
    private String email;

    @Column(name = "cccd", length = 12)
    private String cccd;

    @Column(name = "additional_phones", columnDefinition = "JSON")
    private String additionalPhonesJson;

    @Column(name = "additional_emails", columnDefinition = "JSON")
    private String additionalEmailsJson;

    @Column(name = "additional_cccds", columnDefinition = "JSON")
    private String additionalCccdsJson;

    @Column(name = "interests", columnDefinition = "TEXT")
    private String interests;

    @Temporal(TemporalType.DATE)
    @Column(name = "birth_date")
    private Date birthDate;

    @Lob
    @Column(name = "address_contact")
    private String addressContact;

    @Lob
    @Column(name = "address_permanent")
    private String addressPermanent;

    @Column(name = "nationality", length = 100)
    private String nationality;

    @Column(name = "marital_status", length = 50)
    private String maritalStatus;

    @Column(name = "total_asset", precision = 18, scale = 2)
    private BigDecimal totalAsset;

    @Column(name = "business_field", length = 255)
    private String businessField;

    @Column(name = "avatar_url", length = 512)
    private String avatarUrl;

    @Column(name = "zalo_status", length = 50)
    private String zaloStatus;

    @Column(name = "facebook_link", length = 512)
    private String facebookLink;

    @Column(name = "source_type", length = 50)
    private String sourceType = "Data"; // Data, Leads, Event, Refer

    @Column(name = "source_detail", length = 255)
    private String sourceDetail;

    @Lob
    @Column(name = "notes")
    private String notes;

    @Column(name = "deleted_at")
    private Date deletedAt;

    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Column(name = "current_manager_id")
    private Long currentManagerId;

    @Column(name = "current_staff_id")
    private Long currentStaffId;

    // Transient fields for JSON handling
    @Transient
    private List<String> additionalPhones;

    @Transient
    private List<String> additionalEmails;

    @Transient
    private List<String> additionalCccds;



    // Helper methods for JSON serialization/deserialization
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final TypeReference<List<String>> STRING_LIST_TYPE = new TypeReference<List<String>>() {};

    @PostLoad
    @PostPersist
    @PostUpdate
    private void deserializeJsonFields() {
        this.additionalPhones = deserializeStringList(this.additionalPhonesJson);
        this.additionalEmails = deserializeStringList(this.additionalEmailsJson);
        this.additionalCccds = deserializeStringList(this.additionalCccdsJson);
    }

    @PrePersist
    @PreUpdate
    private void serializeJsonFields() {
        this.additionalPhonesJson = serializeStringList(this.additionalPhones);
        this.additionalEmailsJson = serializeStringList(this.additionalEmails);
        this.additionalCccdsJson = serializeStringList(this.additionalCccds);
    }

    private List<String> deserializeStringList(String json) {
        if (json == null || json.trim().isEmpty()) {
            return new ArrayList<>();
        }
        try {
            return objectMapper.readValue(json, STRING_LIST_TYPE);
        } catch (Exception e) {
            return new ArrayList<>();
        }
    }

    private String serializeStringList(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.writeValueAsString(list);
        } catch (Exception e) {
            return null;
        }
    }

}

