package vn.agis.crm.service;

import io.jsonwebtoken.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.constants.Constants;

/**
 * Created by huyvv
 * Date: 20/01/2020
 * Time: 10:20 AM
 * for all issues, contact me: <EMAIL>
 **/
@Component
public class SecurityService {
    private static final Logger logger = LoggerFactory.getLogger(SecurityService.class);

    public boolean validateToken(String authToken) {
        try {
            Jwts.parser().setSigningKey(Constants.CRMService.JWT_SECRET).parseClaimsJws(authToken);
            return true;
        } catch (SignatureException e) {
            logger.info("Invalid JWT signature.");
            logger.trace("Invalid JWT signature trace: {}", e);
        } catch (MalformedJwtException e) {
            logger.info("Invalid JWT token.");
            logger.trace("Invalid JWT token trace: {}", e);
        } catch (ExpiredJwtException e) {
            logger.info("Expired JWT token.");
            //log.trace("Expired JWT token trace: {}", e);
        } catch (UnsupportedJwtException e) {
            logger.info("Unsupported JWT token.");
            logger.trace("Unsupported JWT token trace: {}", e);
        } catch (IllegalArgumentException e) {
            logger.info("JWT token compact of handler are invalid.");
            logger.trace("JWT token compact of handler are invalid trace: {}", e);
        }
        return false;
    }

    public Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(Constants.CRMService.JWT_SECRET)
                .parseClaimsJws(token)
                .getBody();
    }

    public Long getEmployeeIdFromToken(String token) {
        if (!validateToken(token)) {
            return null;
        }
        Claims claims = parseToken(token);
        Object userId = claims.get(Constants.CRMService.JWT_USER_ID);
        if (userId instanceof Integer) {
            return ((Integer) userId).longValue();
        } else if (userId instanceof Long) {
            return (Long) userId;
        }
        return null;
    }
}

