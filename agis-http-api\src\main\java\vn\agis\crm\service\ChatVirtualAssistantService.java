package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.entity.ChatVirtualAssistant;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

@Service
public class ChatVirtualAssistantService extends CrudService<ChatVirtualAssistant, Long>{
    private static final Logger logger = LoggerFactory.getLogger(ChatVirtualAssistantService.class);
    public ChatVirtualAssistantService() {
        super(ChatVirtualAssistant.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.CHAT_VIRTUAL_ASSISTANT;
    }

    public List<CustomerDto> getSliceCustomer(Set<Long> ids) {
        logger.info("Get Slice Customer #{} with ids #{}", Customers.class, ids);
        Event event = RequestUtils.amqp(Constants.Method.GET_SLICE_CUSTOMER,category,ids,routingKey);
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            return (List<CustomerDto>) event.payload;
        } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
        }
        return new ArrayList<>();
    }

    public List<ChatVirtualAssistant> getAll() {
        Event event = RequestUtils.amqp(JpaConstants.Method.GET_ALL,category,null,routingKey);
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            return (List<ChatVirtualAssistant>) event.payload;
        } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
        }
        return new ArrayList<>();
    }

    public ChatVirtualAssistant getFirst() {
        Event event = RequestUtils.amqp(JpaConstants.Method.GET_FIRST,category,null,routingKey);
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            return (ChatVirtualAssistant) event.payload;
        } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
        }
        return null;
    }
}
