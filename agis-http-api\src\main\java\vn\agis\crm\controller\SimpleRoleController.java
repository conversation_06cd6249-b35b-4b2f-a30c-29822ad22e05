package vn.agis.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.SimpleRoleSearchDto;
import vn.agis.crm.base.jpa.dto.req.SimpleRoleDto;
import vn.agis.crm.base.jpa.entity.SimpleRole;
import vn.agis.crm.service.SimpleRoleApiService;

@RestController
@RequestMapping("/simple-roles")
public class SimpleRoleController extends CrudController<SimpleRole, Long> {

    private final SimpleRoleApiService roleService;

    @Autowired
    public SimpleRoleController(SimpleRoleApiService service) {
        super(service);
        this.roleService = service;
        this.baseUrl = "/simple-roles";
    }

    @PostMapping
    public ResponseEntity<SimpleRole> createRole(@RequestBody SimpleRoleDto dto) {
        SimpleRole created = roleService.createRole(dto);
        return ResponseEntity.status(201).body(created);
    }

    @PutMapping("/{id}")
    public ResponseEntity<SimpleRole> updateRole(@PathVariable Long id, @RequestBody SimpleRoleDto dto) {
        dto.setId(id);
        SimpleRole updated = roleService.updateRole(dto);
        return ResponseEntity.ok(updated);
    }

    @GetMapping("/{id}")
    public ResponseEntity<SimpleRoleDto> getRoleById(@PathVariable Long id) {
        SimpleRoleDto dto = roleService.findById(id);
        return ResponseEntity.ok(dto);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable Long id) {
        roleService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping
    public ResponseEntity<Page<SimpleRole>> searchRoles(
            @RequestParam(name = "name", required = false) String name,
            @RequestParam(name = "isActive", required = false) Boolean isActive,
            @RequestParam(name = "page", defaultValue = "0") int page,
            @RequestParam(name = "size", defaultValue = "10") int size,
            @RequestParam(name = "sort", defaultValue = "id,asc") String sort) {
        SimpleRoleSearchDto searchDto = new SimpleRoleSearchDto(name, isActive, page, size, sort);
        Page<SimpleRole> result = roleService.search(searchDto, new ListRequest(size, page, sort).getPageable());
        return ResponseEntity.ok(result);
    }

    @GetMapping("/checkExistName")
    public ResponseEntity<Boolean> checkExistName(@RequestParam("name") String name) {
        return ResponseEntity.ok(roleService.checkExistName(name));
    }
}

