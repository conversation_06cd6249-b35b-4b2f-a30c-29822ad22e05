package vn.agis.crm.service;

import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.conn.HttpHostConnectException;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.jpa.dto.req.Auth2RequestInit;
import vn.agis.crm.base.jpa.dto.resp.Auth2Response;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.entity.IOTTenant;
import vn.agis.crm.model.entity.IOTUser;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

import static vn.agis.crm.base.constants.Constants.OAuth2IoT.CLIENT_ID;
import static vn.agis.crm.base.constants.Constants.OAuth2IoT.SECRET_ID;


@Service
public class OAuth2Service {
    @Value("${oauth2Url}")
    String oauth2Url;
    @Value("${oauth2TenantUrl}")
    String oauth2TenantUrl;
    @Value("${oauth2UserUrl}")
    String oauth2UserUrl;
    Logger logger = LoggerFactory.getLogger(OAuth2Service.class);
    public Auth2Response oauth2Login(Auth2RequestInit auth2RequestInit) {
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .disableAutomaticRetries()
                .useSystemProperties()
                .build();
        try {
            HttpUriRequest method = null;
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("grant_type", auth2RequestInit.getGrant_type()));
            params.add(new BasicNameValuePair("client_id", auth2RequestInit.getClient_id()));
            params.add(new BasicNameValuePair("client_secret", auth2RequestInit.getClient_secret()));
            params.add(new BasicNameValuePair("code", auth2RequestInit.getCode()));
            method = new HttpPost(oauth2Url);
            ((HttpPost) method).setEntity(new UrlEncodedFormEntity(params));
            method.addHeader("Content-Type", "application/x-www-form-urlencoded");
            HttpResponse httpResponse = httpClient.execute(method);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity);
            Auth2Response auth2Response = ObjectMapperUtil.objectMapper(body, Auth2Response.class);
            int statusCode = httpResponse.getStatusLine().getStatusCode();
            logger.info("Http Client response: " + entity);
            return auth2Response;
        } catch(HttpHostConnectException e){
            logger.info("Target is not reachable: " + oauth2Url);
        } catch (IOException e){
            logger.error(oauth2Url + " not found", e);
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // Silently fail
                logger.trace("Fail closing the http client", e);
            }
        }
        return null;
    }

    public int createIOTTenant (IOTTenant iotTenant) {
        int statusCode = -1;
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).build();
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .disableAutomaticRetries()
                .useSystemProperties()
                .setDefaultRequestConfig(requestConfig)
                .build();
        try {
            HttpUriRequest method = null;
            method = new HttpPost(oauth2TenantUrl);
            String stringEntity = ObjectMapperUtil.toJsonString(iotTenant);
            ((HttpPost) method).setEntity(new StringEntity(stringEntity, "UTF-8"));
            method.addHeader("Content-Type", "application/json;charset=UTF-8");
            HttpResponse httpResponse = httpClient.execute(method);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity);
            statusCode = httpResponse.getStatusLine().getStatusCode();
            logger.info("Http Client response: " + entity);
        } catch(HttpHostConnectException e){
            logger.info("Target is not reachable: " + oauth2TenantUrl);
            statusCode = HttpStatus.SC_INTERNAL_SERVER_ERROR;
            return statusCode;
        } catch (IOException e){
            logger.error(oauth2TenantUrl + " not found", e);
            statusCode = HttpStatus.SC_INTERNAL_SERVER_ERROR;
            return statusCode;
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // Silently fail
                logger.trace("Fail closing the http client", e);
            }
        }
        return statusCode;
    }

    public int handleUserIOT(IOTUser iotUser, String httpMethod) {
        int statusCode = -1;
        RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(20000).build();
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .disableAutomaticRetries()
                .useSystemProperties()
                .setDefaultRequestConfig(requestConfig)
                .build();
        try {
            HttpUriRequest method = null;

            String stringEntity = ObjectMapperUtil.toJsonString(iotUser);
            if (httpMethod.equals("POST")) {
                method = new HttpPost(oauth2UserUrl);
                ((HttpPost) method).setEntity(new StringEntity(stringEntity, "UTF-8"));
            }
            else if (httpMethod.equals("PUT")) {
                method = new HttpPut(oauth2UserUrl);
                ((HttpPut) method).setEntity(new StringEntity(stringEntity, "UTF-8"));
            }
            method.addHeader("Content-Type", "application/json;charset=UTF-8");
            String basicAuthToken = CLIENT_ID + ":" + SECRET_ID;
            String encodedString = Base64.getEncoder().encodeToString(basicAuthToken.getBytes());
            method.addHeader("Authorization", "Basic " + encodedString);
            HttpResponse httpResponse = httpClient.execute(method);
            HttpEntity entity = httpResponse.getEntity();
            String body = EntityUtils.toString(entity);
            statusCode = httpResponse.getStatusLine().getStatusCode();
            logger.info("Http Client response: " + entity);
        } catch(HttpHostConnectException e){
            logger.info("Target is not reachable: " + oauth2UserUrl);
            statusCode = HttpStatus.SC_INTERNAL_SERVER_ERROR;
            return statusCode;
        } catch (IOException e){
            logger.error(oauth2UserUrl + " not found", e);
            statusCode = HttpStatus.SC_INTERNAL_SERVER_ERROR;
            return statusCode;
        }
        finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                // Silently fail
                logger.trace("Fail closing the http client", e);
            }
        }
        return statusCode;
    }
}

