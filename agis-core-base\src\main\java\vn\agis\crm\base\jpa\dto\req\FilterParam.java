package vn.agis.crm.base.jpa.dto.req;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FilterParam {
    String prKey;
    Integer prType;
    String prDisplayName;
    Boolean required;
    List<ValueList> valueList;
    Boolean isMultiChoice;
    Boolean isAutoComplete;
    String dateType;
    String displayPattern;
    String input;
    String objectKey;
    String output;
    QueryInfo queryInfo;




}
