package vn.agis.crm.base.event.amqp;

import com.google.common.base.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.context.ApplicationContext;
import vn.agis.crm.base.event.AMQPSubscriber;
import vn.agis.crm.base.event.Event;

import java.lang.reflect.InvocationTargetException;
import java.util.Map;

/**
 * Author: kiendt
 * Date: 3/10/2021
 * Contact: <EMAIL>
 */
public class MessageHandlerDirectReplyTo {
    private static final Logger logger = LoggerFactory.getLogger(MessageHandler.class);

    private ApplicationContext ctx;
    private AMQPSubscriber subscriber;

    public MessageHandlerDirectReplyTo(ApplicationContext ctx, AMQPSubscriber subscriber) {
        this.ctx = ctx;
        this.subscriber = subscriber;
    }

    public Object handleMessage(Object convertMessage) {
        if (convertMessage == null) return null;
        Event event = (Event) ((Map) convertMessage).get("event");
        MessageProperties messageProperties = (MessageProperties) ((Map) convertMessage).get("MessageProperties");
        Object instance = ctx.getBean(subscriber.getInstanceClass());
        try {
            if (!Strings.isNullOrEmpty(messageProperties.getReceivedRoutingKey()) && !Strings.isNullOrEmpty(subscriber.getRoutingKey()) && messageProperties.getReceivedRoutingKey().equals(subscriber.getRoutingKey()))
                return subscriber.getConsumeMethod().invoke(instance, event);
        } catch (IllegalAccessException e) {
            logger.error("Error handling event", e);
        } catch (InvocationTargetException e) {
            logger.error("Error handling event", e);
        } catch (Exception e) {
            logger.error("Error handling event", e);
        }
        return null;
    }
}
