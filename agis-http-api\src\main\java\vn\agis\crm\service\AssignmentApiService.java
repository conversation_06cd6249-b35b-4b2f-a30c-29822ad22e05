package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.req.ManualAssignmentRequest;
import vn.agis.crm.base.jpa.entity.CustomerAssignments;
import vn.agis.crm.util.RequestUtils;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import java.util.List;

import java.util.Map;

@Service
public class AssignmentApiService extends CrudService<CustomerAssignments, Long> {

    private static final Logger logger = LoggerFactory.getLogger(AssignmentApiService.class);

    public AssignmentApiService() {
        super(CustomerAssignments.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.ASSIGNMENT;
    }

//    @SuppressWarnings("unchecked")
    public Map<String, Object> manualAssign(ManualAssignmentRequest request) {
        Map<String, Object> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.MANUAL_ASSIGN, category, request, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                response = (Map<String, Object>) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new vn.agis.crm.base.exception.type.ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            // Handle other potential errors like BAD_REQUEST
            logger.error("Manual assignment failed with status code: {} and message: {}", event.respStatusCode, event.respErrorDesc);
            return null;
        } catch (Exception e) {
            logger.error("Error during manual assignment AMQP call: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(response), event);
        }
    }
}

