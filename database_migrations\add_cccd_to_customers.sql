-- Migration script to add CCCD (Citizen Identity Card) field to customers table
-- Vietnamese CCCD format: 12 digits (new format) or 9 digits (old format)
-- This script is compatible with MySQL/MariaDB

-- Add CCCD column to customers table
ALTER TABLE customers 
ADD COLUMN cccd VARCHAR(12) NULL COMMENT 'Citizen Identity Card number (CCCD)';

-- Create index for CCCD field to improve search performance
CREATE INDEX idx_customers_cccd ON customers(cccd);

-- Add unique constraint for CCCD to prevent duplicates (optional - uncomment if needed)
-- ALTER TABLE customers ADD CONSTRAINT uq_customer_cccd UNIQUE (cccd);

-- Update table comment to reflect the new field
ALTER TABLE customers COMMENT = 'Customer information including personal details and CCCD';

-- Verification queries (run after migration to verify)
-- SELECT COUNT(*) FROM customers WHERE cccd IS NOT NULL;
-- DESCRIBE customers;
-- SHOW INDEX FROM customers WHERE Key_name = 'idx_customers_cccd';
