package vn.agis.crm.model.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class AreasResponseDTO {
    private Long id;
    private String name;
    private String description;
    private Long parentId;
    private String parentName;
    private Integer status;
    private Date createdDate;
    private Date updatedDate;
    private Long createdBy;
    private String createdByName;
    private Long updatedBy;
    private String updatedByName;
    
    public AreasResponseDTO() {
    }
    
    public AreasResponseDTO(Long id, String name, String description, Long parentId, String parentName, 
                           Integer status, Date createdDate, Date updatedDate, Long createdBy, 
                           String createdByName, Long updatedBy, String updatedByName) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.parentId = parentId;
        this.parentName = parentName;
        this.status = status;
        this.createdDate = createdDate;
        this.updatedDate = updatedDate;
        this.createdBy = createdBy;
        this.createdByName = createdByName;
        this.updatedBy = updatedBy;
        this.updatedByName = updatedByName;
    }
    
    @Override
    public String toString() {
        return "AreasResponseDTO{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", parentId=" + parentId +
                ", parentName='" + parentName + '\'' +
                ", status=" + status +
                ", createdDate=" + createdDate +
                ", updatedDate=" + updatedDate +
                ", createdBy=" + createdBy +
                ", createdByName='" + createdByName + '\'' +
                ", updatedBy=" + updatedBy +
                ", updatedByName='" + updatedByName + '\'' +
                '}';
    }
}
