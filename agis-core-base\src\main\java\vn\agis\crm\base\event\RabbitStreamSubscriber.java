package vn.agis.crm.base.event;

import java.lang.reflect.Method;

public class RabbitStreamSubscriber {
    private String streamName;
    private short offsetType;
    private int concurrency;
    private boolean isSupperStream;
    private Method consumeMethod;
    private Class<?> instanceClass;
    private Class<? extends Object> eventType;

    public RabbitStreamSubscriber(String streamName, short offsetType, int concurrency, boolean isSupperStream, Method m, Class<?> declaringClass, Class<? extends Object> eventType) {
        this.streamName = streamName;
        this.offsetType = offsetType;
        this.concurrency = concurrency;
        this.isSupperStream = isSupperStream;
        this.consumeMethod = m;
        this.instanceClass = declaringClass;
        this.eventType = eventType;
    }

    public String getStreamName() {
        return streamName;
    }

    public void setStreamName(String streamName) {
        this.streamName = streamName;
    }

    public short getOffsetType() {
        return offsetType;
    }

    public void setOffsetType(short offsetType) {
        this.offsetType = offsetType;
    }

    public int getConcurrency() {
        return concurrency;
    }

    public void setConcurrency(int concurrency) {
        this.concurrency = concurrency;
    }

    public boolean isSupperStream() {
        return isSupperStream;
    }

    public void setSupperStream(boolean supperStream) {
        isSupperStream = supperStream;
    }

    public Method getConsumeMethod() {
        return consumeMethod;
    }

    public void setConsumeMethod(Method consumeMethod) {
        this.consumeMethod = consumeMethod;
    }

    public Class<?> getInstanceClass() {
        return instanceClass;
    }

    public void setInstanceClass(Class<?> instanceClass) {
        this.instanceClass = instanceClass;
    }

    public Class<? extends Object> getEventType() {
        return eventType;
    }

    public void setEventType(Class<? extends Object> eventType) {
        this.eventType = eventType;
    }
}
