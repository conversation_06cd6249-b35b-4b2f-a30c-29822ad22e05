package vn.agis.crm.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.GZIPInputStream;

public class GZip {
	private final static Logger logger = LoggerFactory.getLogger(GZip.class);
	//    public static void main(String[] args) {
	//
	//        String source = ("refused_subs_202107060400.log.gz");
	//        Path target = Paths.get("D:\\COMPANY\\2.PRIVATE\\2020\\SmartSMS\\6.FTP_Blacklist\\refused_subs_202106270400111.log");
	//
	//
	//        try {
	//
	//            decompressGzip2(source);
	//
	//        } catch (IOException e) {
	//            e.printStackTrace();
	//        }
	//
	//    }

	public static String decompressGzip2(String pathSource) throws IOException {

		Path source = Paths.get(pathSource);
		String pathTarget = pathSource.substring(0, pathSource.lastIndexOf("."));
		Path target = Paths.get(pathTarget);
		if (!Files.exists(Paths.get(pathSource))){
			logger.info("file not exist " +pathSource);
			return null;
		}
		try (GZIPInputStream gis = new GZIPInputStream(
				new FileInputStream(source.toFile()));
			 FileOutputStream fos = new FileOutputStream(target.toFile())) {

			// copy GZIPInputStream to FileOutputStream
			byte[] buffer = new byte[1024];
			int len;
			while ((len = gis.read(buffer)) > 0) {
				fos.write(buffer, 0, len);
			}

		}
		return pathTarget;
	}

	public static String decompressGzip(String pathSource) throws Exception {
		String pathTarget = pathSource.substring(0, pathSource.lastIndexOf("."));
		String command = "gunzip " + pathSource;
		Process process = Runtime.getRuntime().exec(command);
		process.waitFor();
		return pathTarget;
	}

	public static void compressGzip(String pathSource) throws Exception {
		String command = "gzip " + pathSource;
		Process process = Runtime.getRuntime().exec(command);
		process.waitFor();
	}

	//    public static String decompressGzip(String pathSource) throws IOException {
	//        String pathTarget = pathSource.substring(0, pathSource.lastIndexOf("."));
	//        try (GZIPInputStream in = new GZIPInputStream(new FileInputStream(new File(pathSource)))) {
	//            try (FileOutputStream out = new FileOutputStream(new File(pathTarget))) {
	//                byte[] buffer = new byte[1024];
	//                int len;
	//                while ((len = in.read(buffer)) != -1) {
	//                    out.write(buffer, 0, len);
	//                }
	//            }
	//        }
	//        return pathTarget;
	//    }
}
