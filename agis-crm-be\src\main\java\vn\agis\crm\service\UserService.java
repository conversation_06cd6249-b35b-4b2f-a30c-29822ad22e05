package vn.agis.crm.service;

import java.util.*;
import java.util.stream.Collectors;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import cz.jirutka.rsql.parser.RSQLParser;
import cz.jirutka.rsql.parser.ast.Node;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import vn.agis.crm.repository.*;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.CRMService;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.Constants.RoleStatus;
import vn.agis.crm.base.constants.Constants.UserStatus;
import vn.agis.crm.base.constants.Constants.UserType;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.MessageKeyConstant.Validation;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.LoginInfo;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.SSOLoginDTO;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.*;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.base.redis.ObjectCache;
import vn.agis.crm.base.rsql.CustomRsqlVisitor;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.config.ApplicationProperties;
import vn.agis.crm.model.dto.IGetUserDTO;
import vn.agis.crm.model.dto.ISearchUserDTO;
import vn.agis.crm.model.dto.UserIdNameDTO;
import vn.agis.crm.repository.*;
import vn.agis.crm.util.BaseController;
import vn.agis.crm.util.RequestUtils;

import java.util.concurrent.TimeUnit;


import static vn.agis.crm.base.constants.Constants.OAuth2IoT.CLIENT_ID;
import static vn.agis.crm.base.constants.Constants.OAuth2IoT.SECRET_ID;
import static vn.agis.crm.base.jpa.constants.JpaConstants.Method.GET_BY_KEY;

@Service
@Transactional
public class UserService {

    private static Logger logger = LoggerFactory.getLogger(UserService.class);

    private UserRepository userRepository;

    private final int authorizationCodeLength = 36;

    @Autowired
    private MailService mailService;

    @Autowired
    private UserRoleRepository userRoleRepository;


    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private OAuth2Service oAuth2Util;

    @Autowired
    private UserManageRepository userManageRepository;

    @Autowired
    private ProvinceRepository provinceRepository;

    @Autowired
    private WardRepository wardRepository;

    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private SmeRepository smeRepository;

    public UserService(UserRepository userRepository, ApplicationProperties applicationProperties) {
        this.userRepository = userRepository;
        this.userRepository = userRepository;
        this.applicationProperties = applicationProperties;
    }

    private final ApplicationProperties applicationProperties;

    private SecurityService securityService;

    @Autowired
    private PermissionRepository permissionRepository;

    @Autowired
    private ObjectCache systemCache;

    @Autowired
    public void setSecurityService(SecurityService securityService) {
        this.securityService = securityService;
    }

    private static String deviceRoutingKey = AMQPConstants.RoutingKey.ROUTING_KEY_DEVICE_MANAGEMENT;
    private static String deviceCategory = Constants.Category.DEVICE;

    public Event process(Event event) {
        switch (event.method) {
            case Constants.Method.SEARCH_USER:
                return processSearchUser(event);
            case Method.GET_ONE_USER:
                return processGetOneUser(event);
            case Method.CHANGE_STATUS_USER:
                return processChangeStatusUser(event);
            case Method.DELETE_USER:
                return processDeleteUser(event);
            case Method.CREATE_USER:
                return processCreateUser(event);
            case Method.UPDATE_USER:
                return processUpdateUser(event);
            case Method.EXIST_BY_EMAIL_OR_USERNAME:
                return processCheckExist(event);
            case Method.GET_LIST_ROLE:
                return processGetListRole(event);
            case Method.LOGIN:
                return login(event);
            case Method.SSO_LOGIN:
                return ssoLogin(event);
            case Method.VALIDATE_PERMISSION:
                return validatePermission(event);
//            case Method.VALIDATE_PERMISSION_TOKEN_PUBLIC:
//                return validatePermissionTokenPublic(event);
            case Method.CURRENT_USER:
                return processCurrentUser(event);
            case Method.FORGOT_PASSWORD_INIT:
                return processForgotPasswordInit(event);
            case Method.FORGOT_PASSWORD_FINISH:
                return processForgotPasswordFinish(event);
            case Method.CHANGE_PASSWORD_USER:
                return processChangePassword(event);
            case Method.VALIDATE_TOKEN_MAIL:
                return processValidateTokenEmail(event);
            case Method.VIEW_PROFILE:
                return processViewProfile(event);
            case Method.UPDATE_PROFILE:
                return processUpdateProfile(event);
            case Constants.Method.GET_LIST_USER_CHILD:
                return processGetListUserChild(event);
            case JpaConstants.Method.GET_ONE:
                return processGetOne(event);
//            case Method.CHANGE_USER_FOR_MANAGER:
//                return processChangeUserForManager(event);
//            case Method.SEARCH_USER_CUSTOMER_MANAGE:
//                return getManagedCustomerAccounts(event);
            case Method.SEARCH_USER_CUSTOMER_NO_ONE_MANAGE:
                return getNoOneManagedCustomerAccounts(event);
            case Method.GET_LIST_ACTIVATED_ACCOUNT:
                return processGetListActivatedAccount(event);
            case Method.GET_PROVINCE:
                return processGetProvince(event);
            case GET_BY_KEY:
                return processGetByKey(event);
            case Constants.Method.GET_LIST_NAME_BY_IDS:
                return processGetListNamesByIds(event);
            case Method.SEARCH_WARD:
                return processSearchWard(event);
        }
        return event;
    }

    private Event processGetProvince(Event event) {
        List<Province> provinces = provinceRepository.findAll();
        return event.createResponse(ObjectMapperUtil.toJsonString(provinces), ResponseCode.OK, null);
    }

    private Event processSearchWard(Event event) {
        SearchWardRequest searchWardRequest = (SearchWardRequest) event.payload;
        List<Ward> wards = wardRepository.searchWard(searchWardRequest);
        return event.createResponse(ObjectMapperUtil.toJsonString(wards), ResponseCode.OK, null);
    }

    private Event processSearchUser(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
        if (searchUserRequest.getCustomerId() != -1 && searchUserRequest.getManagerId() != -1) {
            event.createResponse(null, ResponseCode.CONFLICT, null);
        }
        Page<ISearchUserDTO> pageUser = userRepository.getPageUser(searchUserRequest, event.userType, event.userId, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, ResponseCode.OK, null);
    }


    private Event processGetOneUser(Event event) {
//        try {
        Long id = (Long) event.payload;
        IGetUserDTO userDTO = userRepository.getOneUser(id);
        // trường hợp id không tồn tại
        if (Objects.isNull(userDTO)) {
            return event.createResponse(Arrays.asList("id", id), ResponseCode.NOT_FOUND, null);
        }
        if (userDTO.getStatus().intValue() == UserStatus.DEACTIVE) {
            return event.createResponse(Arrays.asList("id", id), ResponseCode.FORBIDDEN, null);
        }
        if (event.userType.equals(UserType.ENTERPRISE)) {
            List<Long> listManagedUserId = userManageRepository.getUserIdByUserManageId(event.userId);
            listManagedUserId.add(event.userId);
            if (!listManagedUserId.contains(id)) {
                return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
            }
        }
        UserResponseDTO userResponseDTO = new UserResponseDTO();
        BeanUtils.copyProperties(userDTO, userResponseDTO);
//                // lấy ds role
        if (!StringUtils.isBlank(userDTO.getRoleNames()) && !StringUtils.isBlank(userDTO.getRoleIds())) {
            String[] roleNames = userDTO.getRoleNames().split(",");
            String[] roleIds = userDTO.getRoleIds().split(",");

            for (int i = 0; i < roleNames.length; i++) {
                UserResponseDTO.RoleDTO roleDTO = new UserResponseDTO.RoleDTO(roleNames[i], Long.parseLong(roleIds[i]));
                userResponseDTO.getRoles().add(roleDTO);
            }
        }
        // lấy root acc để hiển thị
        if (userDTO.getType().equals(UserType.CUSTOMER)) {
            try {
                UserManage manager = userManageRepository.getUserManageByUserId(userDTO.getId());

                if (manager != null) {
                    if (event.userType.equals(UserType.CUSTOMER) && !Objects.equals(manager.getUserManageId(), userDTO.getUserManageId())) {
                        return event.createResponse(new ArrayList<String>(), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                    }
                    Optional optionalManagerAccount = userRepository.findById(manager.getUserManageId());
                    User managerAccount = (User) optionalManagerAccount.get();
                    UserResponseDTO.UserManageDTO userManageDTO = new UserResponseDTO.UserManageDTO();
                    userManageDTO.setId(managerAccount.getId());
                    userManageDTO.setName(managerAccount.getName());
                    userManageDTO.setUsername(managerAccount.getUsername());
                    userResponseDTO.setManager(userManageDTO);
                }
//                        }
            } catch (Exception e) {
                logger.error("Lỗi lấy thông tin account root customer", e);
            }
        }
        return event.createResponse(userResponseDTO, ResponseCode.OK, null);
//        } catch (Exception e) {
//            logger.error("Exception get one User {}", e.getMessage(), e);
//        }
    }


    private Event processGetListNamesByIds(Event event) {
        logger.info("Received request to get user names for IDs: {}", event.payload);
        List<Long> userIds = (List<Long>) event.payload;
        List<UserIdNameDTO> userIdNameList = userRepository.findUserIdsAndNamesByIdIn(userIds);
        Map<Long, String> userNamesMap = new HashMap<>();
        for (UserIdNameDTO dto : userIdNameList) {
            userNamesMap.put(dto.getId(), dto.getName());
        }
        logger.info("Returning user names map: {}", userNamesMap);
        return event.createResponse(userNamesMap, ResponseCode.OK, null);
    }


    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void batchInsert(List<Long> entities, String sql) {
//        String sql = "INSERT INTO CUSTOMER_CODE_TEMP_TABLE (customer_code) VALUES (?)";
        jdbcTemplate.batchUpdate(sql, entities, 1000, (ps, entity) -> {
            ps.setLong(1, entity);
        });
    }


    private Event processChangeStatusUser(Event event) {
        Long id = (Long) event.payload;
        Optional<User> optUser = userRepository.findById(id);
        // trường hợp id không tồn tại
        if (!optUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User user = optUser.get();
        if (checkPermissionCreateUpdateUser(event.userType, user.getType())) {
            // với tài khoản login là khách hàng -> check xem tài khoản đó có phải là con của khách hàng không thì mới được chuyển trạng thái
            if (event.userType.equals(UserType.CUSTOMER)) {
                if (isChildOfUser(id, event.userId)) {
                    user.setStatus(user.getStatus() == UserStatus.ACTIVE ? UserStatus.INACTIVE : UserStatus.ACTIVE);
                    return event.createResponse(user, ResponseCode.OK, null);
                } else {
                    return event.createResponse(MessageKeyConstant.FORBIDDEN, ResponseCode.FORBIDDEN, null);
                }
            } else {
                // với các loại tài khoản khác -> chuyển trạng thái Không hoạt động <-> hoạt động
                user.setStatus(user.getStatus() == UserStatus.ACTIVE ? UserStatus.INACTIVE : UserStatus.ACTIVE);
                return event.createResponse(user, ResponseCode.OK, null);
            }
        }
        return event.createResponse(MessageKeyConstant.FORBIDDEN, ResponseCode.FORBIDDEN, null);
    }

    boolean isRootCustomer(Long userId) {
//        List<IGetCountChildUser> iGetCountChildUsers = userRepository.getCountChildOfUser();
//        for (IGetCountChildUser countChildUser : iGetCountChildUsers) {
//            if (countChildUser.getId().equals(userId)) {
//                if (countChildUser.getChildrenCount() > 0) return true;
//            }
//        }
        List<Long> listIdsChild = getListChildOfUser(userId);
        return !listIdsChild.isEmpty();
    }

    /*
     * Check Tài khoản loại khách hàng chỉ có quyền cập nhật tài khoản là cấp dưới của nó
     * */
    boolean isChildOfUser(Long userId, Long userIdLogin) {
        List<Long> listIdsChild = getListChildOfUser(userIdLogin);
        return listIdsChild.contains(userId);
    }

    private Event processDeleteUser(Event event) {
        Long id = (Long) event.payload;
        Optional<User> optUser = userRepository.findById(id);
        // trường hợp id không tồn tại
        if (!optUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User user = optUser.get();
        if (checkPermissionCreateUpdateUser(event.userType, user.getType())) {
            if (user.getType().equals(UserType.CUSTOMER) && event.userType.equals(UserType.ENTERPRISE)) {
                if (!isChildOfUser(user.getId(), event.userId)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
                // khách hàng có con thì sẽ k cho xóa
                if (isRootCustomer(id)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
            }
//                // update trang thai user can xoa
            user.setStatus(UserStatus.DEACTIVE);
            userManageRepository.deleteByUserId(user.getId());
            userRepository.save(user);
            return event.createResponse(user, ResponseCode.OK, null);
        }
        return event.createResponse(Arrays.asList("type"), ResponseCode.FORBIDDEN, null);
    }


    private Event processCreateUser(Event event) {
        try {
            //TODO Lấy user đăng nhập
            User userLogin = userRepository.findById(event.userId).get();

            CreateUserReq createUserReq = (CreateUserReq) event.payload;
            // check email hoặc username tồn tại
            if (!validateCreateUser(createUserReq)) {
                return event.createResponse(Arrays.asList("email", "username"), ResponseCode.CONFLICT, null);
            }

            if (checkPermissionCreateUpdateUser(userLogin.getType(), createUserReq.getType())) {
                User user = createUserRequestToUserEntity(createUserReq);
                user.setCreatedBy(userLogin.getId());
                // tạo tk TỈNH/TP , tạo tk Quận/huyện GDV thì lưu luôn
                User saveUser = userRepository.save(user);
                // case 1: tạo tk ADMIN
//                if (createUserReq.getType().equals(UserType.ADMIN)) {
//
//                } else if (createUserReq.getType().equals(UserType.ENTERPRISE)) {
//                } else
                if (createUserReq.getType().equals(UserType.CUSTOMER)) {
                    if (createUserReq.getAccountRootId() == null) {
                        createUserReq.setAccountRootId(event.userId);
                    }
                    if (createUserReq.getAccountRootId() != null) {
                        addUserManage(List.of(saveUser.getId()), createUserReq.getAccountRootId(), event);
                    }
                }
                // lưu role vào bảng role_user
                addRoleUser(createUserReq.getRoleLst(), saveUser.getId());
                // send email
//                mailService.sendCreationEmail(saveUser, rawPassword);
                return event.createResponse(saveUser, ResponseCode.OK, null);
            }
        } catch (Exception e) {
            logger.error("Exception processCreateUser {} ", e.getMessage(), e);
        }
        return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
    }


    boolean checkPermissionCreateUpdateUser(Integer typeUserLogin, Integer typeCreateUserReq) {
        // ADMIN sẽ có quyền tạo, sửa tất cả user
        // KH cũng có thể tạo KH
        // các role còn lại sẽ có quyền tạo tất cả user cấp dưới
//        if (typeUserLogin.equals(UserType.ADMIN)) {
//            return true;
//        } else
        if (typeUserLogin < typeCreateUserReq) {
            return true;
        }
        return false;
//        } else {
////            return typeUserLogin.equals(UserType.CUSTOMER) && typeUserLogin <= typeCreateUserReq;
//        }
    }

    private User createUserRequestToUserEntity(CreateUserReq createUserReq) {
        User user = new User();
        user.setUsername(createUserReq.getUsername());
        user.setCreatedAt(new Date());
        user.setEncryptedPassword(createUserReq.getPassword());
        user.setStatus(UserStatus.ACTIVE);
//        user.setFullName(createUserReq.getFullName());
        user.setEmail(createUserReq.getEmail());
        user.setDescription(createUserReq.getDescription());
        user.setPhone(createUserReq.getPhone());
        user.setType(createUserReq.getType());
        user.setRepresentativeName(createUserReq.getRepresentativeName());
//        user.setProvinceCode(createUserReq.getProvinceCode());
//        user.setCustomerType(createUserReq.getCustomerType());
        user.setAddressContact(createUserReq.getAddressContact());
        user.setProvinceCodeAddress(createUserReq.getProvinceCodeAddress());
        user.setWardCodeAddress(createUserReq.getWardCodeAddress());
        user.setProvinceCodeOffice(createUserReq.getProvinceCodeOffice());
        user.setWardCodeOffice(createUserReq.getWardCodeOffice());
        user.setApartment(createUserReq.getApartment());
        user.setName(createUserReq.getName());
        if (createUserReq.getType().equals(UserType.ENTERPRISE)) {
            user.setAddressHeadOffice(createUserReq.getAddressHeadOffice());
            user.setTaxCode(createUserReq.getTaxCode());
        }
        return user;
    }


    private void addRoleUser(List<Long> roleLst, Long userId) {
        List<UserRole> userRoles = new ArrayList<>();
        for (Long roleId : roleLst) {
            UserRole userRole = new UserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(userId);
            userRoles.add(userRole);
        }
        userRoleRepository.saveAll(userRoles);
    }


    private void addUserManage(List<Long> userIdLst, Long userMangeId, Event oldEvent) {
        List<UserManage> userManages = new ArrayList<>();
        if (userIdLst != null && userIdLst.size() > 0) {
            // Tìm những user đang dc quản lý bởi userMangeId
            List<UserManage> oldUserManage = userManageRepository.findDistinctByUserManageId(userMangeId);
            List<Long> userIdOldList = oldUserManage.stream().map(dto -> dto.getUserId()).collect(Collectors.toList());
//            Set<Long> noDuplicateUserIdOldList = new HashSet<Long>(userManages);

            for (Long userId : userIdLst) {
                if (!userIdOldList.contains(userId)) {
                    UserManage userCustomer = new UserManage();
                    userCustomer.setUserManageId(userMangeId);
                    userCustomer.setUserId(userId);
                    userManages.add(userCustomer);
                }
            }
            if (userManages.size() > 0) {
                userManageRepository.saveAll(userManages);
                updateDevice(userManages, oldEvent);
            }

        }
    }

    void updateDevice(List<UserManage> userManages, Event oldEvent) {
        Event event = RequestUtils.amqp(Method.UPDATE_DEVICE_ENTERPRISE, deviceCategory, userManages, deviceRoutingKey, oldEvent);
    }

    private List<Long> getListChildOfUser(Long userId) {
        List<Long> listFinal = new ArrayList<>();
        listFinal.addAll(userRepository.findAllByUserManageOrUser(userId)
                .stream()
                .map(user -> user.getId())
                .collect(Collectors.toList()));
        ;
        return listFinal;
    }

    boolean validateCreateUser(CreateUserReq createUserReq) {
        List<User> checkUsers = userRepository.findByEmailOrUsername(createUserReq.getEmail(), createUserReq.getUsername());
        if (!checkUsers.isEmpty()) {
            return false;
        }
        return true;
    }


    public Event processUpdateUser(Event event) {
        UpdateUserReq updateUserReq = (UpdateUserReq) event.payload;
        // check email tồn tại
        if (!validateUpdateUser(updateUserReq)) {
            return event.createResponse(Arrays.asList("email"), ResponseCode.CONFLICT, Validation.EXISTS);
        }
        // check userId của UpdateUserReq
        Optional<User> optUpdateUser = userRepository.findById(updateUserReq.getId());
        if (!optUpdateUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, MessageKeyConstant.NOT_FOUND);
        }
        User updateUser = optUpdateUser.get();
        // k cho update super admin
        if (updateUser.getCreatedBy() == 0) {
            return event.createResponse(Arrays.asList(""), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
        }

        // kiểm tra xem có quyền create hoặc update hay không ?
        if (checkPermissionCreateUpdateUser(event.userType, updateUser.getType())) {

            if (updateUser.getType().equals(UserType.CUSTOMER) && event.userType.equals(UserType.ENTERPRISE)) {
                if (!isChildOfUser(updateUser.getId(), event.userId)) {
                    return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
                }
            }

            if (updateUser.getType().equals(UserType.CUSTOMER) && !isChildOfUser(updateUser.getId(), updateUserReq.getAccountRootId())) { // Nếu tài khoản được cập nhật là CUSTOMER => Xóa bản ghi chứa ENTERPRISE cũ, thêm mới ENTERPRISE mới
                List<Long> listUserUpdate = new ArrayList<>();
                listUserUpdate.add(updateUser.getId());
                userManageRepository.deleteByUserIdIn(listUserUpdate);
                if (updateUserReq.getAccountRootId() != null) {
                    addUserManage(listUserUpdate, updateUserReq.getAccountRootId(), event);
                }
            }
            updateUser.setName(updateUserReq.getName());
            updateUser.setDescription(updateUserReq.getDescription());
            updateUser.setEmail(updateUserReq.getEmail());
            updateUser.setPhone(updateUserReq.getPhone());
            updateUser.setAddressContact(updateUserReq.getAddressContact());
            updateUser.setAddressHeadOffice(updateUserReq.getAddressHeadOffice());
            updateUser.setTaxCode(updateUserReq.getTaxCode());
            updateUser.setRepresentativeName(updateUserReq.getRepresentativeName());
            updateUser.setStatus(updateUserReq.getStatus());
            updateUser.setApartment(updateUserReq.getApartment());
            if (updateUserReq.getPassword() != null) {
                updateUser.setEncryptedPassword(updateUserReq.getPassword());
            }
            if (updateUserReq.getProvinceCodeAddress() != null) {
                updateUser.setProvinceCodeAddress(updateUserReq.getProvinceCodeAddress());
            }
            if (updateUserReq.getWardCodeAddress() != null) {
                updateUser.setWardCodeAddress(updateUserReq.getWardCodeAddress());
            }
            if (updateUserReq.getProvinceCodeOffice() != null) {
                updateUser.setProvinceCodeOffice(updateUserReq.getProvinceCodeOffice());
            }
            if (updateUserReq.getWardCodeOffice() != null) {
                updateUser.setWardCodeOffice(updateUserReq.getWardCodeOffice());
            }
            updateRoleUser(updateUserReq.getRoleLst(), updateUser.getId());
            return event.createResponse(updateUser, ResponseCode.OK, null);
        }
        return event.createResponse(new ArrayList<>(), ResponseCode.FORBIDDEN, null);
    }


    boolean validateUpdateUser(UpdateUserReq updateUserReq) {
        List<User> checkUsers = userRepository.findByEmailAndIdNot(updateUserReq.getEmail(), updateUserReq.getId());
        if (!checkUsers.isEmpty()) {
            return false;
        }
        return true;
    }

    void updateRoleUser(List<Long> lstRole, Long userId) {
        userRoleRepository.deleteByUserId(userId);
        addRoleUser(lstRole, userId);
    }


    private Event processCheckExist(Event event) {
        CheckExistUserReq existUserReq = (CheckExistUserReq) event.payload;
        String email = existUserReq.getEmail();
        String username = existUserReq.getUsername();
        List<User> userLst = userRepository.findByEmailOrUsername(email, username);
        return event.createResponse(userLst.size(), ResponseCode.OK, null);
    }


    private Event processGetListRole(Event event) {
        final Integer roleTypeAll = 0;
        SearchRoleCreateAccountReq search = (SearchRoleCreateAccountReq) event.payload;
        List<Long> userIdList = new ArrayList<>();
        userIdList.add(-1L); // tránh null
        if (event.userType == UserType.ENTERPRISE) {
            // thêm tài khoản con và chính nó vào list
//            userIdList = getListChildOfListUser(List.of(event.userId));
            userIdList = getListChildOfUser(event.userId);
            userIdList.add(event.userId);
        }
        List<Role> roles = roleRepository.findByTypeInAndStatus(event.userType, userIdList, search.getType());
        return event.createResponse(roles, ResponseCode.OK, null);
    }


    private Event login(Event event) {
        LoginInfo loginInfo = (LoginInfo) event.payload;
        User user = this.findByEmail(loginInfo.getEmail());
        if (user == null) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.EMAIL_INCORRECT);
        }
        if (Boolean.FALSE.equals(user.authenticate(loginInfo.getPassword()))) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.PASSWORD_INCORRECT);

        }

        if (user.getStatus() == null || !user.getStatus().equals(1)) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_INACTIVE);
        }
        List<Role> lstRoleActive = roleRepository.getRolesByUserId(user.getId());
        if (lstRoleActive.isEmpty()) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ROLE_NOT_WORKING);
        }
        logger.info("Generate token for user: {}", loginInfo.getEmail());
        Date validity;
        long now = (new Date()).getTime();
        if (Boolean.TRUE.equals(loginInfo.getRememberMe())) {
            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
        } else {
            validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
        }

        String token = Jwts.builder()
                .setSubject(user.getUsername())
                .setExpiration(validity)
                .claim(CRMService.JWT_USER_ID, user.getId())
                .claim(CRMService.JWT_SCOPE, getAuthorities(user))
                .signWith(SignatureAlgorithm.HS512, CRMService.JWT_SECRET)
                .compact();
        logger.info("Token generated for user {}, token: {}", loginInfo.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);
//        if (
//                getPeriodExpirePassword(
//                        Objects.isNull(user.getUpdatedPasswordDate()) ?
//                                user.getCreatedDate() :
//                                user.getUpdatedPasswordDate()
//                ) >= 6
//        ) {
//            return event.createResponse(response, ResponseCode.EXPIRED_PASSWORD, Validation.EXPIRED_PASSWORD);
//        }

        return event.createResponse(response, ResponseCode.OK, null);

    }

    private Event ssoLogin(Event event) {

//         call to sme to check token
        SSOLoginDTO loginInfo = (SSOLoginDTO) event.payload;
        Event checkSSOToken = RequestUtils.amqpAsAdmin(Constants.Method.CHECK_TOKEN_BOS, Constants.Category.USER, loginInfo, AMQPConstants.RoutingKey.ROUTING_KEY_BOS_ADAPTER);
        if (!checkSSOToken.respStatusCode.equals(ResponseCode.OK)) {
            return checkSSOToken;
        }
        logger.info("ssoLogin Info {}",  new Gson().toJson(checkSSOToken));
        // find user from subscriptionId
        Optional<Subscription> subscription = subscriptionRepository.findById(Long.valueOf(loginInfo.getSubscription()));
        if (subscription.isEmpty()) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_NOT_EXIST);
        }
        Long smeId = subscription.get().getSmeId();
        Optional<Sme> sme = smeRepository.findById(smeId);
        if (sme.isEmpty()) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_NOT_EXIST);
        }
        Long userId = sme.get().getUserId();
        User user = this.userRepository.findById(userId).get();
//        User user = this.userRepository.findById(1767l).get();
        if (user == null) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_NOT_EXIST);
        }

        if (user.getStatus() == null || !user.getStatus().equals(1)) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ACCOUNT_INACTIVE);
        }
        List<Role> lstRoleActive = roleRepository.getRolesByUserId(user.getId());
        if (lstRoleActive.isEmpty()) {
            return event.createResponse(null, ResponseCode.NOT_FOUND, Validation.ROLE_NOT_WORKING);
        }
        logger.info("Generate token for user: {}", user.getEmail());
        Date validity;
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
//        if (Boolean.TRUE.equals(loginInfo.getRememberMe())) {
//            validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);
//        } else {
//            validity = new Date(now + this.applicationProperties.getTokenTime().getNoRemember() * 1000);
//        }

        String token = Jwts.builder()
                .setSubject(user.getUsername())
                .setExpiration(validity)
                .claim(CRMService.JWT_USER_ID, user.getId())
                .claim(CRMService.JWT_SCOPE, getAuthorities(user))
                .signWith(SignatureAlgorithm.HS512, CRMService.JWT_SECRET)
                .compact();
        logger.info("Token generated for user {}, token: {}", user.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);
//        if (
//                getPeriodExpirePassword(
//                        Objects.isNull(user.getUpdatedPasswordDate()) ?
//                                user.getCreatedDate() :
//                                user.getUpdatedPasswordDate()
//                ) >= 6
//        ) {
//            return event.createResponse(response, ResponseCode.EXPIRED_PASSWORD, Validation.EXPIRED_PASSWORD);
//        }

        return event.createResponse(response, ResponseCode.OK, null);

    }

    public User findByEmail(String email) {
        User user = this.userRepository.findOneByEmailIgnoreCase(email);
        if (user == null) {
            return this.userRepository.findOneByUsernameIgnoreCase(email);
        }
        return user;
    }

    public List<String> getAuthorities(User user) {
        List<Long> roleIds = userRoleRepository.getRoleIdByUserId(user.getId());
        List<String> authorities = roleRepository.findByIdInAndStatus(roleIds, RoleStatus.ACTIVE)
                .stream()
                .map(Role::getName)
                .collect(Collectors.toList());
        authorities.addAll(permissionRepository.getListPermissionKeyByRoleIds(roleIds));
        return authorities;
    }

    private Event validatePermission(Event event) {
        String token = (String) event.payload;
        if (token.length() == authorizationCodeLength) {
            Auth2RequestInit auth2RequestInit = new Auth2RequestInit();
            auth2RequestInit.setClient_id(CLIENT_ID);
            auth2RequestInit.setClient_secret(SECRET_ID);
            auth2RequestInit.setGrant_type("authorization_code");
            auth2RequestInit.setCode(token);
            Auth2Response auth2Response = oAuth2Util.oauth2Login(auth2RequestInit);
            String[] chunks = auth2Response.getIdToken().split("\\.");
            Base64.Decoder decoder = Base64.getUrlDecoder();
            String payload = new String(decoder.decode(chunks[1]));
            JsonObject jsonObject = new Gson().fromJson(payload, JsonObject.class);
            String userName = jsonObject.get("userName").isJsonNull() ? " " : jsonObject.get("userName").getAsString();
            String email = jsonObject.get("email").getAsString();
            AuthResponseWithTokenAndErrorCodeDTO authResponseWithTokenAndErrorCodeDTO = createTokenEmail(email, userName);
            if (authResponseWithTokenAndErrorCodeDTO != null) {
                token = authResponseWithTokenAndErrorCodeDTO.getAccessToken();
            }
        }
        if (securityService.validateToken(token)) {
            event.respStatusCode = ResponseCode.OK;
            Claims claims = securityService.parseToken((token));
            User user = current(claims);
            if (user == null) {
                return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
            }
            return event.createResponse(user, ResponseCode.OK, null);
        } else {
            return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
        }
    }


    private AuthResponseWithTokenAndErrorCodeDTO createTokenEmail(String email, String userName) {
        User user = userRepository.findOneByUsernameIgnoreCase(userName);
        if (user == null) {
            user = this.findByEmail(email);
        }
        if (user == null) {
            return null;
        }
        if (user.getStatus() == null || !user.getStatus().equals(UserStatus.ACTIVE)) {
            return null;
        }
        List<Role> lstRoleActive = roleRepository.getRolesByUserId(user.getId());
        if (lstRoleActive.isEmpty()) {
            return null;
        }
        Date validity;
        long now = (new Date()).getTime();
        validity = new Date(now + this.applicationProperties.getTokenTime().getRemember() * 1000);

        String token = Jwts.builder()
                .setSubject(user.getEmail())
                .setExpiration(validity)
                .claim(CRMService.JWT_USER_ID, user.getId())
                .claim(CRMService.JWT_SCOPE, getAuthorities(user))
                .signWith(SignatureAlgorithm.HS512, CRMService.JWT_SECRET)
                .compact();
//        logger.info("Token generated for user {}, token: {}", loginInfo.getEmail(), token);
        AuthResponseWithTokenAndErrorCodeDTO response = new AuthResponseWithTokenAndErrorCodeDTO();
        response.setAccessToken(token);
        response.setExp(validity.getTime() / 1000);
        response.setNbf(now / 1000);
//        if (
//                getPeriodExpirePassword(
//                        Objects.isNull(user.getUpdatedPasswordDate()) ?
//                                user.getCreatedDate() :
//                                user.getUpdatedPasswordDate()
//                ) >= 6
//        ) {
//            return null;
//        }

        return response;

    }


    public User current(Claims claims) {
        String email = claims.getSubject();
        User user = this.userRepository.findOneByUsernameIgnoreCase(email);
        if (user == null) {
            return null;
        }
        List<Long> roleIds = userRoleRepository.getRoleIdByUserId(user.getId());
        user.setRoles(roleRepository.findByIdInAndStatus(roleIds, RoleStatus.ACTIVE)
                .stream()
                .map(Role::getName)
                .collect(Collectors.toList()));
        user.setAuthorities(permissionRepository.getListPermissionKeyByRoleIds(roleIds));
        return user;
    }


    private Event processCurrentUser(Event event) {
        String token = (String) event.payload;
        if (token.length() == authorizationCodeLength) {
            Auth2RequestInit auth2RequestInit = new Auth2RequestInit();
            auth2RequestInit.setClient_id(CLIENT_ID);
            auth2RequestInit.setClient_secret(SECRET_ID);
            auth2RequestInit.setGrant_type("authorization_code");
            auth2RequestInit.setCode(token);
            Auth2Response auth2Response = oAuth2Util.oauth2Login(auth2RequestInit);
            if (auth2Response != null) {
                String[] chunks = auth2Response.getIdToken().split("\\.");
                Base64.Decoder decoder = Base64.getUrlDecoder();
                String payload = new String(decoder.decode(chunks[1]));
                JsonObject jsonObject = new Gson().fromJson(payload, JsonObject.class);
                String userName = jsonObject.get("userName").isJsonNull() ? " " : jsonObject.get("userName").getAsString();
                String email = jsonObject.get("email").getAsString();
                AuthResponseWithTokenAndErrorCodeDTO authResponseWithTokenAndErrorCodeDTO = createTokenEmail(email, userName);
                if (authResponseWithTokenAndErrorCodeDTO != null) {
                    token = authResponseWithTokenAndErrorCodeDTO.getAccessToken();
                }
            }
        }
        if (securityService.validateToken(token)) {
            Claims claims = securityService.parseToken(token);
            User user = current(claims);
            user.setTokenOauth2(token);
            return event.createResponse(user, ResponseCode.OK, null);
        }
        return event.createResponse(null, ResponseCode.INVALID_TOKEN, null);
    }


    private Event processForgotPasswordInit(Event event) {
        ForgotPasswordInfo info = (ForgotPasswordInfo) event.payload;
        User userEntity = this.userRepository.findOneByEmailIgnoreCase(info.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        String token = UUID.randomUUID().toString();
        try {
            Long expirePeriodResetPassword = applicationProperties.getActivation().getExpirePeriodResetPassword();
            systemCache.put(userEntity.getUsername(), token);
            systemCache.expire(userEntity.getUsername(), expirePeriodResetPassword, TimeUnit.SECONDS, String.class);
        } catch (Throwable e) {
            logger.error("Error push key #{} to redis", userEntity.getEmail(), e);
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
        if (applicationProperties.getActivation().isEnableMail()) {
            try {
                mailService.sendPasswordResetMail(userEntity, token);
            } catch (Exception e) {
                e.printStackTrace();
            }
            logger.debug("Request forgot password user: #{}", userEntity.getEmail());
        }

        return event.createResponse(null, ResponseCode.OK, null);
    }

    @SuppressWarnings("Duplicates")
    private Event processForgotPasswordFinish(Event event) {
        ResetPasswordInfo resetPasswordInfo = (ResetPasswordInfo) event.payload;
        User userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        if (!resetPasswordInfo.getNewPassword().equals(resetPasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }

        try {
            // check token
            String token = (String) systemCache.get(userEntity.getUsername(), String.class);
            if (token == null || !token.equals(resetPasswordInfo.getForgotPasswordToken())) {
                return handlerInvalidToken(event);
            }
            systemCache.remove(userEntity.getUsername(), String.class);
            userEntity.setEncryptedPassword(resetPasswordInfo.getNewPassword());
            userRepository.save(userEntity);
            logger.debug("Change forgot password user: #{}", userEntity.getEmail());
            return event.createResponse(null, ResponseCode.OK, null);
        } catch (Throwable e) {
            logger.error("Error get,remove key #{} from redis", userEntity.getEmail(), e);
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
    }

    private Event processValidateTokenEmail(Event event) {
        ResetPasswordInfo resetPasswordInfo = (ResetPasswordInfo) event.payload;
        User userEntity = userRepository.findOneByEmailIgnoreCase(resetPasswordInfo.getEmail());
        if (userEntity == null) {
            return handlerNoneEmail(event);
        }
        try {
            // check token
            String token = (String) systemCache.get(userEntity.getUsername(), String.class);
            if (token == null || !token.equals(resetPasswordInfo.getForgotPasswordToken())) {
                return handlerInvalidToken(event);
            }
            return event.createResponse(null, ResponseCode.OK, null);
        } catch (Throwable e) {
            return event.createResponse(null, ResponseCode.BAD_REQUEST, null);
        }
    }

    private Event processUpdateProfile(Event event) {
//        //TODO Lấy user đăng nhập
        UpdateUserReq updateUserReq = (UpdateUserReq) event.payload;
        // check email hoặc username tồn tại
        if (!validateUpdateUser(updateUserReq)) {
            return event.createResponse(Arrays.asList("email"), ResponseCode.CONFLICT, null);
        }
        Optional<User> optUpdateUser = userRepository.findById(event.userId);

        if (!optUpdateUser.isPresent()) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        User updateUser = optUpdateUser.get();
        updateUser.setName(updateUserReq.getName());
        updateUser.setDescription(updateUserReq.getDescription());
        updateUser.setEmail(updateUserReq.getEmail());
        updateUser.setPhone(updateUserReq.getPhone());
        updateUser.setAddressContact(updateUserReq.getAddressContact() == null ? null : updateUserReq.getAddressContact());
        updateUser.setAddressHeadOffice(updateUserReq.getAddressHeadOffice() == null ? null : updateUserReq.getAddressHeadOffice());
        updateUser.setTaxCode(updateUserReq.getTaxCode() == null ? null : updateUserReq.getTaxCode());
        updateUser.setRepresentativeName(updateUserReq.getRepresentativeName() == null ? null : updateUserReq.getRepresentativeName());
        updateUser.setProvinceCodeOffice(updateUserReq.getProvinceCodeOffice() == null ? null : updateUserReq.getProvinceCodeOffice());
        updateUser.setWardCodeOffice(updateUserReq.getWardCodeOffice() == null ? null : updateUserReq.getWardCodeOffice());
        updateUser.setProvinceCodeAddress(updateUserReq.getProvinceCodeAddress() == null ? null : updateUserReq.getProvinceCodeAddress());
        updateUser.setWardCodeAddress(updateUserReq.getWardCodeAddress() == null ? null : updateUserReq.getWardCodeAddress());
        if (updateUserReq.getPassword() != null) {
            updateUser.setEncryptedPassword(updateUserReq.getPassword());
        }
        userRepository.save(updateUser);
//        return event.createResponse(updateUser, ResponseCode.OK, null);
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);

    }

    Event processChangePassword(Event event) {
        //TODO Lấy user đăng nhập
        User userLogin = userRepository.findById(event.userId).get();

        ChangePasswordInfo changePasswordInfo = (ChangePasswordInfo) event.payload;

        if (Boolean.FALSE.equals(userLogin.authenticate(changePasswordInfo.getOldPassword()))) {
            return event.createResponse(Validation.INVALID_PASSWORD, ResponseCode.BAD_REQUEST, null);
        }
        if (!changePasswordInfo.getNewPassword().equals(changePasswordInfo.getConfirmPassword())) {
            return handlerNotMatchConfirmPassword(event);
        }

        userLogin.setEncryptedPassword(changePasswordInfo.getNewPassword());
//        userLogin.setUpdatedPasswordDate(new Date());
        userRepository.save(userLogin);
        return event.createResponse(null, ResponseCode.OK, null);
    }

    Event processGetOne(Event event) {
        Long id = (Long) event.payload;
        Optional<User> userOptional = userRepository.findById(id);
        if (userOptional.isPresent()) {
            return event.createResponse(userOptional.get(), ResponseCode.OK, null);
        } else {
            return event.createResponse(null, ResponseCode.NOT_FOUND, null);
        }
    }

    Event processViewProfile(Event event) {
        IGetUserDTO userDTO = userRepository.getOneUser(event.userId);
        // trường hợp id không tồn tại
        if (Objects.isNull(userDTO)) {
            return event.createResponse(Arrays.asList("id"), ResponseCode.NOT_FOUND, null);
        }
        UserResponseDTO userResponseDTO = new UserResponseDTO();
        BeanUtils.copyProperties(userDTO, userResponseDTO);
        // lấy ds role
        if (!StringUtils.isBlank(userDTO.getRoleNames()) && !StringUtils.isBlank(userDTO.getRoleIds())) {
            String[] roleNames = userDTO.getRoleNames().split(",");
            String[] roleIds = userDTO.getRoleIds().split(",");

            for (int i = 0; i < roleNames.length; i++) {
                UserResponseDTO.RoleDTO roleDTO = new UserResponseDTO.RoleDTO(roleNames[i], Long.parseLong(roleIds[i]));
                userResponseDTO.getRoles().add(roleDTO);
            }
        }
        // lấy root acc để hiển thị
        if (userDTO.getType().equals(UserType.CUSTOMER)) {
            try {
                UserManage manager = userManageRepository.getUserManageByUserId(userDTO.getId());

                if (manager != null) {
                    Optional optionalManagerAccount = userRepository.findById(manager.getUserManageId());
                    User managerAccount = (User) optionalManagerAccount.get();
                    UserResponseDTO.UserManageDTO userManageDTO = new UserResponseDTO.UserManageDTO();
                    userManageDTO.setId(managerAccount.getId());
                    userManageDTO.setName(managerAccount.getName());
                    userManageDTO.setUsername(managerAccount.getUsername());
                    userResponseDTO.setManager(userManageDTO);
                }
            } catch (Exception e) {
                logger.error("Lỗi lấy thông tin account root customer", e);
            }
        }
        return event.createResponse(userResponseDTO, ResponseCode.OK, null);
    }

    private Event handlerNoneEmail(Event event) {
        return event.createResponse(MessageKeyConstant.Validation.INVALID_EMAIL, ResponseCode.BAD_REQUEST, null);
    }

    private Event handlerNotMatchConfirmPassword(Event event) {
        return event.createResponse(Validation.PASSWORD_DO_NOT_MATCH, ResponseCode.BAD_REQUEST, null);
    }

    private Event handlerInvalidToken(Event event) {
        return event.createResponse(Validation.INVALID_TOKEN, ResponseCode.BAD_REQUEST, null);
    }

    public Event processGetListUserChild(Event event) {
        List<Long> lstUserId = getListChildOfUser((Long) event.payload);
        return event.createResponse(lstUserId, ResponseCode.OK, null);
    }


    private Event getNoOneManagedCustomerAccounts(Event event) {
        SearchUserRequest searchUserRequest = (SearchUserRequest) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(searchUserRequest.getSize(), searchUserRequest.getPage(),
                searchUserRequest.getSortBy());
        Page<ISearchUserDTO> pageUser = userRepository.getPageUserNoOneManaged(searchUserRequest, event.userType, event.provinceCode, listRequest.getPageable());
        List<SearchUserResponseDTO> userResponseDTOList = pageUser.getContent().stream().map(userDTO -> {
            SearchUserResponseDTO userResponseDTO = new SearchUserResponseDTO();
            BeanUtils.copyProperties(userDTO, userResponseDTO);
            return userResponseDTO;
        }).collect(Collectors.toList());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(pageUser.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(userResponseDTOList));
        return event.createResponse(pageInfo, 200, null);
    }

    private Event processGetListActivatedAccount(Event event) {
        try {
            List<Long> listAccountId = (List<Long>) event.payload;
            List<Long> listActivatedAccountId = userRepository.getListActivatedAccount(listAccountId, UserStatus.ACTIVE);
            return event.createResponse(listActivatedAccountId, ResponseCode.OK, null);
        } catch (Exception e) {
        }
        return event.createResponse(null, ResponseCode.SERVER_INTERNAL_ERROR, null);
    }

    public Event processGetByKey(Event event) {
        KeyValuePair keyValuePair = (KeyValuePair) event.payload;
        String query = keyValuePair.getKey() + "==" + keyValuePair.getValue();
        Node rootNode = new RSQLParser().parse(query);
        Specification<User> spec = rootNode.accept(new CustomRsqlVisitor<User>());
        return event.createResponse(userRepository.findAll(spec), ResponseCode.OK, null);
    }

}
