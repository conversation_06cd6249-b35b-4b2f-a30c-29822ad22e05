package vn.agis.crm.base.jpa.dto.resp;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Response DTO for batch notification deletion
 */
@Data
@NoArgsConstructor
public class NotificationBatchDeleteResponse {
    
    /**
     * Total number of notifications requested for deletion
     */
    private int totalRequested;
    
    /**
     * Number of notifications successfully deleted
     */
    private int successfullyDeleted;
    
    /**
     * Number of notifications that failed to delete
     */
    private int failed;
    
    /**
     * List of successfully deleted notification IDs
     */
    private List<Long> deletedIds;
    
    /**
     * List of failed deletions with details
     */
    private List<FailedDeletion> failures;
    
    /**
     * Overall success status
     */
    private boolean success;
    
    /**
     * Summary message
     */
    private String message;
    
    /**
     * HTTP status code
     */
    private int statusCode;
    
    public NotificationBatchDeleteResponse(int totalRequested) {
        this.totalRequested = totalRequested;
        this.successfullyDeleted = 0;
        this.failed = 0;
        this.deletedIds = new ArrayList<>();
        this.failures = new ArrayList<>();
        this.success = false;
        this.statusCode = 200;
    }
    
    /**
     * Add a successful deletion
     */
    public void addSuccess(Long id) {
        this.deletedIds.add(id);
        this.successfullyDeleted++;
        updateStatus();
    }
    
    /**
     * Add a failed deletion
     */
    public void addFailure(Long id, String reason, int statusCode) {
        this.failures.add(new FailedDeletion(id, reason, statusCode));
        this.failed++;
        updateStatus();
    }
    
    /**
     * Update overall status and message
     */
    private void updateStatus() {
        if (failed == 0) {
            this.success = true;
            this.message = String.format("Successfully deleted %d notifications", successfullyDeleted);
            this.statusCode = 200;
        } else if (successfullyDeleted == 0) {
            this.success = false;
            this.message = String.format("Failed to delete all %d notifications", failed);
            this.statusCode = 400;
        } else {
            this.success = true; // Partial success
            this.message = String.format("Partially successful: %d deleted, %d failed", successfullyDeleted, failed);
            this.statusCode = 207; // Multi-Status
        }
    }
    
    /**
     * Inner class for failed deletion details
     */
    @Data
    @NoArgsConstructor
    public static class FailedDeletion {
        private Long id;
        private String reason;
        private int statusCode;
        
        public FailedDeletion(Long id, String reason, int statusCode) {
            this.id = id;
            this.reason = reason;
            this.statusCode = statusCode;
        }
    }
}
