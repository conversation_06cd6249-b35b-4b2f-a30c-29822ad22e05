package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import vn.agis.crm.repository.SmeRepository;
import vn.agis.crm.repository.SubscriptionRepository;
import vn.agis.crm.repository.UserRepository;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.req.CreateUserReq;
import vn.agis.crm.base.jpa.entity.Sme;
import vn.agis.crm.base.jpa.entity.Subscription;
import vn.agis.crm.base.jpa.entity.User;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.repository.*;
import vn.agis.crm.util.RequestUtils;

import java.util.Arrays;
import java.util.Date;
import java.util.Map;

@Service
public class SubscriptionService {
    @Autowired
    private SubscriptionRepository subscriptionRepository;

    @Autowired
    private SmeRepository smeRepository;
    @Autowired
    private UserRepository userRepository;

    public SubscriptionService(SubscriptionRepository subscriptionRepository) {
        this.subscriptionRepository = subscriptionRepository;
    }

    public Event process(Event event) {
        Event response = null;
        switch (event.method) {
            case "CREATE_SUBSCRIPTION" -> response = processCreateSubscription(event);
        }
        return response;
    }

    private Event processCreateSubscription(Event event) {
        Map<String, Object> payloadJsonMap = (Map<String, Object>) event.payload;

        Map<String, Object> smeJsonMap = (Map<String, Object>) payloadJsonMap.get("sme");
        Sme sme = ObjectMapperUtil.objectMapper(ObjectMapperUtil.toJsonString(smeJsonMap), Sme.class);
        sme.setCreatedAt(new Date());
        smeRepository.save(sme);
        User user = userRepository.findOneByEmailIgnoreCase(sme.getEmail());
        if (user == null) {
            CreateUserReq createUserReq = new CreateUserReq();
            createUserReq.setEmail(sme.getEmail());
            createUserReq.setUsername(sme.getEmail());
            createUserReq.setPhone(sme.getPhoneNumber());
            createUserReq.setRepresentativeName(sme.getAmName());
            createUserReq.setName(sme.getName());
            createUserReq.setPassword("12345678"); //password mặc định tạm
            createUserReq.setRoleLst(Arrays.asList(542L)); // role mặc định doanh nghiệp tạm
            createUserReq.setType(Constants.UserType.ENTERPRISE);
            createUserReq.setAddressContact(sme.getAddress());
            createUserReq.setAddressHeadOffice(sme.getAddress());
            // fake tạm do chưa có đồng bộ đơn vị hành chính
            createUserReq.setProvinceCodeAddress(1);
            createUserReq.setWardCodeAddress(13);
            createUserReq.setProvinceCodeOffice(1);
            createUserReq.setWardCodeOffice(13);
            createUserReq.setTaxCode(sme.getTaxCode());
            Event createUserRequest = RequestUtils.amqpAsAdmin( Constants.Method.CREATE_USER, Constants.Category.USER, createUserReq, AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT);
            if (createUserRequest.respStatusCode.equals(ResponseCode.OK)) {

            } else {
                return event.createResponse("Failed to create user", 500, "Error creating user: " + createUserRequest.payload);
            }
        } else {
        }
        Map<String, Object> subscriptionJsonMap = (Map<String, Object>) payloadJsonMap.get("subscription");
        Subscription subscription = ObjectMapperUtil.objectMapper(ObjectMapperUtil.toJsonString(subscriptionJsonMap), Subscription.class);
        subscription.setCreatedAt(new Date());
        subscriptionRepository.save(subscription);
        return event.createResponse("Success", 200, "Subscription created successfully");
    }
}
