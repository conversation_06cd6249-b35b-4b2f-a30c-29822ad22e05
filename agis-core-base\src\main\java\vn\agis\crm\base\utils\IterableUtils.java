package vn.agis.crm.base.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by tiemnd on 12/14/19.
 */
public class IterableUtils {
    public static <T> List<T> toList(Iterable<T> iterable) {

        if (iterable instanceof List) {
            return (List<T>) iterable;
        }

        List<T> result = new ArrayList<T>();
        for (T item : iterable) {
            result.add(item);
        }

        return result;
    }
}
