package vn.agis.crm.base.constants;

public interface Constants {

    interface CRMService {
        // BASE - is not service, de phan biet voi cac he thong khac neu cung chay tren mot Rabbitmq
        String BASE = "crm.HH";

        // North APIGW
        String FE_API = BASE + "fe-api";

        // Backend
        String BE_CORE_MGMT = BASE + "be-coremgmt";
        String BE_DEVICE_MGMT = BASE + "be-devicemgmt";
        String BE_ALERT_RULE_MGMT = BASE + "be-rulemgmt";
        String BE_LOGGING = BASE + "be-logging";
        String BE_REPORT = BASE + "be-report";
        String BE_SYNC = BASE + "be-sync";
        String BE_IOT_ADAPTER = BASE + "be-iot-adapter";
        String BE_BOS_ADAPTER = BASE + "be-bos-adapter";


        // Adapter
        String ADAPTER_SMS = BASE + "at-sms";
        String ADAPTER_IOT_PLATFORM_LINK = BASE + "at-iot-platform-link";
        String ADAPTER_SUBSCRIPTION_ADAPTER = BASE + "at-subscription";
        String ADAPTER_SIM_MGMT = BASE + "at-simmgmt";

        String JWT_SECRET = "secret";
        String AUTH_TOKEN_PREFIX = "Bearer ";
        String AUTH_CODE_PREFIX = "AuthCode ";
        String AUTH_HEADER_STRING = "Authorization";
        String JWT_SCOPE = "scope";
        String JWT_TENANT_IDS = "tenant_ids";
        String JWT_USER_ID = "user_id";

        String BLOCK_LIST_USER = BASE + "block-list-user";
    }

    interface RabbitStream {
        String I_SIM_TRANS_LOG = CRMService.BASE + "super.stream.logging.iSimTransLog";
        String I_TRANS_LOG = CRMService.BASE + "super.stream.logging.iTransLog";
        String O_SIM_TRANS_LOG = CRMService.BASE + "super.stream.logging.oSimTransLog";
        String O_TRANS_LOG = CRMService.BASE + "super.stream.logging.oTransLog";
        String THIRD_PARTNER = CRMService.BASE + "super.stream.logging.thirdPartnerLog";
    }

    interface DeviceConnectionStatus {
        int REGISTERED = 1; // đã đăng ký
        int CONNECTED = 2; // đã kết nối
        int DISCONNECTED = 3; // đã ngắt kết nối
    }

    interface Method {

        // customer v2 upsert
        String CREATE_CUSTOMER_V2 = "createCustomerV2";
        String UPDATE_CUSTOMER_V2 = "updateCustomerV2";
        String UPDATE_ASSIGNMENT_EMPLOYEE = "updateAssignmentEmployee";
        String GET_ASSIGNMENT_HISTORY = "getAssignmentHistory";
        String DELETE_ASSIGNMENT = "deleteAssignment";

        //method for user
        String SEARCH_USER = "searchUser";
        String SEARCH_USER_CUSTOMER_MANAGE = "searchUserCustomerManaged";

        String GET_ONE_USER = "detailUser";

        String DELETE_USER = "deleteUser";

        String CHANGE_STATUS_USER = "changeStatusUser";

        String CHANGE_PASSWORD_USER = "changePasswordUser";

        String GET_PROVINCE = "getProvince";

        String GET_PROVINCE_BY_CODE = "getProvinceByCode";

        String SEARCH_WARD = "searchWard";

        String CREATE_USER = "createUser";

        String UPDATE_USER = "updateUser";

        String EXIST_BY_EMAIL_OR_USERNAME = "existByEmailOrUsername";

        String LOGIN = "login";
        String SSO_LOGIN = "sso_login";
        String CHECK_TOKEN_BOS = "check_token_bos";

        String VALIDATE_PERMISSION = "validate_permission";
        String VALIDATE_PERMISSION_TOKEN_PUBLIC = "validate_permission_token_public";


        String UPDATE_PROFILE = "updateProfile";

        String VIEW_PROFILE = "viewProfile";

        String GET_LIST_ROLE = "getListRole";

        String GET_ALL_CUSTOMER_BY_USER = "getAllByUser";

        String GET_ALL_CUSTOMER_BY_USER_PAGING = "getAllByUserPaging";

        String CURRENT_USER = "current_user";

        String FORGOT_PASSWORD_INIT = "forgot_password_init";

        String FORGOT_PASSWORD_FINISH = "forgot_password_finish";

        String VALIDATE_TOKEN_MAIL = "validate_token_mail";
        String GET_LIST_USER_CHILD = "getListUserChild";

        String SEARCH_ACTIVITY_LOG = "searchActivityLog";
        String GET_LIST_ACTIVATED_ACCOUNT = "getListActivatedAccount";
        String GET_LIST_NAME_BY_IDS = "getListNameByIds";

        // device
        String SEARCH_DEVICE = "searchDevice";
        String UPLOAD_IMAGE_DEVICE = "uploadImageDevice";
        String GET_IMAGE_DEVICE = "getImageDevice";
        String CHECK_MSISDN = "checkMsisdn";
        String MSISDN_ISVALID = "msisdnIsvalid";
        String CHECK_IMEI = "checkImei";
        String UPDATE_LOCATION = "updateLocation";
        String SEARCH_DEVICE_TYPE = "searchDeviceType";
        String SEARCH_DEVICE_TYPE_DISTINCT = "searchDeviceTypeDistinct";
        String GET_BY_TYPE_CODE = "getByTypeCode";
        String SEARCH_DEVICE_TELEMETRY = "searchDeviceTelemetry";
        String SEARCH_DEVICE_COMMAND = "searchDeviceCommand";
        String SEND_COMMAND = "sendCommand";
        String CREATE_AE = "createAE";
        String UPDATE_DEVICE_CONNECTION_STATUS = "updateDeviceConnectionStatus";
        String GET_USAGE_BY_DEVICE_IDS = "getUsageByDeviceIds";
        String GET_USAGE_BY_USER_IDS = "getUsageByUserIds";
        String DETAIL_DEVICE_FOR_ALERT = "detailDeviceForAlert";
        String GET_LIST_DEVICEID_BY_MANAGE_BY_USERID = "getListDeviceIdByUserId";
        String UPDATE_DEVICE_ENTERPRISE = "updateDeviceEnterprise";

        // method for rule
        String SEARCH_ALERT = "searchAlert";
        String DETAIL_ALERT = "detailAlert";
        String CREATED_ALERT = "createAlert";
        String UPDATE_ALERT = "updateAlert";
        String DELETE_ALERT = "deleteAlert";
        String CHANGE_STATUS_ALERT = "changeStatusAlert";
        String CHECK_EXIST_ALERT = "checkExistAlert";
        String SEARCH_ALERT_HISTORY = "searchAlertHistory";
        String CHECK_AND_SEND_ALERT = "checkAndSendAlert";
        String GET_COMMAND_LIST_BY_DEVICE_TYPE = "getCommandListByDeviceType";
        String CREATE_DEVICE_FROM_IOT_REQUEST = "createDeviceFromIotRequest";


        // report
        String SEARCH_REPORT = "searchReport";
        String CHANGE_STATUS_REPORT = "changeStatusReport";
        String UPDATE_REPORT_SCHEDULE = "updateReportSchedule";
        String UPDATE_REPORT_SENDING = "updateReportSending";
        String GET_REPORT_BY_PERMISSION = "getReportByPermission";
        String GET_DISTINCT_METHOD_NAME = "getDistinctMethodName";

        String SEARCH_DASHBOARD_CONFIG = "searchDashboardConfig";

        // permission
        String GET_PERMISSION_BY_USER = "getPermissionByUser";
        String GET_PERMISSIONKEY_BY_USER = "getPermissionKeyByUser";
        String GET_PERMISSIONKEY_DYNAMIC_CONFIG = "getPermissionKeyDynamicConfig";
        String PREVIEW_REPORT = "previewReport";
        String EXPORT_REPORT = "exportReport";

        //project & others common
        String CREATE = "create";
        String UPDATE = "update";
        String DELETE = "delete";
        String FIND_BY_ID = "findById";
        String SEARCH = "search";
        String GET_ALL = "getAll";
        String CHECK_EXIST_PROJECT_NAME = "checkExistName";
        String CHECK_EXIST_UNIT_CODE = "checkExistCode";
        String CHECK_EXIST_ROLE_NAME = "checkExistName";
        String CHECK_EXIST_PERMISSION_NAME = "checkExistName";
        String CHECK_EXISTS = "checkExists";
        String VALIDATE_PROJECT_DELETION = "validateProjectDeletion";
        String VALIDATE_CUSTOMER_DELETION = "validateCustomerDeletion";
        String GET_BOUGHT_PROJECT = "getBoughtProject";
        String GET_GREETING_PROJECT = "getGreetingProject";

        // Assignment
        String MANUAL_ASSIGN = "manualAssign";
        String GET_ALL_RULES = "getAllRules";
        String UPDATE_RULE_PRIORITY = "updateRulePriority";
        String GET_JOB_HISTORY = "getJobHistory";
        String GET_JOB_DETAILS = "getJobDetails";



        // DynamicConfig
        String SEARCH_DYNAMIC_CONFIG = "searchDynamicConfig";
        String GET_ALL_DYNAMIC = "getAllDynamic";
        String GET_CONTENT_DASHBOARD = "getContentDashboard";



        // send sms
        String SEND_SMS = "sendSms";

        //Docs
        String GET_PROJECT_INFO = "getProjectInfo";
        String GET_LIST_PAGE = "getListPage";
        String GET_PAGE_INFO = "getPageInfo";
        String GET_CONTENT_PAGE = "getContentPage";

        // usermanage
        String CHANGE_USER_FOR_MANAGER = "changeUserForManager";
        String SEARCH_USER_CUSTOMER_NO_ONE_MANAGE = "searchUserCustomerNoOneManage";

        // IMPORT METHODS
        String CREATE_IMPORT_JOB_WEB_UPLOAD = "createImportJobWebUpload";
        String GET_IMPORT_JOB = "getImportJob";
        String GET_EXCEL_SHEETS = "getExcelSheets";
        String GET_FILE_METADATA = "getFileMetadata";
        String GET_SUPPORTED_FORMATS = "getSupportedFormats";
        String GET_IMPORT_TEMPLATE = "getImportTemplate";

        // DRY-RUN METHODS
        String START_DRY_RUN = "startDryRun";
        String GET_DRY_RUN_RESULT = "getDryRunResult";
        String GET_IMPORT_ERRORS = "getImportErrors";
        String VALIDATE_IMPORT_DATA = "validateImportData";

        // EXECUTION METHODS (Step 3)
        String CONFIRM_IMPORT = "confirmImport";
        String GET_IMPORT_PROGRESS = "getImportProgress";
        String CANCEL_IMPORT = "cancelImport";
        String GET_IMPORT_RESULT = "getImportResult";

        //CLIENT AUTHENTICATION
        String SEARCH_CLIENT_AUTH = "searchClientAuth";
        String GEN_AUTH_CODE = "genAuthCode";
        String AUTH2_LOGIN = "auth2_login";
        String GET_USER_SEARCH_API = "getUserSearchApi";
        // DEVICE TELEMETRY
        String CREATE_DEVICE_TELEMETRY = "createDeviceTelemetry";
        String UPDATE_DEVICE_COMMAND_RESPONSE = "updateDeviceCommandResponse";
        String SEND_DEVICE_COMMAND = "sendDeviceCommand";
        String UPDATE_DEVICE_STATUS = "updateDeviceStatus";


        // TICH HOP VOI BOS
        String CREATE_SUBSCRIPTION = "createSubscription";

        // Cham soc khach hang
        String GET_CUSTOMER_PROFILE = "getCustomerProfile";
        String GET_SUGGEST_QUERY = "getSuggestQuery";
        String GET_SLICE_CUSTOMER = "getSliceCustomer";


        // Chat bot
        String SEND_QUESTION = "sendQuestion";
    }


    interface Category {
        String USER = "USER";
        String EMPLOYEE = "EMPLOYEE";
        String PERMISSION = "PERMISSION";
        String ROLE = "ROLE";
        String DEVICE = "DEVICE";
        String DEVICE_TYPE = "DEVICE_TYPE";
        String RULE = "RULE";
        String RECEIVING_GROUP = "RECEIVING_GROUP";
        String REPORT = "REPORT";
        String EMAIL_GROUP = "EMAIL_GROUP";
        String DASHBOARD_CONFIG = "DASHBOARD_CONFIG";
        String DASHBOARD = "DASHBOARD";
        String SMS = "SMS";
        String DOCS = "DOCS";
        String USER_MANAGE = "USER_MANAGE";
        String GROUP_SUB = "GROUP_SUB";

        String CLIENT_AUTHENTICATION = "CLIENT_AUTHENTICATION";
        String DEVICE_TELEMETRY = "DEVICE_TELEMETRY";
        String DEVICE_COMMAND = "DEVICE_COMMAND";

        String SUBSCRIPTION = "SUBSCRIPTION";
        String AREAS = "AREAS";
        String PROJECT = "PROJECT";
        String UNIT = "UNIT";
        String CUSTOMER = "CUSTOMER";
        String ASSIGNMENT_JOB = "ASSIGNMENT_JOB";
        String ASSIGNMENT = "ASSIGNMENT";
        String ASSIGNMENT_RULE = "ASSIGNMENT_RULE";
        String ROLE_SIMPLE = "ROLE_SIMPLE";
        String PERMISSION_SIMPLE = "PERMISSION_SIMPLE";
        String CONFIG = "CONFIG";
        String IMPORT_JOB = "IMPORT_JOB";
        String IMPORT_TEMPLATE = "IMPORT_TEMPLATE";

        String CHAT_VIRTUAL_ASSISTANT = "CHAT_VIRTUAL_ASSISTANT";
        String QUERY_VIRTUAL_ASSISTANT = "QUERY_VIRTUAL_ASSISTANT";
        String QUERY_SUGGEST = "QUERY_SUGGEST";
        String NOTIFICATION = "NOTIFICATION";

        String CHAT_BOT = "CHAT_BOT";
    }

    interface UserStatus {
        Integer INACTIVE = 0;
        Integer ACTIVE = 1;
        Integer DEACTIVE = -1;  //khi quản trị viên thực hiện xóa
    }

    interface UserType {
        Integer ADMIN = 1;      // Full access to all customers
        Integer MANAGER = 2;    // Access to customers where user is current_manager_id
        Integer STAFF = 3;      // Access to customers where user is current_staff_id
        // Legacy constants for backward compatibility
        Integer ENTERPRISE = 2; // KH doanh nghiệp (same as MANAGER)
        Integer CUSTOMER = 3;   // KH cá nhân (same as STAFF)
    }

    interface SynchronizeStatus {
        Integer FAIL = 0;
        Integer SUCCESS = 1;
    }

    interface RoleStatus {
        Integer ACTIVE = 1;
        Integer INACTIVE = 0;
    }

    interface Batch {
        String PREFIX = "batch-";
    }

    interface CommandStatus {
        int STORED = 0;
        int SENT = 1;
        int ACCEPTED = 2;
        int REJECT = 3;
        int CONFLICT = 4;
        int PROCESSED = 5;

    }

    interface CommandSentStatus {
        int NOT_SENT = 0; // Chưa gửi
        int SENT = 1; // Đã gửi
    }


    interface EventTypeAlert {
        int DATA_OVER_RATE_PERCENT = 1;
        int DATA_OVER_MAX_THRESHOLD = 2;
        int DISCONNECT = 3;
        int NEW_CONNECTION = 4;
        int SMS_OVER_RATE_PERCENT = 5;
        int SMS_OVER_MAX_THRESHOLD = 6;
        int INACTIVE = 7;
        int DEACTIVE = 8;
        int INACTIVE_OR_DEACTIVE = 9;
        int NO_CONNECTION = 10;
        int SIM_EXPIRATION = 11;
        int DATA_POOL_EXPIRATION = 12;
        int WALLET_THRESHOLD = 15;
    }

    //Dùng cho cảnh báo chạm ngưỡng ví
    interface AlertUnit {
        int PERCENT = 1;
        int MB = 2;
        int SMS = 3;
    }

    interface SeverityAlert {
        int CRITICAL = 0;
        int MAJOR = 1;
        int MINOR = 2;
        int INFO = 3;
    }

    interface NotifyInterval{
        int NO_LOOP = 0;
        int LOOP = 1;
    }


    // Word
    interface Word {
        String SLASH = "/";
        String PERCENT = "%";
        String COMMA = ",";
        String SPACE = " ";
        String BEARER_PREFIX = "Bearer ";
        String AUTHORIZATION_HEADER = "Authorization";
        String LEFT_BRACKET = "[";
        String RIGHT_BRACKET = "]";
        int PREFIX_BEARER = 7;
    }


    interface AlertProcessStatus {
        int PROCESSED = 1;
        int NOT_PROCESS = 0;
    }



    interface Report {
        int LIMIT_ROW_RETURN = 10000;
        int LIMIT_ROW_RETURN_EXCEL = 1000000;
    }


    interface Regex {
        String PHONE_REGEX = "^([+]|[0-9]{1})[0-9]{0,11}$|^$";

        String EMAIL_REGEX = "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9]{2,}(?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)+$";

        String DATE_REGEX = "^(?:(?:31(\\/|-|\\.)(?:0?[13578]|1[02]))\\1|(?:(?:29|30)(\\/|-|\\.)(?:0?[13-9]|1[0-2])\\2))(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$|^(?:29(\\/|-|\\.)0?2\\3(?:(?:(?:1[6-9]|[2-9]\\d)?(?:0[48]|[2468][048]|[13579][26])|(?:(?:16|[2468][048]|[3579][26])00))))$|^(?:0?[1-9]|1\\d|2[0-8])(\\/|-|\\.)(?:(?:0?[1-9])|(?:1[0-2]))\\4(?:(?:1[6-9]|[2-9]\\d)?\\d{2})$";

        String PASSWORD_REGEX = "(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[\\|/{}()<>;:?/@#$%^&+=`!,.*_'~-])(?=\\S+$).{8,16}";

        String URL_REGEX = "[-a-zA-Z0-9+&@#/%?=~_|!:,.;]*[-a-zA-Z0-9+&@#/%=~_|]*";
    }

    interface RoleType {
        Integer ALL = 0;
        Integer ADMIN = 1;
        Integer ENTERPRISE = 2; // KH doanh nghiệp
        Integer CUSTOMER = 3;  // KH cá nhân
    }






    interface OAuth2IoT {
        String CLIENT_ID = "04d6071b-aa82-49a5-b792-5f63bb5ef61c";
        String SECRET_ID = "bb25e1bd-dd7c-4238-8fee-e7bb04271617";
    }


    interface RedisCommand {
        interface Action {
            String BLOCK = "block";
            String UNBLOCK = "unblock";
            String DELETE = "delete";

            // Redis-type operations
            String SMEMBERS = "smembers";
            String GET = "get";
            String SET = "set";
            String KEYS = "keys";
        }
        interface Key {
            String BLOCK_LIST_USER = "blockListUser";
        }
    }
}
