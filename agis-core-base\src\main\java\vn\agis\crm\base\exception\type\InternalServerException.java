/**
 * <AUTHOR>
 */
package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

public class InternalServerException extends BaseException {

    /**
	 *
	 */
	private static final long serialVersionUID = 4552064508407342581L;

	public InternalServerException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }

	public InternalServerException(String title, String entityName, String field, String errorCode) {
		super(title, entityName, Collections.singletonList(field), errorCode);
	}
}
