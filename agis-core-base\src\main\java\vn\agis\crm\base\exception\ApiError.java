/**
 * <AUTHOR> VienNN
 * @version : 1.0
 */

package vn.agis.crm.base.exception;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.internal.engine.path.PathImpl;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;

import javax.validation.ConstraintViolation;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Getter
@Setter
public class ApiError {

    private String systemCode;

    @Schema(description = "Thoi gian xay ra loi")
    private Long timestamp;

    @Schema(description = "Thong bao loi", example = "User not found")
    private String message;

    @Schema(description = "Ten doi tuong xay ra loi", example = "User")
    private String object;

    @Schema(description = "Truong cua doi tuong xay ra loi", example = "ID")
    private String field;

    @Schema(description = "Ma loi ", example = "error.object.not.found")
    private String errorCode;

    @Schema(description = "Trong truong hop nhieu truong xay ra loi ")
    private List<ApiSubError> fields;

    public ApiError() {
        timestamp = new Date().getTime();
    }

    public ApiError(String status) {
        this();
        this.systemCode = status;
        this.message = "Unexpected error";
    }

    public ApiError(String status, String message) {
        this();
        this.systemCode = status;
        this.message = message;
    }

    public void addSubError(ApiSubError subError) {
        if (fields == null) {
            fields = new ArrayList<>();
        }
        fields.add(subError);
    }

    public void addValidationError(String object, String field, Object rejectedValue, String message) {
        addSubError(new ApiValidationError(object, field, rejectedValue, message));
    }

    public void addValidationError(String object, String field, Object rejectedValue) {
        addSubError(new ApiValidationError(object, field, rejectedValue, message));
    }

    public void addValidationError(String object, String field, Object rejectedValue, String message, String errorCode) {
        addSubError(new ApiValidationError(object, field, rejectedValue, message, errorCode));
    }


    public void addValidationError(String object, String message) {
        addSubError(new ApiValidationError(object, message));
    }

    public void addValidationErrors(List<FieldError> fieldErrors) {
        fieldErrors.forEach(this::addValidationError);
    }

    public void addValidationError(ObjectError objectError) {
        this.addValidationError(objectError.getObjectName(), objectError.getDefaultMessage());
    }

    public void addValidationError(List<ObjectError> globalErrors) {
        globalErrors.forEach(this::addValidationError);
    }

    /**
     * Utility method for adding error of ConstraintViolation. Usually when a @Validated validation fails.
     *
     * @param cv the ConstraintViolation
     */
    public void addValidationError(ConstraintViolation<?> cv) {
        this.addValidationError(cv.getRootBeanClass().getSimpleName(), ((PathImpl) cv.getPropertyPath()).getLeafNode().asString(), cv.getInvalidValue(), cv.getMessage(), cv.getMessageTemplate());
    }

    public void addValidationError(ConstraintViolation<?> cv, String message) {
        this.addValidationError(cv.getRootBeanClass().getSimpleName(), ((PathImpl) cv.getPropertyPath()).getLeafNode().asString(), cv.getInvalidValue(), message, cv.getMessageTemplate());
    }


    public void addValidationErrors(Set<ConstraintViolation<?>> constraintViolations) {
        constraintViolations.forEach(this::addValidationError);
    }


    public interface ApiSubError {

    }

    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    @Getter
    @Setter
    public static class ApiValidationError implements ApiSubError {

        private String object;

        private String field;

        private Object rejectedValue;

        private String message;

        private String apiErrorCode;

        ApiValidationError(String object, String message) {
            this.object = object;
            this.message = message;
        }

        public ApiValidationError(String object, String field, Object rejectedValue, String message) {
            super();
            this.object = object;
            this.field = field;
            this.rejectedValue = rejectedValue;
            this.message = message;
        }
    }

    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    class ApiValidationSubError implements ApiSubError {

        @Getter
        @Setter
        private String field;

        @Getter
        @Setter
        private Object rejectedValue;

        @Getter
        @Setter
        private String apiErrorCode;

        @Getter
        @Setter
        private String message;
    }

    @EqualsAndHashCode(callSuper = false)
    @AllArgsConstructor
    @Getter
    @Setter
    class ApiValidationBusinessSubError implements ApiSubError {

        private String field;

        private Object rejectedValue;

        private String apiErrorCode;

        private String message;

        private boolean limitMaxRecord;
    }

    /**
     * @param field
     * @param rejectedValue
     * @param apiErrorCode
     * @param message
     */
    public void apiValidationSubError(String field, Object rejectedValue, String apiErrorCode, String message) {
        addSubError(new ApiValidationSubError(field, rejectedValue, apiErrorCode, message));
    }

    /**
     * @param field
     * @param rejectedValue
     * @param apiErrorCode
     * @param message
     * @param limitMaxRecord
     */
    public void apiValidationBusinessSubError(String field, Object rejectedValue, String apiErrorCode, String message, boolean limitMaxRecord) {
        addSubError(new ApiValidationBusinessSubError(field, rejectedValue, apiErrorCode, message, limitMaxRecord));
    }
}
