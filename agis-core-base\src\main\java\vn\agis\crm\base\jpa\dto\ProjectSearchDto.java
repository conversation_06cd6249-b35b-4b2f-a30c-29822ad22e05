package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProjectSearchDto {
    private String name;
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "id,asc";

    public ProjectSearchDto(String name, Integer page, Integer size, String sortBy) {
        this.name = name;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }

    public ProjectSearchDto() {
    }
}

