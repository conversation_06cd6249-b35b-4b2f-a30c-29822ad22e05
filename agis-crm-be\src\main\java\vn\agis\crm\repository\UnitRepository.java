package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Units;

@Repository
public interface UnitRepository extends JpaRepository<Units, Long>, JpaSpecificationExecutor<Units> {

    Page<Units> findByCodeContainingIgnoreCase(String code, Pageable pageable);

    Page<Units> findByProjectIdAndCodeContainingIgnoreCase(Long projectId, String code, Pageable pageable);

    boolean existsByProjectIdAndCode(Long projectId, String code);

    Units findFirstByProjectIdAndCode(Long projectId, String code);

    /**
     * Find unit by project ID and code with case-insensitive search
     * Used for import validation to prevent duplicate units with different capitalization
     */
    @Query("SELECT u FROM Units u WHERE u.projectId = :projectId AND LOWER(u.code) = LOWER(:code) AND u.deletedAt IS NULL")
    Units findFirstByProjectIdAndCodeIgnoreCase(@Param("projectId") Long projectId, @Param("code") String code);

    /**
     * Count active units for a project (for dependency validation)
     */
    @Query("SELECT COUNT(u) FROM Units u WHERE u.projectId = :projectId AND u.deletedAt IS NULL")
    long countActiveUnitsByProjectId(@Param("projectId") Long projectId);

    /**
     * Check if project has any active units (for dependency validation)
     */
    @Query("SELECT CASE WHEN COUNT(u) > 0 THEN true ELSE false END FROM Units u WHERE u.projectId = :projectId AND u.deletedAt IS NULL")
    boolean hasActiveUnitsByProjectId(@Param("projectId") Long projectId);
}

