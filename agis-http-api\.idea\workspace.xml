<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="58bbdb0e-c883-4f9a-9a42-a847f0901bbf" name="Changes" comment="fix api import" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev_import" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;hosyhaiha&quot;,
      &quot;fullname&quot;: &quot;hosyhaiha&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.com/agis6491334/agis-http-api.git&quot;,
    &quot;second&quot;: &quot;2cfce9aa-ecdd-4155-af73-b6317d6ca807&quot;
  }
}</component>
  <component name="HttpClientEndpointsTabState">
    <option name="cachedRequestData" value="&lt;CachedHttpClientTabRequests&gt;&#10;  &lt;entry key=&quot;199d640ea9b2fdcf72c633f619611b42e97b85b8&quot; value=&quot;###&amp;#10;GET http://localhost:8080/api/configs/{{id}}&quot; /&gt;&#10;&lt;/CachedHttpClientTabRequests&gt;" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="31pwEEp6kWNAFhqU0JwKRf9y2mf" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Application.Unnamed.executor&quot;: &quot;Debug&quot;,
    &quot;Maven.crm-http-api [clean].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RunManager">
    <configuration name="Unnamed" type="Application" factoryName="Application" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="vn.agis.crm.CrmHttpApplication" />
      <module name="crm-http-api" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="JetRunConfigurationType">
      <module name="agis-http-api" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="agis-http-api" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.22562.218" />
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-IU-243.22562.218" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="58bbdb0e-c883-4f9a-9a42-a847f0901bbf" name="Changes" comment="api login" />
      <created>1756234207961</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1756234207961</updated>
      <workItem from="1756234209294" duration="289000" />
      <workItem from="1756234797761" duration="6000" />
      <workItem from="1756301380699" duration="400000" />
      <workItem from="1756302091592" duration="20309000" />
      <workItem from="1756556833876" duration="3969000" />
      <workItem from="1756630029111" duration="48000" />
      <workItem from="1756630149422" duration="7899000" />
      <workItem from="1756648984810" duration="1726000" />
      <workItem from="1756652095226" duration="7505000" />
      <workItem from="1756732113456" duration="2965000" />
      <workItem from="1756739125983" duration="3792000" />
      <workItem from="1756814322647" duration="2688000" />
      <workItem from="1757171355585" duration="27839000" />
      <workItem from="1757431934123" duration="13574000" />
      <workItem from="1757550566840" duration="235000" />
      <workItem from="1757755214061" duration="301000" />
      <workItem from="1757857715981" duration="1673000" />
      <workItem from="1757860899222" duration="43998000" />
      <workItem from="1758292536011" duration="3489000" />
      <workItem from="1758298560602" duration="2025000" />
      <workItem from="1758334138124" duration="6296000" />
      <workItem from="1758364379195" duration="19034000" />
      <workItem from="1758717352873" duration="3573000" />
      <workItem from="1758819369609" duration="4468000" />
    </task>
    <task id="LOCAL-00001" summary="api login">
      <option name="closed" value="true" />
      <created>1756234467609</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1756234467609</updated>
    </task>
    <task id="LOCAL-00002" summary="fix api project">
      <option name="closed" value="true" />
      <created>1756318507451</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1756318507451</updated>
    </task>
    <task id="LOCAL-00003" summary="api căn hộ">
      <option name="closed" value="true" />
      <created>1756321629149</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1756321629149</updated>
    </task>
    <task id="LOCAL-00004" summary="api khách hàng">
      <option name="closed" value="true" />
      <created>1756352158985</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1756352158985</updated>
    </task>
    <task id="LOCAL-00005" summary="thêm api check trùng project và unit">
      <option name="closed" value="true" />
      <created>1756631756824</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1756631756824</updated>
    </task>
    <task id="LOCAL-00006" summary="role and permission">
      <option name="closed" value="true" />
      <created>1756649342391</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1756649342391</updated>
    </task>
    <task id="LOCAL-00007" summary="employee">
      <option name="closed" value="true" />
      <created>1756660384099</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1756660384099</updated>
    </task>
    <task id="LOCAL-00008" summary="fix api seach employee">
      <option name="closed" value="true" />
      <created>1756662051311</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1756662051311</updated>
    </task>
    <task id="LOCAL-00009" summary="fix api check trùng employee">
      <option name="closed" value="true" />
      <created>1756663260942</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1756663260942</updated>
    </task>
    <task id="LOCAL-00010" summary="api config">
      <option name="closed" value="true" />
      <created>1756818729436</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1756818729436</updated>
    </task>
    <task id="LOCAL-00011" summary="bổ sung DTO trả ra cho assignment">
      <option name="closed" value="true" />
      <created>1757178062719</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1757178062719</updated>
    </task>
    <task id="LOCAL-00012" summary="bổ sung DTO trả ra cho customer">
      <option name="closed" value="true" />
      <created>1757181904971</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1757181904971</updated>
    </task>
    <task id="LOCAL-00013" summary="fix api customer">
      <option name="closed" value="true" />
      <created>1757266620358</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1757266620358</updated>
    </task>
    <task id="LOCAL-00014" summary="api upload">
      <option name="closed" value="true" />
      <created>1757349102480</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1757349102480</updated>
    </task>
    <task id="LOCAL-00015" summary="api checkExists customer">
      <option name="closed" value="true" />
      <created>1757515461216</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1757515461216</updated>
    </task>
    <task id="LOCAL-00016" summary="api for assign">
      <option name="closed" value="true" />
      <created>1757550762050</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1757550762050</updated>
    </task>
    <task id="LOCAL-00017" summary="fix bug 12 xóa dự án">
      <option name="closed" value="true" />
      <created>1757863920441</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1757863920441</updated>
    </task>
    <task id="LOCAL-00018" summary="fix bug 13">
      <option name="closed" value="true" />
      <created>1757867178867</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1757867178867</updated>
    </task>
    <task id="LOCAL-00019" summary="fix bug 16">
      <option name="closed" value="true" />
      <created>1757869363056</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1757869363056</updated>
    </task>
    <task id="LOCAL-00020" summary="fix bug 17">
      <option name="closed" value="true" />
      <created>1757871747296</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1757871747296</updated>
    </task>
    <task id="LOCAL-00021" summary="fix bug 17">
      <option name="closed" value="true" />
      <created>1757874622292</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1757874622292</updated>
    </task>
    <task id="LOCAL-00022" summary="fix bug 12, 22">
      <option name="closed" value="true" />
      <created>1757943528466</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1757943528466</updated>
    </task>
    <task id="LOCAL-00023" summary="fix bug 33">
      <option name="closed" value="true" />
      <created>1757950562176</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1757950562176</updated>
    </task>
    <task id="LOCAL-00024" summary="fix bug 42">
      <option name="closed" value="true" />
      <created>1757963568812</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1757963568812</updated>
    </task>
    <task id="LOCAL-00025" summary="fix bug 32">
      <option name="closed" value="true" />
      <created>1757982597793</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1757982597793</updated>
    </task>
    <task id="LOCAL-00026" summary="fix bug 24, 47 , seach theo ngày sinh nhật khách hàng">
      <option name="closed" value="true" />
      <created>1758126060990</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1758126060990</updated>
    </task>
    <task id="LOCAL-00027" summary="fix bug 39 - noti">
      <option name="closed" value="true" />
      <created>1758216527757</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1758216527757</updated>
    </task>
    <task id="LOCAL-00028" summary="fix bug 48">
      <option name="closed" value="true" />
      <created>1758217950605</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1758217950605</updated>
    </task>
    <task id="LOCAL-00029" summary="fix api import">
      <option name="closed" value="true" />
      <created>1758364892434</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1758364892434</updated>
    </task>
    <task id="LOCAL-00030" summary="fix api import">
      <option name="closed" value="true" />
      <created>1758434047971</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1758434047971</updated>
    </task>
    <option name="localTasksCounter" value="31" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="api khách hàng" />
    <MESSAGE value="thêm api check trùng project và unit" />
    <MESSAGE value="role and permission" />
    <MESSAGE value="employee" />
    <MESSAGE value="fix api seach employee" />
    <MESSAGE value="fix api check trùng employee" />
    <MESSAGE value="api config" />
    <MESSAGE value="bổ sung DTO trả ra cho assignment" />
    <MESSAGE value="bổ sung DTO trả ra cho customer" />
    <MESSAGE value="fix api customer" />
    <MESSAGE value="api upload" />
    <MESSAGE value="api checkExists customer" />
    <MESSAGE value="api for assign" />
    <MESSAGE value="fix bug 12 xóa dự án" />
    <MESSAGE value="fix bug 13" />
    <MESSAGE value="fix bug 16" />
    <MESSAGE value="fix bug 17" />
    <MESSAGE value="fix bug 12, 22" />
    <MESSAGE value="fix bug 33" />
    <MESSAGE value="fix bug 42" />
    <MESSAGE value="fix bug 32" />
    <MESSAGE value="fix bug 24, 47 , seach theo ngày sinh nhật khách hàng" />
    <MESSAGE value="fix bug 39 - noti" />
    <MESSAGE value="fix bug 48" />
    <MESSAGE value="fix api import" />
    <option name="LAST_COMMIT_MESSAGE" value="fix api import" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/agis/crm/controller/ProjectController.java</url>
          <line>97</line>
          <option name="timeStamp" value="8" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="java-line">
          <url>file://$PROJECT_DIR$/src/main/java/vn/agis/crm/controller/FileUploadController.java</url>
          <line>31</line>
          <option name="timeStamp" value="13" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>