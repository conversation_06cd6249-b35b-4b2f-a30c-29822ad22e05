package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "PERMISSION")
@Data
public class Permission {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "PERMISSION_KEY")
    private String permissionKey;
    @Column(name = "OBJECT_KEY")
    private String objectKey;
    @Column(name = "DESCRIPTION")
    private String description;

}