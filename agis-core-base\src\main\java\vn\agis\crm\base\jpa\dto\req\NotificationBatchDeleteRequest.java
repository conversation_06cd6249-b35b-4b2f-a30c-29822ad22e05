package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * Request DTO for batch deletion of notifications
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotificationBatchDeleteRequest {
    
    /**
     * List of notification IDs to delete
     */
    private List<Long> ids;
    
    /**
     * Validation method to ensure the request is valid
     */
    public boolean isValid() {
        return ids != null && !ids.isEmpty();
    }
    
    /**
     * Get the number of notifications to delete
     */
    public int getCount() {
        return ids != null ? ids.size() : 0;
    }
}
