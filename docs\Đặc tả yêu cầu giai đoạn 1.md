# **1\) <PERSON><PERSON><PERSON> tiêu & <PERSON><PERSON>t quả mong đợi**

* **X<PERSON>y dựng hệ thống CRM** tối ưu nhập liệu, chu<PERSON>n hóa dữ liệu, tra cứu và xem **hồ sơ khách hàng 360°** n<PERSON>h, chính xác.

* **Kiến trúc linh ho<PERSON>t, mô-đun**, sẵn sàng mở rộng ở các giai đoạn tiếp theo (t<PERSON><PERSON> nă<PERSON>, tích hợ<PERSON> kênh, bá<PERSON> c<PERSON><PERSON>).

* **Thiết kế AI Assistant-first**: cho phép ra lệnh và khám phá dữ liệu bằng **Trợ lý AI** (ngôn ngữ tự nhiên), đồng thời duy trì các **màn hình CRUD truyền thống** để thao tác chi tiết và xử lý hàng loạt.

# **2\) Layout giao diện**

1. **Module quản trị:  Desktop-first**

	Left Nav (thanh điều hướng trái): <PERSON><PERSON> hiển thị (theo thứ tự):

- Trợ lý AI  
- Dự án  
- Căn  
- Khách hàng  
- Nhân viên  
- Quyền  
  Header (thanh trên cùng)  
- Avatar \+ Tên nhân sự (góc phải) \+ Thông báo mới: hiển thị ảnh (hoặc chữ cái đầu) và Họ Tên và số thông báo mới  
- Menu tài khoản (popup) mở khi click avatar/tên, gồm:  
  - Thông báo  
  - Edit profile  
  - Logout

  Vùng nội dung (Main Content)

- Là khu vực thay đổi theo từng trang khi chọn menu tương ứng.  
- Hỗ trợ Breadcrumbs (tùy chọn) để người dùng biết đang ở đâu (VD: Khách hàng \> Danh sách).  
    
2. **Module Chăm sóc khách hàng: Mobile-first**  
   Giao diện “to, dễ bấm”, quy trình 1–2 ngón tay, thao tác nhanh: Chat trợ lý ảo, Hồ sơ 360°, ghi tương tác, xem thông báo

# **3\) Nhóm quyền**

* **Admin**: toàn quyền cấu hình, quyền hạn

* **Manager**: xem, cập nhật và phân nhân sự chăm sóc cho các KH được Admin giao quản lý.

* **Staff**: xem & cập nhật **thông tin các KH được phân chăm sóc** 

# **4\) Luồng chức năng cơ bản**

1. ## Admin nhập danh sách khách hàng

   * Upload file (trực tiếp trên web-app hoặc kết nối qua Google Drive) → hệ thống **dry-run** hiện lỗi/số dòng hợp lệ → người dùng xác nhận chạy → xem tiến trình & báo cáo lỗi tải về.  
   * **File template mẫu:** [https://docs.google.com/spreadsheets/d/1xBawEGMUx4F9V8tbLw1qFXfRKsHENyjK/edit?usp=sharing\&ouid=109902165320799737096\&rtpof=true\&sd=true](https://docs.google.com/spreadsheets/d/1xBawEGMUx4F9V8tbLw1qFXfRKsHENyjK/edit?usp=sharing&ouid=109902165320799737096&rtpof=true&sd=true)   
   * Mở trang Thêm khách hàng để tạo khách hàng mới  
     	

2. ## Admin/Manager phân nhân sự quản lý và chăm sóc KH

   * Gán người quản lý lead (Manager) và người chăm sóc lead (Staff) cho từng lead/khách hàng theo quy tắc tự động hoặc thao tác thủ công.  
   * Phân lead thủ công tại Trang danh sách Khách hàng.  
   * Phân lead tự động tại Trang quản trị rule phân lead.

3. ## Sale tra cứu nhanh thông tin KH bằng Trợ lý AI

   * Gõ câu lệnh tiếng Việt tự nhiên → hệ thống trả kết quả dạng bảng/hồ sơ → có nút “Mở trong màn hình chi tiết”.  
   * Trong giai đoạn 1 sẽ hỗ trợ những câu lệnh cơ bản kiểu như sau:  
     1. Tìm khách hàng có tên là Thành và số điện thoại là 0906244804  
     2. Tìm tất cả các khách hàng đã mua nhà tại dự án AQUA trong 3 tháng qua  
     3. Tìm thông tin khách hàng đã mua căn NA.GV4-05 dự án AQUA

4. ## Sale xem “Hồ sơ 360° khách hàng”

   * Từ danh sách Khách hàng hoặc kết quả Trợ lý AI → mở **Hồ sơ 360°**.  
   * Màn hình hiển thị dạng **CV** gồm:  
     1. Bên trái  
        1. Thông tin cá nhân (họ tên, SĐT, email, địa chỉ, ngày sinh, tình trạng hôn nhân…).  
        2. Nhân viên sale đang phụ trách (và lịch sử phân công trước đây).  
        3. Người thân (nhiều dòng).  
     2. Bên phải  
        1. Các căn đã giao dịch (mỗi căn hiển thị thẻ thông tin riêng).  
        2. Các căn đang chào  
           	

5. ## Sale cập nhật thông tin tương tác mới với KH

   * Trên trang **Hồ sơ 360°**  
   * Nhân viên sale sẽ thêm các nội dung và thời điểm tương tác  
   * Có 2 loại tương tác  
     1. Tương tác thứ cấp (chuyển nhượng) áp dụng cho các căn mà KH đã giao dịch   
     2. Tương tác sơ cấp (bán mới) áp dụng cho các căn mà đang chào KH

# **5\) Mô tả chi tiết từng tính năng**

## **5.1 Module Quản trị**

### 5.1.1 Quản lý Dự án

**Phạm vi & Giao diện**

* Gồm 4 phần: **Trang danh sách dự án**, **Trang thêm dự án**, **Trang sửa dự án**, **Tính năng xóa dự án**.

* **Giao diện:** bảng (list) và form **CRUD** truyền thống, đơn giản, rõ ràng.

#### 1\) Trang Danh sách dự án

**Mục tiêu:** Xem nhanh toàn bộ dự án, tìm kiếm, thao tác nhanh (thêm/sửa/xóa).

**Thành phần chính:**

* Thanh công cụ: **Tìm kiếm theo Tên dự án**, nút **\+ Thêm dự án**.

* Bảng: cột **Tên dự án** (bắt buộc) & cột **Thống kê nhanh thông tin khách hàng có liên quan đến dự án** (Tổng số khách hàng có mua dự án này \+ Tổng số khách hàng đang mời mua dự án này)

* Hành động dòng: **Sửa**, **Xóa**.

* Phân trang mặc định 20 dòng/trang

#### 2\) Trang Thêm dự án

**Mục tiêu:** Tạo mới một dự án.

**Form trường dữ liệu:**

* **Tên dự án**\* (bắt buộc, tối đa 255 ký tự, **không trùng** sau khi loại bỏ khoảng trắng đầu/cuối; so khớp không phân biệt hoa–thường).

**Hành vi & Luồng:**

* Nút **Lưu** (kích hoạt khi hợp lệ) / **Hủy** (quay lại danh sách).

* Sau khi lưu thành công: hiển thị thông báo và **quay về danh sách** (dòng mới nằm trên cùng).

#### 3\) Trang Sửa dự án

**Mục tiêu:** Cập nhật thông tin dự án hiện có.

**Form trường dữ liệu:**

* **Tên dự án**\* (như quy tắc ở trang Thêm).

**Hành vi & Luồng:**

* Nút **Lưu** / **Hủy**.

* Kiểm tra trùng tên với các dự án khác (không tính chính bản ghi đang sửa).

#### 4\) Xóa dự án

**Mục tiêu:** Gỡ dự án không còn sử dụng mà không mất dữ liệu lịch sử.  
**Quy tắc nghiệp vụ:**

* **Xóa mềm (soft-delete)**: dự án được đưa vào trạng thái đã xóa (ẩn khỏi danh sách mặc định) và **có thể khôi phục** trong thời hạn quy định (đề xuất 30 ngày).

* Nếu hệ thống đã có ràng buộc (ví dụ Căn thuộc Dự án), cần chặn xóa khi còn dữ liệu phụ thuộc quan trọng; hiển thị hướng dẫn “ngưng hoạt động/thu hồi liên kết” trước khi xóa.

**Hành vi:**

* Nhấn **Xóa** → hộp thoại xác nhận nêu rõ hệ quả và khả năng khôi phục.

* Ghi **nhật ký thao tác** (ai, khi nào).

### 5.1.2 Quản lý Căn

**Phạm vi & Giao diện**

* Gồm 4 phần: **Trang danh sách căn**, **Trang thêm căn**, **Trang sửa căn**, **Tính năng xóa căn**.

* **Giao diện:** bảng và form **CRUD** truyền thống, dễ thao tác, nhất quán với các màn khác.

#### 1\) Trang Danh sách căn

**Mục tiêu:** Xem nhanh các căn theo dự án, tìm/lọc và thao tác nhanh.

**Thành phần:**

* Bộ lọc: **Dự án** (dropdown/autocomplete), **Mã căn** (text/autocomplete).

* Bảng cột tối thiểu: **Mã căn,** **Dự án, Loại sản phẩm, Phân khu**, **Diện tích (m²)**, **Hướng cửa**, **View**, **Giá gốc HĐ**.

* Hành động dòng: **Sửa**, **Xóa**.

* Nút **\+ Thêm căn**; phân trang mặc định 20 dòng/trang.

#### 2\) Trang Thêm căn

**Mục tiêu:** Tạo mới một căn thuộc **một Dự án**.

**Trường dữ liệu (tối thiểu):**

* **Dự án**\*: chọn từ danh sách (bắt buộc).

* **Mã căn**\*: chuỗi ký tự (bắt buộc, duy nhất **trong cùng dự án**).  
* **Loại sản phẩm:** chọn trong danh sách lấy từ cấu hình  
* **Phân khu**

* **Diện tích (m²)**\*: số ≥ 0 (hỗ trợ thập phân).

* **Hướng cửa**\*: chọn trong danh sách: **Bắc, Đông Bắc, Đông, Đông Nam, Nam, Tây Nam, Tây, Tây Bắc, Khác**.

* **View**\*: text  
* **Giá gốc trên hợp đồng (VND)**\*: số ≥ 0\.

**Quy tắc/Validation:**

* (**Dự án**, **Mã căn**) phải **duy nhất**.

* **Diện tích ≥ 0**, **Giá gốc ≥ 0**; chuẩn hóa đơn vị **m²**; định dạng tiền **VND**.

**Hành vi:**

* **Lưu** (khi hợp lệ) / **Hủy** (về danh sách).

* Lưu thành công → thông báo và quay lại danh sách (hiển thị trên cùng).

#### 3\) Trang Sửa căn

**Mục tiêu:** Cập nhật thông tin căn hiện có.

**Trường dữ liệu:** như trang Thêm căn (giữ nguyên quy tắc).  
 **Hành vi:**

* **Lưu/Hủy**; kiểm tra trùng **Mã căn** với các căn **khác** trong cùng dự án.

#### 4\) Xóa căn

**Mục tiêu:** Gỡ căn không còn sử dụng mà không mất lịch sử.

**Quy tắc nghiệp vụ:**

* **Xóa mềm (soft-delete)**: ẩn khỏi danh sách mặc định, có thể **khôi phục** trong thời hạn (đề xuất 30 ngày).

* **Chặn xóa** nếu căn đã liên kết nghiệp vụ quan trọng; hướng dẫn “ngưng hoạt động/thu hồi liên kết” trước khi xóa.

**Hành vi:**

* Nhấn **Xóa** → hộp thoại xác nhận, nêu rõ hệ quả & khả năng khôi phục.

* Ghi **nhật ký thao tác** (ai, khi nào).

### 5.1.3 Quản lý Khách hàng

**Phạm vi & Giao diện**

* Trang: **Danh sách (Admin)**, **Danh sách (Manager \+ Staff)**, **Thêm**, **Sửa**, **Xóa**.

* **UI:** bảng & form **CRUD** truyền thống; có tìm kiếm, lọc, phân trang.

#### 1\) Trang Danh sách khách hàng

* **Mục tiêu:**   
  * Admin xem **tất cả** khách hàng.  
  * Manager chỉ thấy khách hàng được phân công quản lý

* **Chức năng:** tìm theo tên/điện thoại/email; lọc theo dự án đã giao dịch/nhân viên phụ trách; cột tùy chọn; hành động **Xem/Sửa/Xóa**; phân trang.

#### 2\) Trang Thêm khách hàng

**Trường bắt buộc:**

* **Họ và tên**\*

* **Phone**\* (duy nhất, chuẩn E.164)

**Nguồn & Nguồn chi tiết:**

* **Nguồn:** Chọn từ danh sách gồm:  
  * Data  
  * Leads  
  * Event  
  * Refer

* **Nguồn chi tiết**  
  * Nếu Nguồn là Data thì sẽ chọn theo danh sách lấy từ cấu hình  
  * Nếu Nguồn là Leads thì sẽ chọn danh sách lấy từ cấu hình

**Trường khác:** Ảnh chân dung, Ngày sinh, Email, Địa chỉ liên hệ, Địa chỉ thường trú, Quốc tịch, Tình trạng hôn nhân (chọn theo danh sách lấy từ cấu hình), Sở thích, Tổng tài sản, Lĩnh vực kinh doanh, Ghi chú.

**Trạng thái Zalo, Link facebook**

**Người thân:**

Ghi chú về về người thân

Danh sách người thân:

* Mối quan hệ: Chọn từ danh sách

* Họ và tên

* Năm sinh

* Số phone

**Sales được phân chăm khách:**

* **Chọn nhân viên** từ danh sách

* **Thời điểm nhận lead** để chăm

**Danh sách bất động sản đã sở hữu** (có thể sở hữu nhiều bất động sản):

* **Dự án** (chọn từ danh sách) → **Căn** (lọc theo dự án)

* **Ngày giao dịch**

* **Sales đã bán (ngoài hệ thống):** *(sale thuộc đơn vị khác, không phải nhân viên nội bộ)*

* **Tên đại lý**

* **Tên sale**

* **Số phone**

* **Email**

* **Lịch sử tương tác thứ cấp (chuyển nhượng)**

  * **Giá bán kỳ vọng (VND)**

  * **Giá cho thuê kỳ vọng (VND)**

  * **Kết quả**: chọn trong danh sách lấy từ cấu hình

  * **Thời điểm tương tác** (datetime)

  * **Ghi chú**

* **Tương tác lần đầu** (datetime)

* **Tương tác gần nhất** (datetime)

* **Giá gốc trên hợp đồng (VND)**  
* **Pháp lý:** chọn trong danh sách lấy từ cấu hình  
* **Diện tích đất/Diện tích tim tường**  
* **Diện tích sàn xây dựng/Diện tích thông thủy**

**Danh sách bất động sản đang chào** (có thể chào nhiều bất động sản):

* **Dự án đang chào** *chọn từ danh sách dự án*

* **Lịch sử tương tác sơ cấp (chào bán)**

  * **Thời điểm tương tác** (datetime)

  * **Kết quả:** chọn trong danh sách lấy từ cấu hình

  * **Ghi chú**  
* **Tương tác lần đầu** (datetime)

* **Tương tác gần nhất** (datetime)

#### 3\) Trang Sửa khách hàng

* Nội dung và kiểm tra như trang Thêm; cho phép cập nhật hồ sơ, phân công, giao dịch liên quan.

#### 4\) Xóa khách hàng

* **Xóa mềm (soft-delete)**: ẩn khỏi danh sách mặc định; có thể **khôi phục** trong thời hạn (đề xuất 30 ngày).

* Ghi **nhật ký thao tác** (ai/khi nào). Có thể **chặn xóa** nếu có liên kết nghiệp vụ.

### 5.1.4 Trang Import dữ liệu khách hàng

**Mục tiêu**  
Cho phép nhập danh sách khách hàng từ **tệp tải trực tiếp** hoặc **Google Drive**, có **dry-run** kiểm tra lỗi trước khi ghi, sau đó **xác nhận** chạy.

**Luồng 3 bước**

**Bước 1 — Chọn nguồn tệp**

* Nguồn:

  * **Tải lên** từ máy (drag-and-drop / chọn file).

  * **Kết nối Google Drive** → mở picker để chọn file.

* Hỗ trợ định dạng: **.xlsx, .xls, .csv** (UTF-8).

* Tùy chọn: chọn **sheet** (Excel nhiều sheet).

* Tải **file mẫu** (template) và **hướng dẫn format**.

* Giới hạn (đề xuất): ≤ **10 MB** hoặc **≤ 10k dòng**/file.

**Bước 2 — Dry-run (kiểm tra & ước tính)**

* Chạy kiểm tra **không ghi dữ liệu**, trả về:

  * **Tổng dòng**, **Hợp lệ**, **Lỗi**, **Cảnh báo**.

  * Bảng lỗi (phân trang) với: số dòng, cột, loại lỗi, mô tả.

* **Nhóm lỗi điển hình & rule**

  * Thiếu trường bắt buộc (full\_name/phone).

  * **Phone** sai định dạng / trùng **trong file** / trùng **trong hệ thống**.

  * Email sai định dạng; ngày sinh sai format; giá trị enum (marital\_status) không hợp lệ.

* **Xử lý trùng** (tùy chọn):

  * **Upsert theo phone** (khuyến nghị): cập nhật các trường non-key nếu đã tồn tại.

  * **Bỏ qua bản ghi trùng** (skip).

* **Idempotency**: hiển thị **checksum** file; nếu file trùng checksum đã chạy trước đó → cảnh báo “đã import”, cho phép **Force re-import** nếu muốn.

**Bước 3 — Xác nhận & Chạy**

* Tóm tắt: nguồn tệp, mapping, chế độ xử lý trùng, ước tính số bản ghi ghi vào.

* Nút **Xác nhận chạy** → tạo **job nền**.

* Tuỳ chọn: **chỉ chạy các dòng hợp lệ** (bỏ lỗi) hoặc **dừng khi gặp lỗi**.

### 5.1.5 Phân nhân sự quản lý và CSKH

**Mục tiêu**

Gán người **quản lý lead** (Manager) và người **chăm sóc lead** (Staff) cho từng lead/khách hàng theo **quy tắc tự động** hoặc **thao tác thủ công**, bảo đảm 1 lead có **tối đa 1 Manager** và **tối đa 1 Staff** đang **active** tại một thời điểm.

#### Phân lead thủ công tại Trang danh sách Khách hàng

Chọn 1 hoặc nhiều khách (lọc/xuất hiện checkbox) → bấm Phân công Manager/Phân công Staff, chọn nhân sự đích → Dry-run hiển thị số lead bị tác động → Xác nhận.

RBAC: Manager chỉ phân Staff cho khách mình quản lý; Admin không giới hạn.

#### Phân lead tự động tại Trang quản trị rule phân lead

* Danh sách **Rule** (bảng): Tên, Trạng thái (Bật/Tắt), **Ưu tiên** (kéo thả sắp xếp), Điều kiện tóm tắt, Hành động (Manager, Staff), Lần chạy gần nhất, Số lead phân lần gần nhất, **Sửa/Xóa/Nhân bản**.

* Nút **\+ Tạo Rule** → **Form Rule** gồm:

  * **Tên rule** (bắt buộc)

  * **Điều kiện** (tối thiểu hỗ trợ):

    * Dự án \= … (có thể nhiều giá trị)

    * Nguồn lead (nếu có), Khu vực (nếu có), Thời gian tạo trong X ngày gần đây…

    * Trạng thái lead (mới/đang chăm/đã liên hệ/đã loại…)

  * **Gán Manager**: chọn **một** Manager trong danh sách (có thể bỏ trống)  
  * **Gán Staff**: chọn **một** Staff trong danh sách  
  * **Khi xung đột** (đã có Manager/Staff active): **Bỏ qua** / **Ghi đè** / **Chỉ ghi đè nếu trống Staff**.

**Quy tắc nghiệp vụ**

* Mỗi lead tại một thời điểm: **1 Manager active**, **1 Staff active**.

* Thứ tự áp dụng: theo **Ưu tiên** (từ trên xuống). Rule sau **không** chạy cho lead đã được rule trước gán (nếu “bỏ qua”).

* **Fallback**: khi rule không tìm được nhân sự đủ điều kiện để trống.

### 5.1.6 Quản lý Nhân viên

**Phạm vi & Giao diện**

* Gồm 4 phần: **Trang danh sách nhân viên**, **Trang thêm nhân viên**, **Trang sửa nhân viên**, **Tính năng xóa nhân viên**.

* **Giao diện:** bảng & form **CRUD** truyền thống, nhất quán với các module khác.

#### 1\) Trang Danh sách nhân viên

**Mục tiêu:** Xem nhanh toàn bộ nhân viên, tìm/lọc và thao tác nhanh.  
 **Thành phần:**

* Thanh công cụ: **Tìm kiếm** theo Mã số/Name/Phone/Email, nút **\+ Thêm nhân viên**.

* Bảng cột tối thiểu: **Mã số nhân viên**, **Họ và tên**, **Số phone**, **Email**, **Quyền**.

* Hành động dòng: **Sửa**, **Xóa**.

* Phân trang mặc định 20 dòng/trang.

#### 2\) Trang Thêm nhân viên

**Mục tiêu:** Tạo mới hồ sơ nhân viên.

**Trường dữ liệu:**

* **Mã số nhân viên**\* (duy nhất, không khoảng trắng đầu/cuối)

* **Họ và tên**\*

* **Số phone** (định dạng chuẩn E.164, đề xuất duy nhất)

* **Email** (định dạng RFC, đề xuất duy nhất)

* **Quyền**\* (chọn từ danh sách vai trò)

**Validation & Hành vi:**

* **Mã số nhân viên** bắt buộc & **duy nhất**.

* Nếu nhập **Phone/Email**: kiểm tra định dạng; có thể đặt **duy nhất** để tránh trùng.

* **Lưu/Hủy**; lưu thành công → thông báo và quay về **Danh sách** (dòng mới trên cùng).

####  3\) Trang Sửa nhân viên

**Mục tiêu:** Cập nhật thông tin nhân viên hiện có.

**Trường dữ liệu:** như trang Thêm nhân viên.  
 **Hành vi & Validation:**

* **Lưu/Hủy**; khi đổi **Mã số/Phone/Email** phải kiểm tra **duy nhất** với các nhân viên khác.

#### 4\) Xóa nhân viên

**Mục tiêu:** Gỡ nhân viên không còn sử dụng mà không mất lịch sử.

**Quy tắc nghiệp vụ:**

* **Xóa mềm (soft-delete)**: ẩn khỏi danh sách mặc định; cho phép **khôi phục** trong thời hạn (đề xuất 30 ngày).

* **Chặn xóa** nếu nhân viên còn đang **phụ trách khách hàng** (còn phân công active). Yêu cầu **chuyển giao** khách hàng cho người khác trước khi xóa.

**Hành vi:**

* Nhấn **Xóa** → hộp thoại xác nhận, nêu rõ hệ quả & khả năng khôi phục.

* Ghi **nhật ký thao tác** (ai, khi nào).

### 5.1.7 Quản lý quyền

**Phạm vi & Giao diện**

* Gồm 4 phần: **Trang danh sách quyền**, **Trang thêm quyền**, **Trang sửa quyền**, **Tính năng xóa quyền**.

* **UI:** bảng & form **CRUD** truyền thống, cấu hình theo **ma trận quyền**.

**Truy cập:** Chỉ **Admin** mới thấy và thao tác module này.

#### 1\) Trang Danh sách quyền

**Mục tiêu:** Xem toàn bộ quyền, tìm nhanh, chỉnh sửa/xóa.  
 **Thành phần:**

* Thanh công cụ: **Tìm kiếm theo Tên quyền**, **\+ Thêm quyền**.

* Bảng cột: **Tên quyền**, **Số nhân viên đang dùng**, **Trạng thái**.

* Hành động dòng: **Sửa**, **Xóa** *(tuỳ chọn: Nhân bản)*.

* Phân trang 20 dòng/trang.

#### 2\) Trang Thêm quyền

**Mục tiêu:** Tạo một vai trò mới.

**Trường dữ liệu:**

* **Tên quyền**\* (duy nhất, ≤ 100 ký tự).

* **Ma trận quyền** (checkbox) theo **Menu × Tác vụ**:

* Nút **Chọn tất cả / Bỏ chọn tất cả** theo hàng/cột.

**Validation & Hành vi:**

* **Tên quyền** bắt buộc & **duy nhất**.

* Cho phép lưu khi có **ít nhất “Xem”** ở tối thiểu 1 menu.

* **Lưu/Hủy**; lưu thành công → quay về danh sách.

#### 3\) Trang Sửa quyền

* Giống trang Thêm; tải sẵn ma trận hiện tại.

* **Cảnh báo:** thay đổi quyền **có hiệu lực ngay** với tất cả nhân viên đang gán vai trò này.

#### 4\) Xóa quyền

**Quy tắc:**

* **Xóa mềm (soft-delete)**, có thể **khôi phục** (đề xuất trong 30 ngày).

* **Không cho xóa** nếu quyền đang được **gán cho ≥1 nhân viên** → yêu cầu chuyển vai trò khác trước.

* **Tự bảo vệ:** không cho **xóa/quay về không quyền** vai trò **Admin mặc định** và không cho người dùng **tự tước toàn bộ quyền** của mình.

### 5.1.8 Quản lý cấu hình

* **Mục tiêu**  
   Cho phép admin quản lý các tham số cấu hình của phần mềm  
* **Cấu hình kết nối tới các hệ thống khác**  
  * Thông tin kết nối Mail Server  
* **Cấu hình danh sách**  
  * Danh sách Nguồn chi tiết của Data  
  * Danh sách Nguồn chi tiết của Leads  
  * Danh sách Mối quan hệ của người thân  
  * Danh sách kết quả tương tác thứ cấp (chuyển nhượng)  
  * Danh sách kết quả tương tác sơ cấp (mời bán)  
  * Danh sách loại sản phẩm  
* **Cấu hình thông báo**  
  * Có các loại thông báo sau  
    * Thông báo cho nhân sự khi có lead mới được phân  
    * Thông báo sinh nhật của khách hàng  
    * Cảnh báo chưa thấy chăm sóc lead mới được phân  
    * Cảnh báo đã lâu chưa chăm sóc lead   
  * Cả 4 loại thông báo này đều cấu hình được hình thức thông báo (có thể chọn nhiều hình thức thông báo)  
    * Thông báo nhanh trên header của web app  
    * Gửi thông báo qua email  
    * Gửi thông báo qua SMS Brand name (sẽ triển khai trong giai đoạn sau)  
    * Gửi thông báo qua ZNS Zalo (sẽ triển khai trong giai đoạn sau)  
    * Gửi thông báo qua Instagram (sẽ triển khai trong giai đoạn sau)  
  * Với thông báo sinh nhật của khách hàng thì sẽ cấu hình sẽ gửi thông báo trước bao nhiêu ngày?  
  * Với cảnh báo chưa thấy chăm sóc lead mới được phân thì sẽ cấu hình xem sau bao nhiêu ngày được phân mà không có tương tác mới thì sẽ gửi cảnh báo?  
  * Với cảnh báo đã lâu chưa chăm sóc lead thì sẽ cấu hình xem sau bao nhiêu lâu mà không có tương tác mới thì sẽ gửi cảnh báo?

## **5.2 Module Chăm sóc khách hàng**

### 5.2.1 Trợ lý AI

* **Mục tiêu**  
   Cho phép người dùng **chat bằng ngôn ngữ tự nhiên** để ra lệnh và **khám phá nhanh** dữ liệu khách hàng (tìm kiếm, tra cứu hồ sơ).  
* **Giao diện**  
   Dạng hội thoại **tương tự ChatGPT**: ô nhập tin nhắn, gợi ý lệnh, lịch sử hội thoại, thẻ kết quả (bảng/hồ sơ) kèm nút hành động mở sang màn hình chi tiết/CRUD.

### 5.2.2 Trang Hồ sơ 360° khách hàng

* **Mục tiêu:** Xem **toàn bộ thông tin** của một khách hàng trên một trang: hồ sơ cá nhân, **lịch sử chăm sóc**, **lịch sử giao dịch** và các bên liên quan.

* **Giao diện:** Dạng **CV** (2 cột, dễ in/Export PDF).

* **Cột trái (hồ sơ tĩnh)**

  * **Thông tin cá nhân của khách hàng**

    * Ảnh chân dung

    * **Họ & tên**, **Phone**

    * Email, Ngày sinh, Địa chỉ liên hệ, Địa chỉ thường trú, Quốc tịch

    * Tình trạng hôn nhân, Sở thích, Tổng tài sản, Lĩnh vực kinh doanh, Ghi chú

    * Tình trạng Zalo, Link Facebook

  * **Thông tin về Nguồn & Chi tiết nguồn**  
  * **Thông tin nhân viên sale được phân chăm sóc khách hàng (nội bộ)**

    * **Nhân viên phụ trách** (chọn từ danh sách)

    * **Thời điểm nhận lead để chăm**

    * Lịch sử phân công trước đây (nếu có)  
  * **Thông tin những người thân của khách hàng**

    * Danh sách người thân: Mối quan hệ, Tên, Ngày sinh, Số phone

    * **Ghi chú**

* **Cột phải (động hoạt động & giao dịch)**

  * **Thông tin các căn đã giao dịch** *(danh sách thẻ theo từng căn)*  
     Mỗi thẻ căn hiển thị:

    * **Dự án**, **Mã căn**

    * **Thông tin sales đã bán của từng căn (ngoài hệ thống)**

      * **Tên đại lý**, **Tên sale**, **Số phone**, **Email**

      * (*Lưu ý: đây là sales của **đơn vị khác**, không phải nhân viên nội bộ*)

    * **Giá gốc trên hợp đồng**  
    * **Thông tin thứ cấp** *(chuyển nhượng)*

      * **Tương tác lần đầu**, **Tương tác gần nhất**, **Kết quả**

      * ,**Giá bán kỳ vọng**, **Giá cho thuê kỳ vọng**, **Ghi chú**

  * **Thông tin các căn đang chào** *(bán mới)*

    * **Tương tác lần đầu**, **Tương tác gần nhất**

    * **Dự án đang chào** 

    * **Kết quả**, **Ghi chú**

### 5.2.3 Thông báo

**Nội dung thông báo**

* Thông báo cho nhân sự khi có lead mới được phân

* Thông báo sinh nhật của khách hàng

* Cảnh báo chưa thấy chăm sóc lead mới được phân

* Cảnh báo đã lâu chưa chăm sóc lead 

**Thông báo nhanh trên Header**

* Avatar \+ **Tên nhân sự** \+ **badge số thông báo chưa đọc**.

* Click **badge** (hoặc mục “Thông báo” trong popup avatar) → mở **Trang Thông báo**.

**Trang Thông báo**

* **Tabs:** **Hộp thư** (chưa đọc) | **Đã đọc**.

* **Item thông báo:** **tiêu đề đậm** (Tên KH • Dự án) · **loại thông báo** (lead mới phân | sinh nhật | lead mới chưa chăm sóc | lead lâu không chăm sóc).

* · **thời gian** (“5 phút trước”).

* **Chạm item:** mở **Hồ sơ 360°** và **tự động đánh dấu đã đọc**.

* **Trạng thái:** trống (“Chưa có thông báo”), kéo để làm mới, cuộn vô hạn.

