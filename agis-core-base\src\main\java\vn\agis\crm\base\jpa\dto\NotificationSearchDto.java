package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class NotificationSearchDto {
    private Long targetEmployeeId; // optional filter
    private Long targetCustomerId; // optional filter
    private Integer type; // optional filter (1-4)
    private String title; // optional filter - case-insensitive partial match
    private String content; // optional filter - case-insensitive partial match
    private Boolean isRead; // optional filter
    private String readAt; // optional filter - date range
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "createdAt,desc"; // default sort by newest first

    public NotificationSearchDto() {}

    public NotificationSearchDto(Long targetEmployeeId, Long targetCustomerId, Integer type, 
                               String title, String content, Boolean isRead, String readAt,
                               Integer page, Integer size, String sortBy) {
        this.targetEmployeeId = targetEmployeeId;
        this.targetCustomerId = targetCustomerId;
        this.type = type;
        this.title = title;
        this.content = content;
        this.isRead = isRead;
        this.readAt = readAt;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
