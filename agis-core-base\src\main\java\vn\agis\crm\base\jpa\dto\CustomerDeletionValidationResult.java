package vn.agis.crm.base.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * Result of customer deletion validation containing all dependency information
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDeletionValidationResult {
    
    /**
     * Whether the customer can be safely deleted
     */
    private boolean canDelete;
    
    /**
     * List of dependency errors preventing deletion
     */
    private List<CustomerDependencyError> dependencies;
    
    /**
     * Overall validation message
     */
    private String message;
    
    /**
     * Customer ID that was validated
     */
    private Long customerId;
    
    /**
     * Customer name for reference
     */
    private String customerName;
    
    /**
     * Creates a successful validation result (no dependencies found)
     */
    public static CustomerDeletionValidationResult success(Long customerId, String customerName) {
        CustomerDeletionValidationResult result = new CustomerDeletionValidationResult();
        result.setCanDelete(true);
        result.setCustomerId(customerId);
        result.setCustomerName(customerName);
        result.setDependencies(new ArrayList<>());
        result.setMessage("Khách hàng có thể xóa an toàn.");
        return result;
    }
    
    /**
     * Creates a failed validation result with dependency errors
     */
    public static CustomerDeletionValidationResult failure(Long customerId, String customerName, 
                                                         List<CustomerDependencyError> dependencies) {
        CustomerDeletionValidationResult result = new CustomerDeletionValidationResult();
        result.setCanDelete(false);
        result.setCustomerId(customerId);
        result.setCustomerName(customerName);
        result.setDependencies(dependencies);
        result.setMessage(createFailureMessage(dependencies));
        return result;
    }
    
    /**
     * Creates a validation result for customer not found
     */
    public static CustomerDeletionValidationResult customerNotFound(Long customerId) {
        CustomerDeletionValidationResult result = new CustomerDeletionValidationResult();
        result.setCanDelete(false);
        result.setCustomerId(customerId);
        result.setCustomerName(null);
        result.setDependencies(new ArrayList<>());
        result.setMessage("Không tìm thấy khách hàng");
        return result;
    }
    
    /**
     * Creates an overall failure message based on dependency errors
     */
    private static String createFailureMessage(List<CustomerDependencyError> dependencies) {
        if (dependencies == null || dependencies.isEmpty()) {
            return "Không thể xóa khách hàng do có phụ thuộc không xác định.";
        }

        if (dependencies.size() == 1) {
            return dependencies.get(0).getMessage();
        }

        StringBuilder message = new StringBuilder("Không thể xóa khách hàng vì còn tồn tại: ");
        List<String> dependencyMessages = new ArrayList<>();
        
        for (CustomerDependencyError error : dependencies) {
            dependencyMessages.add(error.getCount() + " " + error.getDependencyType());
        }
        
        message.append(String.join(", ", dependencyMessages));
        return message.toString();
    }
    
    /**
     * Adds a dependency error to the result
     */
    public void addDependencyError(CustomerDependencyError error) {
        if (this.dependencies == null) {
            this.dependencies = new ArrayList<>();
        }
        this.dependencies.add(error);
        this.canDelete = false;
        this.message = createFailureMessage(this.dependencies);
    }
    
    /**
     * Gets total count of all dependencies
     */
    public long getTotalDependencyCount() {
        if (dependencies == null) {
            return 0;
        }
        return dependencies.stream().mapToLong(CustomerDependencyError::getCount).sum();
    }
}
