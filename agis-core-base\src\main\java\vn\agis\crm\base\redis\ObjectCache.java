package vn.agis.crm.base.redis;

import java.io.IOException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Created by tiemnd on 12/14/19.
 */
public interface ObjectCache {
    Object get(final String key, Class<?> type) throws IOException;

    Collection<Object> getAll(final Collection<String> keySet, Class<?> type) throws IOException;

    void put(final String key, final Object item, Class<?> type) throws Exception;

    void put(final String key, final Object item, final int ttl, Class<?> type) throws Exception;

    void put(final String key, final Object item, final long timeout, TimeUnit timeUnit, Class<?> type) throws Exception;

    void putAll(Map<String, Object> m, Class<?> type);

    void remove(final String key, Class<?> type);

    Long increment(String key, Long interval);

    boolean expire(String keys, long timeout, TimeUnit timeUnit);

    void put(String key, Object item) throws Exception;

    boolean expire(String keys, long timeout, TimeUnit timeUnit, Class<?> typeClass);

    Set<String> getAllKey(Class<?> type);

    List<Object> getAllForType(Class<?> type) throws IOException;
}
