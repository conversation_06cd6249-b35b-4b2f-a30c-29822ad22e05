package vn.agis.crm.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.InteractionPrimarySearchDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryUpdateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryUpdateDto;
import vn.agis.crm.controller.InteractionsPrimaryController;
import vn.agis.crm.controller.InteractionsSecondaryController;
import vn.agis.crm.service.InteractionsPrimaryApiService;
import vn.agis.crm.service.InteractionsSecondaryApiService;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;

/**
 * Comprehensive test suite for Interactions CRUD API implementation
 * Tests both Primary and Secondary interaction endpoints
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class InteractionsCrudApiTest {

    @Autowired
    private InteractionsPrimaryController primaryController;

    @Autowired
    private InteractionsSecondaryController secondaryController;

    @Autowired
    private InteractionsPrimaryApiService primaryApiService;

    @Autowired
    private InteractionsSecondaryApiService secondaryApiService;

    private final HttpServletRequest mockRequest = mock(HttpServletRequest.class);

    // ========================================
    // PRIMARY INTERACTIONS TESTS
    // ========================================

    @Test
    public void testCreatePrimaryInteraction() {
        // Arrange
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("Successful contact");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Customer showed interest in the project");

        // Act
        ResponseEntity<InteractionPrimaryDto> response = primaryController.createInteraction(createDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(201, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("Successful contact", response.getBody().getResult());
        assertEquals("15/01/2024", response.getBody().getHappenedAt());
    }

    @Test
    public void testUpdatePrimaryInteraction() {
        // Arrange - First create an interaction
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("Initial contact");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("First contact made");

        ResponseEntity<InteractionPrimaryDto> createResponse = primaryController.createInteraction(createDto, mockRequest);
        Long interactionId = createResponse.getBody().getId();

        // Arrange - Update DTO
        InteractionPrimaryUpdateDto updateDto = new InteractionPrimaryUpdateDto();
        updateDto.setId(interactionId);
        updateDto.setCustomerOfferId(1L);
        updateDto.setResult("Follow-up contact");
        updateDto.setHappenedAt("16/01/2024");
        updateDto.setNotes("Follow-up call completed");

        // Act
        ResponseEntity<InteractionPrimaryDto> response = primaryController.updateInteraction(interactionId, updateDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("Follow-up contact", response.getBody().getResult());
        assertEquals("16/01/2024", response.getBody().getHappenedAt());
    }

    @Test
    public void testGetPrimaryInteractionById() {
        // Arrange - Create an interaction first
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("Test interaction");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Test notes");

        ResponseEntity<InteractionPrimaryDto> createResponse = primaryController.createInteraction(createDto, mockRequest);
        Long interactionId = createResponse.getBody().getId();

        // Act
        ResponseEntity<InteractionPrimaryDto> response = primaryController.getInteractionById(interactionId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals(interactionId, response.getBody().getId());
        assertEquals("Test interaction", response.getBody().getResult());
    }

    @Test
    public void testSearchPrimaryInteractions() {
        // Arrange
        InteractionPrimarySearchDto searchDto = new InteractionPrimarySearchDto();
        searchDto.setCustomerOfferId(1L);
        searchDto.setResult("contact");
        searchDto.setPage(0);
        searchDto.setSize(10);
        searchDto.setSortBy("happenedAt,desc");

        // Act
        ResponseEntity<Page<InteractionPrimaryDto>> response = primaryController.searchInteractions(searchDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getContent() instanceof List);
    }

    @Test
    public void testGetPrimaryInteractionsByCustomerOfferId() {
        // Arrange
        Long customerOfferId = 1L;

        // Act
        ResponseEntity<List<InteractionPrimaryDto>> response = primaryController.getInteractionsByCustomerOfferId(customerOfferId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof List);
    }

    @Test
    public void testCountPrimaryInteractionsByCustomerOfferId() {
        // Arrange
        Long customerOfferId = 1L;

        // Act
        ResponseEntity<Long> response = primaryController.countInteractionsByCustomerOfferId(customerOfferId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() >= 0);
    }

    @Test
    public void testDeletePrimaryInteraction() {
        // Arrange - Create an interaction first
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("To be deleted");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("This will be deleted");

        ResponseEntity<InteractionPrimaryDto> createResponse = primaryController.createInteraction(createDto, mockRequest);
        Long interactionId = createResponse.getBody().getId();

        // Act
        ResponseEntity<Void> response = primaryController.deleteInteraction(interactionId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
    }

    // ========================================
    // SECONDARY INTERACTIONS TESTS
    // ========================================

    @Test
    public void testCreateSecondaryInteraction() {
        // Arrange
        InteractionSecondaryCreateDto createDto = new InteractionSecondaryCreateDto();
        createDto.setCustomerPropertyId(1L);
        createDto.setExpectedSellPrice(new BigDecimal("5000000000"));
        createDto.setExpectedRentPrice(new BigDecimal("50000000"));
        createDto.setResult("Property valuation completed");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Customer provided expected prices");

        // Act
        ResponseEntity<InteractionSecondaryDto> response = secondaryController.createInteraction(createDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(201, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("Property valuation completed", response.getBody().getResult());
        assertEquals(0, new BigDecimal("5000000000").compareTo(response.getBody().getExpectedSellPrice()));
    }

    @Test
    public void testUpdateSecondaryInteraction() {
        // Arrange - First create an interaction
        InteractionSecondaryCreateDto createDto = new InteractionSecondaryCreateDto();
        createDto.setCustomerPropertyId(1L);
        createDto.setExpectedSellPrice(new BigDecimal("5000000000"));
        createDto.setResult("Initial valuation");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Initial assessment");

        ResponseEntity<InteractionSecondaryDto> createResponse = secondaryController.createInteraction(createDto, mockRequest);
        Long interactionId = createResponse.getBody().getId();

        // Arrange - Update DTO
        InteractionSecondaryUpdateDto updateDto = new InteractionSecondaryUpdateDto();
        updateDto.setId(interactionId);
        updateDto.setCustomerPropertyId(1L);
        updateDto.setExpectedSellPrice(new BigDecimal("5500000000"));
        updateDto.setExpectedRentPrice(new BigDecimal("55000000"));
        updateDto.setResult("Updated valuation");
        updateDto.setHappenedAt("16/01/2024");
        updateDto.setNotes("Revised assessment with market analysis");

        // Act
        ResponseEntity<InteractionSecondaryDto> response = secondaryController.updateInteraction(interactionId, updateDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertEquals("Updated valuation", response.getBody().getResult());
        assertEquals(0, new BigDecimal("5500000000").compareTo(response.getBody().getExpectedSellPrice()));
    }

    @Test
    public void testSearchSecondaryInteractions() {
        // Arrange
        InteractionSecondarySearchDto searchDto = new InteractionSecondarySearchDto();
        searchDto.setCustomerPropertyId(1L);
        searchDto.setExpectedSellPriceFrom(new BigDecimal("1000000000"));
        searchDto.setExpectedSellPriceTo(new BigDecimal("10000000000"));
        searchDto.setPage(0);
        searchDto.setSize(10);
        searchDto.setSortBy("happenedAt,desc");

        // Act
        ResponseEntity<Page<InteractionSecondaryDto>> response = secondaryController.searchInteractions(searchDto, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody().getContent() instanceof List);
    }

    @Test
    public void testGetSecondaryInteractionsByCustomerPropertyId() {
        // Arrange
        Long customerPropertyId = 1L;

        // Act
        ResponseEntity<List<InteractionSecondaryDto>> response = secondaryController.getInteractionsByCustomerPropertyId(customerPropertyId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() instanceof List);
    }

    @Test
    public void testCountSecondaryInteractionsByUnitId() {
        // Arrange
        Long unitId = 1L;

        // Act
        ResponseEntity<Long> response = secondaryController.countInteractionsByUnitId(unitId, mockRequest);

        // Assert
        assertNotNull(response);
        assertEquals(200, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertTrue(response.getBody() >= 0);
    }

    // ========================================
    // VALIDATION TESTS
    // ========================================

    @Test
    public void testCreatePrimaryInteractionWithInvalidData() {
        // Arrange - Missing required fields
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        // Missing customerOfferId, result, and happenedAt

        // Act & Assert
        assertThrows(Exception.class, () -> {
            primaryController.createInteraction(createDto, mockRequest);
        });
    }

    @Test
    public void testCreateSecondaryInteractionWithInvalidPrice() {
        // Arrange - Negative price
        InteractionSecondaryCreateDto createDto = new InteractionSecondaryCreateDto();
        createDto.setCustomerPropertyId(1L);
        createDto.setExpectedSellPrice(new BigDecimal("-1000000"));
        createDto.setResult("Invalid price test");
        createDto.setHappenedAt("15/01/2024");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            secondaryController.createInteraction(createDto, mockRequest);
        });
    }

    @Test
    public void testInvalidDateFormat() {
        // Arrange - Invalid date format
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("Invalid date test");
        createDto.setHappenedAt("2024-01-15"); // Wrong format, should be dd/MM/yyyy
        createDto.setNotes("Testing invalid date format");

        // Act & Assert
        assertThrows(Exception.class, () -> {
            primaryController.createInteraction(createDto, mockRequest);
        });
    }

    // ========================================
    // INTEGRATION TESTS
    // ========================================

    @Test
    public void testFullPrimaryInteractionLifecycle() {
        // Create
        InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
        createDto.setCustomerOfferId(1L);
        createDto.setResult("Lifecycle test");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Testing full lifecycle");

        ResponseEntity<InteractionPrimaryDto> createResponse = primaryController.createInteraction(createDto, mockRequest);
        assertNotNull(createResponse.getBody());
        Long interactionId = createResponse.getBody().getId();

        // Read
        ResponseEntity<InteractionPrimaryDto> getResponse = primaryController.getInteractionById(interactionId, mockRequest);
        assertNotNull(getResponse.getBody());
        assertEquals("Lifecycle test", getResponse.getBody().getResult());

        // Update
        InteractionPrimaryUpdateDto updateDto = new InteractionPrimaryUpdateDto();
        updateDto.setId(interactionId);
        updateDto.setCustomerOfferId(1L);
        updateDto.setResult("Updated lifecycle test");
        updateDto.setHappenedAt("16/01/2024");
        updateDto.setNotes("Updated notes");

        ResponseEntity<InteractionPrimaryDto> updateResponse = primaryController.updateInteraction(interactionId, updateDto, mockRequest);
        assertNotNull(updateResponse.getBody());
        assertEquals("Updated lifecycle test", updateResponse.getBody().getResult());

        // Delete
        ResponseEntity<Void> deleteResponse = primaryController.deleteInteraction(interactionId, mockRequest);
        assertEquals(200, deleteResponse.getStatusCodeValue());
    }

    @Test
    public void testFullSecondaryInteractionLifecycle() {
        // Create
        InteractionSecondaryCreateDto createDto = new InteractionSecondaryCreateDto();
        createDto.setCustomerPropertyId(1L);
        createDto.setExpectedSellPrice(new BigDecimal("3000000000"));
        createDto.setResult("Secondary lifecycle test");
        createDto.setHappenedAt("15/01/2024");
        createDto.setNotes("Testing secondary lifecycle");

        ResponseEntity<InteractionSecondaryDto> createResponse = secondaryController.createInteraction(createDto, mockRequest);
        assertNotNull(createResponse.getBody());
        Long interactionId = createResponse.getBody().getId();

        // Read
        ResponseEntity<InteractionSecondaryDto> getResponse = secondaryController.getInteractionById(interactionId, mockRequest);
        assertNotNull(getResponse.getBody());
        assertEquals("Secondary lifecycle test", getResponse.getBody().getResult());

        // Update
        InteractionSecondaryUpdateDto updateDto = new InteractionSecondaryUpdateDto();
        updateDto.setId(interactionId);
        updateDto.setCustomerPropertyId(1L);
        updateDto.setExpectedSellPrice(new BigDecimal("3500000000"));
        updateDto.setResult("Updated secondary lifecycle test");
        updateDto.setHappenedAt("16/01/2024");
        updateDto.setNotes("Updated secondary notes");

        ResponseEntity<InteractionSecondaryDto> updateResponse = secondaryController.updateInteraction(interactionId, updateDto, mockRequest);
        assertNotNull(updateResponse.getBody());
        assertEquals("Updated secondary lifecycle test", updateResponse.getBody().getResult());

        // Delete
        ResponseEntity<Void> deleteResponse = secondaryController.deleteInteraction(interactionId, mockRequest);
        assertEquals(200, deleteResponse.getStatusCodeValue());
    }
}
