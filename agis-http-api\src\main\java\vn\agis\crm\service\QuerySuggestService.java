package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.jpa.entity.QuerySuggest;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.HashMap;
import java.util.List;

@Service
public class QuerySuggestService extends CrudService<QuerySuggest, Long>{
    private static final Logger logger = LoggerFactory.getLogger(QuerySuggestService.class);
    public QuerySuggestService() {
        super(QuerySuggest.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.QUERY_SUGGEST;
    }

    public List<QuerySuggest> search() {
        List<QuerySuggest> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, new HashMap<>(), routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<QuerySuggest>) event.payload;
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return response;
        } catch (Exception e) {
            logger.error("Error in searchAreas: {}", e.getMessage(), e);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, "", response != null ? response.toString() : null, event);
        }
    }
}
