package vn.agis.crm.base.jpa.dto;

import lombok.Data;

@Data
public class FileUploadDto {
    private byte[] content;
    private String originalFilename;
    private String contentType;
    private long size;
    private String options;
    
    public FileUploadDto() {}
    
    public FileUploadDto(byte[] content, String originalFilename, String contentType, long size, String options) {
        this.content = content;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.size = size;
        this.options = options;
    }
}
