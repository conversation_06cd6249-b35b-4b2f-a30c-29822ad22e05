package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Entity
@Table(name = "customer_relatives")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerRelatives extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "relation_type")
    private String relationType;

    @Column(name = "full_name")
    private String fullName;

    @Column(name = "year_of_birth")
    private Integer yearOfBirth;

    @Column(name = "phone")
    private String phone;

    @Lob
    @Column(name = "notes")
    private String notes;
}

