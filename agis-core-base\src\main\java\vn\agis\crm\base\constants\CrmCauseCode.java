package vn.agis.crm.base.constants;

public class CrmCauseCode {
    // success
    public static final int CAUSE_OK = 0; // Thành công
    public static final int CAUSE_OK_PARTIAL = 1; // D<PERSON> liệu trên một số hàng không hợp lệ
    // file error
    public static final int CAUSE_FILE_CANNOT_BE_OPENED = 400; // Không thể mở file
    public static final int CAUSE_FILE_SIZE_EXCEED = 401; // Kích thước file vượt quá giới hạn
    public static final int CAUSE_FILE_REDUNDANT_COLUMN = 402; // Thừa cột
    public static final int CAUSE_FILE_MISSING_COLUMN = 403; // Thiếu cột
    public static final int CAUSE_FILE_DUPLICATE_COLUMN = 404; // Trùng cột
    public static final int CAUSE_NOT_READABLE_ROW_DATA = 405; // Không thể lấy thông tin hàng từ file excel
    public static final int CAUSE_FILE_NAME_TOO_SHORT = 406; // Tên file quá ngắn
    public static final int CAUSE_FILE_NAME_TOO_LONG = 407; // Tên file quá dài
    public static final int CAUSE_FILE_EXTENSION_NOT_ALOW = 408; // Định dạng file không cho phép
    public static final int CAUSE_FILE_HAVE_ERROR = 400; // Trùng cột
    public static final int CAUSE_BELONG_TO_OTHER_PROVINCE = 410; // Dữ liệu thuộc tỉnh khác
    public static final int CAUSE_NOT_HAS_PERMISSION = 420; // Không có quyền xử lý dữ liệu
    public static final int CAUSE_FORMAT_FILE_EXAMPLE_NOT_VALID = 430; // Sai định dạng file mẫu
    public static final int EXCEEDED_FILE_LENGTH = 440; // Kích thước dòng dữ liệu vượt ngưỡng
    public static final int EXCEEDED_TOTAL_DATA_LENGTH = 450; // Tổng bản ghi trong file và db vượt ngưỡng
    public static final int CAUSE_UNKNOWN = 501; // Các lỗi khác

    public static class Message {
        public static final String INVALID_DATE_FORMAT = "Sai định dạng ngày";
    }

    public static String getCause(int crmCauseCode) {
        switch (crmCauseCode) {
            case CAUSE_OK: return "CAUSE_OK";
            case CAUSE_FILE_REDUNDANT_COLUMN: return "CAUSE_FILE_REDUNDANT_COLUMN";
        }
        return "CAUSE_UNKNOWN";
    }
}
