package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.DynamicConfigSearchDTO;
import vn.agis.crm.base.jpa.dto.req.FilterDashboardRequest;
import vn.agis.crm.base.jpa.entity.DynamicConfig;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.DynamicConfigService;

import java.util.List;


@RestController
@RequestMapping("/dashboard")
public class DynamicConfigController extends CrudController <DynamicConfig , Long>{

    private final Logger logger = LoggerFactory.getLogger(ReportController.class);
    DynamicConfigService dynamicConfigService;

    @Autowired
    public DynamicConfigController(DynamicConfigService service) {
        super(service);
        this.dynamicConfigService = service;
        this.baseUrl = "/dashboard";
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('searchCnfDynamicChart')")
    public ResponseEntity<Page<DynamicConfig>> searchDynamicConfig(
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "type", required = false, defaultValue = " ") String type,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "createdDate,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        DynamicConfigSearchDTO dynamicConfigSearchDTO = new DynamicConfigSearchDTO(name, type, page, size, sortBy);
        Page<DynamicConfig> reportRespones = dynamicConfigService.searchDynamicConfig(dynamicConfigSearchDTO, listRequest.getPageable());
        logger.info("Search DynamicConfig End");
        return ResponseEntity.ok().body(reportRespones);
    }

    @GetMapping("/getAll")
    public ResponseEntity<List<DynamicConfig>> getAll() {
        List<DynamicConfig> reportRespones = dynamicConfigService.getAll();
        logger.info("Search DynamicConfig End");
        return ResponseEntity.ok().body(reportRespones);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('getCnfDynamicChart')")
    public ResponseEntity<DynamicConfig> getDetail(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        DynamicConfig dynamicConfig = dynamicConfigService.get(id);
        return ResponseEntity.ok().body(dynamicConfig);
    }

    @PostMapping("")
    @PreAuthorize("hasAnyAuthority('createCnfDynamicChart')")
    public ResponseEntity<DynamicConfig> createDashboard(@RequestBody DynamicConfig entity, HttpServletRequest httpServletRequest) {
//        Long userId =  ((UserPrincipal) SecurityContextHolder.getContext().getAuthentication().getPrincipal()).getUserId();
//        entity.setUserId(userId);
        DynamicConfig dynamicConfig = dynamicConfigService.create(entity);
        return ResponseEntity.ok().body(dynamicConfig);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('updateCnfDynamicChart')")
    public ResponseEntity<DynamicConfig> updateDashboard(@PathVariable Long id ,@RequestBody DynamicConfig entity, HttpServletRequest httpServletRequest) {
        DynamicConfig dynamicConfig = dynamicConfigService.update(id,entity);
        return ResponseEntity.ok().body(dynamicConfig);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('deleteCnfDynamicChart')")
    public ResponseEntity<ResponseBase> deleteDashboard(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = dynamicConfigService.deleteById(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @GetMapping("/checkName")
    public ResponseEntity<Long> checkExistName(@RequestParam(value = "name", required = true) String name, HttpServletRequest request) {
        try {
            return new ResponseEntity<>(dynamicConfigService.countName(name), HttpStatus.OK);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/getContent")
    public ResponseEntity<?> getContentDashboard(@RequestBody FilterDashboardRequest filterDashboardRequest , HttpServletRequest request) {
        return ResponseEntity.ok().body(dynamicConfigService.getContent(filterDashboardRequest));
    }
}
