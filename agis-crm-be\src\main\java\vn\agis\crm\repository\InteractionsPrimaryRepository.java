package vn.agis.crm.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;

import java.util.List;

@Repository
public interface InteractionsPrimaryRepository extends JpaRepository<InteractionsPrimary, Long> {
    List<InteractionsPrimary> findByCustomerOfferId(Long customerOfferId);
}

