package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.entity.ChatVirtualAssistant;
import vn.agis.crm.service.ChatVirtualAssistantService;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/assistant/chat")
public class ChatVirtualAssistantController extends CrudController<ChatVirtualAssistant, Long>{

    private ChatVirtualAssistantService chatVirtualAssistantService;
    @Autowired
    public ChatVirtualAssistantController(ChatVirtualAssistantService service) {
        super(service);
        this.chatVirtualAssistantService = service;
        this.baseUrl = "/assistant/chat";
    }

    @GetMapping("")
    public List<ChatVirtualAssistant> getAll() {
        return chatVirtualAssistantService.getAll();
    }

    @GetMapping("first")
    public ChatVirtualAssistant getFirst() {
        return chatVirtualAssistantService.getFirst();
    }

    @PostMapping("")
    public ResponseEntity<ChatVirtualAssistant> create(@RequestBody ChatVirtualAssistant chat, HttpServletRequest request) {
        return super.create(chat, request);
    }

    @GetMapping("{id}")
    public ResponseEntity<ChatVirtualAssistant> getOne(@PathVariable Long id, HttpServletRequest request) {
        return super.get(id, request);
    }

    @PostMapping("/get-slice-customer")
    public List<CustomerDto> getSliceCustomer(@RequestBody Set<Long> ids) {
        return chatVirtualAssistantService.getSliceCustomer(ids);
    }

    @DeleteMapping("{id}")
    public ResponseEntity deleteOne(@PathVariable Long id, HttpServletRequest request) {
        return super.delete(id, request);
    }

    @PutMapping("{id}")
    public ResponseEntity<ChatVirtualAssistant> update(@PathVariable Long id,@RequestBody ChatVirtualAssistant chat, HttpServletRequest request) {
        return super.update(id, chat, request);
    }
}
