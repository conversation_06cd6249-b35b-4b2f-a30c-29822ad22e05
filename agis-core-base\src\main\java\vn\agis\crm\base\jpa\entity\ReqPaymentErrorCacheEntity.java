package vn.agis.crm.base.jpa.entity;

import org.springframework.data.redis.core.RedisHash;
import org.springframework.data.redis.core.index.Indexed;

import java.io.Serializable;

@RedisHash("ReqPaymentErrorCache")
public class ReqPaymentErrorCacheEntity implements Serializable {
    public static final Integer STATE_PENDING = 0;
    public static final Integer STATE_RUNNING = 1;
    public static final Integer STATE_UNKNOWN = 2;

    @Indexed
    private String isdn;
    private Integer count;
    private Integer errorCode;
    private Integer state;
    @Indexed
    private String transactionID;
    private Integer amount;
    @Indexed
    protected Long id;
    protected Long created;
    protected Long updated;
    protected String settlementDate;
    private int maxCount;
    private String msgId;
    private long lastTimeExcuted;
    private String outTransactionId;
    private String bankCode;

    public ReqPaymentErrorCacheEntity(Long id, int maxCount, String settlementDate) {
        this.id = id;
        created = System.currentTimeMillis();
        lastTimeExcuted = created;
        if (settlementDate != null && !settlementDate.isEmpty())
            this.settlementDate = settlementDate;
        else this.settlementDate = created + "";
        this.maxCount = maxCount;
    }

    public String getIsdn() {
        return isdn;
    }

    public void setIsdn(String isdn) {
        this.isdn = isdn;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }

    public String getTransactionID() {
        return transactionID;
    }

    public void setTransactionID(String transactionID) {
        this.transactionID = transactionID;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public Long getId() {
        return id;
    }

    public Long getCreated() {
        return created;
    }

    public void setCreated(Long created) {
        this.created = created;
    }

    public Long getUpdated() {
        return updated;
    }

    public void setUpdated(Long updated) {
        this.updated = updated;
    }

    public int getMaxCount() {
        return maxCount;
    }

    public void setMaxCount(int maxCount) {
        this.maxCount = maxCount;
    }

    public String getMsgId() {
        return msgId;
    }

    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }

    public void increMaxCount(int maxCount) {
        if (state == STATE_PENDING.intValue()) {
            this.maxCount += maxCount;
        }
    }

    public void increRetryCount() {
        count++;
        if (count > maxCount) {
            state = STATE_PENDING;
        } else {
            state = STATE_RUNNING;
        }
    }

    public void updateState() {
        if (count > maxCount) {
            state = STATE_PENDING;
        } else {
            state = STATE_RUNNING;
        }
    }

    public void increCountFailWithoutState() {
        count++;
        if (count > maxCount) {
            state = STATE_PENDING;
        }
    }

    public Long getLastTimeExcuted() {
        return lastTimeExcuted;
    }

    public void setLastTimeExcuted(Long lastTimeExcuted) {
        this.lastTimeExcuted = lastTimeExcuted;
    }

    public String getOutTransactionId() {
        return outTransactionId;
    }

    public void setOutTransactionId(String outTransactionId) {
        this.outTransactionId = outTransactionId;
    }

    public String getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(String settlementDate) {
        this.settlementDate = settlementDate;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }
}
