{"info": {"_postman_id": "9dbdb0c8-50c0-4fbb-b9ce-d0fe5337afaa", "name": "AGIS_CRM", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://dark-meteor-833001.postman.co/workspace/AGIS_CRM~3c19fc44-cd30-4362-aeb1-0b9225837a9e/collection/********-9dbdb0c8-50c0-4fbb-b9ce-d0fe5337afaa?action=share&source=collection_link&creator=********"}, "item": [{"name": "AUTH", "item": [{"name": "login", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    // \"email\": \"<EMAIL>\",\r\n    // \"email\": \"<EMAIL>\",\r\n    \"email\": \"<EMAIL>\",\r\n    \"password\": \"AccountTestCMP@123\",\r\n    \"rememberMe\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}, "response": []}, {"name": "GET current", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/current", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "current"]}, "description": "Get current employee (includes roles & authorities)"}, "response": []}]}, {"name": "PROJECT", "item": [{"name": "project update", "request": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 19,\r\n    \"name\": \"prj 20\",\r\n    \"description\": \"prj 20\",\r\n    \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8081/api/project-mgmt/update/19", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "update", "19"]}}, "response": [{"name": "project update", "originalRequest": {"method": "PUT", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"id\": 19,\r\n    \"name\": \"prj 20\",\r\n    \"description\": \"prj 20\",\r\n    \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8081/api/project-mgmt/update/19", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "update", "19"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 27 Aug 2025 17:29:16 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"createdAt\": \"2025-08-27T16:07:58.000+00:00\",\n    \"createdBy\": 1,\n    \"id\": 19,\n    \"name\": \"prj 20\",\n    \"description\": \"prj 20\",\n    \"deletedAt\": null,\n    \"updatedAt\": \"2025-08-27T17:29:08.000+00:00\",\n    \"updatedBy\": 1,\n    \"active\": true\n}"}]}, {"name": "project detail", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:8081/api/project-mgmt/2", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "2"]}}, "response": [{"name": "project detail", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "localhost:8081/api/project-mgmt/18", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "18"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 27 Aug 2025 17:47:38 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"createdAt\": \"2025-08-27T16:05:48.000+00:00\",\n    \"createdBy\": 1,\n    \"id\": 18,\n    \"name\": \"prj 19\",\n    \"description\": \"prj 9\",\n    \"deletedAt\": null,\n    \"updatedAt\": null,\n    \"updatedBy\": null,\n    \"active\": true\n}"}]}, {"name": "checkExistName", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "localhost:8081/api/project-mgmt/checkExistName?name=prj 1", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "checkExistName"], "query": [{"key": "name", "value": "prj 1"}]}}, "response": []}, {"name": "project delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "localhost:8081/api/project-mgmt/delete/2", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "delete", "2"]}}, "response": []}, {"name": "project create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"prj\",\r\n  \"description\": \"prj 9\",\r\n  \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8081/api/project-mgmt/create", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "create"]}}, "response": [{"name": "project create", "originalRequest": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"prj 21\",\r\n  \"description\": \"prj 9\",\r\n  \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8081/api/project-mgmt/create", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Wed, 27 Aug 2025 17:48:10 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"createdAt\": \"2025-08-27T17:48:05.000+00:00\",\n    \"createdBy\": 1,\n    \"id\": 20,\n    \"name\": \"prj 21\",\n    \"description\": \"prj 9\",\n    \"deletedAt\": null,\n    \"updatedAt\": null,\n    \"updatedBy\": null,\n    \"active\": true\n}"}]}, {"name": "project seach", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"name\": \"prj 18\",\r\n  \"description\": \"prj 9\",\r\n  \"isActive\": true\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:8081/api/project-mgmt/search?page=0&size=20&sort=createdAt,asc", "protocol": "http", "host": ["localhost"], "port": "8081", "path": ["api", "project-mgmt", "search"], "query": [{"key": "name", "value": "SKYLINE", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sort", "value": "createdAt,asc"}]}}, "response": []}]}, {"name": "Units", "item": [{"name": "Unit - Search", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/unit-mgmt/search?page=0&size=30&sort=code,asc", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "search"], "query": [{"key": "projectId", "value": "1", "disabled": true}, {"key": "productType", "value": "<PERSON> cấp", "disabled": true}, {"key": "code", "value": "A101", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "30"}, {"key": "sort", "value": "code,asc"}]}}, "response": []}, {"name": "Unit - Get by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/unit-mgmt/1", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "1"]}}, "response": []}, {"name": "Unit - checkExistCode", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/unit-mgmt/checkExistCode?projectId=1&code=A101", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "checkExistCode"], "query": [{"key": "projectId", "value": "1"}, {"key": "code", "value": "A101"}]}}, "response": []}, {"name": "Unit - Create", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"projectId\": 1,\n    \"code\": \"A1033\",\n    \"sector\": \"S1\",\n    \"area\": 75.5,\n    \"doorDirection\": \"Đông Nam\",\n    \"view\": \"Công viên\",\n    \"contractPrice\": 2500000000,\n    \"floorArea\": 100.20,\n    \"floorNumber\": \"77\",\n    \"unitNumber\": \"12\",\n    \"isActive\": true\n}"}, "url": {"raw": "localhost:8081/api/unit-mgmt/create", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "create"]}}, "response": []}, {"name": "Unit - Update", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Accept", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"projectId\": 1,\n  \"code\": \"A101\",\n  \"productType\":\"<PERSON> cấp\",\n  \"sector\": \"S1\",\n  \"area\": 76.0,\n  \"doorDirection\": \"Đông Nam\",\n  \"view\": \"<PERSON><PERSON> bơi\",\n  \"contractPrice\": 2550000000,\n  \"isActive\": true\n}"}, "url": {"raw": "localhost:8081/api/unit-mgmt/update/1", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "update", "1"]}}, "response": []}, {"name": "Unit - Delete", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg", "type": "string"}]}, "method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/unit-mgmt/delete/1", "host": ["localhost"], "port": "8081", "path": ["api", "unit-mgmt", "delete", "1"]}}, "response": []}]}, {"name": "Customers", "item": [{"name": "Customer - Search", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDMiLCJleHAiOjE3NjA4OTEyMTksInVzZXJfaWQiOjMsInNjb3BlIjpbImNyZWF0ZUN1c3RvbWVyIiwidXBkYXRlQ3VzdG9tZXIiLCJzZWFyY2hDdXN0b21lciIsImRldGFpbEN1c3RvbWVyIiwiaW1wb3J0Q3VzdG9tZXIiXX0.zynGGo73iHxXSFQCrxqbTZarDBtIK-ntxhEx72C3kppqRphxKO8bxjRtcjS9OXDQJIVnBF378TqnVy4pqSHPeA", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NTg4OTM3NTIsInVzZXJfaWQiOjEsInNjb3BlIjpbIkVNUExPWUVFIiwiUk9MRV8xIl19.oguIQ_ho0K8RNR6wCokGfyAPQp8CWbFSRJtGrxQxR2vRMPq1fyweSBFVTjReSvaNefsg4UYpeTCRJuPPj2J-Cg"}], "url": {"raw": "localhost:8081/api/customer-mgmt/search?page=0&size=60&sort=created_at,desc", "host": ["localhost"], "port": "8081", "path": ["api", "customer-mgmt", "search"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "60"}, {"key": "sort", "value": "created_at,desc"}, {"key": "fullName", "value": "Phong Customer 7", "disabled": true}, {"key": "phone", "value": "987654326", "disabled": true}, {"key": "email", "value": "nguyenvanan2%40email.com", "disabled": true}, {"key": "sourceType", "value": "Leads", "description": "Data | Leads | Event | Refer (optional)", "disabled": true}, {"key": "projectId", "value": "1", "disabled": true}, {"key": "employeeId", "value": "3", "disabled": true}, {"key": "cccd", "value": "", "disabled": true}, {"key": "address", "value": "456", "disabled": true}, {"key": "sourceDetail", "value": "Facebook%20Ad%20Campaign", "disabled": true}, {"key": "businessField", "value": "Real%20Estate", "disabled": true}, {"key": "interests", "value": "cam", "disabled": true}, {"key": "relativeName", "value": "<PERSON><PERSON><PERSON>", "disabled": true}, {"key": "purchasedProjectId", "value": "2", "disabled": true}, {"key": "activeOfferProjectId", "value": "2", "disabled": true}, {"key": "propertyType", "value": "Apartment", "disabled": true}, {"key": "birthDateFrom", "value": "1990-01-01", "disabled": true}, {"key": "birthDateTo", "value": "2000-12-31", "disabled": true}, {"key": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "value": "15-5", "disabled": true}]}}, "response": [{"name": "Customer - Search", "originalRequest": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/customer-mgmt/search?page=0&size=50&sort=full_name,asc", "host": ["localhost"], "port": "8081", "path": ["api", "customer-mgmt", "search"], "query": [{"key": "fullName", "value": "C<PERSON>", "disabled": true}, {"key": "phone", "value": "0987654321", "disabled": true}, {"key": "email", "value": "cuc", "disabled": true}, {"key": "sourceType", "value": "Leads", "description": "Data | Leads | Event | Refer (optional)", "disabled": true}, {"key": "projectId", "value": "1", "disabled": true}, {"key": "employeeId", "value": "3", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "50"}, {"key": "sort", "value": "full_name,asc"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Thu, 28 Aug 2025 02:59:58 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"content\": [\n        {\n            \"createdAt\": \"2025-08-26T15:43:45.000+00:00\",\n            \"createdBy\": 4,\n            \"id\": 3,\n            \"fullName\": \"<PERSON><PERSON>\",\n            \"phone\": \"0987654321\",\n            \"email\": \"<EMAIL>\",\n            \"birthDate\": \"1978-02-28T17:00:00.000+00:00\",\n            \"addressContact\": \"789 Đường QWE, Quận 7, TP.HCM\",\n            \"addressPermanent\": null,\n            \"nationality\": null,\n            \"maritalStatus\": null,\n            \"totalAsset\": null,\n            \"businessField\": null,\n            \"avatarUrl\": null,\n            \"zaloStatus\": null,\n            \"facebookLink\": null,\n            \"sourceType\": \"Event\",\n            \"sourceDetail\": \"Hội thảo BĐS\",\n            \"notes\": null,\n            \"deletedAt\": null,\n            \"updatedAt\": null,\n            \"updatedBy\": null\n        },\n        {\n            \"createdAt\": \"2025-08-26T15:43:45.000+00:00\",\n            \"createdBy\": 4,\n            \"id\": 1,\n            \"fullName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n            \"phone\": \"0906244804\",\n            \"email\": \"<EMAIL>\",\n            \"birthDate\": \"1985-05-14T17:00:00.000+00:00\",\n            \"addressContact\": \"123 Đường ABC, Quận 1, TP.HCM\",\n            \"addressPermanent\": null,\n            \"nationality\": null,\n            \"maritalStatus\": null,\n            \"totalAsset\": null,\n            \"businessField\": null,\n            \"avatarUrl\": null,\n            \"zaloStatus\": null,\n            \"facebookLink\": null,\n            \"sourceType\": \"Leads\",\n            \"sourceDetail\": \"Facebook Ads\",\n            \"notes\": null,\n            \"deletedAt\": null,\n            \"updatedAt\": null,\n            \"updatedBy\": null\n        },\n        {\n            \"createdAt\": \"2025-08-28T02:09:02.000+00:00\",\n            \"createdBy\": 1,\n            \"id\": 4,\n            \"fullName\": \"Nguyen Van A\",\n            \"phone\": \"0901234567\",\n            \"email\": \"<EMAIL>\",\n            \"birthDate\": \"1989-12-31T17:00:00.000+00:00\",\n            \"addressContact\": \"123 ABC\",\n            \"addressPermanent\": \"XYZ\",\n            \"nationality\": \"VN\",\n            \"maritalStatus\": \"Single\",\n            \"totalAsset\": 1000000000,\n            \"businessField\": \"IT\",\n            \"avatarUrl\": \"https://example.com/avatar.jpg\",\n            \"zaloStatus\": \"Active\",\n            \"facebookLink\": \"https://facebook.com/user\",\n            \"sourceType\": \"Data\",\n            \"sourceDetail\": \"Campaign 2024\",\n            \"notes\": \"Khach VIP\",\n            \"deletedAt\": null,\n            \"updatedAt\": null,\n            \"updatedBy\": null\n        },\n        {\n            \"createdAt\": \"2025-08-26T15:43:45.000+00:00\",\n            \"createdBy\": 4,\n            \"id\": 2,\n            \"fullName\": \"Trần Văn Bình\",\n            \"phone\": \"0912345678\",\n            \"email\": \"<EMAIL>\",\n            \"birthDate\": \"1990-11-19T17:00:00.000+00:00\",\n            \"addressContact\": \"456 Đường XYZ, Quận 3, TP.HCM\",\n            \"addressPermanent\": null,\n            \"nationality\": null,\n            \"maritalStatus\": null,\n            \"totalAsset\": null,\n            \"businessField\": null,\n            \"avatarUrl\": null,\n            \"zaloStatus\": null,\n            \"facebookLink\": null,\n            \"sourceType\": \"Data\",\n            \"sourceDetail\": \"Đối tác BĐS\",\n            \"notes\": null,\n            \"deletedAt\": null,\n            \"updatedAt\": null,\n            \"updatedBy\": null\n        }\n    ],\n    \"pageable\": {\n        \"pageNumber\": 0,\n        \"pageSize\": 50,\n        \"sort\": {\n            \"empty\": false,\n            \"sorted\": true,\n            \"unsorted\": false\n        },\n        \"offset\": 0,\n        \"unpaged\": false,\n        \"paged\": true\n    },\n    \"last\": true,\n    \"totalElements\": 4,\n    \"totalPages\": 1,\n    \"size\": 50,\n    \"number\": 0,\n    \"sort\": {\n        \"empty\": false,\n        \"sorted\": true,\n        \"unsorted\": false\n    },\n    \"first\": true,\n    \"numberOfElements\": 4,\n    \"empty\": false\n}"}]}, {"name": "Customer - Get by ID", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDMiLCJleHAiOjE3NjA4OTEyMTksInVzZXJfaWQiOjMsInNjb3BlIjpbImNyZWF0ZUN1c3RvbWVyIiwidXBkYXRlQ3VzdG9tZXIiLCJzZWFyY2hDdXN0b21lciIsImRldGFpbEN1c3RvbWVyIiwiaW1wb3J0Q3VzdG9tZXIiXX0.zynGGo73iHxXSFQCrxqbTZarDBtIK-ntxhEx72C3kppqRphxKO8bxjRtcjS9OXDQJIVnBF378TqnVy4pqSHPeA", "type": "string"}]}, "method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/customer-mgmt/48", "host": ["localhost"], "port": "8081", "path": ["api", "customer-mgmt", "48"]}}, "response": []}, {"name": "Customer - Delete", "request": {"method": "DELETE", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "localhost:8081/api/customer-mgmt/delete/{{id}}", "host": ["localhost"], "port": "8081", "path": ["api", "customer-mgmt", "delete", "{{id}}"]}}, "response": []}, {"name": "Customer - Create (assignments + relatives + properties + offers)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200/201', function () { pm.expect([200,201]).to.include(pm.response.code); });", "pm.test('Response has id and relatives', function () { var b = pm.response.json(); pm.expect(b).to.have.property('id'); pm.expect(b).to.have.property('relatives'); });"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"additionalPhones\": [],\n    \"additionalEmails\": [],\n    \"additionalCccds\": [],\n    \"fullName\": \"Phong Customer 16\",\n    \"phone\": \"9876534323\",\n    \"email\": null,\n    \"cccd\": null,\n    \"birthDate\": null,\n    \"addressContact\": null,\n    \"addressPermanent\": null,\n    \"nationality\": null,\n    \"maritalStatus\": null,\n    \"totalAsset\": null,\n    \"businessField\": null,\n    \"avatarUrl\": null,\n    \"zaloStatus\": null,\n    \"facebookLink\": null,\n    \"sourceType\": null,\n    \"interests\":\"222222222\",\n    \"sourceDetail\": null,\n    \"notes\": null,\n    \"customerRelatives\": [],\n    \"customerProperties\": [],\n    \"customerOffers\": [],\n    \"customerAssignments\": []\n}"}, "url": {"raw": "{{baseUrl}}/customer-mgmt/create", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "create"]}}, "response": [{"name": "Customer - Create v2 (assignments + relatives + properties + offers)", "originalRequest": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"id\": null,\n  \"fullName\": \"<PERSON>uy<PERSON><PERSON>\",\n  \"phone\": \"0987654328\",\n  \"email\": \"nguy<PERSON><EMAIL>\",\n  \"birthDate\": \"15/05/1985\",\n  \"addressContact\": \"123 Đường ABC, Quận 1, TP.HCM\",\n  \"addressPermanent\": \"456 Đường XYZ, Quận 2, TP.HCM\",\n  \"nationality\": \"Việt Nam\",\n  \"maritalStatus\": \"Đã kết hôn\",\n  \"totalAsset\": 5000000000,\n  \"businessField\": \"<PERSON><PERSON> doanh bất động sản\",\n  \"avatarUrl\": \"link_anh_dai_dien\",\n  \"zaloStatus\": \"Đang hoạt động\",\n  \"facebookLink\": \"https://facebook.com/nguyenvanan\",\n  \"sourceType\": \"Data\",\n  \"sourceDetail\": \"Website\",\n  \"notes\": \"<PERSON>h<PERSON><PERSON> hàng tiềm năng, quan tâm B<PERSON> cao cấp\",\n  \"customerRelatives\": [\n    {\n      \"id\": null,\n      \"relationType\": \"Vợ\",\n      \"fullName\": \"<PERSON>rầ<PERSON>\",\n      \"birthYear\": 1987,\n      \"phone\": \"0912345678\",\n      \"notes\": \"Vợ, làm việc tại ngân hàng\"\n    },\n    {\n      \"id\": null,\n      \"relationType\": \"Con\",\n      \"fullName\": \"Nguyễn Văn Cường\",\n      \"birthYear\": 2010,\n      \"phone\": null,\n      \"notes\": \"Con trai đầu\"\n    }\n  ],\n  \"customerAssignments\": [\n    {\n      \"id\": null,\n      \"employeeId\": 1,\n      \"roleType\": 1,\n      \"assignedFrom\": \"01/01/2024\",\n      \"assignedTo\": \"31/12/2024\"\n    },\n    {\n      \"id\": null,\n      \"employeeId\": 2,\n      \"roleType\": 2,\n      \"assignedFrom\": \"15/06/2024\",\n      \"assignedTo\": \"15/12/2024\"\n    }\n  ],\n  \"customerProperties\": [\n    {\n      \"id\": null,\n      \"projectId\": 1,\n      \"unitId\": 1,\n      \"transactionDate\": \"15/03/2024\",\n      \"contractPrice\": 3500000000,\n      \"externalAgencyName\": \"ABC Realty\",\n      \"externalSaleName\": \"Lê Thị Dung\",\n      \"externalSalePhone\": \"0909123456\",\n      \"externalSaleEmail\": \"<EMAIL>\",\n      \"employeeId\": 1,\n      \"notes\": \"Giao dịch thành công\",\n      \"firstInteraction\": \"15/01/2024\",\n      \"lastInteraction\": \"15/03/2024\"\n    }\n  ],\n  \"customerOffers\": [\n    {\n      \"id\": null,\n      \"projectId\": 2,\n      \"notes\": \"Quan tâm đến căn hộ 3PN view biển\",\n      \"firstInteraction\": \"01/04/2024\",\n      \"lastInteraction\": \"15/08/2024\",\n      \"status\": \"OPEN\"\n    },\n    {\n      \"id\": null,\n      \"projectId\": 11,\n      \"notes\": \"Tìm hiểu về biệt thự\",\n      \"firstInteraction\": \"10/07/2024\",\n      \"lastInteraction\": \"20/08/2024\",\n      \"status\": \"CANCELLED\"\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/customer-mgmt/create", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "create"]}}, "status": "OK", "code": 200, "_postman_previewlanguage": null, "header": [{"key": "Vary", "value": "Origin"}, {"key": "Vary", "value": "Access-Control-Request-Method"}, {"key": "Vary", "value": "Access-Control-Request-Headers"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-XSS-Protection", "value": "0"}, {"key": "Cache-Control", "value": "no-cache, no-store, max-age=0, must-revalidate"}, {"key": "Pragma", "value": "no-cache"}, {"key": "Expires", "value": "0"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Transfer-Encoding", "value": "chunked"}, {"key": "Date", "value": "Sun, 07 Sep 2025 16:06:00 GMT"}, {"key": "Keep-Alive", "value": "timeout=60"}, {"key": "Connection", "value": "keep-alive"}], "cookie": [], "body": "{\n    \"id\": 17,\n    \"fullName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"phone\": \"0987654328\",\n    \"email\": \"nguy<PERSON><PERSON><EMAIL>\",\n    \"birthDate\": \"1985-05-14T17:00:00.000+00:00\",\n    \"addressContact\": \"123 Đường ABC, Quận 1, TP.HCM\",\n    \"addressPermanent\": \"456 Đường XYZ, Quận 2, TP.HCM\",\n    \"nationality\": \"Việt Nam\",\n    \"maritalStatus\": \"<PERSON><PERSON> kết hôn\",\n    \"totalAsset\": 5000000000,\n    \"businessField\": \"<PERSON>nh doanh bất động sản\",\n    \"avatarUrl\": \"link_anh_dai_dien\",\n    \"zaloStatus\": \"Đang hoạt động\",\n    \"facebookLink\": \"https://facebook.com/nguyenvanan\",\n    \"sourceType\": \"Data\",\n    \"sourceDetail\": \"Website\",\n    \"notes\": \"<PERSON>h<PERSON>ch hàng tiềm năng, quan tâm B<PERSON> cao cấp\",\n    \"currentManager\": {\n        \"id\": 1,\n        \"employeeCode\": \"ADM001\",\n        \"fullName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n        \"phone\": \"0901234567\",\n        \"email\": \"<EMAIL>\"\n    },\n    \"currentStaff\": {\n        \"id\": 2,\n        \"employeeCode\": \"ADM002\",\n        \"fullName\": \"Phongg\",\n        \"phone\": \"6789876543\",\n        \"email\": \"<EMAIL>\"\n    },\n    \"relatives\": [\n        {\n            \"id\": 15,\n            \"relationType\": \"Vợ\",\n            \"fullName\": \"Trần Thị Bình\",\n            \"yearOfBirth\": 1987,\n            \"phone\": \"0912345678\",\n            \"notes\": \"Vợ, làm việc tại ngân hàng\"\n        },\n        {\n            \"id\": 16,\n            \"relationType\": \"Con\",\n            \"fullName\": \"Nguyễn Văn Cường\",\n            \"yearOfBirth\": 2010,\n            \"phone\": null,\n            \"notes\": \"Con trai đầu\"\n        }\n    ]\n}"}]}, {"name": "Customer - Update (assignments + relatives + properties + offers)", "event": [{"listen": "test", "script": {"exec": ["pm.test('Status is 200', function () { pm.expect(pm.response.code).to.eql(200); });", "pm.test('Response has id and relatives', function () { var b = pm.response.json(); pm.expect(b).to.have.property('id'); pm.expect(b).to.have.property('relatives'); });"], "type": "text/javascript", "packages": {}}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 17,\n    \"fullName\": \"<PERSON><PERSON><PERSON><PERSON>\",\n    \"phone\": \"0987654328\",\n    \"additionalPhones\": [\n                \"987654326\",\n                \"987654327\"\n            ],\n    \"email\": \"<EMAIL>\",\n    \"additionalEmails\": [\"<EMAIL>\",\"<EMAIL>\"],\n    \"cccd\": \"***********\",\n    \"additionalCccds\": [\"***********\",\"***********\"],\n    \"birthDate\": \"15/05/1985\",\n    \"addressContact\": \"123 Đường ABC, Quận 1, TP.HCM\",\n    \"addressPermanent\": \"456 Đường XYZ, Quận 2, TP.HCM\",\n    \"nationality\": \"Việt Nam\",\n    \"maritalStatus\": \"Đã kết hôn\",\n    \"totalAsset\": 5000000000.00,\n    \"businessField\": \"Kinh doanh bất động sản\",\n    \"avatarUrl\": \"link_anh_dai_dien\",\n    \"zaloStatus\": \"<PERSON><PERSON> ho<PERSON>t động\",\n    \"facebookLink\": \"https://facebook.com/nguyenvanan\",\n    \"sourceType\": \"Data\",\n    \"sourceDetail\": \"Website\",\n    \"notes\": \"Khách hàng tiềm năng, quan tâm BDS cao cấp\",\n    \"customerRelatives\": [\n        {\n            \"id\": 15,\n            \"relationType\": \"Vợ\",\n            \"fullName\": \"Trần Thị Bình\",\n            \"yearOfBirth\": 1987,\n            \"phone\": \"0912345678\",\n            \"notes\": \"Vợ, làm việc tại ngân hàng\"\n        },\n        {\n            \"id\": 16,\n            \"relationType\": \"Con\",\n            \"fullName\": \"Nguyễn Văn Cường\",\n            \"yearOfBirth\": 2010,\n            \"phone\": null,\n            \"notes\": \"Con trai đầu\"\n        }\n    ],\n    \"customerAssignments\": [\n        {\n            \"employeeId\": 1,\n            \"roleType\": 1\n        },\n        {\n            \"employeeId\": 2,\n            \"roleType\": 2\n        }\n    ],\n    \"customerProperties\": [\n        {\n            \"id\": 4,\n            \"projectId\": 1,\n            \"unitId\": 1,\n            \"transactionDate\": \"15/03/2024\",\n            \"contractPrice\": 3500000000.00,\n            \"externalAgencyName\": \"ABC Realty\",\n            \"externalSaleName\": \"Lê Thị Dung\",\n            \"externalSalePhone\": \"0909123456\",\n            \"externalSaleEmail\": \"<EMAIL>\",\n            \"employeeId\": 1,\n            \"notes\": \"Giao dịch thành công\",\n            \"firstInteraction\": \"15/01/2024\",\n            \"lastInteraction\": \"15/03/2024\",\n            \"interactionsSecondary\": [\n                {\n                    \"id\": 2,\n                    \"expectedSellPrice\": 3600000000.00,\n                    \"expectedRentPrice\": 25000000.00,\n                    \"result\": \"Thành công\",\n                    \"happenedAt\": \"01/02/2024\",\n                    \"notes\": \"Khách hàng đồng ý giá\",\n                    \"deleted\": null\n                },\n                {\n                    \"id\": 3,\n                    \"expectedSellPrice\": 3500000000.00,\n                    \"expectedRentPrice\": null,\n                    \"result\": \"Hoàn thành\",\n                    \"happenedAt\": \"15/03/2024\",\n                    \"notes\": \"Ký hợp đồng thành công\",\n                    \"deleted\": null\n                }\n            ]\n        }\n    ],\n    \"customerOffers\": [\n        {\n            \"id\": 4,\n            \"projectId\": 2,\n            \"firstInteraction\": \"01/04/2024\",\n            \"lastInteraction\": \"15/08/2024\",\n            \"status\": \"OPEN\",\n            \"notes\": \"Quan tâm đến căn hộ 3PN view biển\",\n            \"interactionsPrimary\": [\n                {\n                    \"id\": 2,\n                    \"result\": \"Thành công\",\n                    \"happenedAt\": \"01/04/2024\",\n                    \"notes\": \"Khách hàng rất quan tâm\",\n                    \"deleted\": null\n                },\n                {\n                    \"id\": 3,\n                    \"result\": \"Đang chờ\",\n                    \"happenedAt\": \"15/08/2024\",\n                    \"notes\": \"Đang chờ quyết định cuối\",\n                    \"deleted\": null\n                }\n            ]\n        },\n        {\n            \"id\": 5,\n            \"projectId\": 11,\n            \"firstInteraction\": \"10/07/2024\",\n            \"lastInteraction\": \"20/08/2024\",\n            \"status\": \"CANCELLED\",\n            \"notes\": \"Tìm hiểu về biệt thự\",\n            \"interactionsPrimary\": [\n                {\n                    \"id\": 4,\n                    \"result\": \"Thành công\",\n                    \"happenedAt\": \"10/07/2024\",\n                    \"notes\": \"Lần đầu tham quan\",\n                    \"deleted\": null\n                }\n            ]\n        }\n    ]\n}"}, "url": {"raw": "{{baseUrl}}/customer-mgmt/update/17", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "update", "17"]}}, "response": []}, {"name": "check-exists", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/customer-mgmt/check-exists?key=cccd&value=187857570", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "check-exists"], "query": [{"key": "key", "value": "cccd", "description": "phone / email / cccd"}, {"key": "value", "value": "187857570"}]}}, "response": []}, {"name": "update assignment", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"id\": 54,\n    \"customerId\": 19,\n    \"roleType\": 2,\n    \"newEmployeeId\": 2\n  }", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/customer-mgmt/assignment/update-employee", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "assignment", "update-employee"]}}, "response": []}, {"name": "assignment-history", "request": {"method": "GET", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/customer-mgmt/17/assignment-history?page=0&size=50&sortBy=assigned_from&sortDirection=DESC", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "17", "assignment-history"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "50"}, {"key": "sortBy", "value": "assigned_from"}, {"key": "sortDirection", "value": "DESC"}]}}, "response": []}, {"name": "delete assignment", "request": {"method": "DELETE", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/customer-mgmt/assignments/15", "host": ["{{baseUrl}}"], "path": ["customer-mgmt", "assignments", "15"]}}, "response": []}]}, {"name": "Permission Management", "item": [{"name": "Get All Permissions", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/permissions?description=tạo", "host": ["{{baseUrl}}"], "path": ["permissions"], "query": [{"key": "description", "value": "tạo"}]}}, "response": []}, {"name": "Create Permission", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"manage-invoices\",\n  \"description\": \"Can create, view, and delete invoices.\"\n}"}, "url": {"raw": "{{baseUrl}}/permissions", "host": ["{{baseUrl}}"], "path": ["permissions"]}}, "response": []}, {"name": "Update Permission", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Can create, view, update, and delete invoices.2\"\n}"}, "url": {"raw": "{{baseUrl}}/permissions/1", "host": ["{{baseUrl}}"], "path": ["permissions", "1"]}}, "response": []}, {"name": "Delete Permission", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/permissions/1", "host": ["{{baseUrl}}"], "path": ["permissions", "1"]}}, "response": []}, {"name": "Check if Permission Name Exists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/permissions/checkExistName?name=manage-invoices", "host": ["{{baseUrl}}"], "path": ["permissions", "checkExistName"], "query": [{"key": "name", "value": "manage-invoices"}]}}, "response": []}]}, {"name": "Role Management (Simple)", "item": [{"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sales Manager\",\n  \"description\": \"Can view and manage sales leads\",\n  \"isActive\": true,\n  \"permissionIds\": [1, 2, 5]\n}"}, "url": {"raw": "{{baseUrl}}/simple-roles", "host": ["{{baseUrl}}"], "path": ["simple-roles"]}}, "response": []}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"description\": \"Can view and manage sales leads and reports\",\n  \"isActive\": true,\n  \"permissionIds\": [1, 2, 5, 8]\n}"}, "url": {"raw": "{{baseUrl}}/simple-roles/1", "host": ["{{baseUrl}}"], "path": ["simple-roles", "1"]}}, "response": []}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/simple-roles/1", "host": ["{{baseUrl}}"], "path": ["simple-roles", "1"]}}, "response": []}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/simple-roles/1", "host": ["{{baseUrl}}"], "path": ["simple-roles", "1"]}}, "response": []}, {"name": "Search Roles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/simple-roles?page=0&size=10", "host": ["{{baseUrl}}"], "path": ["simple-roles"], "query": [{"key": "name", "value": "Manager", "disabled": true}, {"key": "isActive", "value": "true", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}]}}, "response": []}, {"name": "Check if Role Name Exists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}"}], "url": {"raw": "{{baseUrl}}/simple-roles/checkExistName?name=Sales Manager", "host": ["{{baseUrl}}"], "path": ["simple-roles", "checkExistName"], "query": [{"key": "name", "value": "Sales Manager"}]}}, "response": []}]}, {"name": "employ", "item": [{"name": "GET /employee-mgmt/search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/search?employeeCode=&fullName=&email=&page=0&size=20&sort=fullName,asc", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "search"], "query": [{"key": "employeeCode", "value": ""}, {"key": "fullName", "value": ""}, {"key": "email", "value": ""}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sort", "value": "fullName,asc"}]}, "description": "Search employees by code/name/phone/email with pagination"}, "response": []}, {"name": "GET /employee-mgmt/{id}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/1", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "1"]}, "description": "Get employee by ID"}, "response": []}, {"name": "POST /employee-mgmt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"employeeCode\": \"E003\",\n  \"fullName\": \"<PERSON><PERSON><PERSON>\",\n  \"phone\": \"0900000002\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Passw0rd!\",\n  \"roleId\": 2\n}"}, "url": {"raw": "{{baseUrl}}/employee-mgmt", "host": ["{{baseUrl}}"], "path": ["employee-mgmt"]}, "description": "Create a new employee (CreateEmployeeReq)"}, "response": []}, {"name": "PUT /employee-mgmt/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"employeeCode\": \"E001\",\n  \"fullName\": \"<PERSON><PERSON><PERSON> (Updated)\",\n  \"phone\": \"0900000001\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Passw0rd!\",\n  \"roleId\": 3,\n  \"status\": \"active\"\n}"}, "url": {"raw": "{{baseUrl}}/employee-mgmt/2", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "2"]}, "description": "Update an employee by ID (UpdateEmployeeReq)"}, "response": []}, {"name": "PUT /employee-mgmt/change-status/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/change-status/2", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "change-status", "2"]}, "description": "Toggle employee status active/inactive"}, "response": []}, {"name": "DELETE /employee-mgmt/{id}", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/3", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "3"]}, "description": "Delete an employee by ID"}, "response": []}, {"name": "GET /employee-mgmt/all", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/all", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "all"]}, "description": "Get all employees (no pagination)"}, "response": []}, {"name": "GET /employee-mgmt/check-exists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/check-exists?value=ADM001&key=employeeCode", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "check-exists"], "query": [{"key": "value", "value": "ADM001"}, {"key": "key", "value": "employeeCode", "description": "employeeCode/ email/ phone"}]}, "description": "Check if any of employee code/email/phone exists"}, "response": []}, {"name": "GET current", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/employee-mgmt/current", "host": ["{{baseUrl}}"], "path": ["employee-mgmt", "current"]}, "description": "Get current employee (includes roles & authorities)"}, "response": []}]}, {"name": "Config", "item": [{"name": "GET /configs/search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/configs/search?page=0&size=20&description=<PERSON><PERSON> s<PERSON>ch lo<PERSON>i sản phẩm", "host": ["{{baseUrl}}"], "path": ["configs", "search"], "query": [{"key": "config<PERSON><PERSON>", "value": "{{config<PERSON><PERSON>}}", "disabled": true}, {"key": "configType", "value": "{{configType}}", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sort", "value": "{{sort}}", "disabled": true}, {"key": "description", "value": "<PERSON><PERSON> s<PERSON>ch lo<PERSON>i sản ph<PERSON>m"}]}, "description": "Search configs by key/type with pagination"}, "response": []}, {"name": "GET /configs/by-key/{configKey}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/configs/by-key/UNIT_TYPE", "host": ["{{baseUrl}}"], "path": ["configs", "by-key", "UNIT_TYPE"]}, "description": "Get config by config<PERSON><PERSON>"}, "response": []}, {"name": "POST /configs", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configKey\": \"APP_THEME\",\n  \"configType\": 1,\n  \"configValue\": \"dark\",\n  \"description\": \"Giao diện mặc định\"\n}"}, "url": {"raw": "{{baseUrl}}/configs", "host": ["{{baseUrl}}"], "path": ["configs"]}, "description": "Create new config"}, "response": []}, {"name": "PUT /configs/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"configKey\": \"APP_THEME\",\n  \"configType\": 1,\n  \"configValue\": \"light\",\n  \"description\": \"<PERSON><PERSON><PERSON> di<PERSON>n mặc định (sửa)\"\n}"}, "url": {"raw": "{{baseUrl}}/configs/15", "host": ["{{baseUrl}}"], "path": ["configs", "15"]}, "description": "Update config by id"}, "response": []}, {"name": "GET /configs/{id}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/configs/1", "host": ["{{baseUrl}}"], "path": ["configs", "1"]}, "description": "Get config by id"}, "response": []}, {"name": "DELETE /configs/{id}", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/configs/12", "host": ["{{baseUrl}}"], "path": ["configs", "12"]}, "description": "Delete config by id"}, "response": []}]}, {"name": "Assignment", "item": [{"name": "Manual Assignment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n    \"customerIds\": [\n        29\n    ],\n    \"managerId\": 3,\n    \"staffId\": 3,\n    \"isDryRun\": false\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/assignments/manual", "host": ["{{baseUrl}}"], "path": ["assignments", "manual"]}}, "response": []}]}, {"name": "Rule Management", "item": [{"name": "Get All Rules", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/assignment-rules", "host": ["{{baseUrl}}"], "path": ["assignment-rules"]}}, "response": []}, {"name": "Create Rule", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"<PERSON><PERSON> lead nguồn Facebook cho dự án c\",\n    \"isActive\": true,\n    \"priority\": 1,\n    \"conditions\": {\n        \"projects\": [\n            { \"id\": 1, \"name\": \"Dự án A\" },\n            { \"id\": 2, \"name\": \"Dự án B\" }\n        ],\n        \"sourceType\": [\"Facebook\", \"Website\"],\n        \"createdInLastDays\": 30\n    },\n    \"managerId\": 1,\n    \"staffId\": 2,\n    \"conflictPolicy\": \"OVERWRITE\"\n}"}, "url": {"raw": "{{baseUrl}}/assignment-rules", "host": ["{{baseUrl}}"], "path": ["assignment-rules"]}}, "response": []}, {"name": "Get Rule By ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/assignment-rules/2", "host": ["{{baseUrl}}"], "path": ["assignment-rules", "2"]}}, "response": []}, {"name": "Update Rule", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"name\": \"(UPDATED) <PERSON><PERSON> lead nguồn Facebook cho dự án C\",\n    \"isActive\": false,\n    \"priority\": 1,\n    \"conditions\": {\n        \"projectId\": [1],\n        \"sourceType\": [\"Facebook\"]\n    },\n    \"managerId\": 1,\n    \"staffId\": 2,\n    \"conflictPolicy\": \"OVERWRITE\"\n}"}, "url": {"raw": "{{baseUrl}}/assignment-rules/5", "host": ["{{baseUrl}}"], "path": ["assignment-rules", "5"]}}, "response": []}, {"name": "Delete Rule", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/assignment-rules/6", "host": ["{{baseUrl}}"], "path": ["assignment-rules", "6"]}}, "response": []}, {"name": "Update Priority Order", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n    \"orderedRuleIds\": [3, 4, 5]\n}"}, "url": {"raw": "{{baseUrl}}/assignment-rules/update-priority", "host": ["{{baseUrl}}"], "path": ["assignment-rules", "update-priority"]}}, "response": []}]}, {"name": "Job History & Reporting", "item": [{"name": "Get Job History", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/assignment-jobs/history?page=0&size=10&sort=jobStartedAt,desc", "host": ["{{baseUrl}}"], "path": ["assignment-jobs", "history"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "sort", "value": "jobStartedAt,desc"}]}}, "response": []}, {"name": "Get Job Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/assignment-jobs/1/details?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["assignment-jobs", "1", "details"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}, "response": []}]}, {"name": "file", "item": [{"name": "upload File", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () { pm.response.to.have.status(200); });", "pm.test(\"Response has file details\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.fileName).to.not.be.empty;", "    pm.expect(jsonData.fileDownloadUri).to.not.be.empty;", "    pm.collectionVariables.set(\"fileName\", jsonData.fileName);", "});"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [], "body": {"mode": "formdata", "formdata": [{"key": "files", "type": "file", "src": "/C:/Users/<USER>/Pictures/Screenshot 2025-08-26 141525.png"}]}, "url": {"raw": "{{baseUrl}}/api/files/upload", "host": ["{{baseUrl}}"], "path": ["api", "files", "upload"]}, "description": "Uploads a file and returns its details, including the download URI."}, "response": []}, {"name": "get File", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/files/{{fileName}}", "host": ["{{baseUrl}}"], "path": ["api", "files", "{{fileName}}"]}, "description": "Downloads the file specified by the 'fileName' collection variable. The variable is set automatically from the Upload File request."}, "response": []}]}, {"name": "Import Jobs", "item": [{"name": "Create Import Job - File Upload (WEB_UPLOAD)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "description": "CSV, XLSX, or XLS file to upload", "type": "file", "src": "/C:/Users/<USER>/Desktop/CRM/template/DATA-FORMAT-V2.csv"}, {"key": "source", "value": "WEB_UPLOAD", "description": "Source type: WEB_UPLOAD for direct file upload", "type": "text"}, {"key": "mode", "value": "DRY_RUN", "description": "Import mode (DRY_RUN for validation only)", "type": "text"}, {"key": "options", "value": "{\"delimiter\":\",\",\"encoding\":\"utf-8\"}", "description": "Optional JSON configuration", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/imports", "host": ["{{baseUrl}}"], "path": ["imports"]}, "description": "Upload a file directly and create an import job for customer data"}, "response": []}, {"name": "Create Import Job - Google Drive File (GOOGLE_DRIVE)", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "body": {"mode": "formdata", "formdata": [{"key": "file", "description": "File downloaded from Google Drive (CSV, XLSX, or XLS)", "type": "file", "src": "/C:/Users/<USER>/Downloads/DATA-FORMAT-V2.xlsx"}, {"key": "source", "value": "GOOGLE_DRIVE", "description": "Source type: GOOGLE_DRIVE for files downloaded from Google Drive", "type": "text"}, {"key": "mode", "value": "DRY_RUN", "description": "Import mode (DRY_RUN for validation only)", "type": "text"}, {"key": "options", "value": "{\"delimiter\":\",\",\"encoding\":\"utf-8\"}", "description": "Optional JSON configuration", "type": "text"}]}, "url": {"raw": "{{baseUrl}}/imports", "host": ["{{baseUrl}}"], "path": ["imports"]}, "description": "Upload a file that was downloaded from Google Drive and create an import job"}, "response": []}, {"name": "Get Import Job Details", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/imports/6", "host": ["{{baseUrl}}"], "path": ["imports", "6"]}, "description": "Retrieve import job information by ID"}, "response": []}, {"name": "Get Excel Sheets", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/imports/2/sheets", "host": ["{{baseUrl}}"], "path": ["imports", "2", "sheets"]}, "description": "List available sheets in Excel file for import job"}, "response": []}, {"name": "Get File Metadata", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/imports/2/file-metadata", "host": ["{{baseUrl}}"], "path": ["imports", "2", "file-metadata"]}, "description": "Retrieve file metadata for import job"}, "response": []}]}, {"name": "Utility Endpoints Copy", "item": [{"name": "Get Supported Formats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/supported-formats", "host": ["{{baseUrl}}"], "path": ["api", "imports", "supported-formats"]}, "description": "Get information about supported file formats and limits"}, "response": []}, {"name": "Download Import Template", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/template", "host": ["{{baseUrl}}"], "path": ["api", "imports", "template"]}, "description": "Download the CSV template file for customer import"}, "response": []}, {"name": "Start Dry-run Validation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/imports/{{job_id}}/dry-run", "host": ["{{baseUrl}}"], "path": ["imports", "{{job_id}}", "dry-run"]}, "description": "Start dry-run validation for an uploaded import job"}, "response": []}, {"name": "Get Dry-run Results", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/imports/{{job_id}}/dry-run-result", "host": ["{{baseUrl}}"], "path": ["imports", "{{job_id}}", "dry-run-result"]}, "description": "Get detailed results of dry-run validation including errors and statistics"}, "response": []}, {"name": "Get Import Errors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/errors?page=0&size=50&severity=ERROR&errorType=MISSING_REQUIRED_FIELD", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "errors"], "query": [{"key": "page", "value": "0", "description": "Page number (0-based)"}, {"key": "size", "value": "50", "description": "Number of items per page"}, {"key": "severity", "value": "ERROR", "description": "Filter by error severity (ERROR, WARNING, INFO)"}, {"key": "errorType", "value": "MISSING_REQUIRED_FIELD", "description": "Filter by specific error type"}]}, "description": "Get paginated list of validation errors for an import job"}, "response": []}]}, {"name": "Import Workflow Copy", "item": [{"name": "Start Dry-run Validation", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/dry-run", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "dry-run"]}, "description": "Start dry-run validation for an uploaded import job"}, "response": []}, {"name": "Get Dry-run Results", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/dry-run-result", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "dry-run-result"]}, "description": "Get detailed results of dry-run validation including errors and statistics"}, "response": []}, {"name": "Get Import Errors", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/errors?page=0&size=50&severity=ERROR&errorType=MISSING_REQUIRED_FIELD", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "errors"], "query": [{"key": "page", "value": "0", "description": "Page number (0-based)"}, {"key": "size", "value": "50", "description": "Number of items per page"}, {"key": "severity", "value": "ERROR", "description": "Filter by error severity (ERROR, WARNING, INFO)"}, {"key": "errorType", "value": "MISSING_REQUIRED_FIELD", "description": "Filter by specific error type"}]}, "description": "Get paginated list of validation errors for an import job"}, "response": []}, {"name": "Confirm Import", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/confirm?options={}", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "confirm"], "query": [{"key": "options", "value": "{}", "description": "Import execution options (JSON string)"}]}, "description": "Confirm dry-run results and start actual import execution"}, "response": []}, {"name": "Get Import Progress", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/progress", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "progress"]}, "description": "Get real-time progress information for a running import job"}, "response": []}, {"name": "Cancel Import", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/cancel?reason=User requested cancellation", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "cancel"], "query": [{"key": "reason", "value": "User requested cancellation", "description": "Reason for cancellation"}]}, "description": "Request cancellation of a running import job"}, "response": []}, {"name": "Get Import Result", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{jwt_token}}"}], "url": {"raw": "{{baseUrl}}/api/imports/{{job_id}}/result", "host": ["{{baseUrl}}"], "path": ["api", "imports", "{{job_id}}", "result"]}, "description": "Get final results and statistics of a completed import job"}, "response": []}]}, {"name": "notifications", "item": [{"name": "search", "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "Bearer YOUR_JWT_TOKEN_HERE"}], "url": {"raw": "{{baseUrl}}/notification-mgmt/search?page=0&size=20&sort=createdAt,desc", "host": ["{{baseUrl}}"], "path": ["notification-mgmt", "search"], "query": [{"key": "targetEmployeeId", "value": "123", "disabled": true}, {"key": "targetCustomerId", "value": "456", "disabled": true}, {"key": "type", "value": "2", "disabled": true}, {"key": "title", "value": "sinh%20nh%E1%BA%ADt", "disabled": true}, {"key": "content", "value": "ch%C3%BAc%20m%E1%BB%ABng", "disabled": true}, {"key": "isRead", "value": "true", "disabled": true}, {"key": "readAt", "value": "2024-01-01,2024-12-31", "disabled": true}, {"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "sort", "value": "createdAt,desc"}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiJBRE0wMDEiLCJleHAiOjE3NjA4ODcwNDcsInVzZXJfaWQiOjEsInNjb3BlIjpbImNyZWF0ZVByb2plY3QiLCJkZXRhaWxQcm9qZWN0IiwidXBkYXRlUHJvamVjdCIsImRlbGV0ZVByb2plY3QiLCJzZWFyY2hQcm9qZWN0IiwiY3JlYXRlVW5pdCIsInVwZGF0ZVVuaXQiLCJkZWxldGVVbml0Iiwic2VhcmNoVW5pdCIsImRldGFpbFVuaXQiLCJjcmVhdGVDdXN0b21lciIsInVwZGF0ZUN1c3RvbWVyIiwiZGVsZXRlQ3VzdG9tZXIiLCJzZWFyY2hDdXN0b21lciIsImRldGFpbEN1c3RvbWVyIiwiaW1wb3J0Q3VzdG9tZXIiLCJjcmVhdGVSb2xlIiwidXBkYXRlUm9sZSIsImRlbGV0ZVJvbGUiLCJzZWFyY2hSb2xlIiwiZGV0YWlsUm9sZSIsInNlYXJjaFBlcm1pc3Npb24iLCJ1cGRhdGVDb25maWciLCJzZWFyY2hDb25maWciLCJkZXRhaWxDb25maWciLCJjcmVhdGVFbXBsb3llZSIsImRldGFpbEVtcGxveWVlIiwidXBkYXRlRW1wbG95ZWUiLCJkZWxldGVFbXBsb3llZSIsInNlYXJjaEVtcGxveWVlIiwibWFudWFsTWFuYWdlclJ1bGUiLCJtYW51YWxTdGFmZlJ1bGUiLCJhdXRvUnVsZSIsInNlYXJjaE5vdGlmaWNhdGlvbiJdfQ.1AAJCEIzLxM74-M0mti7cyPkWYgEugOzwN4lwe2RLRJb6T-ksLanmEhWPGR2KjxZ5IaYLaSBSXcAA0iaiXYazw", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:8081/api"}, {"key": "fileName", "value": ""}]}