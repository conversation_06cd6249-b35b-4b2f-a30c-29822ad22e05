package vn.agis.crm.base.utils;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Created by huyvv
 * Date: 13/05/2021
 * Time: 9:11 AM
 * for all issues, contact me: <EMAIL>
 **/
public class CopyPropertiesUtils<T extends Serializable> {

    private CopyPropertiesUtils() {

    }

    private static List<Field> getAllFields(List<Field> fields, Class<?> type) {
        fields.addAll(Arrays.asList(type.getDeclaredFields()));

        if (type.getSuperclass() != null) {
            getAllFields(fields, type.getSuperclass());
        }

        return fields;
    }

    private static String[] getIgnoreFields(String copyFields, Class<?> classType) {
        List<String> ignoreFields = new ArrayList<>();
        Set<String> copyFieldsSet = new HashSet<String>(Arrays.asList(copyFields.split(Pattern.quote(","))));

        List<Field> fieldList = new LinkedList<>();
        getAllFields(fieldList, classType);
        for (Field field : fieldList){
            if(!copyFieldsSet.contains(field.getName().trim())) ignoreFields.add(field.getName());
        }

        String[] result = new String[ignoreFields.size()];
        for (int i = 0; i < ignoreFields.size(); i++) {
            result[i] = ignoreFields.get(i);
        }
        return result;
    }

    public static <T> List copyList(List source, String copyFields, Class<?> classType) {
        List<T> target = new ArrayList<>();
        List<Field> fieldList = new LinkedList<>();
        getAllFields(fieldList, classType);

        String[] ignoreFields = getIgnoreFields(copyFields, classType);
        for (int i = 0; i < source.size(); i++) {
            for (int j = 0; j < ignoreFields.length; j++) {
                try {
                    Field prop = source.get(i).getClass().getDeclaredField(ignoreFields[j]);
                    prop.setAccessible(true);
                    prop.set(source.get(i), null);
                } catch (NoSuchFieldException | IllegalAccessException ex) {
                    ex.printStackTrace();
                }
            }
            target.add((T) source.get(i));
        }
        return target;
    }
}
