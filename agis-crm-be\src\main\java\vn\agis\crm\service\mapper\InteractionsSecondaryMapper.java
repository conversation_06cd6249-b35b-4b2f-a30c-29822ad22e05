package vn.agis.crm.service.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryUpdateDto;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;
import vn.agis.crm.repository.EmployeeRepository;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Optional;

/**
 * Mapper for InteractionsSecondary entity and DTOs
 * Handles conversion between entity and various DTO types
 */
@Component
public class InteractionsSecondaryMapper {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Autowired
    private EmployeeRepository employeeRepository;

    /**
     * Convert InteractionsSecondary entity to InteractionSecondaryDto
     */
    public InteractionSecondaryDto toDto(InteractionsSecondary entity) {
        if (entity == null) {
            return null;
        }

        InteractionSecondaryDto dto = new InteractionSecondaryDto();
        dto.setId(entity.getId());
        dto.setExpectedSellPrice(entity.getExpectedSellPrice());
        dto.setExpectedRentPrice(entity.getExpectedRentPrice());
        dto.setResult(entity.getResult());
        dto.setHappenedAt(entity.getHappenedAt() != null ? dateFormat.format(entity.getHappenedAt()) : null);
        dto.setNotes(entity.getNotes());
        dto.setDeleted(entity.getDeleted());
        dto.setCreatedBy(entity.getCreatedBy());

        // Set created name from employee
        if (entity.getCreatedBy() != null) {
            Optional<Employee> employeeOpt = employeeRepository.findById(entity.getCreatedBy());
            if (employeeOpt.isPresent()) {
                Employee employee = employeeOpt.get();
                dto.setCreatedName(employee.getFullName() != null ? employee.getFullName() : employee.getUsername());
            }
        }

        return dto;
    }

    /**
     * Convert InteractionSecondaryCreateDto to InteractionsSecondary entity
     */
    public InteractionsSecondary toEntity(InteractionSecondaryCreateDto createDto) {
        if (createDto == null) {
            return null;
        }

        InteractionsSecondary entity = new InteractionsSecondary();
        entity.setCustomerPropertyId(createDto.getCustomerPropertyId());
        entity.setExpectedSellPrice(createDto.getExpectedSellPrice());
        entity.setExpectedRentPrice(createDto.getExpectedRentPrice());
        entity.setResult(createDto.getResult());
        entity.setNotes(createDto.getNotes());
        entity.setDeleted(false); // Default to not deleted

        // Parse happened at date
        if (createDto.getHappenedAt() != null && !createDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(createDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }

        return entity;
    }

    /**
     * Update existing InteractionsSecondary entity from InteractionSecondaryUpdateDto
     */
    public void updateEntityFromDto(InteractionsSecondary entity, InteractionSecondaryUpdateDto updateDto) {
        if (entity == null || updateDto == null) {
            return;
        }

        entity.setCustomerPropertyId(updateDto.getCustomerPropertyId());
        entity.setExpectedSellPrice(updateDto.getExpectedSellPrice());
        entity.setExpectedRentPrice(updateDto.getExpectedRentPrice());
        entity.setResult(updateDto.getResult());
        entity.setNotes(updateDto.getNotes());

        // Parse happened at date
        if (updateDto.getHappenedAt() != null && !updateDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(updateDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }
    }

    /**
     * Convert InteractionSecondaryUpdateDto to InteractionsSecondary entity
     * Used when creating a new entity from update DTO (for validation purposes)
     */
    public InteractionsSecondary toEntity(InteractionSecondaryUpdateDto updateDto) {
        if (updateDto == null) {
            return null;
        }

        InteractionsSecondary entity = new InteractionsSecondary();
        entity.setId(updateDto.getId());
        entity.setCustomerPropertyId(updateDto.getCustomerPropertyId());
        entity.setExpectedSellPrice(updateDto.getExpectedSellPrice());
        entity.setExpectedRentPrice(updateDto.getExpectedRentPrice());
        entity.setResult(updateDto.getResult());
        entity.setNotes(updateDto.getNotes());
        entity.setDeleted(false); // Default to not deleted

        // Parse happened at date
        if (updateDto.getHappenedAt() != null && !updateDto.getHappenedAt().trim().isEmpty()) {
            try {
                entity.setHappenedAt(dateFormat.parse(updateDto.getHappenedAt()));
            } catch (Exception e) {
                throw new IllegalArgumentException("Invalid date format for happenedAt. Expected format: dd/MM/yyyy");
            }
        }

        return entity;
    }
}
