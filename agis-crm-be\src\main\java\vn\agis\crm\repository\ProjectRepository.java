package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Projects;

import java.util.Set;


@Repository
public interface ProjectRepository extends JpaRepository<Projects, Long> {

    Projects findFirstByName(String name);

    /**
     * Find project by name with case-insensitive search
     * Used for import validation to prevent duplicate projects with different capitalization
     */
    @Query("SELECT p FROM Projects p WHERE LOWER(p.name) = LOWER(:name) AND p.deletedAt IS NULL")
    Projects findFirstByNameIgnoreCase(@Param("name") String name);

    Page<Projects> findByNameContainingIgnoreCase(String name, Pageable pageable);

    @Query(value = "select project_id from customer_offers", nativeQuery = true)
    Set<Long> getGreetingProjectIds();

    @Query(value = "select project_id from customer_properties", nativeQuery = true)
    Set<Long> getBoughtProjectIds();
}
