package vn.agis.crm.base.jpa.dto.resp;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SearchDeviceRespone {
    private Long id;
    private String msisdn;
    private String deviceName;
    private String serialNumber;
    private String model;
    private String deviceType;
    private String imei;
    private Integer updatedBy;
    private Date updatedDate;
    private String enterpriseName;
    private String customerName;
    private Long lastConnected;
    private Integer connectionStatus;
    private Float traffic;
    private Float expanse;
    private Long deviceTypeId;
    private Float currentVolume;
    private Long estimatedCost;
}
