package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Table(name = "REPORT_Content")
@Entity
@Data
public class ReportContent extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "REPORT_CONFIG_ID")
    private Long reportConfigId;
    @Column(name = "TABLE_NAME")
    private String tableName;
    @Column(name = "SCHEMA")
    private String schema;
    @Lob
    @Column(name = "QUERY")
    private String query;
    @Column(name = "COLUMN_DISPLAY")
    private String columnDisplay;
    @Column(name = "COLUMN_QUERY_RESULT")
    private String columnQueryResult;
    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
    @Column(name = "UPDATED_BY")
    private Long updatedBy;
}