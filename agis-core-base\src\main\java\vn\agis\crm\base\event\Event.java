package vn.agis.crm.base.event;

import com.google.gson.Gson;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.constants.ResponseCode;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class Event implements Serializable {
    private static final long serialVersionUID = 1L;

    public String id;
    public String appId;
    public Long userId;
    public Integer userType;
    public String provinceCode;
    public Date timeStamp;
    public String method;
    public String category;
    public Object payload;
    public String conversionClass;
    public Integer respStatusCode;
    public String respErrorDesc;
    public String respRoutingKey;
    public String replyQueue;
    public String receiveRoutingKey;

    public Event(EventBuilder eventBuilder) {
        this.id = eventBuilder.id;
        this.conversionClass = eventBuilder.convertClass;
        this.payload = eventBuilder.payload;
        this.respRoutingKey = eventBuilder.resRouting;
        this.method = eventBuilder.method;
        this.category = eventBuilder.category;
        this.replyQueue = eventBuilder.replyTo;
        this.receiveRoutingKey = eventBuilder.receiveRoutingKey;
        this.timeStamp = eventBuilder.timestamp;
        this.userType = eventBuilder.userType;
        this.userId = eventBuilder.userId;
        this.provinceCode = eventBuilder.provinceCode;
    }

    public Event() {

    }

    @Override
    public String toString() {
        // request
        if (respStatusCode == null)
            return "Event: {" +
                    "id=" + id +
                    ", type=request" +
                    ", method=" + method +
                    ", category=" + category +
                    ", conversionClass=" + conversionClass +
                    ", timestamp=" + timeStamp +
                    ", appId=" + appId +
                    ", userId=" + userId +
                    ", userType=" + userType +
                    ", provinceCode=" + provinceCode +
                    ", respRoutingKey=" + respRoutingKey +
                    ", replyQueue=" + replyQueue +
                    ", payload='" + new Gson().toJson(payload) +
                    '}';
            // response
        else
            return "Event: {" +
                    "id=" + id +
                    ", type=response" +
                    ", respStatusCode=" + respStatusCode +
                    ", respErrorDesc=" + respErrorDesc +
                    ", method=" + method +
                    ", category=" + category +
                    ", conversionClass=" + conversionClass +
                    ", timestamp=" + timeStamp +
                    ", appId=" + appId +
                    ", userId=" + userId +
                    ", userType=" + userType +
                    ", provinceCode=" + provinceCode +
                    ", respRoutingKey=" + respRoutingKey +
                    ", replyQueue=" + replyQueue +
                    ", payload='" + new Gson().toJson(payload) +
                    '}';
    }

    public static Event createResponse(Event request) {
        Event response = new Event();
        response.id = request.id;
        response.method = request.method;
        response.category = request.category;
        response.appId = SpringContext.getServiceName();
        response.timeStamp = request.timeStamp;
        response.userId = request.userId;
        response.userType = request.userType;
        response.provinceCode = request.provinceCode;
        return response;
    }

    public static Event createResponse(Event request, Object responsePayload, int respStatusCode, String respErrorDesc) {
        Event response = createResponse(request);
        response.respStatusCode = respStatusCode;
        if (respErrorDesc != null) response.respErrorDesc = respErrorDesc;
        else response.respErrorDesc = ResponseCode.getResponseStatusMessage(respStatusCode);
        response.payload = responsePayload;
        return response;
    }

    public Event createResponse() {
        Event response = new Event();
        response.id = this.id;
        response.method = this.method;
        response.category = this.category;
        response.appId = SpringContext.getApplicationId();
        response.timeStamp = this.timeStamp;
        response.userId = this.userId;
        return response;
    }

    public Event createResponse(Object responsePayload, int respStatusCode, String respErrorDesc) {
        Event response = createResponse(this);
        response.respStatusCode = respStatusCode;
        if (respErrorDesc != null) response.respErrorDesc = respErrorDesc;
        else response.respErrorDesc = ResponseCode.getResponseStatusMessage(respStatusCode);
        response.payload = responsePayload;
        if (responsePayload != null) {
            if ((responsePayload instanceof List) && ((List<?>) responsePayload).size() > 0) {
                Object actualObj = ((List<?>) responsePayload).get(0);
                response.conversionClass = "List<" + actualObj.getClass().getName() + ">";
            } else response.conversionClass = responsePayload.getClass().getName();
        }
        return response;
    }

    public static class EventBuilder {
        private String id;
        private String method;
        private String category;
        private String convertClass;
        private Object payload;
        private String resRouting;
        private String replyTo;
        private String receiveRoutingKey;
        private Date timestamp;
        private Long userId;
        private Integer userType;
        private String provinceCode;

        public EventBuilder() {
        }

        public EventBuilder provinceCode(String provinceCode) {
            this.provinceCode = provinceCode;
            return this;
        }

        public EventBuilder userId(Long userId) {
            this.userId = userId;
            return this;
        }

        public EventBuilder userType(Integer userType) {
            this.userType = userType;
            return this;
        }

        public EventBuilder method(String method) {
            this.method = method;
            return this;
        }

        public EventBuilder id(String id) {
            this.id = id;
            return this;
        }

        public EventBuilder category(String category) {
            this.category = category;
            return this;
        }

        public EventBuilder convertClass(String convertClass) {
            this.convertClass = convertClass;
            return this;
        }

        public EventBuilder withPayload(Object payload) {
            this.payload = payload;
            return this;
        }

        public EventBuilder andResRouting(String resRouting) {
            this.resRouting = resRouting;
            return this;
        }

        public EventBuilder andReplyTo(String replyTo) {
            this.replyTo = replyTo;
            return this;
        }

        public EventBuilder andReceiveRoutingKey(String receiveRoutingKey) {
            this.receiveRoutingKey = receiveRoutingKey;
            return this;
        }

        public EventBuilder andTimeStamp(Date timestamp) {
            this.timestamp = timestamp;
            return this;
        }

        //Return the finally consrcuted User object
        public Event build() {
            Event event = new Event(this);
            return event;
        }
    }
}
