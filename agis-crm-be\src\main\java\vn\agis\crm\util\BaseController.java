package vn.agis.crm.util;


import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;

public class BaseController {

    private static final String STR_REGEXP_SORT = "^[A-z0-9]+,(asc|desc|ASC|DESC)$";
    public BaseController() {
    	 // Do nothing
    }
    public static class ListRequest {
        private final Integer page;
        private final Integer size;
        private final String sort;

        public ListRequest(Integer size, Integer page, String sort) {
            if(sort != null && !sort.matches(STR_REGEXP_SORT)) {
            }
            this.size = size == null ? 10 : (size < 0) ? 10 : size;
            this.page = page == null ? 0 : (page < 0) ? 0 : page;
            this.sort = (sort != null ) ? sort : "id,asc";
        }

        public Pageable getPageable() {
            String[] part = sort.split(",", 2);
            Sort sortable = Sort.by(Sort.Direction.valueOf(part[1].toUpperCase()), part[0]);
            if(!part[0].toLowerCase().contains("date")){
                sortable = JpaSort.unsafe(Sort.Direction.valueOf(part[1].toUpperCase()),part[0]);
            }
            return PageRequest.of(page, size, sortable);
        }

        public Pageable getPageable(Sort sort) {
            return PageRequest.of(page, size, sort);
        }
    }

    public static class MultiSortListRequest {
        private final Integer page;
        private final Integer size;
        private final String sort;

        private static final String STR_REGEXP_SORT = "(\\w+,(asc|desc))(;\\w+,(asc|desc))*"; // Ví dụ: "field1,asc;field2,desc"

        public MultiSortListRequest(Integer size, Integer page, String sort) {
            if (sort != null && !sort.matches(STR_REGEXP_SORT)) {
                throw new IllegalArgumentException("Invalid sort format. Expected: field1,asc;field2,desc");
            }
            this.size = size == null ? 10 : (size < 0) ? 10 : size;
            this.page = page == null ? 0 : (page < 0) ? 0 : page;
            this.sort = (sort != null) ? sort : "id,asc";
        }

        public Pageable getPageable() {
            Sort sortable = parseSort(sort);
            return PageRequest.of(page, size, sortable);
        }

        public Pageable getPageable(Sort sort) {
            return PageRequest.of(page, size, sort);
        }

        private Sort parseSort(String sortString) {
            if (sortString == null || sortString.isEmpty()) {
                return Sort.by("id").ascending();
            }

            String[] sortFields = sortString.split(";");
            Sort sort = Sort.unsorted();

            for (String fieldSort : sortFields) {
                String[] part = fieldSort.split(",", 2);
                if (part.length != 2) {
                    throw new IllegalArgumentException("Invalid sort field: " + fieldSort);
                }
                String field = part[0].trim();
                String direction = part[1].trim().toUpperCase();

                if (!field.toLowerCase().contains("date")) {
                    // Kết hợp JpaSort.unsafe
                    sort = sort.and(
                            JpaSort.unsafe(Sort.Direction.valueOf(direction), "NLSSORT(UPPER(" + field + "), 'nls_sort = Vietnamese')")
                    );
                } else {
                    // Kết hợp Sort thông thường
                    sort = sort.and(Sort.by(Sort.Direction.valueOf(direction), field));
                }
            }

            return sort;
        }

    }


}
