package vn.agis.crm.controller;

import vn.agis.crm.base.jpa.dto.AssignmentHistoryResponseDto;
import vn.agis.crm.base.jpa.dto.GetAssignmentHistoryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Parameter;
import vn.agis.crm.base.jpa.dto.CustomerSearchDto;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.dto.res.CustomerResDto;
import vn.agis.crm.base.jpa.dto.CustomerDeletionValidationResult;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.CustomerService;

@RestController
@RequestMapping("/customer-mgmt")
public class CustomerController extends CrudController<Customers, Long> {

    CustomerService customerService;

    @Autowired
    public CustomerController(CustomerService service) {
        super(service);
        this.customerService = service;
        this.baseUrl = "/customer-mgmt";
    }

    @GetMapping("/search")
    @PreAuthorize("hasAnyAuthority('searchCustomer')")
    public ResponseEntity<Page<CustomerResDto>> getPageCustomers(
        @RequestParam(name = "fullName", required = false, defaultValue = "") String fullName,
        @RequestParam(name = "phone", required = false, defaultValue = "") String phone,
        @RequestParam(name = "email", required = false, defaultValue = "") String email,
        @RequestParam(name = "cccd", required = false, defaultValue = "") String cccd,
        @RequestParam(name = "address", required = false, defaultValue = "") String address,
        @RequestParam(name = "sourceType", required = false) String sourceType,
        @RequestParam(name = "sourceDetail", required = false, defaultValue = "") String sourceDetail,
        @RequestParam(name = "businessField", required = false, defaultValue = "") String businessField,
        @RequestParam(name = "interests", required = false, defaultValue = "") String interests,
        @RequestParam(name = "relativeName", required = false, defaultValue = "") String relativeName,
        @RequestParam(name = "birthDateFrom", required = false) String birthDateFrom,
        @RequestParam(name = "birthDateTo", required = false) String birthDateTo,
        @RequestParam(name = "projectId", required = false) Long projectId,
        @RequestParam(name = "purchasedProjectId", required = false) Long purchasedProjectId,
        @RequestParam(name = "activeOfferProjectId", required = false) Long activeOfferProjectId,
        @RequestParam(name = "propertyType", required = false, defaultValue = "") String propertyType,
        @RequestParam(name = "birthdayDayMonth", required = false, defaultValue = "") String birthdayDayMonth,
        @RequestParam(name = "employeeId", required = false) Long employeeId,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "createdAt,asc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        CustomerSearchDto searchDto = new CustomerSearchDto(fullName, phone, email, cccd, address, sourceType,
                sourceDetail, businessField, interests, relativeName, projectId, purchasedProjectId,
                activeOfferProjectId, propertyType, birthDateFrom, birthDateTo, birthdayDayMonth, employeeId, page, size, sortBy);
        Page<CustomerResDto> list = customerService.search(searchDto, listRequest.getPageable());
        return ResponseEntity.ok().body(list);
    }

    @GetMapping("/{id}")
    public CustomerResDto getOneCustomer(@PathVariable Long id) {
        return customerService.getOneRes(id);
    }

    @DeleteMapping("/delete/{id}")
    public ResponseEntity<String> deleteCustomer(@PathVariable Long id) {
        try {
            // First validate if customer can be deleted
            CustomerDeletionValidationResult validationResult = customerService.validateCustomerDeletion(id);

            if (!validationResult.isCanDelete()) {
                // Return detailed error message about dependencies
                throw new BadRequestException(
                    validationResult.getMessage(),
                    "CUSTOMER",
                    validationResult.getMessage(),
                    MessageKeyConstant.CustomerDeletion.CUSTOMER_HAS_DEPENDENCIES
                );
            }

            // If validation passes, proceed with deletion
            customerService.deleteById(id);
            return ResponseEntity.ok("Xóa khách hàng thành công");

        } catch (BadRequestException e) {
            throw e; // Re-throw BadRequestException to maintain error handling
        } catch (Exception e) {
            throw new BadRequestException(
                "Lỗi hệ thống khi xóa khách hàng: " + e.getMessage(),
                "CUSTOMER",
                e.getMessage(),
                MessageKeyConstant.CustomerDeletion.CUSTOMER_DELETION_SYSTEM_ERROR
            );
        }
    }

//    @PostMapping("/create")
//    public CustomerResDto createCustomer(@RequestBody CustomerDto createReq) {
//        return customerService.createCustomerRes(createReq);
//    }

//    @PutMapping("/update/{id}")
//    public CustomerResDto updateCustomer(@RequestBody CustomerDto updateReq, @PathVariable Long id) {
//        updateReq.setId(id);
//        return customerService.updateRes(updateReq);
//    }

    // v2 endpoints: upsert with assignments + relatives + properties + offers
    @PostMapping("/create")
    public CustomerResDto createCustomerV2(@RequestBody vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest req) {
        return customerService.createCustomerV2(req);
    }

    @PutMapping("/update/{id}")
    public CustomerResDto updateCustomerV2(@PathVariable Long id, @RequestBody vn.agis.crm.base.jpa.dto.req.CustomerUpsertRequest req) {
        req.setId(id);
        return customerService.updateCustomerV2(req);
    }

    @GetMapping("/check-exists")
    public ResponseEntity<Boolean> checkExists(@RequestParam String key, @RequestParam String value) {
        boolean exists = customerService.checkExists(key, value);
        return ResponseEntity.ok().body(exists);
    }

    @PutMapping("/assignment/update-employee")
    public ResponseEntity<String> updateAssignmentEmployee(@RequestBody vn.agis.crm.base.jpa.dto.req.UpdateAssignmentEmployeeDto request) {
        String result = customerService.updateAssignmentEmployee(request);
        return ResponseEntity.ok().body(result);
    }

    @GetMapping("/{customerId}/assignment-history")
    public ResponseEntity<AssignmentHistoryResponseDto> getAssignmentHistory(
            @PathVariable Long customerId,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
            @Parameter(description = "Sort field", example = "assignedFrom")
            @RequestParam(name = "sortBy", required = false, defaultValue = "assignedFrom") String sortBy,
            @Parameter(description = "Sort direction", example = "DESC")
            @RequestParam(name = "sortDirection", required = false, defaultValue = "DESC") String sortDirection) {

        GetAssignmentHistoryDto request = new GetAssignmentHistoryDto(customerId);
        request.setPage(page);
        request.setSize(size);
        request.setSortBy(sortBy);
        request.setSortDirection(sortDirection);

        AssignmentHistoryResponseDto result = customerService.getAssignmentHistory(request);
        return ResponseEntity.ok().body(result);
    }

    @DeleteMapping("/assignments/{assignmentId}")
    public ResponseEntity<Void> deleteAssignment(
            @Parameter(description = "ID of the customer assignment to delete", example = "123", required = true)
            @PathVariable Long assignmentId) {

        try {
            // Validate input
            if (assignmentId == null || assignmentId <= 0) {
                return ResponseEntity.badRequest().build();
            }

            customerService.deleteAssignment(assignmentId);
            return ResponseEntity.ok().build();

        } catch (IllegalArgumentException e) {
            // Assignment not found or invalid input
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            // Internal server error
            return ResponseEntity.internalServerError().build();
        }
    }
}

