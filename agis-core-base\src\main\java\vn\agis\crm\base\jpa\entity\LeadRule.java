package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

@Entity
@Table(name = "lead_rules")
@Data
public class LeadRule extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "priority", nullable = false)
    private Integer priority;

    @Lob
    @Column(name = "conditions", nullable = false)
    private String conditions; // Stored as JSON string

    @Column(name = "manager_id")
    private Long managerId;

    @Column(name = "staff_id")
    private Long staffId;


    @Column(name = "updated_at")
    private java.util.Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;

    @Enumerated(EnumType.STRING)
    @Column(name = "conflict_policy", nullable = false)
    private ConflictPolicy conflictPolicy = ConflictPolicy.SKIP;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    public enum ConflictPolicy {
        SKIP, OVERWRITE, OVERWRITE_IF_STAFF_EMPTY
    }
}

