# AGIS CRM Enhanced Import Workflow - cURL Examples

## Overview

This document provides comprehensive cURL examples demonstrating the enhanced import workflow with 8-step validation and CustomerUpsertRequest-based processing.

## Complete Import Workflow

### Step 1: Upload Import File

Upload a CSV or Excel file to create an import job:

```bash
curl -X POST "http://localhost:8080/imports" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -F "file=@/path/to/your/import_file.csv" \
  -F "options={\"sheet_name\":\"Sheet1\",\"encoding\":\"UTF-8\",\"delimiter\":\",\"\"}"
```

**Response Example:**
```json
{
  "id": 123,
  "fileName": "import_file.csv",
  "fileChecksum": "abc123def456",
  "source": "WEB_UPLOAD",
  "mode": "DRY_RUN",
  "status": "PENDING",
  "totalRows": null,
  "validRows": null,
  "errorRows": null,
  "createdAt": "2024-01-15T10:30:00Z",
  "createdBy": 1
}
```

### Step 2: Start Enhanced Dry-Run Validation (8-Step Workflow)

Start the comprehensive validation process:

```bash
curl -X POST "http://localhost:8080/imports/123/dry-run" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Response Example:**
```json
{
  "id": 123,
  "fileName": "import_file.csv",
  "status": "RUNNING",
  "mode": "DRY_RUN",
  "message": "Enhanced dry-run validation started using 8-step workflow"
}
```

### Step 3: Check Dry-Run Results

Monitor the validation progress and get detailed results:

```bash
curl -X GET "http://localhost:8080/imports/123/dry-run-result" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Response Example:**
```json
{
  "jobId": 123,
  "fileName": "import_file.csv",
  "status": "SUCCESS",
  "totalRows": 150,
  "validRows": 145,
  "errorRows": 5,
  "warningRows": 12,
  "validationSteps": {
    "step1_project_validation": {
      "processed": 150,
      "errors": 2,
      "warnings": 5,
      "newProjects": 3
    },
    "step2_unit_validation": {
      "processed": 150,
      "errors": 1,
      "warnings": 3,
      "newUnits": 8
    },
    "step3_customer_validation": {
      "processed": 150,
      "errors": 2,
      "warnings": 4,
      "duplicatePhones": 2,
      "duplicateEmails": 1
    },
    "step4_relatives_validation": {
      "processed": 45,
      "errors": 0,
      "warnings": 2
    },
    "step5_secondary_validation": {
      "processed": 30,
      "errors": 0,
      "warnings": 1
    },
    "step6_primary_validation": {
      "processed": 120,
      "errors": 0,
      "warnings": 3
    },
    "step7_external_sales_validation": {
      "processed": 25,
      "errors": 0,
      "warnings": 1
    },
    "step8_assignment_validation": {
      "processed": 140,
      "errors": 0,
      "warnings": 2,
      "newEmployees": 1
    }
  },
  "estimation": {
    "estimatedProcessingTime": "00:02:30",
    "estimatedCustomersCreated": 145,
    "estimatedRelativesCreated": 43,
    "estimatedPropertiesCreated": 148,
    "estimatedOffersCreated": 150,
    "estimatedAssignmentsCreated": 140
  }
}
```

### Step 4: Get Detailed Validation Errors

Review specific validation errors and warnings:

```bash
curl -X GET "http://localhost:8080/imports/123/errors?page=0&size=20&severity=ERROR" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Response Example:**
```json
{
  "content": [
    {
      "id": 1,
      "jobId": 123,
      "rowNumber": 15,
      "fieldName": "PHONE",
      "fieldValue": "invalid_phone",
      "errorType": "INVALID_PHONE_FORMAT",
      "errorMessage": "Số điện thoại không đúng định dạng: invalid_phone",
      "severity": "ERROR",
      "validationStep": "step3_customer_validation"
    },
    {
      "id": 2,
      "jobId": 123,
      "rowNumber": 23,
      "fieldName": "TÊN DỰ ÁN",
      "fieldValue": "",
      "errorType": "MISSING_REQUIRED_FIELD",
      "errorMessage": "Tên dự án là bắt buộc",
      "severity": "ERROR",
      "validationStep": "step1_project_validation"
    }
  ],
  "totalElements": 5,
  "totalPages": 1,
  "size": 20,
  "number": 0
}
```

### Step 5: Confirm Import Execution (CustomerUpsertRequest Pattern)

After successful validation, confirm the import to start actual data processing:

```bash
curl -X POST "http://localhost:8080/imports/123/confirm" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d "options={\"skip_errors\":true,\"create_missing_projects\":true,\"create_missing_employees\":true}"
```

**Response Example:**
```json
{
  "jobId": 123,
  "fileName": "import_file.csv",
  "status": "PENDING",
  "totalRows": 150,
  "processedRows": 0,
  "successfulRows": 0,
  "failedRows": 0,
  "startedAt": "2024-01-15T10:45:00Z",
  "estimatedCompletionTime": "2024-01-15T10:47:30Z"
}
```

### Step 6: Monitor Import Progress

Track the import execution progress:

```bash
curl -X GET "http://localhost:8080/imports/123/progress" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Response Example:**
```json
{
  "jobId": 123,
  "fileName": "import_file.csv",
  "status": "RUNNING",
  "totalRows": 150,
  "processedRows": 75,
  "successfulRows": 73,
  "failedRows": 2,
  "progressPercentage": 50.0,
  "startedAt": "2024-01-15T10:45:00Z",
  "estimatedCompletionTime": "2024-01-15T10:47:30Z",
  "currentStep": "Processing customer assignments",
  "processingRate": "30 rows/minute"
}
```

### Step 7: Get Final Import Results

Retrieve the final import execution results:

```bash
curl -X GET "http://localhost:8080/imports/123/result" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

**Response Example:**
```json
{
  "jobId": 123,
  "fileName": "import_file.csv",
  "status": "SUCCESS",
  "totalRows": 150,
  "successfulRows": 145,
  "failedRows": 5,
  "successRate": 96.67,
  "startedAt": "2024-01-15T10:45:00Z",
  "completedAt": "2024-01-15T10:47:15Z",
  "totalProcessingTime": "00:02:15",
  "databaseOperations": {
    "customersCreated": 145,
    "customersUpdated": 0,
    "relativesCreated": 43,
    "propertiesCreated": 148,
    "offersCreated": 150,
    "assignmentsCreated": 140,
    "interactionsCreated": 165,
    "projectsCreated": 3,
    "unitsCreated": 8,
    "transactionsCommitted": 145,
    "transactionsRolledBack": 5
  },
  "errorSummary": {
    "validationErrors": 3,
    "databaseErrors": 2,
    "systemErrors": 0
  }
}
```

## Advanced Usage Examples

### Cancel Running Import

```bash
curl -X POST "http://localhost:8080/imports/123/cancel" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE" \
  -d "{\"reason\":\"Data quality issues found\",\"userId\":1}"
```

### Get Import Job Details

```bash
curl -X GET "http://localhost:8080/imports/123" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Filter Validation Errors by Type

```bash
curl -X GET "http://localhost:8080/imports/123/errors?page=0&size=10&errorType=DUPLICATE_IN_SYSTEM&severity=WARNING" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Search Errors by Field Name

```bash
curl -X GET "http://localhost:8080/imports/123/errors?page=0&size=10&fieldName=PHONE" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

## Error Handling Examples

### Handle Validation Failures

```bash
# If dry-run fails, check specific errors
curl -X GET "http://localhost:8080/imports/123/errors?severity=ERROR" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"

# Response will show specific validation issues:
{
  "content": [
    {
      "rowNumber": 15,
      "fieldName": "HỌ VÀ TÊN KHÁCH HÀNG",
      "errorMessage": "Họ tên khách hàng là bắt buộc",
      "severity": "ERROR",
      "validationStep": "step3_customer_validation"
    }
  ]
}
```

### Handle Import Execution Failures

```bash
# Check import result for failures
curl -X GET "http://localhost:8080/imports/123/result" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"

# Response will show detailed failure information:
{
  "status": "PARTIAL_SUCCESS",
  "failedRows": 5,
  "errorSummary": {
    "validationErrors": 2,
    "databaseErrors": 3
  }
}
```

## Integration with AGIS CRM Workflow

### Verify Created Customers

After successful import, verify customers were created using the customer search API:

```bash
curl -X GET "http://localhost:8080/customer-mgmt/search?page=0&size=10&sort=createdAt,desc" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Check Customer Assignments

Verify customer assignments were created:

```bash
curl -X GET "http://localhost:8080/assignments/search?page=0&size=10&assignedFrom=2024-01-15" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

### Validate Customer Properties

Check customer properties were linked correctly:

```bash
curl -X GET "http://localhost:8080/customer-mgmt/123/properties" \
  -H "Accept: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN_HERE"
```

This comprehensive workflow ensures data quality through the 8-step validation process and maintains consistency with the existing AGIS CRM customer creation patterns using the CustomerUpsertRequest approach.
