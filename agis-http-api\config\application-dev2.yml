server:
  port: 8081
  servlet:
    context-path: /api
spring:
  rabbitmq:
    addresses: ************:5672, ************:5672, ************:5672
    port: 5672
    username: admin
    password: ssdc_cmp
  redis:
    mode: Sentinel
    standalone:
      host: ************
      port: 6379
      username: aaaa
      password: oneiot@2020
    cluster:
      nodes: *********:6380,*********:6381,*********:6385
      username: default
      password:
    sentinel:
      password: ssdc_cmp
      master: mymaster
      nodes: ************:26379, ************:26379, ************:26379
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
management:
  endpoints:
    web:
      exposure:
        include: health
google:
  api-key: AIzaSyB7FzJScOWtqr7IjUgaMYhSVDnyG1urh58
app:
  redis-limiter:
    redis-pool-max-total: 200       # max total connection              default：200
    redis-key-prefix: RL           # key prefix for visit footprint    default: #RL
    check-action-timeout: 100       # check action execution timeout    default: 100
    enable-dynamical-conf: true     # the switch for enable dynamical   default：false
    channel: #channel
  application:
    name: httpapi
