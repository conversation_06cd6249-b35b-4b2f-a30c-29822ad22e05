package vn.agis.crm.base.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import vn.agis.crm.base.utils.SpringContextUtils;

@Data
@EqualsAndHashCode(callSuper = false)
public class CustomBaseException extends RuntimeException {

    private static final long serialVersionUID = 5151078656511954845L;

    private final ApiError apiError = new ApiError();

    protected CustomBaseException(String keyMessage, String entityName, String field,
                                  String[] variable) {
        MessageSource messageSource = SpringContextUtils.getBean(MessageSource.class);
        String message = messageSource
            .getMessage(keyMessage, variable, LocaleContextHolder.getLocale());
        this.apiError.setMessage(message);
        this.apiError.setObject(entityName);
        this.apiError.setField(field);
        this.apiError.setErrorCode(keyMessage);
    }
}
