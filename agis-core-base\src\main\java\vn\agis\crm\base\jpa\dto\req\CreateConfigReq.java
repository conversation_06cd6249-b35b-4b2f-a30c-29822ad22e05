package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Data
public class CreateConfigReq {
    @NotBlank
    @Size(max = 100)
    private String configKey;

    @NotNull
    private Integer configType; // 1=single, 2=list

    private String configValue; // JSON hoặc text

    @Size(max = 255)
    private String description;
}

