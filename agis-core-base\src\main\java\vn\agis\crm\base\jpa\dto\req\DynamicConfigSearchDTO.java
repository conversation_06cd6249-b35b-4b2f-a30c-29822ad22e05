package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;
import java.util.Objects;

@Getter
@Setter
public class DynamicConfigSearchDTO {
    private String name;
    private String type;
    private Integer page;
    private Integer size;
    private String sortBy;
    public DynamicConfigSearchDTO(
            String name,
            String type,
            Integer page,
            Integer size,
            String sortBy) {
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.type = Objects.isNull(type) ? " " : SqlUtils.optimizeSearchLike(type);
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
