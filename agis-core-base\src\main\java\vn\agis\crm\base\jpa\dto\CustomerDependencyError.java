package vn.agis.crm.base.jpa.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Represents a specific dependency error that prevents customer deletion
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerDependencyError {
    
    /**
     * Type of dependency (e.g., "bản ghi phân công nhân viên", "giao dịch bất động sản")
     */
    private String dependencyType;
    
    /**
     * Count of dependent records
     */
    private long count;
    
    /**
     * Detailed error message in Vietnamese
     */
    private String message;
    
    /**
     * Table name that contains the dependency
     */
    private String tableName;
    
    /**
     * Creates a dependency error for customer assignments
     */
    public static CustomerDependencyError customerAssignments(long count) {
        return new CustomerDependencyError(
            "bản ghi phân công nhân viên",
            count,
            "Không thể xóa khách hàng vì còn tồn tại " + count + " bản ghi phân công nhân viên",
            "customer_assignments"
        );
    }
    
    /**
     * Creates a dependency error for customer offers
     */
    public static CustomerDependencyError customerOffers(long count) {
        return new CustomerDependencyError(
            "chào bán khách hàng",
            count,
            "Không thể xóa khách hàng vì còn tồn tại " + count + " chào bán khách hàng",
            "customer_offers"
        );
    }
    
    /**
     * Creates a dependency error for customer properties
     */
    public static CustomerDependencyError customerProperties(long count) {
        return new CustomerDependencyError(
            "giao dịch bất động sản",
            count,
            "Không thể xóa khách hàng vì còn tồn tại " + count + " giao dịch bất động sản",
            "customer_properties"
        );
    }
    
    /**
     * Creates a dependency error for notifications
     */
    public static CustomerDependencyError notifications(long count) {
        return new CustomerDependencyError(
            "thông báo",
            count,
            "Không thể xóa khách hàng vì còn tồn tại " + count + " thông báo",
            "notifications"
        );
    }
    
    /**
     * Creates a dependency error for rule runs
     */
    public static CustomerDependencyError ruleRuns(long count) {
        return new CustomerDependencyError(
            "lịch sử chạy quy tắc",
            count,
            "Không thể xóa khách hàng vì còn tồn tại " + count + " lịch sử chạy quy tắc",
            "rule_runs"
        );
    }
}
