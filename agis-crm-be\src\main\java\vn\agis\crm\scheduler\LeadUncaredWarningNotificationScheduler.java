package vn.agis.crm.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;
import vn.agis.crm.service.NotificationService;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * Scheduled job to automatically send lead uncared warning notifications
 * Runs daily at 8:00 AM Vietnam timezone to notify employees about leads
 * that have been assigned but haven't had any interactions
 */
@Component
public class LeadUncaredWarningNotificationScheduler {

    private static final Logger logger = LoggerFactory.getLogger(LeadUncaredWarningNotificationScheduler.class);
    private static final String TIMEZONE_VIETNAM = "Asia/Ho_Chi_Minh";
    private static final String CONFIG_KEY_WARNING_DAYS = "NOTIFICATION_LEAD_UNCARED_WARNING_DAYS";
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("dd/MM/yyyy");

    @Autowired
    private ConfigRepository configRepository;

    @Autowired
    private CustomerAssignmentRepository customerAssignmentRepository;

    @Autowired
    private CustomerRepository customerRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private CustomerPropertyRepository customerPropertyRepository;

    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private InteractionsSecondaryRepository interactionsSecondaryRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private NotificationService notificationService;

    /**
     * Main scheduled method that runs daily at 8:00 AM Vietnam time
     */
    @Scheduled(cron = "0 0 8 * * *", zone = TIMEZONE_VIETNAM)
    public void processLeadUncaredWarningNotifications() {
        logger.info("🚨 Starting lead uncared warning notification job");

        try {
            // Step 1: Get configuration for warning days
            Integer warningDays = getWarningDaysFromConfig();
            if (warningDays == null) {
                logger.info("Lead uncared warning notifications disabled (no configuration found)");
                return;
            }

            logger.info("Processing lead uncared warnings for assignments older than {} days", warningDays);

            // Step 2: Find uncared lead assignments
            List<CustomerAssignments> uncaredAssignments = findUncaredLeadAssignments(warningDays);
            logger.info("Found {} uncared lead assignments", uncaredAssignments.size());

            if (uncaredAssignments.isEmpty()) {
                logger.info("No uncared lead assignments found");
                return;
            }

            // Step 3: Process each assignment
            int totalProcessed = 0;
            int successCount = 0;
            int errorCount = 0;

            for (CustomerAssignments assignment : uncaredAssignments) {
                totalProcessed++;
                boolean success = processUncaredLeadNotification(assignment, warningDays);
                if (success) {
                    successCount++;
                } else {
                    errorCount++;
                }
            }

            logger.info("🚨 Lead uncared warning job completed. Processed: {}, Success: {}, Errors: {}",
                       totalProcessed, successCount, errorCount);

        } catch (Exception e) {
            logger.error("Error in lead uncared warning notification job: {}", e.getMessage(), e);
        }
    }

    /**
     * Get warning days configuration from database
     * @return Number of days for warning, or null if disabled/invalid
     */
    private Integer getWarningDaysFromConfig() {
        try {
            Config config = configRepository.findOneByConfigKeyIgnoreCase(CONFIG_KEY_WARNING_DAYS);
            if (config == null || config.getConfigValue() == null || config.getConfigValue().trim().isEmpty()) {
                return null; // Configuration not found or empty
            }

            String configValue = config.getConfigValue().trim();
            int days = Integer.parseInt(configValue);

            // Validate range (1-365 days)
            if (days < 1 || days > 365) {
                logger.warn("Invalid warning days configuration: {}. Must be between 1-365", days);
                return null;
            }

            logger.debug("Lead uncared warning days configuration: {}", days);
            return days;

        } catch (NumberFormatException e) {
            logger.warn("Invalid warning days configuration format: {}. Must be a valid integer",
                       0);
            return null;
        } catch (Exception e) {
            logger.error("Error reading warning days configuration: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * Find lead assignments that haven't been cared for within the warning period
     * @param warningDays Number of days to look back
     * @return List of uncared assignments
     */
    private List<CustomerAssignments> findUncaredLeadAssignments(int warningDays) {
        try {
            // Calculate cutoff date (warningDays ago)
            LocalDate cutoffLocalDate = LocalDate.now(ZoneId.of(TIMEZONE_VIETNAM)).minusDays(warningDays);
            Date cutoffDate = Date.from(cutoffLocalDate.atStartOfDay(ZoneId.of(TIMEZONE_VIETNAM)).toInstant());

            logger.debug("Looking for assignments assigned before: {}", cutoffLocalDate.format(DATE_FORMATTER));

            // Find all active assignments assigned before cutoff date
            List<CustomerAssignments> candidateAssignments = customerAssignmentRepository.findActiveAssignmentsBeforeDate(cutoffDate);
            logger.debug("Found {} candidate assignments before cutoff date", candidateAssignments.size());

            List<CustomerAssignments> uncaredAssignments = new ArrayList<>();

            for (CustomerAssignments assignment : candidateAssignments) {
                // Check if customer is a lead and hasn't been cared for
                if (isUncaredLead(assignment)) {
                    uncaredAssignments.add(assignment);
                }
            }

            return uncaredAssignments;

        } catch (Exception e) {
            logger.error("Error finding uncared lead assignments: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * Check if a customer assignment represents an uncared lead
     * @param assignment Customer assignment to check
     * @return true if it's an uncared lead, false otherwise
     */
    private boolean isUncaredLead(CustomerAssignments assignment) {
        try {
            // Step 1: Get customer details
            Customers customer = customerRepository.findById(assignment.getCustomerId()).orElse(null);
            if (customer == null) {
                logger.debug("Customer {} not found for assignment {}", assignment.getCustomerId(), assignment.getId());
                return false;
            }

            // Step 2: Check if customer is a lead (source_type = 'Leads')
            if (!"Leads".equals(customer.getSourceType())) {
                logger.debug("Customer {} is not a lead (source_type: {})", customer.getId(), customer.getSourceType());
                return false;
            }

            // Step 3: Check if customer is deleted
            if (customer.getDeletedAt() != null) {
                logger.debug("Customer {} is deleted, skipping", customer.getId());
                return false;
            }

            // Step 4: Check for interactions after assignment date
            boolean hasInteractions = hasInteractionsAfterAssignment(customer, assignment.getAssignedFrom());
            if (hasInteractions) {
                logger.debug("Customer {} has interactions after assignment, not uncared", customer.getId());
                return false;
            }

            logger.debug("Customer {} is an uncared lead", customer.getId());
            return true;

        } catch (Exception e) {
            logger.error("Error checking if assignment {} is uncared lead: {}", assignment.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if customer has any interactions after the assignment date
     * @param customer Customer to check
     * @param assignedFrom Assignment date
     * @return true if interactions exist after assignment, false otherwise
     */
    private boolean hasInteractionsAfterAssignment(Customers customer, Date assignedFrom) {
        try {
            // Check primary interactions (through customer_offers)
            List<CustomerOffers> customerOffers = customerOfferRepository.findByCustomerId(customer.getId());
            for (CustomerOffers offer : customerOffers) {
                List<InteractionsPrimary> primaryInteractions = interactionsPrimaryRepository.findByCustomerOfferId(offer.getId());
                for (InteractionsPrimary interaction : primaryInteractions) {
                    if (interaction.getHappenedAt() != null && interaction.getHappenedAt().after(assignedFrom)) {
                        logger.debug("Found primary interaction {} after assignment for customer {}", 
                                   interaction.getId(), customer.getId());
                        return true;
                    }
                }
            }

            // Check secondary interactions (through customer_properties)
            List<CustomerProperties> customerProperties = customerPropertyRepository.findByCustomerId(customer.getId());
            for (CustomerProperties property : customerProperties) {
                List<InteractionsSecondary> secondaryInteractions = interactionsSecondaryRepository.findByCustomerPropertyId(property.getId());
                for (InteractionsSecondary interaction : secondaryInteractions) {
                    if (interaction.getHappenedAt() != null && interaction.getHappenedAt().after(assignedFrom)) {
                        logger.debug("Found secondary interaction {} after assignment for customer {}", 
                                   interaction.getId(), customer.getId());
                        return true;
                    }
                }
            }

            return false; // No interactions found after assignment

        } catch (Exception e) {
            logger.error("Error checking interactions for customer {}: {}", customer.getId(), e.getMessage(), e);
            return false; // Assume no interactions on error to be safe
        }
    }

    /**
     * Process uncared lead notification for a single assignment
     * @param assignment Assignment to process
     * @param warningDays Number of warning days from config
     * @return true if notification was created, false otherwise
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public boolean processUncaredLeadNotification(CustomerAssignments assignment, int warningDays) {
        try {
            // Step 1: Validate employee
            Employee employee = employeeRepository.findById(assignment.getEmployeeId()).orElse(null);
            if (employee == null || employee.getStatus() != Employee.Status.active || employee.getDeletedAt() != null) {
                logger.warn("Employee {} for assignment {} is not active, skipping notification", 
                           assignment.getEmployeeId(), assignment.getId());
                return false;
            }

            // Step 2: Get customer details
            Customers customer = customerRepository.findById(assignment.getCustomerId()).orElse(null);
            if (customer == null) {
                logger.warn("Customer {} for assignment {} not found, skipping notification", 
                           assignment.getCustomerId(), assignment.getId());
                return false;
            }

            // Step 3: Create notification content
            String title = "Cảnh báo lead chưa được chăm sóc";
            String content = createUncaredLeadNotificationContent(customer, assignment.getAssignedFrom());

            // Step 4: Create notification
            Notifications notification = notificationService.createNotification(
                employee.getId(),
                3, // LeadUncaredWarning type
                title,
                content,
                customer.getId(),
                null // System-generated notification
            );

            logger.debug("Created uncared lead notification {} for employee {} about customer {} ({})", 
                        notification.getId(), employee.getId(), customer.getId(), customer.getFullName());

            return true;

        } catch (Exception e) {
            logger.error("Error processing uncared lead notification for assignment {}: {}", 
                        assignment.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Create notification content for uncared lead warning
     * @param customer Customer details
     * @param assignedFrom Assignment date
     * @return Formatted notification content
     */
    private String createUncaredLeadNotificationContent(Customers customer, Date assignedFrom) {
        try {
            // Format assignment date
            LocalDate assignmentDate = assignedFrom.toInstant()
                .atZone(ZoneId.of(TIMEZONE_VIETNAM))
                .toLocalDate();
            String formattedDate = assignmentDate.format(DATE_FORMATTER);

            // Create content with customer name, phone, and assignment date
            return String.format(
                "Lead %s (SĐT: %s) đã được phân công cho bạn từ ngày %s nhưng chưa có tương tác nào. " +
                "Hãy liên hệ khách hàng sớm nhất có thể.",
                customer.getFullName() != null ? customer.getFullName() : "N/A",
                customer.getPhone() != null ? customer.getPhone() : "N/A",
                formattedDate
            );

        } catch (Exception e) {
            logger.error("Error creating notification content for customer {}: {}", customer.getId(), e.getMessage(), e);
            return String.format(
                "Lead %s đã được phân công cho bạn nhưng chưa có tương tác nào. Hãy liên hệ khách hàng sớm nhất có thể.",
                customer.getFullName() != null ? customer.getFullName() : "N/A"
            );
        }
    }
}
