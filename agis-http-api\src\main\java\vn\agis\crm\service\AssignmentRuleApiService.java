package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.req.LeadRuleDto;
import vn.agis.crm.base.jpa.entity.LeadRule;
import vn.agis.crm.base.jpa.dto.res.LeadRuleResDto;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.List;

@Service
public class AssignmentRuleApiService extends CrudService<LeadRule, Long> {

    private static final Logger logger = LoggerFactory.getLogger(AssignmentRuleApiService.class);

    public AssignmentRuleApiService() {
        super(LeadRule.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.ASSIGNMENT_RULE;
    }

    @SuppressWarnings("unchecked")
    public List<LeadRuleResDto> getAllRules() {
        Event event = RequestUtils.amqp(Constants.Method.GET_ALL_RULES, category, null, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (List<LeadRuleResDto>) event.payload;
        }
        logger.error("Failed to get all rules, status: {}, message: {}", event.respStatusCode, event.respErrorDesc);
        return null;
    }

    public LeadRuleResDto createRule(LeadRuleDto dto) {
        Event event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, convertToEntity(dto), routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (LeadRuleResDto) event.payload;
        }
        return null;
    }

    public LeadRuleResDto updateRule(Long id, LeadRuleDto dto) {
        dto.setId(id);
        Event event = RequestUtils.amqp(JpaConstants.Method.UPDATE, category, convertToEntity(dto), routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (LeadRuleResDto) event.payload;
        }
        return null;
    }

    public LeadRuleResDto getOneWithStaff(Long id) {
        Event event = RequestUtils.amqp(JpaConstants.Method.GET_ONE, category, id, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (LeadRuleResDto) event.payload;
        }
        return null;
    }


    public void updatePriority(vn.agis.crm.base.jpa.dto.req.UpdateRulePriorityRequest request) {
        Event event = RequestUtils.amqp(Constants.Method.UPDATE_RULE_PRIORITY, category, request, routingKey);
        if (event.respStatusCode != ResponseCode.OK) {
            logger.error("Failed to update rule priority, status: {}, message: {}", event.respStatusCode, event.respErrorDesc);
            // Consider throwing an exception here
        }
    }

    // Helper to convert DTO to Entity
    private LeadRule convertToEntity(LeadRuleDto dto) {
        LeadRule entity = new LeadRule();
        entity.setId(dto.getId());
        entity.setName(dto.getName());
        entity.setPriority(dto.getPriority());
        entity.setManagerId(dto.getManagerId());
        entity.setStaffId(dto.getStaffId());
        entity.setConflictPolicy(dto.getConflictPolicy());
        entity.setIsActive(dto.getIsActive());
        // Convert conditions map to JSON string for storage
        if (dto.getConditions() != null) {
            entity.setConditions(ObjectMapperUtil.toJsonString(dto.getConditions()));
        }
        return entity;
    }
}

