package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.*;
import vn.agis.crm.util.RequestUtils;

import java.util.HashMap;
import java.util.Map;



@Service
public class ImportJobService {

    private static final Logger logger = LoggerFactory.getLogger(ImportJobService.class);
    private final String routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
    private final String category = Category.IMPORT_JOB;

    /**
     * Create import job from web upload
     */
    public ImportJobDto createFromWebUpload(MultipartFile file, String options, Long createdBy) {
        try {
            // Convert MultipartFile to serializable DTO to avoid Gson serialization issues
            FileUploadDto fileDto = new FileUploadDto(
                file.getBytes(),
                file.getOriginalFilename(),
                file.getContentType(),
                file.getSize(),
                options
            );

            Event event = RequestUtils.amqp(Constants.Method.CREATE_IMPORT_JOB_WEB_UPLOAD, category, fileDto, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (ImportJobDto) event.payload;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, event.respErrorDesc);
            } else {
                throw new RuntimeException("Error creating import job: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in createFromWebUpload: {}", e.getMessage(), e);
            throw new RuntimeException("Error creating import job", e);
        }
    }



    /**
     * Get import job by ID
     */
    public ImportJobDto getById(Long id, Long userId) {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_IMPORT_JOB, category, id, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (ImportJobDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new ResourceNotFoundException("Import job not found", category, id.toString(), "Import job not found");
            } else {
                throw new RuntimeException("Error retrieving import job: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getById: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving import job", e);
        }
    }

    /**
     * Get Excel sheets for import job
     */
    public ExcelSheetDto getExcelSheets(Long id, Long userId) {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_EXCEL_SHEETS, category, id, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (ExcelSheetDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new ResourceNotFoundException("Import job not found", category, id.toString(), "Import job not found");
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, id.toString(), event.respErrorDesc);
            } else {
                throw new RuntimeException("Error retrieving Excel sheets: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getExcelSheets: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving Excel sheets", e);
        }
    }

    /**
     * Get file metadata for import job
     */
    public FileMetadataDto getFileMetadata(Long id, Long userId) {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_FILE_METADATA, category, id, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (FileMetadataDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new ResourceNotFoundException("Import job not found", category, id.toString(), "Import job not found");
            } else {
                throw new RuntimeException("Error retrieving file metadata: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getFileMetadata: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving file metadata", e);
        }
    }

    /**
     * Get supported formats information
     */
    public SupportedFormatsDto getSupportedFormats() {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_SUPPORTED_FORMATS, category, null, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (SupportedFormatsDto) event.payload;
            } else {
                throw new RuntimeException("Error retrieving supported formats: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getSupportedFormats: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving supported formats", e);
        }
    }

    /**
     * Start dry-run validation for import job
     */
    public ImportJobDto startDryRun(Long id, Long userId) {
        try {
            Event event = RequestUtils.amqp(Constants.Method.START_DRY_RUN, category, id, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (ImportJobDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new IllegalArgumentException(event.respErrorDesc);
            } else {
                throw new RuntimeException("Error starting dry-run: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in startDryRun: {}", e.getMessage(), e);
            throw new RuntimeException("Error starting dry-run", e);
        }
    }

    /**
     * Get dry-run validation results
     */
    public DryRunResultDto getDryRunResult(Long id, Long userId) {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_DRY_RUN_RESULT, category, id, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return (DryRunResultDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else {
                throw new RuntimeException("Error retrieving dry-run result: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getDryRunResult: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving dry-run result", e);
        }
    }

    /**
     * Get import errors with pagination and filtering
     */
    public Object getImportErrors(Long id, Long userId, int page, int size, java.util.Map<String, Object> filters) {
        try {
            java.util.Map<String, Object> payload = new java.util.HashMap<>();
            payload.put("id", id);
            payload.put("page", page);
            payload.put("size", size);
            payload.put("filters", filters);

            Event event = RequestUtils.amqp(Constants.Method.GET_IMPORT_ERRORS, category, payload, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                return event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else {
                throw new RuntimeException("Error retrieving import errors: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getImportErrors: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving import errors", e);
        }
    }

    // ================================
    // STEP 3: CONFIRMATION & EXECUTION
    // ================================

    /**
     * Confirm and start import execution
     */
    public ImportProgressDto confirmImport(Long jobId, String options, Long userId) {
        try {
            logger.info("Confirming import for job {} by user {}", jobId, userId);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("jobId", jobId);
            requestData.put("options", options);
            requestData.put("userId", userId);

            Event event = RequestUtils.amqp(Constants.Method.CONFIRM_IMPORT, category, requestData, routingKey);

            if (event.respStatusCode == 200) {
                return (ImportProgressDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else if (event.respStatusCode == 409) {
                throw new IllegalStateException(event.respErrorDesc != null ? event.respErrorDesc : "Job is not in the correct state for execution");
            } else if (event.respStatusCode == 400) {
                throw new IllegalArgumentException(event.respErrorDesc != null ? event.respErrorDesc : "Invalid request");
            } else {
                throw new RuntimeException("Error confirming import: " + event.respErrorDesc);
            }

        } catch (IllegalArgumentException | IllegalStateException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error in confirmImport: {}", e.getMessage(), e);
            throw new RuntimeException("Error confirming import", e);
        }
    }

    /**
     * Get import progress
     */
    public ImportProgressDto getImportProgress(Long jobId, Long userId) {
        try {
            logger.debug("Getting import progress for job {} by user {}", jobId, userId);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("jobId", jobId);
            requestData.put("userId", userId);

            Event event = RequestUtils.amqp(Constants.Method.GET_IMPORT_PROGRESS, category, requestData, routingKey);

            if (event.respStatusCode == 200) {
                return (ImportProgressDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else {
                throw new RuntimeException("Error retrieving import progress: " + event.respErrorDesc);
            }

        } catch (IllegalArgumentException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error in getImportProgress: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving import progress", e);
        }
    }

    /**
     * Cancel running import
     */
    public ImportProgressDto cancelImport(Long jobId, String reason, Long userId) {
        try {
            logger.info("Cancelling import for job {} by user {} with reason: {}", jobId, userId, reason);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("jobId", jobId);
            requestData.put("reason", reason);
            requestData.put("userId", userId);

            Event event = RequestUtils.amqp(Constants.Method.CANCEL_IMPORT, category, requestData, routingKey);

            if (event.respStatusCode == 200) {
                return (ImportProgressDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else if (event.respStatusCode == 409) {
                throw new IllegalStateException(event.respErrorDesc != null ? event.respErrorDesc : "Job is not in a cancellable state");
            } else if (event.respStatusCode == 400) {
                throw new IllegalArgumentException(event.respErrorDesc != null ? event.respErrorDesc : "Invalid request");
            } else {
                throw new RuntimeException("Error cancelling import: " + event.respErrorDesc);
            }

        } catch (IllegalArgumentException | IllegalStateException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error in cancelImport: {}", e.getMessage(), e);
            throw new RuntimeException("Error cancelling import", e);
        }
    }

    /**
     * Get import result
     */
    public ImportResultDto getImportResult(Long jobId, Long userId) {
        try {
            logger.debug("Getting import result for job {} by user {}", jobId, userId);

            Map<String, Object> requestData = new HashMap<>();
            requestData.put("jobId", jobId);
            requestData.put("userId", userId);

            Event event = RequestUtils.amqp(Constants.Method.GET_IMPORT_RESULT, category, requestData, routingKey);

            if (event.respStatusCode == 200) {
                return (ImportResultDto) event.payload;
            } else if (event.respStatusCode == 404) {
                throw new IllegalArgumentException("Import job not found");
            } else if (event.respStatusCode == 409) {
                throw new IllegalStateException(event.respErrorDesc != null ? event.respErrorDesc : "Import job is not yet completed");
            } else {
                throw new RuntimeException("Error retrieving import result: " + event.respErrorDesc);
            }

        } catch (IllegalArgumentException | IllegalStateException e) {
            throw e;
        } catch (Exception e) {
            logger.error("Error in getImportResult: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving import result", e);
        }
    }
}
