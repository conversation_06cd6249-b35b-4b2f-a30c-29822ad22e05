//package vn.agis.crm.controller;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
////import vn.agis.crm.limiter.dynamic.DynamicRateLimiter;
//
//import java.util.concurrent.TimeUnit;
//@RestController
//@RequestMapping("/demo")
//public class DemoController {
//
//    @GetMapping("/test")
//    //limiting based on userid， and an unique user can visit twice per one minute，userid was contained in the http header
//    //RateLimiter annotation that means this configuration can not be change in runtime
////    @DynamicRateLimiter(base = "#Headers['userid']", permits = 1, timeUnit = TimeUnit.MINUTES)
//    public String test() {
//        return "test!";
//    }
//
////    @GetMapping("/dynamictest")
////    //limiting based on user's IP, and an unique IP can visit five times per one minute, so 'X-Real-IP' is key of header for IP
////    //DynamicRateLimiter annotation that means this configuration can be changed dynamically in runime
////    @DynamicRateLimiter(base = "#Headers['X-Real-IP']", permits = 5, timeUnit = TimeUnit.MINUTES)
////    public String dynamicTest() {
////        return "dynamictest!";
////    }
//
//}
