package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

import java.text.SimpleDateFormat;
import java.util.Objects;

@Getter
@Setter
public class DeviceSearchDTO {
    private String deviceName;
    private String serialNumber;
    private String imei;
    private String msisdn;
    private String deviceType;
    private String model;
    private Long enterpriseUserId;
    private Long customerUserId;
    private Integer connectionStatus;
    private Integer page;
    private Integer size;

    private String sortBy;
    public DeviceSearchDTO(
            String deviceName,
            String serialNumber,
            String imei,
            String msisdn,
            String deviceType,
            String model,
            Long enterpriseUserId,
            Long customerUserId,
            Integer connectionStatus,
            Integer page,
            Integer size,
            String sortBy) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        this.deviceName = Objects.isNull(deviceName) ? "" : SqlUtils.optimizeSearchLike(deviceName);
        this.serialNumber = Objects.isNull(serialNumber) ? " " : SqlUtils.optimizeSearchLike(serialNumber);
        this.imei = Objects.isNull(imei) ? " " : SqlUtils.optimizeSearchLike(imei);
        this.msisdn = Objects.isNull(msisdn) ? " " : SqlUtils.optimizeSearchLike(msisdn);
        this.deviceType = Objects.isNull(deviceType) ? null : SqlUtils.optimizeSearchLike(deviceType);
        this.model = Objects.isNull(model) ? null : SqlUtils.optimizeSearchLike(model);
        this.enterpriseUserId = enterpriseUserId;
        this.customerUserId = customerUserId;
        this.connectionStatus = connectionStatus;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
