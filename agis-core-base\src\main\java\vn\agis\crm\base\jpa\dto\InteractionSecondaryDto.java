package vn.agis.crm.base.jpa.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class InteractionSecondaryDto {
    private Long id;
    private BigDecimal expectedSellPrice;
    private BigDecimal expectedRentPrice;
    private String result;
    private String happenedAt; // dd/MM/yyyy in request
    private String notes;
    private Boolean deleted;

    // NEW: Audit fields for tracking who created the interaction
    private Long createdBy;
    private String createdName;
}

