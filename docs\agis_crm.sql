/*
 Navicat Premium Dump SQL

 Source Server         : new db local
 Source Server Type    : MariaDB
 Source Server Version : 101113 (10.11.13-MariaDB)
 Source Host           : localhost:3309
 Source Schema         : agis_crm

 Target Server Type    : MariaDB
 Target Server Version : 101113 (10.11.13-MariaDB)
 File Encoding         : 65001

 Date: 20/09/2025 15:34:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for audit_logs
-- ----------------------------
DROP TABLE IF EXISTS `audit_logs`;
CREATE TABLE `audit_logs`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `table_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `record_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `action` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `performed_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `performed_at` datetime NOT NULL DEFAULT current_timestamp(),
  `before_state` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `after_state` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_audit_logs_performed_by`(`performed_by` ASC) USING BTREE,
  CONSTRAINT `fk_audit_logs_performed_by` FOREIGN KEY (`performed_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for chat_virtual_assistant
-- ----------------------------
DROP TABLE IF EXISTS `chat_virtual_assistant`;
CREATE TABLE `chat_virtual_assistant`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `created_by` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for configs
-- ----------------------------
DROP TABLE IF EXISTS `configs`;
CREATE TABLE `configs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã cấu hình duy nhất',
  `config_type` tinyint(4) NOT NULL COMMENT 'Loại cấu hình: 1 = Giá trị đơn, 2 = Danh sách',
  `config_value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Giá trị cấu hình (string hoặc JSON nếu là danh sách)',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Mô tả cấu hình cho người dùng',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `config_key`(`config_key` ASC) USING BTREE,
  INDEX `fk_configs_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_configs_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_configs_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_configs_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 41 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer_assignments
-- ----------------------------
DROP TABLE IF EXISTS `customer_assignments`;
CREATE TABLE `customer_assignments`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `customer_id` int(10) UNSIGNED NOT NULL COMMENT 'Khách hàng được phân công',
  `employee_id` int(10) UNSIGNED NOT NULL COMMENT 'Nhân viên phụ trách',
  `role_type` tinyint(4) NOT NULL COMMENT 'Vai trò: 1 = Quản lý, 2 = Nhân viên chăm sóc',
  `assigned_from` datetime NULL DEFAULT current_timestamp() COMMENT 'Thời điểm phân công',
  `assigned_to` datetime NULL DEFAULT NULL COMMENT 'Thời điểm kết thúc phân công (NULL nếu đang hoạt động)',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo phân công',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_ca_customer`(`customer_id` ASC) USING BTREE,
  INDEX `fk_ca_employee`(`employee_id` ASC) USING BTREE,
  INDEX `fk_ca_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_ca_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_ca_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_ca_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_ca_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_ca_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer_offers
-- ----------------------------
DROP TABLE IF EXISTS `customer_offers`;
CREATE TABLE `customer_offers`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `customer_id` int(10) UNSIGNED NOT NULL COMMENT 'Khách hàng quan tâm dự án',
  `project_id` int(10) UNSIGNED NOT NULL COMMENT 'Dự án BĐS đang chào bán',
  `first_interaction` datetime NULL DEFAULT NULL COMMENT 'Lần tương tác đầu tiên',
  `last_interaction` datetime NULL DEFAULT NULL COMMENT 'Lần tương tác gần nhất',
  `status` enum('OPEN','CLOSED','CANCELLED') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'OPEN' COMMENT 'Trạng thái chào bán',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Ghi chú khác',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_co_customer`(`customer_id` ASC) USING BTREE,
  INDEX `fk_co_project`(`project_id` ASC) USING BTREE,
  INDEX `fk_co_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_co_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_co_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_co_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_co_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_co_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer_properties
-- ----------------------------
DROP TABLE IF EXISTS `customer_properties`;
CREATE TABLE `customer_properties`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `customer_id` int(10) UNSIGNED NOT NULL COMMENT 'Khách hàng sở hữu',
  `project_id` int(10) UNSIGNED NOT NULL COMMENT 'Dự án liên quan',
  `unit_id` int(10) UNSIGNED NOT NULL COMMENT 'Căn hộ / sản phẩm cụ thể',
  `transaction_date` date NOT NULL COMMENT 'Ngày giao dịch',
  `contract_price` decimal(18, 2) NOT NULL COMMENT 'Giá trị hợp đồng',
  `external_agency_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Tên đại lý ngoài hệ thống',
  `external_sale_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Tên nhân viên sale ngoài hệ thống',
  `external_sale_phone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Số điện thoại nhân viên sale ngoài hệ thống',
  `external_sale_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Email nhân viên sale ngoài hệ thống',
  `employee_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Nhân viên nội bộ đã môi giới BĐS này',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Ghi chú khác',
  `first_interaction` datetime NULL DEFAULT NULL,
  `last_interaction` datetime NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  `legal_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Legal status or compliance information for the customer property (e.g., \"Approved\", \"Pending Review\", \"Compliant\", \"Under Investigation\")',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_cp_customer`(`customer_id` ASC) USING BTREE,
  INDEX `fk_cp_project`(`project_id` ASC) USING BTREE,
  INDEX `fk_cp_unit`(`unit_id` ASC) USING BTREE,
  INDEX `fk_cp_employee`(`employee_id` ASC) USING BTREE,
  INDEX `fk_cp_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_cp_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_cp_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_cp_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_cp_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_cp_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_cp_unit` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_cp_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 20 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customer_relatives
-- ----------------------------
DROP TABLE IF EXISTS `customer_relatives`;
CREATE TABLE `customer_relatives`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `customer_id` int(10) UNSIGNED NOT NULL,
  `relation_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `year_of_birth` int(11) NULL DEFAULT NULL,
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最礀 琀ꄀ漞 瀀栀渀 挀渀最',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最뀀�椞 琀ꄀ漞',
  `updated_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最礀 挀관瀞 渀栀관琞',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '最뀀�椞 挀관瀞 渀栀관琞',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_customer_id`(`customer_id` ASC) USING BTREE,
  CONSTRAINT `fk_rel_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 31 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for customers
-- ----------------------------
DROP TABLE IF EXISTS `customers`;
CREATE TABLE `customers`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `birth_date` date NULL DEFAULT NULL,
  `address_contact` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `address_permanent` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `nationality` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `marital_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `total_asset` decimal(18, 2) NULL DEFAULT NULL,
  `business_field` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `avatar_url` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `zalo_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `facebook_link` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `source_type` enum('Data','Leads','Event','Refer') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Data',
  `source_detail` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `deleted_at` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `current_manager_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'ID Manager đang active (để truy vấn nhanh)',
  `current_staff_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'ID Staff đang active (để truy vấn nhanh)',
  `cccd` varchar(12) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Citizen Identity Card number (CCCD)',
  `additional_phones` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'JSON array storing additional phone numbers for the customer',
  `additional_emails` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'JSON array storing additional email addresses for the customer',
  `additional_cccds` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'JSON array storing additional CCCD (Citizen Identity Card) numbers for the customer',
  `interests` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'JSON array storing customer interests and preferences (e.g., [\"Real Estate Investment\", \"Luxury Properties\"])',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_customer_phone`(`phone` ASC) USING BTREE,
  INDEX `idx_source_type`(`source_type` ASC) USING BTREE,
  INDEX `fk_customers_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_customers_updated_by`(`updated_by` ASC) USING BTREE,
  INDEX `fk_cust_manager`(`current_manager_id` ASC) USING BTREE,
  INDEX `fk_cust_staff`(`current_staff_id` ASC) USING BTREE,
  FULLTEXT INDEX `ft_customers_name`(`full_name`),
  CONSTRAINT `fk_cust_manager` FOREIGN KEY (`current_manager_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_cust_staff` FOREIGN KEY (`current_staff_id`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_customers_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_customers_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = 'Customer information including personal details and CCCD' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for employees
-- ----------------------------
DROP TABLE IF EXISTS `employees`;
CREATE TABLE `employees`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mã nhân viên',
  `full_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Họ tên đầy đủ',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Số điện thoại',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Email',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Mật khẩu (hash)',
  `role_id` int(10) UNSIGNED NOT NULL COMMENT 'Vai trò của nhân viên (FK tới roles)',
  `status` enum('active','inactive') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'active' COMMENT 'Trạng thái làm việc',
  `deleted_at` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_employee_code`(`employee_code` ASC) USING BTREE,
  UNIQUE INDEX `phone`(`phone` ASC) USING BTREE,
  UNIQUE INDEX `email`(`email` ASC) USING BTREE,
  INDEX `fk_employee_role`(`role_id` ASC) USING BTREE,
  CONSTRAINT `fk_employee_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for import_job_errors
-- ----------------------------
DROP TABLE IF EXISTS `import_job_errors`;
CREATE TABLE `import_job_errors`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `import_job_id` int(10) UNSIGNED NOT NULL COMMENT 'Liên kết với job import (import_jobs)',
  `row_num` int(11) NOT NULL COMMENT 'Số dòng trong file bị lỗi',
  `column_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Tên cột bị lỗi',
  `error_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Loại lỗi (thiếu dữ liệu, sai định dạng, ...)',
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Mô tả chi tiết lỗi',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo log lỗi',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_ije_job`(`import_job_id` ASC) USING BTREE,
  CONSTRAINT `fk_ije_job` FOREIGN KEY (`import_job_id`) REFERENCES `import_jobs` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for import_jobs
-- ----------------------------
DROP TABLE IF EXISTS `import_jobs`;
CREATE TABLE `import_jobs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên file đã import',
  `file_checksum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Mã kiểm tra trùng file',
  `source` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Nguồn import: WEB_UPLOAD, GOOGLE_DRIVE, ...',
  `source_link` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Link tới file (nếu có, VD: Google Drive)',
  `mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'RUN' COMMENT 'Chế độ: DRY_RUN (kiểm tra), RUN (thực thi)',
  `total_rows` int(11) NULL DEFAULT 0 COMMENT 'Tổng số dòng trong file',
  `valid_rows` int(11) NULL DEFAULT 0 COMMENT 'Số dòng hợp lệ',
  `error_rows` int(11) NULL DEFAULT 0 COMMENT 'Số dòng bị lỗi',
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'PENDING' COMMENT 'Trạng thái: PENDING, RUNNING, SUCCESS, FAILED',
  `options` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT 'Cấu hình bổ sung khi import',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo job',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_ij_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_ij_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_ij_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_ij_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for interactions_primary
-- ----------------------------
DROP TABLE IF EXISTS `interactions_primary`;
CREATE TABLE `interactions_primary`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `customer_offer_id` int(10) UNSIGNED NOT NULL COMMENT 'Gắn với bất động sản đang chào bán (customer_offers)',
  `result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Kết quả tương tác sơ cấp (theo cấu hình)',
  `happened_at` datetime NOT NULL COMMENT 'Thời điểm tương tác',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Ghi chú chi tiết',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Nhân viên ghi nhận',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_ip_offer`(`customer_offer_id` ASC) USING BTREE,
  INDEX `fk_ip_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_ip_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_ip_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_ip_offer` FOREIGN KEY (`customer_offer_id`) REFERENCES `customer_offers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_ip_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for interactions_secondary
-- ----------------------------
DROP TABLE IF EXISTS `interactions_secondary`;
CREATE TABLE `interactions_secondary`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `customer_property_id` int(10) UNSIGNED NOT NULL COMMENT 'Gắn với bất động sản khách đã sở hữu (customer_properties)',
  `expected_sell_price` decimal(18, 2) NULL DEFAULT NULL COMMENT 'Giá bán kỳ vọng (VND)',
  `expected_rent_price` decimal(18, 2) NULL DEFAULT NULL COMMENT 'Giá cho thuê kỳ vọng (VND)',
  `result` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Kết quả tương tác thứ cấp (theo cấu hình)',
  `happened_at` datetime NOT NULL COMMENT 'Thời điểm tương tác',
  `notes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Ghi chú chi tiết',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Nhân viên ghi nhận',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_is_property`(`customer_property_id` ASC) USING BTREE,
  INDEX `fk_is_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_is_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_is_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_is_property` FOREIGN KEY (`customer_property_id`) REFERENCES `customer_properties` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_is_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for lead_rules
-- ----------------------------
DROP TABLE IF EXISTS `lead_rules`;
CREATE TABLE `lead_rules`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tên luật phân bổ',
  `priority` int(11) NOT NULL COMMENT 'Độ ưu tiên (cao hơn chạy trước)',
  `conditions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'Điều kiện áp dụng (VD: khu vực, dự án, nguồn khách)',
  `manager_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Hành động: gán cho Manager (có thể null)',
  `staff_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Hành động: gán cho Staff (bắt buộc)',
  `conflict_policy` enum('SKIP','OVERWRITE','OVERWRITE_IF_STAFF_EMPTY') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'SKIP' COMMENT 'Xử lý xung đột: SKIP = Bỏ qua, OVERWRITE = Ghi đè, OVERWRITE_IF_STAFF_EMPTY = Chỉ ghi đè nếu chưa có Staff',
  `is_active` tinyint(4) NOT NULL DEFAULT 1 COMMENT 'Trạng thái luật (1 = hoạt động, 0 = tạm dừng)',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người tạo',
  `updated_at` datetime NULL DEFAULT current_timestamp() ON UPDATE CURRENT_TIMESTAMP COMMENT 'Ngày cập nhật',
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người cập nhật',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_lr_manager`(`manager_id` ASC) USING BTREE,
  INDEX `fk_lr_staff`(`staff_id` ASC) USING BTREE,
  INDEX `fk_lr_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_lr_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_lr_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_lr_manager` FOREIGN KEY (`manager_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_lr_staff` FOREIGN KEY (`staff_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_lr_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for notifications
-- ----------------------------
DROP TABLE IF EXISTS `notifications`;
CREATE TABLE `notifications`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `target_employee_id` int(10) UNSIGNED NOT NULL COMMENT 'Nhân viên nhận thông báo',
  `target_customer_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Khách hàng liên quan (nếu có)',
  `type` tinyint(4) NOT NULL COMMENT 'Loại thông báo: 1 = LeadAssigned, 2 = CustomerBirthday, 3 = LeadUncaredWarning, 4 = LeadInactiveWarning',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'Tiêu đề ngắn gọn của thông báo',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Nội dung chi tiết',
  `is_read` tinyint(4) NOT NULL DEFAULT 0 COMMENT 'Trạng thái đã đọc: 0 = chưa đọc, 1 = đã đọc',
  `read_at` datetime NULL DEFAULT NULL COMMENT 'Thời điểm đọc (nếu có)',
  `created_at` datetime NULL DEFAULT current_timestamp() COMMENT 'Ngày tạo thông báo',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người/tiến trình tạo (nếu có)',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_notif_employee`(`target_employee_id` ASC) USING BTREE,
  INDEX `fk_notif_customer`(`target_customer_id` ASC) USING BTREE,
  INDEX `fk_notif_created_by`(`created_by` ASC) USING BTREE,
  CONSTRAINT `fk_notif_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_notif_customer` FOREIGN KEY (`target_customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_notif_employee` FOREIGN KEY (`target_employee_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for permissions
-- ----------------------------
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(150) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for projects
-- ----------------------------
DROP TABLE IF EXISTS `projects`;
CREATE TABLE `projects`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_projects_name`(`name` ASC) USING BTREE,
  INDEX `fk_projects_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_projects_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_projects_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_projects_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for query_suggest
-- ----------------------------
DROP TABLE IF EXISTS `query_suggest`;
CREATE TABLE `query_suggest`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `created_by` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for query_virtual_assistant
-- ----------------------------
DROP TABLE IF EXISTS `query_virtual_assistant`;
CREATE TABLE `query_virtual_assistant`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `question` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `answer` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `chat_id` bigint(20) NULL DEFAULT NULL,
  `created_at` datetime NULL DEFAULT NULL,
  `created_by` bigint(20) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for role_permissions
-- ----------------------------
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions`  (
  `role_id` int(10) UNSIGNED NOT NULL,
  `permission_id` int(10) UNSIGNED NOT NULL,
  PRIMARY KEY (`role_id`, `permission_id`) USING BTREE,
  INDEX `fk_rp_permission`(`permission_id` ASC) USING BTREE,
  CONSTRAINT `fk_rp_permission` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_rp_role` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `is_active` tinyint(1) NULL DEFAULT 1,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rule_job_history
-- ----------------------------
DROP TABLE IF EXISTS `rule_job_history`;
CREATE TABLE `rule_job_history`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `job_started_at` datetime NOT NULL COMMENT 'Thời điểm job bắt đầu chạy',
  `job_finished_at` datetime NULL DEFAULT NULL COMMENT 'Thời điểm job kết thúc',
  `status` enum('RUNNING','COMPLETED','FAILED') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Trạng thái job',
  `total_leads_assigned` int(11) NOT NULL DEFAULT 0 COMMENT 'Tổng số lead đã được phân công trong job này',
  `triggered_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'SYSTEM' COMMENT 'Nguồn kích hoạt (SYSTEM, MANUAL)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'Lịch sử các lần chạy job phân lead tự động' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for rule_runs
-- ----------------------------
DROP TABLE IF EXISTS `rule_runs`;
CREATE TABLE `rule_runs`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Khóa chính',
  `job_id` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Liên kết tới job cha',
  `rule_id` int(10) UNSIGNED NOT NULL COMMENT 'Luật phân bổ được áp dụng',
  `customer_id` int(10) UNSIGNED NOT NULL COMMENT 'Khách hàng được phân bổ',
  `employee_id` int(10) UNSIGNED NOT NULL COMMENT 'Nhân viên được gán',
  `assigned_role` tinyint(4) NOT NULL COMMENT 'Vai trò: 1 = Manager, 2 = Staff',
  `executed_at` datetime NOT NULL DEFAULT current_timestamp() COMMENT 'Thời điểm thực hiện phân bổ',
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL COMMENT 'Người/tiến trình thực hiện',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `fk_rr_rule`(`rule_id` ASC) USING BTREE,
  INDEX `fk_rr_customer`(`customer_id` ASC) USING BTREE,
  INDEX `fk_rr_employee`(`employee_id` ASC) USING BTREE,
  INDEX `fk_rr_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_rr_job`(`job_id` ASC) USING BTREE,
  CONSTRAINT `fk_rr_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_rr_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_rr_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_rr_job` FOREIGN KEY (`job_id`) REFERENCES `rule_job_history` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_rr_rule` FOREIGN KEY (`rule_id`) REFERENCES `lead_rules` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for units
-- ----------------------------
DROP TABLE IF EXISTS `units`;
CREATE TABLE `units`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `project_id` int(10) UNSIGNED NOT NULL,
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
  `product_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Loại sản phẩm',
  `sector` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `area` decimal(10, 2) NOT NULL,
  `door_direction` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'Khác',
  `view` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `contract_price` decimal(18, 2) NOT NULL DEFAULT 0.00,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `deleted_at` datetime NULL DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `created_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `updated_at` datetime NULL DEFAULT NULL,
  `updated_by` int(10) UNSIGNED NULL DEFAULT NULL,
  `floor_area` decimal(10, 2) NULL DEFAULT NULL COMMENT 'Floor construction area in square meters (dien_tich_san_xay_dung/dien_tich_thong_thuy)',
  `floor_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Floor number where the unit is located (tang)',
  `unit_number` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Unit number/apartment number (can_so)',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uq_project_code`(`project_id` ASC, `code` ASC) USING BTREE,
  INDEX `idx_project_id`(`project_id` ASC) USING BTREE,
  INDEX `fk_units_created_by`(`created_by` ASC) USING BTREE,
  INDEX `fk_units_updated_by`(`updated_by` ASC) USING BTREE,
  CONSTRAINT `fk_units_created_by` FOREIGN KEY (`created_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `fk_units_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE RESTRICT ON UPDATE CASCADE,
  CONSTRAINT `fk_units_updated_by` FOREIGN KEY (`updated_by`) REFERENCES `employees` (`id`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- View structure for vw_current_assignments
-- ----------------------------
DROP VIEW IF EXISTS `vw_current_assignments`;
CREATE ALGORITHM = UNDEFINED SQL SECURITY DEFINER VIEW `vw_current_assignments` AS select `ca`.`customer_id` AS `customer_id`,`ca`.`role_type` AS `role_type`,`ca`.`employee_id` AS `employee_id`,`ca`.`assigned_from` AS `assigned_from` from `customer_assignments` `ca` where `ca`.`assigned_to` is null ;

SET FOREIGN_KEY_CHECKS = 1;
