package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.DeviceTypeSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchDeviceTypeResponse;
import vn.agis.crm.base.jpa.entity.DeviceType;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.List;

@Service
public class DeviceTypeService {
    
    private Logger logger = LoggerFactory.getLogger(DeviceTypeService.class);
    private String routingKey;
    private String category;

    public DeviceTypeService() {
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_DEVICE_MANAGEMENT;
        this.category = Constants.Category.DEVICE_TYPE;
    }
    
    public Page<SearchDeviceTypeResponse> searchDeviceType(DeviceTypeSearchDTO reqDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<SearchDeviceTypeResponse> response = new ArrayList<>();
        
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DEVICE_TYPE, category, reqDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchDeviceTypeResponse.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else {
                // Handle error case
                logger.error("Error searching device types: {}", event.respErrorDesc);
            }
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        } catch (Exception e) {
            logger.error("Error in searchDeviceType: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }

    public Page<SearchDeviceTypeResponse> searchDeviceTypeDistinct(DeviceTypeSearchDTO reqDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        List<SearchDeviceTypeResponse> response = new ArrayList<>();

        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_DEVICE_TYPE_DISTINCT, category, reqDTO, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), SearchDeviceTypeResponse.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else {
                // Handle error case
                logger.error("Error searching device types: {}", event.respErrorDesc);
            }
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        } catch (Exception e) {
            logger.error("Error in searchDeviceType: {}", e.getMessage(), e);
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }
    }
    public List<DeviceType> getByCode(String typeCode) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();

        try {
            event = RequestUtils.amqp(Constants.Method.GET_BY_TYPE_CODE, category, typeCode, routingKey);
            timeResponse = System.currentTimeMillis();

            if (event.respStatusCode == ResponseCode.OK) {
                return ObjectMapperUtil.listMapper(String.valueOf(event.payload), DeviceType.class);
            } else {
                logger.error("Error getting device types by code: {}", event.respErrorDesc);
            }
            return new ArrayList<>();
        } catch (Exception e) {
            logger.error("Error in getByCode: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    public DeviceType get(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        
        try {
            event = RequestUtils.amqp("getOne", category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                return (DeviceType) event.payload;
            } else {
                logger.error("Error getting device type: {}", event.respErrorDesc);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in get: {}", e.getMessage(), e);
            return null;
        }
    }
    
    public DeviceType create(DeviceType entity) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        
        try {
            event = RequestUtils.amqp("create", category, entity, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                return (DeviceType) event.payload;
            } else {
                logger.error("Error creating device type: {}", event.respErrorDesc);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in create: {}", e.getMessage(), e);
            return null;
        }
    }
    
    public DeviceType update(Long id, DeviceType entity) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        
        try {
            entity.setId(id);
            event = RequestUtils.amqp("update", category, entity, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                return (DeviceType) event.payload;
            } else {
                logger.error("Error updating device type: {}", event.respErrorDesc);
            }
            return null;
        } catch (Exception e) {
            logger.error("Error in update: {}", e.getMessage(), e);
            return null;
        }
    }
    
    public void deleteById(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        Event event = new Event();
        
        try {
            event = RequestUtils.amqp("delete", category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode != ResponseCode.OK) {
                logger.error("Error deleting device type: {}", event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in deleteById: {}", e.getMessage(), e);
        }
    }
}

