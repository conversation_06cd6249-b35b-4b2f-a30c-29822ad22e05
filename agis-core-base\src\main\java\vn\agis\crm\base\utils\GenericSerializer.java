package vn.agis.crm.base.utils;

import com.google.gson.Gson;

import java.io.IOException;

/**
 * Created by tiemnd on 12/14/19.
 */
public class GenericSerializer implements Serializer<Object> {

    private Gson mapper;

    public GenericSerializer() {
        mapper = new Gson();
    }

    @Override
    public Object deSerialize(Class type, String value) throws IOException {
        return ObjectMapperUtil.objectMapper(value, type);
    }

    @Override
    public String serialize(Object value) throws Exception {
        return ObjectMapperUtil.toJsonString(value);
    }

    @Override
    public Object deSerializeItem(String value) throws IOException {
        return mapper.fromJson(value, String.class);
    }

    @Override
    public String serializeItem(Object value) throws Exception {
        return mapper.toJson(value);
    }
}
