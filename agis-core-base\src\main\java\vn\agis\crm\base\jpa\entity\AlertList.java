package vn.agis.crm.base.jpa.entity;

import java.util.Date;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.Data;

@Entity
@Table(name = "alert_list")
@Data
public class AlertList {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "ALERT_ID")
    private Long alertId;

    @Column(name = "device_id")
    private Long deviceId;

    @Column(name = "created_at")
    private Long createdAt;

    @Column(name = "detection")
    private String detection;

    public AlertList() {
    }
}