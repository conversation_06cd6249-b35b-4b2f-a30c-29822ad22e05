package vn.agis.crm.model.dto;

import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

@Getter
@Setter
public class CreateAreasRequest {
    
    @NotBlank(message = "Areas name is required")
    @Size(max = 255, message = "Areas name must not exceed 255 characters")
    private String name;
    
    @Size(max = 1000, message = "Description must not exceed 1000 characters")
    private String description;
    
    private Long parentId;
    
    @NotNull(message = "Status is required")
    private Integer status = 1;

    @NotNull(message = "Status is required")
    private Integer type;
    
    public CreateAreasRequest() {
    }
    
    public CreateAreasRequest(String name, String description, Long parentId, Integer status) {
        this.name = name;
        this.description = description;
        this.parentId = parentId;
        this.status = status;
    }
    
    @Override
    public String toString() {
        return "CreateAreasRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", parentId=" + parentId +
                ", status=" + status +
                '}';
    }
}
