package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
public class SearchAlertReqDTO {
    private String name;
    private Integer deviceTypeId;
    private String modelCode;
    private String typeCode;

    private Integer eventType;
    private Integer status;
    private Integer severity;

    private String username;
    private String fromDate;
    private String toDate;

    private Integer page;
    private Integer size;
    private String sortBy;

    public SearchAlertReqDTO(
            String name,
            Integer deviceTypeId,
            String modelCode,
            String typeCode,
            Integer eventType,
            Integer status,
            Integer severity,
            String username,
            Long fromDate,
            Long toDate,
            Integer page,
            Integer size,
            String sortBy) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.username = Objects.isNull(username) ? " " : SqlUtils.optimizeSearchLike(username);
        this.deviceTypeId = Objects.isNull(deviceTypeId) ? -1 : deviceTypeId;
        this.modelCode = Objects.isNull(modelCode) ? " " : SqlUtils.optimizeSearchLike(modelCode);
        this.typeCode = Objects.isNull(typeCode) ? " " : typeCode;
        this.eventType = Objects.isNull(eventType) ? -1 : eventType;
        this.status = Objects.isNull(status) ? -1 : status;
        this.severity = Objects.isNull(severity) ? -1 : severity;

        this.fromDate = Objects.isNull(fromDate) ? " " : formatter.format(new Date(fromDate));
        this.toDate = Objects.isNull(toDate) ? " " : formatter.format(new Date(toDate));

        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
