package vn.agis.crm.base.event;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.event.amqp.AMQPEventBus;
import vn.agis.crm.base.event.amqp.AnnotationProcessor;

import java.util.*;

/**
 * Created by tiemnd on 12/14/19.
 */
public class AMQPService {
    private static final Logger logger = LoggerFactory.getLogger(AMQPService.class);
    private Set<String> listeners = new HashSet<>();
    private Set<String> streamListener = new HashSet<>();
    private ApplicationContext ctx;
    private Map<String, AMQPEventBus> eventBuses = new HashMap<String, AMQPEventBus>();

    public AMQPService(ApplicationContext ctx) {
        this.ctx = ctx;
        SpringContext.setContext(ctx);
        doInitialize();
    }

    public static void initialize(ApplicationContext ctx) {
        SpringContext.setContext(ctx);
        AMQPService service = ctx.getBean(AMQPService.class);
        service.doInitialize();
    }

    private synchronized void doInitialize() {
        logger.info("Initializing ampq listeners ....");
        List<AMQPSubscriber> subscribers = AnnotationProcessor.findSubscribers();
        EventBus eventBus = ctx.getBean(EventBus.class);
        for (AMQPSubscriber subscriber : subscribers) {
            String listenKey = subscriber.getInstanceClass().getSimpleName() + "_" + subscriber.getConsumeMethod().getName();
            if (!listeners.contains(listenKey)) {
                logger.info("Registering handler for routing key: {}, queue: {}", subscriber.getRoutingKey(), subscriber.getQueue());
                eventBus.registerSubscriber(subscriber);
                listeners.add(listenKey);
            }
        }
        List<RabbitStreamSubscriber> rabbitStreamSubscribers = AnnotationProcessor.findStreamSubscribers();
        for (RabbitStreamSubscriber subscriber : rabbitStreamSubscribers) {
            String listenKey = subscriber.getInstanceClass().getSimpleName() + "_" + subscriber.getConsumeMethod().getName();
            if (!listeners.contains(listenKey)) {
                logger.info("Registering handler for stream name: {}", subscriber.getStreamName());
                eventBus.registerStreamSubscriber(subscriber);
                listeners.add(listenKey);
            }
        }
        logger.info("Registered {} amqp listeners {} stream listener", listeners.size(), streamListener.size());
    }
}
