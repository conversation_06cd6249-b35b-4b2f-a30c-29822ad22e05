package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "units", uniqueConstraints = {
    @UniqueConstraint(name = "uq_project_code", columnNames = {"project_id", "code"})
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Units extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "project_id", nullable = false)
    private Long projectId;

    @Column(name = "code", nullable = false, length = 100)
    private String code;


    @Column(name = "product_type", length = 100)
    private String productType;

    @Column(name = "sector", length = 100)
    private String sector;

    @Column(name = "area", nullable = false, precision = 10, scale = 2)
    private BigDecimal area;

    @Column(name = "door_direction", length = 50)
    private String doorDirection = "Khác";

    @Column(name = "view", length = 255)
    private String view;

    // NEW: Unit detail fields
    @Column(name = "floor_area", precision = 10, scale = 2)
    private BigDecimal floorArea;

    @Column(name = "floor_number", length = 50)
    private String floorNumber;

    @Column(name = "unit_number", length = 50)
    private String unitNumber;

    @Column(name = "contract_price", precision = 18, scale = 2, nullable = false)
    private BigDecimal contractPrice = BigDecimal.ZERO;

    @Column(name = "is_active", nullable = false)
    private Boolean isActive = true;

    @Column(name = "deleted_at")
    private Date deletedAt;

    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;
}

