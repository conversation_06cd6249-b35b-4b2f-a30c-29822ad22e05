package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

public class DataInvalidException extends BaseException {
    private static final long serialVersionUID = 5355439102807051567L;

    public DataInvalidException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, Collections.singletonList(field), errorCode);
    }

    public DataInvalidException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }
}
