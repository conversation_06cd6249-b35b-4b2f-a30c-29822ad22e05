Với mỗi dòng dữ liệu

## Bước 1: <PERSON><PERSON><PERSON> tên dự án 

Kiểm tra xem đã có dự án này trong hệ thống chưa? (Chú ý tìm kiếm kiểu không phân biệt hoa thường để tránh tạo trùng trong trường hợp nhân viên fill tên dự án lệch hoa thường so với trong CSDL)

Nếu chưa có thì tạo dự án mới và lấy id của dự án này để xử lý tiếp

Nếu có rồi thì lấy id của dự án đó để xử lý tiếp

## Bước 2: Đọc mã căn

Kiểm tra xem đã có mã căn nào trong dự án đó trong hệ thống chưa? (Chú ý tìm kiếm kiểu không phân biệt hoa thường để tránh tạo trùng)

Nếu chưa có thì tạo căn mới và lấy id của căn này để xử lý tiếp

Nếu có rồi thì lấy id của căn đó để xử lý tiếp. Đồng thời cập nhật bổ sung những dữ liệu mới của căn đó từ file xls đang import vào hệ thống theo nguyên tắc sau:  
\- Nếu trường thông tin nào mà trong file xls có nhưng trong hệ thống đang trống thì sẽ cập nhật luôn theo dữ liệu trong file xls.  
\- Nếu trường thông tin nào mà trong file xls có và trong hệ thống cũng có rồi nhưng khác nhau thì đưa dòng đó vào danh sách dòng cảnh báo với nội dung là:

Thông tin Loại sản phẩm của mã căn NA.GV4-05.04 không khớp với dữ liệu trên hệ thống

Trong file xls: BIỆT THỰ ĐƠN LẬP (GV)

Trên hệ thống: DUPLEX (DUP)

## Bước 3: Đọc thông tin khách hàng

\- Trên giao diện phần mềm thì bắt buộc người dùng phải nhập Họ tên và SĐT của khách hàng. Nhưng khi import từ file xls vào thì cho phép người dùng để trống cột SĐT chỉ cần cột Họ tên thôi.

\- Đầu tiên sẽ dùng SĐT, CCCD và Email để kiểm tra xem có khách hàng nào trong hệ thống sử dụng những thông tin định danh này chưa? Chú ý:  
   \+ SĐT sẽ tìm kiếm ở cả SĐT chính và Các SĐT khác  
   \+ CCCD sẽ tìm kiếm ở cả CCCD chính và Các CCCD khác  
   \+ Email sẽ tìm kiếm ở cả Email chính và Các email khác

Nếu chưa có thì tạo khách hàng mới và gán căn tạo ở bước 2 vào danh sách bất động sản đã sở hữu của khách hàng này. Đồng thời lấy id của khách hàng để xử lý các bước tiếp theo.

Nếu có rồi thì lấy id của khách hàng đó để xử lý tiếp các bước tiếp theo. Đồng thời:

1\. Gán căn tạo ở bước 2 vào danh sách bất động sản đã sở hữu của khách hàng này

2\. Cập nhật bổ sung những dữ liệu mới của khách hàng đó từ file xls đang import vào hệ thống theo nguyên tắc sau:

Với các thông tin định danh (Email, CCCD & SĐT)  
\- Nếu trường thông tin email trong file xls có nhưng trong hệ thống đang trống email chính thì sẽ cập nhật luôn trường email chính theo dữ liệu trong file xls.  
\- Nếu trường thông tin email trong file xls có và trong hệ thống trường email chính cũng có rồi nhưng khác nhau thì sẽ thêm trường thông tin email trong file xls vào trường Các email khác  
(Làm tương tự với CCCD và SĐT) 

Với các thông tin khác  
\- Nếu trường thông tin nào mà trong file xls có nhưng trong hệ thống đang trống thì sẽ cập nhật luôn theo dữ liệu trong file xls.  
\- Nếu trường thông tin nào mà trong file xls có và trong hệ thống cũng có rồi nhưng khác nhau thì đưa dòng đó vào danh sách dòng cảnh báo với nội dung là:

Thông tin Quốc tịch của khách hàng Lê Xuân Thành không khớp với dữ liệu trên hệ thống

Trong file xls: Việt Nam

Trên hệ thống: Hàn Quốc

## Bước 4: Đọc thông tin người thân của khách hàng

Nếu có thông tin thì sẽ ghi nhận thông tin của người thân đó vào bảng danh sách người thân của khách hàng

## Bước 5: Đọc thông tin thứ cấp chuyển nhượng

Nếu có thông tin thì sẽ tạo thêm bản ghi mới trong lịch sử tương tác thứ cấp.

## Bước 6: Đọc thông tin sơ cấp bán mới

Đầu tiên sẽ đọc thông tin dự án đang chào.

Kiểm tra xem đã có dự án này trong hệ thống chưa? (Chú ý tìm kiếm kiểu không phân biệt hoa thường để tránh tạo trùng trong trường hợp nhân viên fill tên dự án lệch hoa thường so với trong CSDL)

Nếu chưa có thì tạo dự án mới và lấy id của dự án này để gán vào danh sách dự án đang chào của khách hàng đã tạo hoặc tìm được ở bước 2\.

Nếu có rồi thì lấy id của dự án đó để gán vào danh sách dự án đang chào của khách hàng đã tạo hoặc tìm được ở bước 2\.

Sau đó, sẽ đọc các trường thông tin còn lại để tạo bản ghi mới trong lịch sử tương tác sơ cấp. 

## Bước 7: Đọc thông tin của sales ngoài đã bán sản phẩm này.

Nếu mối quan hệ sở hữu giữa căn được tìm/tạo ở bước 2 và khách hàng được tìm/tạo ở bước 3 là mối quan hệ mới được gán thì sẽ cập nhật các thông tin của sales ngoài vào mối quan hệ này luôn.

Còn nếu là mối quan hệ có từ trước trong hệ thống thì. 

\- Nếu trường thông tin nào mà trong file xls có nhưng trong hệ thống đang trống thì sẽ cập nhật luôn theo dữ liệu trong file xls.

\- Nếu trường thông tin nào mà trong file xls có và trong hệ thống cũng có rồi nhưng khác nhau thì đưa dòng đó vào danh sách dòng cảnh báo với nội dung là:

Thông tin Tên đại lý đã bán mã căn NA.GV4-05.04 cho khách hàng Lê Xuân Thành không khớp với dữ liệu trên hệ thống

Trong file xls: AUREAL

Trên hệ thống: PHÁT ĐẠT REAL

## Bước 8: Đọc thông tin của sale được phân khách để chăm

Đầu tiên sẽ dùng Mã số nhân viên & Email để kiểm tra xem có nhân viên nào trong hệ thống sử dụng những thông tin định danh này chưa? 

Nếu chưa thì tạo tài khoản trên hệ thống cho nhân viên và lấy id này để phân quyền CSKH với khách hàng đã được tìm/tạo ở bước 3

Nếu có rồi thì sẽ gán quyền CSKH với khách hàng đã được tìm/tạo ở bước 3

