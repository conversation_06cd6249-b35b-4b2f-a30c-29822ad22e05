package vn.agis.crm.service;

import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.NotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.event.Event;

import vn.agis.crm.base.jpa.dto.req.KeyValuePair;
import vn.agis.crm.base.jpa.entity.AbstractEntity;
import vn.agis.crm.base.jpa.entity.Report;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.util.RequestUtils;
import vn.agis.crm.base.utils.ObjectMapperUtil;

import java.io.Serializable;
import java.lang.reflect.ParameterizedType;
import java.util.List;
import java.util.Set;


public class CrudService <T extends AbstractEntity, ID extends Serializable>{
    protected static Logger logger = LoggerFactory.getLogger(CrudService.class);

    protected int timeout = 30000;
    private final Class<T> typeParameterClass;
    protected String routingKey;
    protected String category;
    private final Class<ID> IDClass;

    @Autowired
    MessageSource messageSource;

    public CrudService(Class<T> typeParameterClass) {
        this.typeParameterClass = typeParameterClass;
        this.IDClass = (Class<ID>) ((ParameterizedType) getClass()
                .getGenericSuperclass()).getActualTypeArguments()[1];
    }

    public Class<T> getTypeParameterClass() {
        return typeParameterClass;
    }
    public T get(ID id){
        logger.info("Get Entity #{} with id #{}", typeParameterClass.getSimpleName(), id);
        T response = null;
        Event event = new Event();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {

            event = RequestUtils.amqp(JpaConstants.Method.GET_ONE,category,id,routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                response = (T) event.payload;
                return response;
            } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND){
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode.intValue() == ResponseCode.FORBIDDEN){
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        }finally {
            String responseString = ObjectMapperUtil.toJsonString(response);
            if (response instanceof Report) {
                JSONObject jsonObject = new JSONObject(responseString);
                jsonObject.remove("reportContents");
                responseString = jsonObject.toString();
            }
            writeLogDetail(id, response, timeRequest, timeResponse, null, responseString, event);
        }
    }
    public List<T> getMany(List<ID> ids){
        logger.info("Get Entity #{} with id #{}", typeParameterClass.getSimpleName(), ids);
        List<T> response = null;
        Event event = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        try {
            event = RequestUtils.amqp
                    (JpaConstants.Method.GET_MANY,category, ids, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode.intValue() ==ResponseCode.OK) {
                response = ObjectMapperUtil.listMapper(String.valueOf(event.payload), typeParameterClass);
                return response;
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        }finally {
            writeLog(timeRequest,timeResponse,ObjectMapperUtil.toJsonString(ids),ObjectMapperUtil.toJsonString(response),event);
        }

    }

    public T create(T entity) {
        logger.info("Create Entity #{}", typeParameterClass.getSimpleName());
        T response = null;
        Event event = new Event();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {
             event = RequestUtils.amqp
                    (JpaConstants.Method.CREATE,category, entity, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode.intValue() ==ResponseCode.OK) {
                response = (T) event.payload;
                return response;
            } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
            }
            return null;
        }finally {
            String responseString = ObjectMapperUtil.toJsonString(response);
            if (response instanceof Report) {
                JSONObject jsonObject = new JSONObject(responseString);
                jsonObject.remove("reportContents");
                responseString = jsonObject.toString();
            }
            writeLogDetail(null, response, timeRequest, timeResponse, ObjectMapperUtil.toJsonString(entity), responseString, event);
        }

    }

    public T update(ID id, T entity){
        logger.info("Update Entity #{} with id #{}", typeParameterClass.getSimpleName(), id);
        T response = null;
        Event event = new Event();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {
            event = RequestUtils.amqp
                    (JpaConstants.Method.UPDATE,category, entity, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode.intValue() ==ResponseCode.OK) {
                response = (T) event.payload;
                return response;
            } else if (event.respStatusCode.intValue() == ResponseCode.FORBIDDEN){
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (String) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        }finally {
            String responseString = ObjectMapperUtil.toJsonString(response);
            if (response instanceof Report) {
                JSONObject jsonObject = new JSONObject(responseString);
                jsonObject.remove("reportContents");
                responseString = jsonObject.toString();
            }
            writeLogDetail(id, response ,timeRequest,timeResponse,ObjectMapperUtil.toJsonString(entity),responseString,event);
        }

    }

    public ResponseBase deleteById(ID id){
        logger.info("Delete Entity #{} with id #{}", typeParameterClass.getSimpleName(), id);
        T response = null;
        Event event = new Event();
        Long timeRequest = System.currentTimeMillis(), timeResponse = System.currentTimeMillis();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.DELETE,category,id,routingKey);
            timeResponse = System.currentTimeMillis();
            ResponseBase responseBase = new ResponseBase();
            if (event.respStatusCode.intValue() == ResponseCode.OK) {
                responseBase.setErrorCode(event.respStatusCode);
                responseBase.setErrorMsg(event.respErrorDesc);
                return responseBase;
            } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND){
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            }else if (event.respStatusCode.intValue() == ResponseCode.FORBIDDEN){
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        }finally {
            writeLogDetail(id, null ,timeRequest,timeResponse,null,ObjectMapperUtil.toJsonString(response),event);
        }

    }
    public List<ID> batchDelete(Set<ID> ids){
        logger.info("Batch Delete Entity #{} with ids #{}", typeParameterClass.getSimpleName(), ids);
        Event event = RequestUtils.amqp(JpaConstants.Method.DELETE_MANY,category,ids,routingKey);
        if (event.respStatusCode.intValue() ==ResponseCode.OK) {
            return ObjectMapperUtil.listMapper(String.valueOf(event.payload), IDClass);
        } else {
//            throw new PlatformException(event.respErrorDesc, event.respStatusCode);
        }
        return null;
    }
    public long count(String query){
        logger.info("Search Entity #{} with query #{}", typeParameterClass.getSimpleName(), query);
        Event event = RequestUtils.amqp
                (JpaConstants.Method.CHECK_EXITS,category, query, routingKey);
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            return (long) event.payload;
        } else {
//            throw new PlatformException(event.errorText, BigInteger.valueOf(event.statusCode));
        }
        return 0;
    }

    public List<T> getByKey(String key, String value){
        logger.info("Get by Key #{} with key #{}", typeParameterClass.getSimpleName());
        KeyValuePair keyValuePair = new KeyValuePair();
        keyValuePair.setKey(key);
        keyValuePair.setValue(value);
        Event event = RequestUtils.amqp
            (JpaConstants.Method.GET_BY_KEY,category, keyValuePair, routingKey);
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            return (List<T>) event.payload;
        } else {
//            throw new PlatformException(event.errorText, BigInteger.valueOf(event.statusCode));
        }
        return null;
    }

    void writeLog(long timeRequest,long timeResponse,String request,String response,Event event){
    }

    void writeLogDetail(ID id, T entity, long timeRequest,long timeResponse,String request,String response,Event event){
    }
}
