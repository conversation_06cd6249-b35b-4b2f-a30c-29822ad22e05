package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UnitSearchDto {
    private Long projectId; // optional filter
    private String code;    // optional filter
    private String productType; // optional filter
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "createdAt,asc";

    public UnitSearchDto() {}

    public UnitSearchDto(Long projectId, String code, String productType, Integer page, Integer size, String sortBy) {
        this.projectId = projectId;
        this.code = code;
        this.productType = productType;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

