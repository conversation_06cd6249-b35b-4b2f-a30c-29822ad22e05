package vn.agis.crm.repository;

import jakarta.transaction.Transactional;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.QueryVirtualAssistant;

import java.util.List;

@Repository
@Transactional
public interface QueryVirtualAssistantRepository extends JpaRepository<QueryVirtualAssistant, Long> {
    List<QueryVirtualAssistant> findAllByChatId(Long chatId);
    void deleteAllByChatId(Long chatId);

    List<QueryVirtualAssistant> searchByChatIdOrderByIdDesc(Long chatId, Pageable pageable);
}
