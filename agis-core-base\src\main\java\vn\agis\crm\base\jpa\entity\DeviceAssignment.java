package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

@Table(name = "DEVICE_ASSIGNMENT")
@Entity
@Data
public class DeviceAssignment {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "IMEI")
    private String imei;
    @Column(name = "MSISDN")
    private String msisdn;
    @Column(name = "SERIAL_NUMBER")
    private String serialNumber;
    @Column(name = "USER_ENTERPRISE_ID")
    private Long userEnterpriseId;
}
