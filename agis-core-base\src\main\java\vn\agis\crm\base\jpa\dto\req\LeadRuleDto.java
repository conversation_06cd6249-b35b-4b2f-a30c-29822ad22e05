package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import vn.agis.crm.base.jpa.entity.LeadRule.ConflictPolicy;

import java.util.Map;

@Data
public class LeadRuleDto {

    private Long id;

    private String name;

    private Integer priority;

    // Using Map<String, Object> to represent flexible JSON conditions
    private Map<String, Object> conditions;

    private Long managerId;

    private Long staffId;

    private ConflictPolicy conflictPolicy;

    private Boolean isActive;
}

