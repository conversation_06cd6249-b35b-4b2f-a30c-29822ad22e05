/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */
package vn.agis.crm.core.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.redis.ObjectCache;
import vn.agis.crm.base.utils.BeanUtil;

/**
 * Created by HIEUDT on 12/12/2019.
 */
public class MessageMap {

    private static final Logger logger = LoggerFactory.getLogger(MessageMap.class);
    public static final String REQUEST_DOMAIN = "REQUEST_CMD_";
    ObjectCache objectCache;

    MessageMap() {
        try {
            objectCache = BeanUtil.getBean(ObjectCache.class);
        } catch (Exception ex) {
            logger.error("ERROR when init MessageMap: ", ex);
        }
    }


    public static String createKey(String key) {
        return REQUEST_DOMAIN + key;
    }
}
