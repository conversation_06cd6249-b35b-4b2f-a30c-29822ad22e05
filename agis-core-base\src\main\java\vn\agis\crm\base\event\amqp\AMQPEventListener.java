package vn.agis.crm.base.event.amqp;


import com.google.common.base.Strings;
import com.rabbitmq.stream.Environment;
import com.rabbitmq.stream.OffsetSpecification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.AbstractMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.rabbit.listener.adapter.MessageListenerAdapter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.rabbit.stream.config.SuperStream;
import org.springframework.rabbit.stream.listener.StreamListenerContainer;
import org.springframework.rabbit.stream.support.StreamMessageProperties;
import org.springframework.util.ErrorHandler;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.event.AMQPSubscriber;
import vn.agis.crm.base.event.RabbitStreamSubscribers;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.event.RabbitStreamSubscriber;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.LinkedList;
import java.util.Map;

/**
 * Created by tiemnd on 12/14/19.
 */
public class AMQPEventListener extends AMQPAbstractConfiguration {
    private static final Logger logger = LoggerFactory.getLogger(AMQPEventListener.class);
    private Queue queue;
    public AMQPSubscriber subscriber;
    public RabbitStreamSubscriber streamSubscriber;
    private AbstractMessageListenerContainer listenerContainer;
    private StreamListenerContainer streamListenerContainer;
    private ApplicationContext ctx;

    public AbstractMessageListenerContainer getListenerContainer() {
        return listenerContainer;
    }

    public StreamListenerContainer getStreamListenerContainer() {
        return streamListenerContainer;
    }

    public AMQPEventListener(CachingConnectionFactory connectionFactory, ApplicationContext ctx, AMQPSubscriber subscriber) {
        this.CONNECTION_FACTORY = connectionFactory;
        this.subscriber = subscriber;
        this.ctx = ctx;
        this.listenerContainer = messageListenerContainer();
        if ((subscriber.getExchange() != null) && (!subscriber.getExchange().isEmpty())) {
            this.exchangeName = subscriber.getExchange();
        } else {
            this.exchangeName = DEFAULT_EXCHANGE_NAME;
        }
        if (this.subscriber.getExchangeType() == null || this.subscriber.getExchangeType().isBlank() ||
                subscriber.getExchangeType().equals(AMQPConstants.ExchangeType.DIRECT)) {
            amqpAdmin().declareBinding(directBinding());
        } else if (subscriber.getExchangeType().equals(AMQPConstants.ExchangeType.TOPIC)) {
            amqpAdmin().declareBinding(topicBinding());
        } else if (subscriber.getExchangeType().equals(AMQPConstants.ExchangeType.X_DELAY_MESSAGE)) {
            amqpAdmin().declareBinding(xDelayedMessageBinding());
        } else {
            amqpAdmin().declareBinding(customBinding(subscriber.getExchangeType()));
        }
    }

    public AMQPEventListener(CachingConnectionFactory connectionFactory, ApplicationContext ctx, Environment env, RabbitStreamSubscriber subscriber) {
        this.CONNECTION_FACTORY = connectionFactory;
        this.streamSubscriber = subscriber;
        this.ctx = ctx;
        this.streamEnv = env;
        RabbitAdmin rabbitAdmin = ctx.getBean("amqpAdmin", RabbitAdmin.class);
        Map<String, Object> argumentForStream = getArgumentForStream(subscriber.getStreamName());
        // Super stream
        if (subscriber.isSupperStream()) {
            /** Start declare super stream : exchange, stream partition (queue type is stream), binding for exchange **/
            SuperStream superStream = new SuperStream(subscriber.getStreamName(), subscriber.getConcurrency());
            Collection<Exchange> contextExchanges = new LinkedList();
            Collection<Queue> contextQueues = new LinkedList();
            Collection<Binding> contextBindings = new LinkedList<>();
            superStream.getDeclarables().forEach((declarable) -> {
                if (declarable instanceof Exchange) contextExchanges.add((Exchange)declarable);
                else if (declarable instanceof Queue) contextQueues.add((Queue)declarable);
                else if (declarable instanceof Binding) contextBindings.add((Binding)declarable);
            });
            for (Exchange exchange: contextExchanges) rabbitAdmin.declareExchange(exchange);
            for (Queue queue: contextQueues) {
                for(Map.Entry<String, Object> entry: argumentForStream.entrySet()) queue.addArgument(entry.getKey(), entry.getValue());
                rabbitAdmin.declareQueue(queue);
            }
            for (Binding binding: contextBindings) rabbitAdmin.declareBinding(binding);
            // Create consumer
            this.streamListenerContainer = superStreamListenerContainer(ctx, streamEnv, subscriber.getConsumeMethod(), subscriber.getStreamName(), SpringContext.getApplicationId(), 1);
        }
        // Normal stream
        else {
            // Declare normal stream (queue type is stream)
            Queue queue = QueueBuilder.durable(subscriber.getStreamName()).stream().withArguments(argumentForStream).build();
            rabbitAdmin.declareQueue(queue);
            // Create consumer
            this.streamListenerContainer = streamListenerContainer(ctx, streamEnv, subscriber.getConsumeMethod(), subscriber.getStreamName());
        }
    }

    private Binding customBinding(String exchangeType) {
        CustomExchange exchange = customExchange(this.exchangeName, exchangeType);
        amqpAdmin().declareExchange(exchange);
        Binding binding = BindingBuilder.bind(eventQueue()).to(exchange).with(subscriber.getRoutingKey()).noargs();
        return binding;
    }

    private Binding directBinding() {
        DirectExchange exchange = directExchange(this.exchangeName);
        amqpAdmin().declareExchange(exchange);
        Binding binding = BindingBuilder.bind(eventQueue()).to(exchange).with(subscriber.getRoutingKey());
        return binding;
    }

    private Binding xDelayedMessageBinding() {
        CustomExchange exchange = xDelayedMessageExchange(this.exchangeName);
        amqpAdmin().declareExchange(exchange);
        Binding binding = BindingBuilder.bind(eventQueue()).to(exchange).with(subscriber.getRoutingKey()).noargs();
        return binding;
    }

    @Override
    public void configureRabbitTemplate(RabbitTemplate template) {
        template.setExchange(exchangeName);
    }

    public final Binding topicBinding() {
        TopicExchange exchange = topicExchange(this.exchangeName);
        amqpAdmin().declareExchange(exchange);
        Binding binding = BindingBuilder.bind(eventQueue()).to(exchange).with(subscriber.getRoutingKey());
        return binding;
    }

    public final StreamListenerContainer superStreamListenerContainer(ApplicationContext ctx, Environment env, Method consumeMethod, String streamName, String consumerName, int nrOfConsumers) {
        StreamListenerContainer container = new StreamListenerContainer(env);
        container.setApplicationContext(ctx);
        container.superStream(streamName, consumerName, nrOfConsumers); // concurrency = 3
        container.setupMessageListener(msg -> {
            StreamMessageProperties properties = (StreamMessageProperties) msg.getMessageProperties();
            logger.info("Received message ###{}### from stream partition {}", new String(msg.getBody()), properties.getContext().stream());
            Object instance = ctx.getBean(streamSubscriber.getInstanceClass());
            try {
                consumeMethod.invoke(instance, msg.getBody());
            } catch (IllegalAccessException e) {
                logger.error("Error handling event", e);
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                logger.error("Error handling event", e);
                e.printStackTrace();
            };
        });
        switch (streamSubscriber.getOffsetType()) {
            case RabbitStreamSubscribers.TYPE_FIRST:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.first()));
                break;
            case RabbitStreamSubscribers.TYPE_LAST:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.last()));
                break;
            case RabbitStreamSubscribers.TYPE_NEXT:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.next()));
                break;
            case RabbitStreamSubscribers.TYPE_NONE:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.none()));
                break;

        }
        container.setConsumerCustomizer((id, builder) -> builder.autoTrackingStrategy());
        container.afterPropertiesSet();
//        container.start();
        return container;
    }


    public final StreamListenerContainer streamListenerContainer(ApplicationContext ctx, Environment env, Method consumeMethod, String streamName) {
        StreamListenerContainer container = new StreamListenerContainer(env);
        container.setApplicationContext(ctx);
        container.setQueueNames(streamName);
        container.setupMessageListener(msg -> {
            StreamMessageProperties properties = (StreamMessageProperties) msg.getMessageProperties();
            logger.info("Received message ###{}### from stream partition {}", new String(msg.getBody()), properties.getContext().stream());
            Object instance = ctx.getBean(streamSubscriber.getInstanceClass());
            try {
                consumeMethod.invoke(instance, msg.getBody());
            } catch (IllegalAccessException e) {
                logger.error("Error handling event", e);
                e.printStackTrace();
            } catch (InvocationTargetException e) {
                logger.error("Error handling event", e);
                e.printStackTrace();
            };
        });
        switch (streamSubscriber.getOffsetType()) {
            case RabbitStreamSubscribers.TYPE_FIRST:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.first()));
                break;
            case RabbitStreamSubscribers.TYPE_LAST:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.last()));
                break;
            case RabbitStreamSubscribers.TYPE_NEXT:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.next()));
                break;
            case RabbitStreamSubscribers.TYPE_NONE:
                container.setConsumerCustomizer((id, builder) -> builder.offset(OffsetSpecification.none()));
                break;

        }
        container.setConsumerCustomizer((id, builder) -> {
            builder.name(SpringContext.getApplicationId());
            builder.autoTrackingStrategy();
        });
        container.afterPropertiesSet();
        // Khong start o day nua ma de spring boo start xong thi manual start
//        container.start();
        return container;
    }

    public final SimpleMessageListenerContainer messageListenerContainer() {
        RabbitProperties rabbitProperties = SpringContext.getBean(RabbitProperties.class);
        RabbitProperties.SimpleContainer simpleContainer = rabbitProperties!= null?rabbitProperties.getListener().getSimple():null;
        MySimpleMessageListenerContainer container = new MySimpleMessageListenerContainer(connectionFactory());
        if (!Strings.isNullOrEmpty(this.subscriber.getExchangeType()) && this.subscriber.getExchangeType().equals(ExchangeTypes.DIRECT)) {
            container.setMessageListener(messageListenerAdapterForDirectReplyTo());
        } else {
            container.setMessageListener(messageListenerAdapter());
        }
        container.setDefaultRequeueRejected(false);
        container.setErrorHandler(new SimpleErrorHandler());
        container.setQueues(eventQueue());
        if (simpleContainer != null && simpleContainer.getConcurrency() != null && simpleContainer.getConcurrency() > subscriber.getConcurrency()) {
            container.setConcurrentConsumers(simpleContainer.getConcurrency());
        } else if (subscriber.getConcurrency() > 0) {
            container.setConcurrentConsumers(subscriber.getConcurrency());
        }
        if (simpleContainer != null && simpleContainer.getPrefetch() != null) {
            container.setPrefetchCount(simpleContainer.getPrefetch());
        }
        container.afterPropertiesSet();
        // Khong start o day nua ma de spring boo start xong thi manual start
//        container.start();
        return container;
    }

    public Queue eventQueue() {
        if (queue == null) {
            if (subscriber.getQueue().equals("")) {
                queue = amqpAdmin().declareQueue();
            } else {
                queue = new Queue(subscriber.getQueue());
                if (SpringContext.getX_message_ttl() != null)
                    queue.getArguments().put("x-message-ttl", SpringContext.getX_message_ttl());
                queue.getArguments().put("x-queue-type", "quorum");
                amqpAdmin().declareQueue(queue);
            }
        }
        return queue;
    }

    public MessageListenerAdapter messageListenerAdapterForDirectReplyTo() {
        return new CustomMessageListenerAdapter(new MessageHandlerDirectReplyTo(ctx, subscriber), jsonMessageConverter());
    }


    public MessageListenerAdapter messageListenerAdapter() {
        return new CustomMessageListenerAdapter(new MessageHandler(ctx, subscriber), jsonMessageConverter());
    }

    public class SimpleErrorHandler implements ErrorHandler {

        @Override
        public void handleError(Throwable throwable) {
            logger.error(throwable.getMessage(), throwable);
        }
    }

    public MessageConverter jsonMessageConverter() {
        return new JsonMessageConverter(subscriber.getEventType());
    }
}
