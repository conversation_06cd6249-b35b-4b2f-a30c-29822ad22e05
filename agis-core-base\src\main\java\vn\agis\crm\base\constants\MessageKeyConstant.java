package vn.agis.crm.base.constants;

public class MessageKeyConstant {

    public static final String BAD_REQUEST = "bad.request";
    public static final String NOT_FOUND = "error.object.not.found";
    public static final String FORBIDDEN = "error.forbbiden.resource";
    public static final String API_URL_NOT_EDIT = "error.cannot.update.api.url";
    public static final String API_URL_NOT_NULL = "error.api.url.not.null";
    public static final String USER_NOT_FOUND = "error.user.not.found";

    private MessageKeyConstant() {
    }

    public static class Validation {

        public static final String ERROR_LENGTH = "error.length";
        public static final String RANGE = "error.valid.range";
        public static final String SIZE = "error.valid.size";
        public static final String NOT_NULL = "error.valid.not.null";
        public static final String LENGTH = "error.valid.length";
        public static final String ASSERT_FALSE = "error.valid.assert.false";
        public static final String ASSERT_TRUE = "error.valid.assert.true";
        public static final String DECIMAL_MAX = "error.valid.decimal.max";
        public static final String DECIMAL_MIN = "error.valid.decimal.min";
        public static final String DIGITS = "error.valid.digits";
        public static final String EMAIL = "error.valid.email";
        public static final String MAX = "error.valid.max";
        public static final String MIN = "error.valid.min";
        public static final String NEGATIVE = "error.valid.negative";
        public static final String NEGATIVE_OR_ZERO = "error.valid.negative.or.zero";
        public static final String NOT_BLANK = "error.valid.not.blank";
        public static final String NOT_EMPTY = "error.valid.not.empty";
        public static final String NULL = "error.valid.null";
        public static final String PAST = "error.valid.past";
        public static final String PAST_OR_PRESENT = "error.valid.past.or.present";
        public static final String PATTERN = "error.valid.pattern";
        public static final String POSITIVE = "error.valid.positive";
        public static final String POSITIVE_OR_ZERO = "error.valid.positive.or.zero";
        public static final String PHONE = "error.valid.phone";
        public static final String URL = "error.valid.url";
        public static final String DATA_FORMAT = "error.data.format";
        public static final String PATTERN_PHONE = "error.valid.phone.pattern";
        public static final String FIELD_WRONG_JSON_FORMAT = "error.field.wrong.json.format";
        public static final String VALUE = "error.valid.value";
        public static final String INVALID_EMAIL = "error.invalid.email";
        public static final String INVALID_FIELD = "error.invalid.field";
        public static final String INVALID_PASSWORD = "error.invalid.password";
        public static final String ID_MUST_BE_NULL = "id.must.be.null";
        public static final String PASSWORD_DO_NOT_MATCH = "error.passwords.do.not.match";
        public static final String INVALID_TYPE = "invalid.type";
        public static final String EXISTS = "exists";
        public static final String DUPLICATE_NAME = "error.duplicate.name";
        public static final String DUPLICATE_VALUE = "error.duplicate.value";
        public static final String FIELD_MUST_BE_NOT_NULL = "error.field.must.be.not.null";
        public static final String NEW_PASSWORD_MUST_BE_DIFFERENT = "the.new.password.must.be.different.from.the.old.one";
        public static final String INVALID_PHONE_NUMBER = "invalid.phone.number";
        public static final String VALIDATE_OK_MESSSAGE = "error.invalid.ok.message";
        public static final String ISDN_EMPTY = "error.invalid.isdn.empty";
        public static final String ISDN_NOT_FORMAT = "error.invalid.isdn.not.format";
        public static final String ISDN_NOT_PERMISSION = "error.invalid.isdn.not.permission";
        public static final String ISDN_NOT_EXIST = "error.invalid.isdn.not.exist";
        public static final String ISDN_DUPLICATED = "error.invalid.isdn.duplicated";
        public static final String MSISDN_MUST_BE_ACTIVE = "error.invalid.msisdn.not.active";
        public static final String MSISDN_REGISTER_RATE_PENDING = "error.invalid.msisdn.register.rate.pending";
        public static final String RATING_EMPTY = "error.invalid.rating.empty";
        public static final String RATING_NOT_EXIST = "error.invalid.rating.not.exist";
        public static final String RATING_NOT_PERMISSION = "error.invalid.rating.not.permission";
        public static final String RATING_SCOPE_CUSTOMER_NOT_PERMISSION = "error.invalid.rating.customer.not.permission";
        public static final String WRONG_FILE_FORMAT = "error.invalid.file.form.format"; // file sai định dạng isdn, rating_plan
        public static final String INVALID_FILE_FORMAT = "error.invalid.file.form.extension"; // file ko bắt đầu bằng excel
        public static final String INVALID_FILE_MAXROW = "error.invalid.file.form.maxrow"; // file ko bắt đầu bằng excel
        public static final String EMAIL_INCORRECT = "error.emailIncorect";
        public static final String PASSWORD_INCORRECT = "error.passwordIncorect";
        public static final String ACCOUNT_INACTIVE = "error.accountInactive";
        public static final String ACCOUNT_NOT_EXIST = "error.accountNotExist";
        public static final String INVALID_TOKEN = "invalid.token";
        public static final String ROLE_NOT_WORKING = "error.role.not.working";
        public static final String CAN_NOT_UPDATE_PROVINCE_CUSTOMER = "error.cant.not.update.province.customer";
        public static final String EXPIRED_PASSWORD = "error.expiredPassword"; // Mật khẩu hết hạn
        public static final String ISDN_STATUS_PURGE = "error.invalid.isdn.status.purge";
        public static final String PHONE_EXIST_WALLET = "error.exist.phone.wallet";
        public static final String FORBIDDEN_VIEW_DETAIL = "error.forbidden.view.detail";

        private Validation() {
        }
    }

    public static class RatingPlan {
        public static final String SIM_NOTIN_PERMISSION = "register.rate.sim.notin.permission";
        public static final String REGISTER_RATE_FAIL = "register.rate.fail";
        public static final String RATING_PLAN_NOTIN_PERMISSION = "register.rate.ratingplan.notin.permission";
        public static final String RATING_PLAN_NOT_EXIST = "register.rate.ratingplan.not.exist";
        public static final String SIM_NOT_EXIST = "register.rate.sim.not.exist";
        public static final String CONFLICT_PROCESSING = "error.register.rate.in.other.process";

        public static final String GROUP_SIM_EMPTY = "register.rate.groupsim.empty";
        public static final String SIM_STATUS_NOT_ACTIVE = "register.rate.sim.status.not.active";

        private RatingPlan(){

        }
    }
    public static class Report {
        public static final String REPORT_NO_ACCESS_PEREVIEW = "report.no.access.preview";
        public static final String REPORT_QUERY_ERROR = "error.report.query";
        public static final String REPORT_LIMIT_ROW_ERROR = "error.report.limit.row";
        public static final String REPORT_LIMIT_1M = "error.report.excel.limit";

    }

    public static class Sim {
        public static final String EXCEL_LIMIT_100000 = "error.export.excel.limit";
    }

    public static class ProjectDeletion {
        public static final String PROJECT_NOT_FOUND = "error.project.not.found";
        public static final String PROJECT_HAS_DEPENDENCIES = "error.project.has.dependencies";
        public static final String PROJECT_HAS_UNITS = "error.project.has.units";
        public static final String PROJECT_HAS_CUSTOMER_OFFERS = "error.project.has.customer.offers";
        public static final String PROJECT_HAS_CUSTOMER_PROPERTIES = "error.project.has.customer.properties";
        public static final String PROJECT_DELETION_SUCCESS = "success.project.deleted";
        public static final String PROJECT_DEPENDENCY_VALIDATION_FAILED = "error.project.dependency.validation.failed";

        private ProjectDeletion() {
        }
    }

    public static class UnitDeletion {
        public static final String UNIT_NOT_FOUND = "error.unit.not.found";
        public static final String UNIT_HAS_SECONDARY_INTERACTIONS = "error.unit.has.secondary.interactions";
        public static final String UNIT_DELETION_SUCCESS = "success.unit.deleted";
        public static final String UNIT_DELETION_SYSTEM_ERROR = "error.unit.deletion.system.error";

        private UnitDeletion() {
        }
    }

    public static class CustomerDeletion {
        public static final String CUSTOMER_NOT_FOUND = "error.customer.not.found";
        public static final String CUSTOMER_HAS_DEPENDENCIES = "error.customer.has.dependencies";
        public static final String CUSTOMER_HAS_ASSIGNMENTS = "error.customer.has.assignments";
        public static final String CUSTOMER_HAS_OFFERS = "error.customer.has.offers";
        public static final String CUSTOMER_HAS_PROPERTIES = "error.customer.has.properties";
        public static final String CUSTOMER_HAS_NOTIFICATIONS = "error.customer.has.notifications";
        public static final String CUSTOMER_HAS_RULE_RUNS = "error.customer.has.rule.runs";
        public static final String CUSTOMER_DELETION_SUCCESS = "success.customer.deleted";
        public static final String CUSTOMER_DEPENDENCY_VALIDATION_FAILED = "error.customer.dependency.validation.failed";
        public static final String CUSTOMER_DELETION_SYSTEM_ERROR = "error.customer.deletion.system.error";

        private CustomerDeletion() {
        }
    }
}
