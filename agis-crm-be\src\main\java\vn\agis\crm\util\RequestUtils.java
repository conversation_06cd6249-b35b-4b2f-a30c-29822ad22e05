package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.utils.BeanUtil;
import vn.agis.crm.base.utils.SecurityUtils;

import java.util.Date;
import java.util.Objects;


public class RequestUtils {
    private static Logger logger = LoggerFactory.getLogger(RequestUtils.class);

    private static int timeout = 30000;

    private RequestUtils() {

    }

    public static Event amqpAsAdmin(String method, String category, Object payload, String routingKey) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = 1L; // Admin user ID
        event.userType = Constants.UserType.ADMIN;
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        event.timeStamp = new Date();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqp(String method, String category, Object payload, String routingKey, Event oldEvent) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqp(String method, String category, Object payload, String routingKey, Event oldEvent, Integer timeout) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = Objects.isNull(SecurityUtils.getUserId()) ? null : SecurityUtils.getUserId();
        event.userType = Objects.isNull(SecurityUtils.getUserType()) ? null : SecurityUtils.getUserType();
//        event.provinceCode = Objects.isNull(SecurityUtils.getProvinceCode()) ? "" : SecurityUtils.getProvinceCode();
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

    public static Event amqp1(String method, String category, Object payload, String routingKey, Event oldEvent) {

        Event event = new Event();
        event.id = java.util.UUID.randomUUID().toString();
        event.method = method;
        event.category = category;
        event.payload = payload;
        event.userId = oldEvent.userId;
        event.userType = oldEvent.userType;
        event.provinceCode = oldEvent.provinceCode;
        String exchange = getExchangeFromRoutingKey(routingKey);
        EventBus bus = BeanUtil.getBean(EventBus.class);
        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, timeout);
        return eventResp;
    }

//    public static Event amqpWithoutTimeout(String method, String category, Object payload, String routingKey) {
//
//        Event event = new Event();
//        event.id = java.util.UUID.randomUUID().toString();
//        event.method = method;
//        event.category = category;
//        event.payload = payload;
////        event.token = SecurityUtils.getCurrentUserJWT();
//        String exchange = getExchangeFromRoutingKey(routingKey);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        Event eventResp = bus.publish(exchange, routingKey, event);
//        return eventResp;
//    }

    public static String getExchangeFromRoutingKey(String routingKey) {
        return AMQPConstants.Xchange.CRM_DIRECT_EXCHANGE;
    }

//    public static Event amqpTimeout(String method, String category, Object payload, String routingKey, Event oldEvent) {
//
//        Event event = new Event();
//        event.id = java.util.UUID.randomUUID().toString();
//        event.method = method;
//        event.category = category;
//        event.payload = payload;
//        event.userId = oldEvent.userId;
//        event.userType = oldEvent.userType;
//        event.provinceCode = oldEvent.provinceCode;
//        String exchange = getExchangeFromRoutingKey(routingKey);
//        EventBus bus = BeanUtil.getBean(EventBus.class);
//        Event eventResp = bus.publishAndReceiveSynch(exchange, routingKey, event, Constants.SimMngt.timeout);
//        return eventResp;
//    }
}
