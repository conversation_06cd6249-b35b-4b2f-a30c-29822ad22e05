package vn.agis.crm.repository;


import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Customers;

@Repository
public interface CustomerRepository extends JpaRepository<Customers, Long> {

    long countByIdIn(List<Long> ids);


    boolean existsByPhone(String phone);

    boolean existsByEmail(String email);

    boolean existsByCccd(String cccd);

    Customers findFirstByPhone(String phone);

    Customers findFirstByCccd(String cccd);

    Customers findFirstByEmail(String email);

    /**
     * Find customer by phone number (including additional phones)
     */
    @Query(value = "SELECT c.* FROM customers c " +
           "WHERE c.phone = :phone " +
           "OR JSON_SEARCH(c.additional_phones, 'one', :phone) IS NOT NULL " +
           "LIMIT 1", nativeQuery = true)
    Customers findFirstByPhoneIncludingAdditional(@Param("phone") String phone);

    /**
     * Find customer by email (including additional emails)
     */
    @Query(value = "SELECT c.* FROM customers c " +
           "WHERE LOWER(c.email) = LOWER(:email) " +
           "OR JSON_SEARCH(LOWER(c.additional_emails), 'one', LOWER(:email)) IS NOT NULL " +
           "LIMIT 1", nativeQuery = true)
    Customers findFirstByEmailIncludingAdditional(@Param("email") String email);

    /**
     * Find customer by CCCD (including additional CCCDs)
     */
    @Query(value = "SELECT c.* FROM customers c " +
           "WHERE c.cccd = :cccd " +
           "OR JSON_SEARCH(c.additional_cccds, 'one', :cccd) IS NOT NULL " +
           "LIMIT 1", nativeQuery = true)
    Customers findFirstByCccdIncludingAdditional(@Param("cccd") String cccd);

    @Query(value = "SELECT c.* FROM customers c \n" +
           "WHERE (:sourceType IS NULL OR c.source_type = :sourceType) \n" +
           "AND (:fullName IS NULL OR c.full_name LIKE CONCAT('%', :fullName, '%')) \n" +
           "AND (:phone IS NULL OR (c.phone LIKE CONCAT('%', :phone, '%') OR JSON_SEARCH(c.additional_phones, 'one', CONCAT('%', :phone, '%')) IS NOT NULL)) \n" +
           "AND (:email IS NULL OR (c.email LIKE CONCAT('%', :email, '%') OR JSON_SEARCH(c.additional_emails, 'one', CONCAT('%', :email, '%')) IS NOT NULL)) \n" +
           "AND (:cccd IS NULL OR (c.cccd LIKE CONCAT('%', :cccd, '%') OR JSON_SEARCH(c.additional_cccds, 'one', CONCAT('%', :cccd, '%')) IS NOT NULL)) \n" +
           "AND (:address IS NULL OR (c.address_permanent LIKE CONCAT('%', :address, '%') OR c.address_contact LIKE CONCAT('%', :address, '%'))) \n" +
           "AND (:sourceDetail IS NULL OR c.source_detail LIKE CONCAT('%', :sourceDetail, '%')) \n" +
           "AND (:businessField IS NULL OR c.business_field LIKE CONCAT('%', :businessField, '%')) \n" +
           "AND (:interests IS NULL OR c.interests LIKE CONCAT('%', :interests, '%')) \n" +
           "AND (:relativeName IS NULL OR EXISTS (SELECT 1 FROM customer_relatives cr WHERE cr.customer_id = c.id AND cr.full_name LIKE CONCAT('%', :relativeName, '%'))) \n" +
           "AND (:birthDateFrom IS NULL OR c.birth_date >= STR_TO_DATE(:birthDateFrom, '%Y-%m-%d')) \n" +
           "AND (:birthDateTo IS NULL OR c.birth_date <= STR_TO_DATE(:birthDateTo, '%Y-%m-%d')) \n" +
           "AND (:birthdayDay IS NULL OR :birthdayMonth IS NULL OR (DAY(c.birth_date) = :birthdayDay AND MONTH(c.birth_date) = :birthdayMonth)) \n" +
           "AND (:projectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :projectId)) \n" +
           "AND (:purchasedProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :purchasedProjectId)) \n" +
//           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId AND co.status = 'OPEN')) \n" +
           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId )) \n" +
           "AND (:propertyType IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp JOIN units u ON cp.unit_id = u.id WHERE cp.customer_id = c.id AND u.product_type LIKE CONCAT('%', :propertyType, '%'))) \n" +
           "AND (:employeeId IS NULL OR EXISTS (SELECT 1 FROM vw_current_assignments vca WHERE vca.customer_id = c.id AND vca.employee_id = :employeeId))",
           countQuery = "SELECT COUNT(1) FROM customers c \n" +
           "WHERE (:sourceType IS NULL OR c.source_type = :sourceType) \n" +
           "AND (:fullName IS NULL OR c.full_name LIKE CONCAT('%', :fullName, '%')) \n" +
           "AND (:phone IS NULL OR (c.phone LIKE CONCAT('%', :phone, '%') OR JSON_SEARCH(c.additional_phones, 'one', CONCAT('%', :phone, '%')) IS NOT NULL)) \n" +
           "AND (:email IS NULL OR (c.email LIKE CONCAT('%', :email, '%') OR JSON_SEARCH(c.additional_emails, 'one', CONCAT('%', :email, '%')) IS NOT NULL)) \n" +
           "AND (:cccd IS NULL OR (c.cccd LIKE CONCAT('%', :cccd, '%') OR JSON_SEARCH(c.additional_cccds, 'one', CONCAT('%', :cccd, '%')) IS NOT NULL)) \n" +
           "AND (:address IS NULL OR (c.address_permanent LIKE CONCAT('%', :address, '%') OR c.address_contact LIKE CONCAT('%', :address, '%'))) \n" +
           "AND (:sourceDetail IS NULL OR c.source_detail LIKE CONCAT('%', :sourceDetail, '%')) \n" +
           "AND (:businessField IS NULL OR c.business_field LIKE CONCAT('%', :businessField, '%')) \n" +
           "AND (:interests IS NULL OR c.interests LIKE CONCAT('%', :interests, '%')) \n" +
           "AND (:relativeName IS NULL OR EXISTS (SELECT 1 FROM customer_relatives cr WHERE cr.customer_id = c.id AND cr.full_name LIKE CONCAT('%', :relativeName, '%'))) \n" +
           "AND (:birthDateFrom IS NULL OR c.birth_date >= STR_TO_DATE(:birthDateFrom, '%Y-%m-%d')) \n" +
           "AND (:birthDateTo IS NULL OR c.birth_date <= STR_TO_DATE(:birthDateTo, '%Y-%m-%d')) \n" +
           "AND (:birthdayDay IS NULL OR :birthdayMonth IS NULL OR (DAY(c.birth_date) = :birthdayDay AND MONTH(c.birth_date) = :birthdayMonth)) \n" +
           "AND (:projectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :projectId)) \n" +
           "AND (:purchasedProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :purchasedProjectId)) \n" +
//           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId AND co.status = 'OPEN')) \n" +
           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId )) \n" +
           "AND (:propertyType IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp JOIN units u ON cp.unit_id = u.id WHERE cp.customer_id = c.id AND u.product_type LIKE CONCAT('%', :propertyType, '%'))) \n" +
           "AND (:employeeId IS NULL OR EXISTS (SELECT 1 FROM vw_current_assignments vca WHERE vca.customer_id = c.id AND vca.employee_id = :employeeId))",
           nativeQuery = true)
    Page<Customers> search(@Param("fullName") String fullName,
                           @Param("phone") String phone,
                           @Param("email") String email,
                           @Param("cccd") String cccd,
                           @Param("address") String address,
                           @Param("sourceType") String sourceType,
                           @Param("sourceDetail") String sourceDetail,
                           @Param("businessField") String businessField,
                           @Param("interests") String interests,
                           @Param("relativeName") String relativeName,
                           @Param("birthDateFrom") String birthDateFrom,
                           @Param("birthDateTo") String birthDateTo,
                           @Param("birthdayDay") Integer birthdayDay,
                           @Param("birthdayMonth") Integer birthdayMonth,
                           @Param("projectId") Long projectId,
                           @Param("purchasedProjectId") Long purchasedProjectId,
                           @Param("activeOfferProjectId") Long activeOfferProjectId,
                           @Param("propertyType") String propertyType,
                           @Param("employeeId") Long employeeId,
                           Pageable pageable);

    /**
     * Search customers with role-based access control
     * This method applies role-based filtering to ensure users only see customers they have access to
     */
    @Query(value = "SELECT c.* FROM customers c \n" +
           "WHERE (:sourceType IS NULL OR c.source_type = :sourceType) \n" +
           "AND (:fullName IS NULL OR c.full_name LIKE CONCAT('%', :fullName, '%')) \n" +
           "AND (:phone IS NULL OR (c.phone LIKE CONCAT('%', :phone, '%') OR JSON_SEARCH(c.additional_phones, 'one', CONCAT('%', :phone, '%')) IS NOT NULL)) \n" +
           "AND (:email IS NULL OR (c.email LIKE CONCAT('%', :email, '%') OR JSON_SEARCH(c.additional_emails, 'one', CONCAT('%', :email, '%')) IS NOT NULL)) \n" +
           "AND (:cccd IS NULL OR (c.cccd LIKE CONCAT('%', :cccd, '%') OR JSON_SEARCH(c.additional_cccds, 'one', CONCAT('%', :cccd, '%')) IS NOT NULL)) \n" +
           "AND (:address IS NULL OR (c.address_permanent LIKE CONCAT('%', :address, '%') OR c.address_contact LIKE CONCAT('%', :address, '%'))) \n" +
           "AND (:sourceDetail IS NULL OR c.source_detail LIKE CONCAT('%', :sourceDetail, '%')) \n" +
           "AND (:businessField IS NULL OR c.business_field LIKE CONCAT('%', :businessField, '%')) \n" +
           "AND (:interests IS NULL OR c.interests LIKE CONCAT('%', :interests, '%')) \n" +
           "AND (:relativeName IS NULL OR EXISTS (SELECT 1 FROM customer_relatives cr WHERE cr.customer_id = c.id AND cr.full_name LIKE CONCAT('%', :relativeName, '%'))) \n" +
           "AND (:birthDateFrom IS NULL OR c.birth_date >= STR_TO_DATE(:birthDateFrom, '%Y-%m-%d')) \n" +
           "AND (:birthDateTo IS NULL OR c.birth_date <= STR_TO_DATE(:birthDateTo, '%Y-%m-%d')) \n" +
           "AND (:birthdayDay IS NULL OR :birthdayMonth IS NULL OR (DAY(c.birth_date) = :birthdayDay AND MONTH(c.birth_date) = :birthdayMonth)) \n" +
           "AND (:birthdayMonth IS NULL OR MONTH(c.birth_date) = :birthdayMonth) \n" +
           "AND (:projectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :projectId)) \n" +
           "AND (:purchasedProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :purchasedProjectId)) \n" +
           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId )) \n" +
           "AND (:propertyType IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp JOIN units u ON cp.unit_id = u.id WHERE cp.customer_id = c.id AND u.product_type LIKE CONCAT('%', :propertyType, '%'))) \n" +
           "AND (:employeeId IS NULL OR EXISTS (SELECT 1 FROM vw_current_assignments vca WHERE vca.customer_id = c.id AND vca.employee_id = :employeeId)) \n" +
           "AND (:userType = 1 OR \n" +
           "     (:userType = 2 AND c.current_manager_id = :userId) OR \n" +
           "     (:userType = 3 AND c.current_staff_id = :userId))",
           countQuery = "SELECT COUNT(1) FROM customers c \n" +
           "WHERE (:sourceType IS NULL OR c.source_type = :sourceType) \n" +
           "AND (:fullName IS NULL OR c.full_name LIKE CONCAT('%', :fullName, '%')) \n" +
           "AND (:phone IS NULL OR (c.phone LIKE CONCAT('%', :phone, '%') OR JSON_SEARCH(c.additional_phones, 'one', CONCAT('%', :phone, '%')) IS NOT NULL)) \n" +
           "AND (:email IS NULL OR (c.email LIKE CONCAT('%', :email, '%') OR JSON_SEARCH(c.additional_emails, 'one', CONCAT('%', :email, '%')) IS NOT NULL)) \n" +
           "AND (:cccd IS NULL OR (c.cccd LIKE CONCAT('%', :cccd, '%') OR JSON_SEARCH(c.additional_cccds, 'one', CONCAT('%', :cccd, '%')) IS NOT NULL)) \n" +
           "AND (:address IS NULL OR (c.address_permanent LIKE CONCAT('%', :address, '%') OR c.address_contact LIKE CONCAT('%', :address, '%'))) \n" +
           "AND (:sourceDetail IS NULL OR c.source_detail LIKE CONCAT('%', :sourceDetail, '%')) \n" +
           "AND (:businessField IS NULL OR c.business_field LIKE CONCAT('%', :businessField, '%')) \n" +
           "AND (:interests IS NULL OR c.interests LIKE CONCAT('%', :interests, '%')) \n" +
           "AND (:relativeName IS NULL OR EXISTS (SELECT 1 FROM customer_relatives cr WHERE cr.customer_id = c.id AND cr.full_name LIKE CONCAT('%', :relativeName, '%'))) \n" +
           "AND (:birthDateFrom IS NULL OR c.birth_date >= STR_TO_DATE(:birthDateFrom, '%Y-%m-%d')) \n" +
           "AND (:birthDateTo IS NULL OR c.birth_date <= STR_TO_DATE(:birthDateTo, '%Y-%m-%d')) \n" +
           "AND (:birthdayDay IS NULL OR :birthdayMonth IS NULL OR (DAY(c.birth_date) = :birthdayDay AND MONTH(c.birth_date) = :birthdayMonth)) \n" +
           "AND (:birthdayMonth IS NULL OR MONTH(c.birth_date) = :birthdayMonth) \n" +
           "AND (:projectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :projectId)) \n" +
           "AND (:purchasedProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id = :purchasedProjectId)) \n" +
           "AND (:activeOfferProjectId IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id = :activeOfferProjectId )) \n" +
           "AND (:propertyType IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp JOIN units u ON cp.unit_id = u.id WHERE cp.customer_id = c.id AND u.product_type LIKE CONCAT('%', :propertyType, '%'))) \n" +
           "AND (:employeeId IS NULL OR EXISTS (SELECT 1 FROM vw_current_assignments vca WHERE vca.customer_id = c.id AND vca.employee_id = :employeeId)) \n" +
           "AND (:userType = 1 OR \n" +
           "     (:userType = 2 AND c.current_manager_id = :userId) OR \n" +
           "     (:userType = 3 AND c.current_staff_id = :userId))",
           nativeQuery = true)
    Page<Customers> searchWithRoleFilter(@Param("fullName") String fullName,
                           @Param("phone") String phone,
                           @Param("email") String email,
                           @Param("cccd") String cccd,
                           @Param("address") String address,
                           @Param("sourceType") String sourceType,
                           @Param("sourceDetail") String sourceDetail,
                           @Param("businessField") String businessField,
                           @Param("interests") String interests,
                           @Param("relativeName") String relativeName,
                           @Param("birthDateFrom") String birthDateFrom,
                           @Param("birthDateTo") String birthDateTo,
                           @Param("birthdayDay") Integer birthdayDay,
                           @Param("birthdayMonth") Integer birthdayMonth,
                           @Param("projectId") Long projectId,
                           @Param("purchasedProjectId") Long purchasedProjectId,
                           @Param("activeOfferProjectId") Long activeOfferProjectId,
                           @Param("propertyType") String propertyType,
                           @Param("employeeId") Long employeeId,
                           @Param("userType") Integer userType,
                           @Param("userId") Long userId,
                           Pageable pageable);

    @Modifying
    @Query("UPDATE Customers c SET c.currentManagerId = :managerId WHERE c.id IN :customerIds")
    void updateCurrentManager(@Param("customerIds") List<Long> customerIds, @Param("managerId") Long managerId);

    @Modifying
    @Query("UPDATE Customers c SET c.currentStaffId = :staffId WHERE c.id IN :customerIds")
    void updateCurrentStaff(@Param("customerIds") List<Long> customerIds, @Param("staffId") Long staffId);

    @Modifying
    @Query("UPDATE Customers c SET c.currentManagerId = NULL WHERE c.id IN :customerIds")
    void clearCurrentManager(@Param("customerIds") List<Long> customerIds);

    @Modifying
    @Query("UPDATE Customers c SET c.currentStaffId = NULL WHERE c.id IN :customerIds")
    void clearCurrentStaff(@Param("customerIds") List<Long> customerIds);

    // Customer deletion dependency validation methods

    /**
     * Count customer assignments for a specific customer
     */
    @Query("SELECT COUNT(ca) FROM CustomerAssignments ca WHERE ca.customerId = :customerId")
    long countCustomerAssignmentsByCustomerId(@Param("customerId") Long customerId);

    /**
     * Count customer offers for a specific customer
     */
    @Query("SELECT COUNT(co) FROM CustomerOffers co WHERE co.customerId = :customerId")
    long countCustomerOffersByCustomerId(@Param("customerId") Long customerId);

    /**
     * Count customer properties for a specific customer
     */
    @Query("SELECT COUNT(cp) FROM CustomerProperties cp WHERE cp.customerId = :customerId")
    long countCustomerPropertiesByCustomerId(@Param("customerId") Long customerId);

    /**
     * Count notifications for a specific customer using native query
     */
    @Query(value = "SELECT COUNT(*) FROM notifications WHERE target_customer_id = :customerId", nativeQuery = true)
    long countNotificationsByCustomerId(@Param("customerId") Long customerId);


    @Query(value = "SELECT c.* FROM customers c \n" +
            "WHERE (:sourceTypes IS NULL OR c.source_type in (:sourceTypes)) \n" +
            "AND (:sourceDetails IS NULL OR c.source_detail in (:sourceDetails)) \n" +
            "AND (:fullName IS NULL OR c.full_name LIKE CONCAT('%', :fullName, '%')) \n" +
            "AND (:phone IS NULL OR c.phone LIKE CONCAT('%', :phone, '%')) \n" +
            "AND (:email IS NULL OR c.email LIKE CONCAT('%', :email, '%')) \n" +
            "AND (:boughtProjects IS NULL OR EXISTS (SELECT 1 FROM customer_properties cp WHERE cp.customer_id = c.id AND cp.project_id in (:boughtProjects))) \n" +
            "AND (:greetingProjects IS NULL OR EXISTS (SELECT 1 FROM customer_offers co WHERE co.customer_id = c.id AND co.project_id in (:greetingProjects))) \n",
            nativeQuery = true)
    List<Customers> searchAdvance(@Param("fullName") String fullName,
                           @Param("phone") String phone,
                           @Param("email") String email,
                           @Param("sourceTypes") String sourceTypes,
                           @Param("sourceDetails") String sourceDetails,
                           @Param("boughtProjects") String boughtProjects,
                           @Param("greetingProjects") String greetingProjects);

    List<Customers> findAllByCurrentManagerId(Long currentManagerId);
    List<Customers> findAllByCurrentStaffId(Long currentStaffId);
}

