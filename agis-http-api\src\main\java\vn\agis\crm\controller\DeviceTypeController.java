package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.DeviceTypeSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchDeviceTypeResponse;
import vn.agis.crm.base.jpa.entity.DeviceType;
import vn.agis.crm.service.DeviceTypeService;

import java.util.List;

@RestController
@RequestMapping("/device-type")
public class DeviceTypeController {
    
    private final Logger logger = LoggerFactory.getLogger(DeviceTypeController.class);
    
    @Autowired
    DeviceTypeService deviceTypeService;

    @Autowired
    public DeviceTypeController(DeviceTypeService service) {
        this.deviceTypeService = service;
    }
    
    @GetMapping("/search")
//    @PreAuthorize("hasAnyAuthority('searchDeviceType')")
    public ResponseEntity<Page<SearchDeviceTypeResponse>> searchDeviceType(
            @RequestParam(name = "typeCode", required = false, defaultValue = "") String typeCode,
            @RequestParam(name = "typeName", required = false, defaultValue = "") String typeName,
            @RequestParam(name = "modelCode", required = false, defaultValue = "") String modelCode,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "keyType", required = false, defaultValue = "") String keyType,
            @RequestParam(name = "sort", required = false, defaultValue = "typeCode,desc") String sortBy,
            HttpServletRequest httpServletRequest) {
        
        CrudController.ListRequest listRequest = new CrudController.ListRequest(size, page, sortBy);
        DeviceTypeSearchDTO deviceTypeSearchDTO = new DeviceTypeSearchDTO(
            typeCode, typeName, keyType, modelCode, page, size, sortBy
        );
        
        Page<SearchDeviceTypeResponse> deviceTypeResponses = deviceTypeService.searchDeviceType(
            deviceTypeSearchDTO, listRequest.getPageable()
        );
        
        logger.info("Search DeviceType End");
        return ResponseEntity.ok().body(deviceTypeResponses);
    }

    @GetMapping("/searchDistinct")
//    @PreAuthorize("hasAnyAuthority('searchDeviceType')")
    public ResponseEntity<Page<SearchDeviceTypeResponse>> searchDeviceTypeDistinct(
            @RequestParam(name = "typeCode", required = false, defaultValue = "") String typeCode,
            @RequestParam(name = "typeName", required = false, defaultValue = "") String typeName,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "keyType", required = false, defaultValue = "") String keyType,
            @RequestParam(name = "modelCode", required = false, defaultValue = "") String modelCode,
            @RequestParam(name = "sort", required = false, defaultValue = "typeCode,desc") String sortBy,
            HttpServletRequest httpServletRequest) {

        CrudController.ListRequest listRequest = new CrudController.ListRequest(size, page, sortBy);
        DeviceTypeSearchDTO deviceTypeSearchDTO = new DeviceTypeSearchDTO(
                typeCode, typeName, keyType, modelCode, page, size, sortBy
        );

        Page<SearchDeviceTypeResponse> deviceTypeResponses = deviceTypeService.searchDeviceTypeDistinct(
                deviceTypeSearchDTO, listRequest.getPageable()
        );

        logger.info("Search DeviceType End");
        return ResponseEntity.ok().body(deviceTypeResponses);
    }
    
    @GetMapping("/{id}")
//    @PreAuthorize("hasAnyAuthority('getDeviceType')")
    public ResponseEntity<DeviceType> getDetail(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        DeviceType deviceType = deviceTypeService.get(id);
        return ResponseEntity.ok().body(deviceType);
    }

    @GetMapping("/get-by-code/{typeCode}")
    public ResponseEntity<List<DeviceType>> getDeviceTypeByCode(@PathVariable String typeCode, HttpServletRequest httpServletRequest) {
        List<DeviceType> deviceType = deviceTypeService.getByCode(typeCode);
        return ResponseEntity.ok().body(deviceType);
    }
    @PostMapping("")
    @PreAuthorize("hasAnyAuthority('createDeviceType')")
    public ResponseEntity<DeviceType> createDeviceType(@RequestBody DeviceType entity, HttpServletRequest httpServletRequest) {
        DeviceType deviceType = deviceTypeService.create(entity);
        return ResponseEntity.ok().body(deviceType);
    }
    
    @PutMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('updateDeviceType')")
    public ResponseEntity<DeviceType> updateDeviceType(@PathVariable Long id, @RequestBody DeviceType entity, HttpServletRequest httpServletRequest) {
        entity.setId(id);
        DeviceType deviceType = deviceTypeService.update(id, entity);
        return ResponseEntity.ok().body(deviceType);
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyAuthority('deleteDeviceType')")
    public ResponseEntity<Void> deleteDeviceType(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        deviceTypeService.deleteById(id);
        return ResponseEntity.ok().build();
    }
}

