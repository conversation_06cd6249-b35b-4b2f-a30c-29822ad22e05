package vn.agis.crm.endpoint;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.service.UserService;

@Component
public class UserEndpoint {

    @Autowired
    private UserService service;

    public Event process(Event event) {
        return service.process(event);
    }
}
