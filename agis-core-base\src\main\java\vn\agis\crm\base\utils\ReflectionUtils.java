package vn.agis.crm.base.utils;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * Created by huyvv
 * Date: 10/06/2021
 * Time: 3:37 PM
 * for all issues, contact me: <EMAIL>
 **/
public class ReflectionUtils {
    private ReflectionUtils() {

    }

    public static Map<String, Object> convertClassToMap(Object object) {
        Class clazz = object.getClass();
        Map<String, Object> map = new HashMap<>();
        try {
            //Loop up to traverse the parent class
            for (; clazz != Object.class; clazz = clazz.getSuperclass()) {
                Field[] field = clazz.getDeclaredFields();
                for (Field f : field) {
                    f.setAccessible(true);
                    if (f.get(object) == null) {
                        map.put(f.getName(), "");
                    } else {
                        map.put(f.getName(), f.get(object));
                    }
                }
            }
        } catch (IllegalAccessException e) {
            e.printStackTrace();
        }
        return map;
    }

    public static <T> List<Map<String, Object>> convertListClassToMap(List<T> list) {
        List<Map<String, Object>> result = new LinkedList<>();
        for (T t : list) result.add(convertClassToMap(t));
        return result;
    }
}
