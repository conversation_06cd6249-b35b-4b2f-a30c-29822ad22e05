package vn.agis.crm.base.jpa.dto;

import lombok.Data;

/**
 * Search DTO for filtering primary interactions
 * Used for GET /interactions-primary/search endpoint
 */
@Data
public class InteractionPrimarySearchDto {
    
    private Long customerOfferId;
    private String result;
    private String happenedAtFrom; // dd/MM/yyyy format
    private String happenedAtTo;   // dd/MM/yyyy format
    private Long createdBy;
    private String createdAtFrom;  // dd/MM/yyyy format
    private String createdAtTo;    // dd/MM/yyyy format
    
    // Pagination parameters
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "happenedAt,desc";
    
    public InteractionPrimarySearchDto() {}
    
    public InteractionPrimarySearchDto(Long customerOfferId, String result, 
                                     String happenedAtFrom, String happenedAtTo,
                                     Long createdBy, String createdAtFrom, String createdAtTo,
                                     Integer page, Integer size, String sortBy) {
        this.customerOfferId = customerOfferId;
        this.result = result;
        this.happenedAtFrom = happenedAtFrom;
        this.happenedAtTo = happenedAtTo;
        this.createdBy = createdBy;
        this.createdAtFrom = createdAtFrom;
        this.createdAtTo = createdAtTo;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
