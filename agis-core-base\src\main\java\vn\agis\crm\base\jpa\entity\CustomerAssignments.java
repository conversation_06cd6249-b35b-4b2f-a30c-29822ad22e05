package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Entity
@Table(name = "customer_assignments")
@Data
public class CustomerAssignments extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", nullable = false)
    private Long id;

    @Column(name = "customer_id", nullable = false)
    private Long customerId;

    @Column(name = "employee_id", nullable = false)
    private Long employeeId;

    @Column(name = "role_type", nullable = false)
    private Integer roleType; // 1 = Manager, 2 = Staff

    @Column(name = "assigned_from")
    private Date assignedFrom;

    @Column(name = "assigned_to")
    private Date assignedTo;

    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;
}

