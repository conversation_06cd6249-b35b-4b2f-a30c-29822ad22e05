package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * Request DTO for creating secondary interactions
 * Used for POST /interactions-secondary endpoint
 */
@Data
public class InteractionSecondaryCreateDto {
    
    @NotNull(message = "Customer property ID is required")
    private Long customerPropertyId;
    
    @DecimalMin(value = "0.0", inclusive = false, message = "Expected sell price must be greater than 0")
    private BigDecimal expectedSellPrice;
    
    @DecimalMin(value = "0.0", inclusive = false, message = "Expected rent price must be greater than 0")
    private BigDecimal expectedRentPrice;
    
    @NotBlank(message = "Result is required")
    @Size(max = 100, message = "Result must not exceed 100 characters")
    private String result;
    
    @NotBlank(message = "Happened at date is required")
    private String happenedAt; // dd/MM/yyyy format
    
    @Size(max = 5000, message = "Notes must not exceed 5000 characters")
    private String notes;
}
