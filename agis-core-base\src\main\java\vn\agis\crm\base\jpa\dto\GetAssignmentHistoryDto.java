package vn.agis.crm.base.jpa.dto;

public class GetAssignmentHistoryDto {
    private Long customerId;
    private Integer page = 0;
    private Integer size = 20;
    private String sortBy = "assignedFrom";
    private String sortDirection = "DESC";

    // Default constructor
    public GetAssignmentHistoryDto() {}

    // Constructor with customerId
    public GetAssignmentHistoryDto(Long customerId) {
        this.customerId = customerId;
    }

    // Constructor with all fields
    public GetAssignmentHistoryDto(Long customerId, Integer page, Integer size, String sortBy, String sortDirection) {
        this.customerId = customerId;
        this.page = page != null ? page : 0;
        this.size = size != null ? size : 20;
        this.sortBy = sortBy != null ? sortBy : "assignedFrom";
        this.sortDirection = sortDirection != null ? sortDirection : "DESC";
    }

    // Getters and Setters
    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page != null ? page : 0;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size != null ? size : 20;
    }

    public String getSortBy() {
        return sortBy;
    }

    public void setSortBy(String sortBy) {
        this.sortBy = sortBy != null ? sortBy : "assignedFrom";
    }

    public String getSortDirection() {
        return sortDirection;
    }

    public void setSortDirection(String sortDirection) {
        this.sortDirection = sortDirection != null ? sortDirection : "DESC";
    }

    @Override
    public String toString() {
        return "GetAssignmentHistoryDto{" +
                "customerId=" + customerId +
                ", page=" + page +
                ", size=" + size +
                ", sortBy='" + sortBy + '\'' +
                ", sortDirection='" + sortDirection + '\'' +
                '}';
    }
}
