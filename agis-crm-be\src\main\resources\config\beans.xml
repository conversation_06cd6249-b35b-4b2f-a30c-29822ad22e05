<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.1.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd">

    <context:component-scan base-package="com.cimit"/>
    <bean id="thread-pool" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor" scope="singleton">
        <property name="corePoolSize" value="1"/>
        <property name="maxPoolSize" value="128"/>
        <property name="keepAliveSeconds" value="10"/>
        <property name="threadNamePrefix" value="cache-gw-"/>
        <property name="WaitForTasksToCompleteOnShutdown" value="true"/>
    </bean>



    <bean id="configMinIO2" class="vn.agis.crm.base.jpa.dto.dump.MinIOSyncConfig2">
        <property name="mdCode" value="info"/>
        <property name="remoteFile" value="M2M_{ddMMyyyy}.txt"/>
        <property name="localDir" value="/ftp/download/"/>
        <property name="backupDir" value="/ftp/backup/"/>
        <property name="separator" value="\|\|"/>
        <property name="startWithRow" value="1"/>
        <property name="batchSize" value="5000"/>
    </bean>


    <bean id="importData1" class="vn.agis.crm.dump.ImportData">
        <property name="minIOSyncConfig2" ref="configMinIO2"/>
    </bean>

    <bean id="configMinIO3" class="vn.agis.crm.base.jpa.dto.dump.MinIOSyncConfig2">
        <property name="mdCode" value="data"/>
        <property name="remoteFile" value="M2MData_{ddMMyyyy}.txt"/>
        <property name="localDir" value="/ftp/download/"/>
        <property name="backupDir" value="/ftp/backup/"/>
        <property name="separator" value="\|\|"/>
        <property name="startWithRow" value="1"/>
        <property name="batchSize" value="5000"/>
    </bean>


    <bean id="importData2" class="vn.agis.crm.dump.ImportData">
        <property name="minIOSyncConfig2" ref="configMinIO3"/>
    </bean>

    <bean id="updateData" class="vn.agis.crm.dump.UpdateData">
        <property name="minIOSyncConfig2s">
            <list>
                <ref bean="configMinIO2"/>
                <ref bean="configMinIO3"/>
            </list>
        </property>
    </bean>




</beans>
