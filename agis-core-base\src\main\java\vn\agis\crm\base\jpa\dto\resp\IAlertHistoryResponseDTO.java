package vn.agis.crm.base.jpa.dto.resp;

import java.util.Date;

public interface IAlertHistoryResponseDTO {

    Long getId();

    String getAlertName();

    String getSeverity();

    Integer getEventType();

    String getRaisedDate();

    String getSmsContent();

    String getEmailContent();

    Integer getActionType();


    String getAlertEmails();

    String getAlertMsisdns();

    String getZaloContent();

    String getZaloNotify();

    String getSendingMethod();

    String getUserCustomerName();

    String getDeviceName();

    String getImei();

}
