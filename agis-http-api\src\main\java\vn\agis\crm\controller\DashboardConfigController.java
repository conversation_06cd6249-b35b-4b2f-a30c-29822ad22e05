package vn.agis.crm.controller;

import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.DashboardConfigCreateDTO;
import vn.agis.crm.base.jpa.entity.DashboardConfig;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.DashboardConfigService;

import java.util.List;

@RestController
@RequestMapping("/threshold-config")
public class DashboardConfigController extends CrudController<DashboardConfig, Long> {

    DashboardConfigService dashboardConfigService;

    private DashboardConfigController (DashboardConfigService service) {
        super(service);
        this.baseUrl = "/threshold-config";
        this.dashboardConfigService = service;
    }

    @GetMapping("/get-list-config/{userId}")
    public ResponseEntity<List<DashboardConfig>> getListConfig (
            @PathVariable Long userId
    ) {
        List<DashboardConfig> responseList = dashboardConfigService.getListDashboardConfig(userId);

        return ResponseEntity.ok().body(responseList);
    }


    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseBase> deleteDashboardConfig(@PathVariable Long id) {
        ResponseBase result = new ResponseBase();
        result = dashboardConfigService.deleteById(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }


    @PostMapping("/create-config")
    public ResponseEntity<Boolean> createDashboardConfig(@RequestBody List<DashboardConfigCreateDTO> requestBody) {
        Boolean dashboardConfigCreate = dashboardConfigService.createDashboardConfig(requestBody);
        return ResponseEntity.ok().body(dashboardConfigCreate);
    }
}
