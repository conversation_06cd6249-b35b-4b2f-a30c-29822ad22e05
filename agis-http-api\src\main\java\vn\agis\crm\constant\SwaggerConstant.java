package vn.agis.crm.constant;

public class SwaggerConstant {

    private SwaggerConstant() {
    }

    public static class Example {
        public static final String ID = "1";
        public static final String JSON = "{\"key\":\"value\"}";
        public static final String EMAIL_FIELD = "EMAIL";
        public static final String DATA = "Chuỗi Json";
        public static final String DESCRIPTION = "This is Demo";
        public static final String URL = "http://example.com";
        public static final String FILE_PATH = "/resources/upload/file/services/images/31012021/2021013104516c4faf00-5597-4aae-9e86-36f618bb579d.JPG";
        public static final String APPROVE = "APPROVE";
        public static final String FILE_NAME = "Pexels Videos 2634.mp4";
        public static final String ACCESS_TYPE = "0";
        public static final String FILE_SIZE = "2048";
        public static final String SERVICE_NAME = "Handcrafted Concrete Salad";
        public static final String DISPLAY = "INVISIBLE";
        public static final String STATUS = "VISIBLE";
        public static final String LANGUAGE = "[0]";
        public static final String EMAIL = "<EMAIL>";
        public static final String PHONE = "0858585858";
        public static final String TIME = "2023-10-22 08:32:53";
        public static final String PAGE = "0";
        public static final String SIZE = "10";
        public static final String SORT = "id,asc";
        public static final String USER_NAME = "Nguyễn Văn A";
        public static final String COMPANY_NAME = "VNPT";
        public static final String DATE = "20/10/2023";
        public static final String ON = "ON";

        private Example() {
        }

    }

    public class FileAttach {

        public static final String ID = "Mã file";
        public static final String FILE_TYPE = "Loại file";
        public static final String NAME = "Tên file";
        public static final String FILE_PATH = "Đương dẫn file";
        public static final String PRIORITY = "Thứ tự file";
        public static final String ACCESS_TYPE = "Kiểu truy cập";
        public static final String FILE_SIZE = "Kích thước file";
        public static final String EXTERNAL_LINK = "Đường dẫn external";
        public static final String ICON = "ICON";
        public static final String BANNER = "BANNER";

        private FileAttach() {
        }
    }

    public class Sim {
        public static final String MSISDN = "Số thuê bao";
        public static final String IMSI = "Số imsi của sim";
        public static final String STATUS = "Trạng thái sim";
        public static final String APN_CODE = "Mã APN";
        public static final String RATING_PLAN_ID = "Id gói cước";
        public static final String CONTRACT_CODE = "Mã hợp đồng";
        public static final String CONTRACTOR = "Người làm hợp đồng";
        public static final String CONTRACT_DATE_FROM = "Ngày làm hợp đồng từ";
        public static final String CONTRACT_DATE_TO = "Ngày làm hợp đồng đêến";
        public static final String SIM_GROUP_ID = "Id nhóm sim";
        public static final String CUSTOMER = "Mã khách hàng";
        public static final String SIM_TYPE = "Chủng loại sim";
        public static final String CUSTOMER_CODE = "Mã khách hàng";
        private Sim() {
        }
    }

    public class Group{
        public static final String NAME = "Ten group";
        public static final String SCOPE = "Pham vi";
        public static final String GROUP_KEY = "Ma nhom";
        public static final String CUSTOMER_CODE = "Ma khach hang";
        public static final String CONTRACT_CODE = "Ma hop dong";
        public static final String VALUE_SEARCH = "search value";
        private Group(){

        }
    }

    public class Pagination {


        public static final String PAGE = "Chỉ số trang";
        public static final String SIZE = "Số bản ghi trên 1 trang";
        public static final String SORT = "Tuỳ chọn sắp xếp";

        private Pagination() {
        }
    }
}
