package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PropertyUpsertDto {
    private Long id;
    private Long projectId;
    private Long unitId;
    // Accept as string from payload (dd/MM/yyyy)
    private String transactionDate;
    private BigDecimal contractPrice;
    private Long employeeId; // internal employee who brokered
    private String notes;
    private String legalStatus;

    // external sale info
    private String externalAgencyName;
    private String externalSaleName;
    private String externalSalePhone;
    private String externalSaleEmail;

    // optional interactions (summary) and dates
    private String firstInteraction; // yyyy-MM-dd or dd/MM/yyyy
    private String lastInteraction;  // yyyy-MM-dd or dd/MM/yyyy

    // new: detailed secondary interactions as in payload
    private java.util.List<vn.agis.crm.base.jpa.dto.InteractionSecondaryDto> interactionsSecondary;

    private Boolean deleted;
}

