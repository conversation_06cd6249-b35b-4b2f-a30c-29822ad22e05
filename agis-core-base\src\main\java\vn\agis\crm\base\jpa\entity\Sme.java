package vn.agis.crm.base.jpa.entity;

import lombok.Data;


import jakarta.persistence.*;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
@Entity
@Table(name = "sme")
public class Sme {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id1")
    private Long id1;

    @Column(name = "id")
    private Long id;

    @Column(name = "userId")
    private Long userId;

    @Column(name = "name", length = 255)
    private String name;

    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    @Column(name = "email", length = 255)
    private String email;

    @Column(name = "province_id")
    private Integer provinceId;

    @Column(name = "province_name", length = 100)
    private String provinceName;

    @Column(name = "district_id")
    private Integer districtId;

    @Column(name = "district_name", length = 100)
    private String districtName;

    @Column(name = "ward_id")
    private Integer wardId;

    @Column(name = "ward_name", length = 100)
    private String wardName;

    @Column(name = "street_id")
    private Integer streetId;

    @Column(name = "street_name", length = 100)
    private String streetName;

    @Column(name = "address", length = 500)
    private String address;

    @Lob
    @Column(name = "description")
    private String description;

    @Column(name = "tax_code", length = 50)
    private String taxCode;

    @Column(name = "sme_uuid", length = 36)
    private String smeUuid;

    @Column(name = "am_code", length = 50)
    private String amCode;

    @Column(name = "am_name", length = 100)
    private String amName;

    @Column(name = "user_status")
    private Byte userStatus;

    @Column(name = "parent_id")
    private Integer parentId;

    @Column(name = "created_at")
    private Date createdAt;
}
