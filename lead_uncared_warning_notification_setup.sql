-- Lead Uncared Warning Notification Scheduler Setup
-- This script sets up the configuration for automatic lead uncared warning notifications

-- =====================================================
-- CONFIGURATION SETUP
-- =====================================================

-- Enable 3-day lead uncared warning notifications (recommended default)
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_UNCARED_WARNING_DAYS', '3', 1, 'Số ngày cảnh báo lead chưa được chăm sóc', NOW(), 1)
ON DUPLICATE KEY UPDATE 
    config_value = '3',
    description = 'Số ngày cảnh báo lead chưa được chăm sóc',
    updated_at = NOW(),
    updated_by = 1;

-- =====================================================
-- ALTERNATIVE CONFIGURATIONS
-- =====================================================

-- Same-day warnings (very aggressive)
-- UPDATE configs SET config_value = '1' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- 2-day warnings
-- UPDATE configs SET config_value = '2' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- 1-week warnings
-- UPDATE configs SET config_value = '7' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- 2-week warnings
-- UPDATE configs SET config_value = '14' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- Disable lead uncared warnings
-- DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';
-- OR
-- UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check current configuration
SELECT 
    config_key,
    config_value,
    config_type,
    description,
    created_at,
    updated_at
FROM configs 
WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS';

-- Check leads that would trigger warnings with current configuration (3 days)
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.email,
    c.source_type,
    ca.assigned_from,
    e.full_name as employee_name,
    e.email as employee_email,
    DATEDIFF(CURDATE(), DATE(ca.assigned_from)) as days_since_assignment
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN employees e ON ca.employee_id = e.id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
  AND ca.assigned_from <= DATE_SUB(CURDATE(), INTERVAL 3 DAY)  -- Adjust based on your config
ORDER BY ca.assigned_from ASC;

-- Check recent lead uncared warning notifications
SELECT 
    n.id,
    n.target_employee_id,
    n.target_customer_id,
    n.title,
    n.content,
    n.is_read,
    n.created_at,
    c.full_name as customer_name,
    c.phone as customer_phone,
    e.full_name as employee_name
FROM notifications n
LEFT JOIN customers c ON n.target_customer_id = c.id
LEFT JOIN employees e ON n.target_employee_id = e.id
WHERE n.type = 3  -- LeadUncaredWarning type
  AND n.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY n.created_at DESC;

-- Check warning notification statistics by date
SELECT 
    DATE(created_at) as notification_date,
    COUNT(*) as warning_count,
    COUNT(DISTINCT target_customer_id) as unique_leads,
    COUNT(DISTINCT target_employee_id) as unique_employees
FROM notifications 
WHERE type = 3  -- LeadUncaredWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY notification_date DESC;

-- =====================================================
-- LEAD ASSIGNMENT ANALYSIS
-- =====================================================

-- Check all active lead assignments
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.source_type,
    ca.assigned_from,
    ca.role_type,
    e.full_name as employee_name,
    e.status as employee_status,
    DATEDIFF(CURDATE(), DATE(ca.assigned_from)) as days_since_assignment
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN employees e ON ca.employee_id = e.id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
ORDER BY ca.assigned_from ASC;

-- Check leads with interactions (should not trigger warnings)
SELECT DISTINCT
    c.id,
    c.full_name,
    c.phone,
    ca.assigned_from,
    'Primary' as interaction_type,
    ip.happened_at as interaction_date,
    ip.result as interaction_result
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN customer_offers co ON c.id = co.customer_id
JOIN interactions_primary ip ON co.id = ip.customer_offer_id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
  AND ip.happened_at > ca.assigned_from

UNION ALL

SELECT DISTINCT
    c.id,
    c.full_name,
    c.phone,
    ca.assigned_from,
    'Secondary' as interaction_type,
    is_table.happened_at as interaction_date,
    is_table.result as interaction_result
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN customer_properties cp ON c.id = cp.customer_id
JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
  AND is_table.happened_at > ca.assigned_from

ORDER BY assigned_from ASC;

-- =====================================================
-- EMPLOYEE WORKLOAD ANALYSIS
-- =====================================================

-- Check employee workload for uncared leads
SELECT 
    e.id,
    e.full_name as employee_name,
    e.email,
    e.status,
    COUNT(ca.id) as total_lead_assignments,
    COUNT(CASE WHEN ca.assigned_from <= DATE_SUB(CURDATE(), INTERVAL 3 DAY) THEN 1 END) as potentially_uncared_leads
FROM employees e
LEFT JOIN customer_assignments ca ON e.id = ca.employee_id
LEFT JOIN customers c ON ca.customer_id = c.id
WHERE e.status = 'active' 
  AND e.deleted_at IS NULL
  AND (ca.id IS NULL OR (c.source_type = 'Leads' AND c.deleted_at IS NULL AND ca.assigned_to IS NULL))
GROUP BY e.id, e.full_name, e.email, e.status
HAVING total_lead_assignments > 0
ORDER BY potentially_uncared_leads DESC, total_lead_assignments DESC;

-- =====================================================
-- LEAD SOURCE ANALYSIS
-- =====================================================

-- Lead distribution by source type
SELECT 
    source_type,
    COUNT(*) as total_customers,
    COUNT(CASE WHEN deleted_at IS NULL THEN 1 END) as active_customers
FROM customers 
GROUP BY source_type
ORDER BY total_customers DESC;

-- Active lead assignments by assignment age
SELECT 
    CASE 
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) = 0 THEN 'Today'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) = 1 THEN '1 day ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 3 THEN '2-3 days ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 7 THEN '4-7 days ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 14 THEN '1-2 weeks ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 30 THEN '2-4 weeks ago'
        ELSE 'Over 1 month ago'
    END as assignment_age,
    COUNT(*) as lead_count
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
GROUP BY 
    CASE 
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) = 0 THEN 'Today'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) = 1 THEN '1 day ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 3 THEN '2-3 days ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 7 THEN '4-7 days ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 14 THEN '1-2 weeks ago'
        WHEN DATEDIFF(CURDATE(), DATE(ca.assigned_from)) <= 30 THEN '2-4 weeks ago'
        ELSE 'Over 1 month ago'
    END
ORDER BY 
    CASE 
        WHEN assignment_age = 'Today' THEN 1
        WHEN assignment_age = '1 day ago' THEN 2
        WHEN assignment_age = '2-3 days ago' THEN 3
        WHEN assignment_age = '4-7 days ago' THEN 4
        WHEN assignment_age = '1-2 weeks ago' THEN 5
        WHEN assignment_age = '2-4 weeks ago' THEN 6
        ELSE 7
    END;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Check notification creation performance (recent warnings)
SELECT 
    DATE(created_at) as date,
    HOUR(created_at) as hour,
    COUNT(*) as warnings_created,
    MIN(created_at) as first_warning,
    MAX(created_at) as last_warning,
    TIMESTAMPDIFF(SECOND, MIN(created_at), MAX(created_at)) as duration_seconds
FROM notifications 
WHERE type = 3  -- LeadUncaredWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), HOUR(created_at)
ORDER BY date DESC, hour DESC;

-- Check for duplicate warnings (should be minimal)
SELECT 
    target_employee_id,
    target_customer_id,
    DATE(created_at) as notification_date,
    COUNT(*) as duplicate_count
FROM notifications 
WHERE type = 3  -- LeadUncaredWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY target_employee_id, target_customer_id, DATE(created_at)
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, notification_date DESC;

-- =====================================================
-- TROUBLESHOOTING QUERIES
-- =====================================================

-- Find leads that should trigger warnings but don't have notifications
SELECT 
    c.id,
    c.full_name,
    c.phone,
    ca.assigned_from,
    e.full_name as employee_name,
    DATEDIFF(CURDATE(), DATE(ca.assigned_from)) as days_since_assignment
FROM customers c
JOIN customer_assignments ca ON c.id = ca.customer_id
JOIN employees e ON ca.employee_id = e.id
LEFT JOIN notifications n ON (
    n.target_customer_id = c.id 
    AND n.target_employee_id = e.id
    AND n.type = 3 
    AND DATE(n.created_at) = CURDATE()
)
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND ca.assigned_to IS NULL
  AND ca.assigned_from <= DATE_SUB(CURDATE(), INTERVAL 3 DAY)  -- Adjust based on config
  AND e.status = 'active'
  AND e.deleted_at IS NULL
  AND n.id IS NULL;

-- =====================================================
-- SCHEDULER STATUS CHECK
-- =====================================================

-- Check if scheduler is running (look for recent lead uncared warnings)
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'RUNNING - Recent lead uncared warnings found'
        ELSE 'UNKNOWN - No recent warnings (may be normal if no uncared leads)'
    END as scheduler_status,
    COUNT(*) as recent_warnings,
    MAX(created_at) as last_warning
FROM notifications 
WHERE type = 3 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Check configuration status
SELECT 
    CASE 
        WHEN config_value IS NULL THEN 'DISABLED - Configuration not found'
        WHEN config_value = '' THEN 'DISABLED - Empty configuration value'
        WHEN config_value REGEXP '^[0-9]+$' AND CAST(config_value AS UNSIGNED) BETWEEN 1 AND 365 THEN 
            CONCAT('ENABLED - ', config_value, ' days warning period')
        ELSE 'INVALID - Invalid configuration value'
    END as config_status,
    config_value,
    updated_at as last_updated
FROM configs 
WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS'
UNION ALL
SELECT 
    'DISABLED - Configuration not found' as config_status,
    NULL as config_value,
    NULL as last_updated
WHERE NOT EXISTS (
    SELECT 1 FROM configs WHERE config_key = 'NOTIFICATION_LEAD_UNCARED_WARNING_DAYS'
);
