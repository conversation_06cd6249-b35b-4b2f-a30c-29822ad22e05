package vn.agis.crm.base.jpa.dto.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.TreeMap;

@Data
public class FilterParamValueRequest {
    @JsonProperty("id")
    Long reportId;
    @JsonProperty("paramsValue")
    List<Param> paramsValue;
    @JsonProperty("customerCodes")
    List<String> customerCodes;
    @JsonProperty("contractCodes")
    List<String> contractCodes;
    public Map<String, Object> toMap(){
        Map<String, Object> map = new TreeMap<>();
        for (Param param : this.paramsValue){
            map.put(param.prKey, param.value);
        }
        return map;
    }
}

