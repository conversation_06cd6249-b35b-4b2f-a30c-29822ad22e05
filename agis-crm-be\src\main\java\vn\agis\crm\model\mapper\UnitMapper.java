package vn.agis.crm.model.mapper;

import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.req.UnitDto;
import vn.agis.crm.base.jpa.entity.Units;

@Component
public class UnitMapper {

    public Units toEntity(UnitDto dto) {
        if (dto == null) return null;
        Units entity = new Units();
        // required fields
        entity.setProjectId(dto.getProjectId());
        entity.setCode(dto.getCode());
        entity.setArea(dto.getArea());
        // optional fields: set only when not null to keep entity defaults
        if (dto.getProductType() != null) entity.setProductType(dto.getProductType());
        if (dto.getSector() != null) entity.setSector(dto.getSector());
        if (dto.getDoorDirection() != null) entity.setDoorDirection(dto.getDoorDirection());
        if (dto.getView() != null) entity.setView(dto.getView());
        if (dto.getContractPrice() != null) entity.setContractPrice(dto.getContractPrice());
        if (dto.getIsActive() != null) entity.setIsActive(dto.getIsActive());

        // NEW: Map unit detail fields
        if (dto.getFloorArea() != null) entity.setFloorArea(dto.getFloorArea());
        if (dto.getFloorNumber() != null) entity.setFloorNumber(dto.getFloorNumber());
        if (dto.getUnitNumber() != null) entity.setUnitNumber(dto.getUnitNumber());

        return entity;
    }

    public void updateEntityFromDto(Units entity, UnitDto dto) {
        if (entity == null || dto == null) return;
        if (dto.getProjectId() != null) entity.setProjectId(dto.getProjectId());
        if (dto.getCode() != null) entity.setCode(dto.getCode());
        if (dto.getProductType() != null) entity.setProductType(dto.getProductType());
        if (dto.getSector() != null) entity.setSector(dto.getSector());
        if (dto.getArea() != null) entity.setArea(dto.getArea());
        if (dto.getDoorDirection() != null) entity.setDoorDirection(dto.getDoorDirection());
        if (dto.getView() != null) entity.setView(dto.getView());
        if (dto.getContractPrice() != null) entity.setContractPrice(dto.getContractPrice());
        if (dto.getIsActive() != null) entity.setIsActive(dto.getIsActive());

        // NEW: Update unit detail fields
        if (dto.getFloorArea() != null) entity.setFloorArea(dto.getFloorArea());
        if (dto.getFloorNumber() != null) entity.setFloorNumber(dto.getFloorNumber());
        if (dto.getUnitNumber() != null) entity.setUnitNumber(dto.getUnitNumber());
    }

    /**
     * Convert Units entity to UnitDto
     * Used for populating CustomerPropertyDto with complete unit information
     */
    public UnitDto toDto(Units entity) {
        if (entity == null) return null;

        UnitDto dto = new UnitDto();
        dto.setId(entity.getId());
        dto.setProjectId(entity.getProjectId());
        dto.setCode(entity.getCode());
        dto.setProductType(entity.getProductType());
        dto.setSector(entity.getSector());
        dto.setArea(entity.getArea());
        dto.setDoorDirection(entity.getDoorDirection());
        dto.setView(entity.getView());
        dto.setContractPrice(entity.getContractPrice());
        dto.setIsActive(entity.getIsActive());

        // Map unit detail fields
        dto.setFloorArea(entity.getFloorArea());
        dto.setFloorNumber(entity.getFloorNumber());
        dto.setUnitNumber(entity.getUnitNumber());

        return dto;
    }
}

