package vn.agis.crm.base.jpa.entity;

import lombok.Data;

import jakarta.persistence.*;

@Entity
@Data
@Table(name = "device_telemetry_record")
public class DeviceTelemetryRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "created_at")
    private Long createdAt;

    @Column(name = "device_id")
    private Long deviceId;

    @Column(name = "telemetry_data", columnDefinition = "json")
    private String telemetryData;

    @Column(name = "measured_at")
    private Long measuredAt;
}

