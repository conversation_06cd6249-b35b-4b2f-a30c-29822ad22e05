package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.req.PermissionDto;
import vn.agis.crm.base.jpa.entity.Permissions;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.Collections;
import java.util.List;

@Service
public class PermissionApiService extends CrudService<Permissions, Long> {

    private static final Logger logger = LoggerFactory.getLogger(PermissionApiService.class);

    public PermissionApiService() {
        super(Permissions.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.PERMISSION_SIMPLE;
    }

    public List<Permissions> getAll() {
        Event event = RequestUtils.amqp(Constants.Method.GET_ALL, category, null, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            List<Permissions> permissionsList = (List<Permissions>) event.payload;
            return permissionsList;
        }
        logger.error("Failed to get all permissions, status code: {}", event.respStatusCode);
        return Collections.emptyList();
    }

    /**
     * Get all permissions with optional description filtering
     * @param description Optional description filter for case-insensitive partial matching
     * @return List of permissions matching the filter criteria
     */
    public List<Permissions> getAllWithDescriptionFilter(String description) {
        Event event = RequestUtils.amqp("GET_ALL_WITH_DESCRIPTION_FILTER", category, description, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            List<Permissions> permissionsList = (List<Permissions>) event.payload;
            return permissionsList;
        }
        logger.error("Failed to get permissions with description filter '{}', status code: {}", description, event.respStatusCode);
        return Collections.emptyList();
    }

    public Permissions createPermission(PermissionDto dto) {
        Event event = RequestUtils.amqp(Constants.Method.CREATE, category, dto, routingKey);
        if (event.respStatusCode == ResponseCode.CONFLICT) {
            throw new DuplicateException(event.respErrorDesc, category, String.valueOf(event.payload), "");
        }
        Permissions permissions = (Permissions) event.payload;
        return permissions;
    }

    public Permissions updatePermission(PermissionDto dto) {
        Event event = RequestUtils.amqp(Constants.Method.UPDATE, category, dto, routingKey);
        if (event.respStatusCode == ResponseCode.CONFLICT) {
            throw new DuplicateException(event.respErrorDesc, category, String.valueOf(event.payload), "");
        }
        if (event.respStatusCode == ResponseCode.NOT_FOUND) {
            throw new ResourceNotFoundException(event.respErrorDesc, category, (String) null, "");
        }
        Permissions permissions = (Permissions) event.payload;
        return permissions;
    }

    public Boolean checkExistName(String name) {
        Event event = RequestUtils.amqp(Constants.Method.CHECK_EXIST_PERMISSION_NAME, category, name, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (Boolean) event.payload;
        }
        return false;
    }
}

