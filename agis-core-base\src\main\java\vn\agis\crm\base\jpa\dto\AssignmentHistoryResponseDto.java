package vn.agis.crm.base.jpa.dto;

import java.util.List;

public class AssignmentHistoryResponseDto {
    private Long customerId;
    private String customerName;
    private String customerPhone;
    private String customerEmail;
    private List<AssignmentHistoryDto> assignmentHistory;
    private Long totalRecords;
    private Integer currentPage;
    private Integer pageSize;
    private Integer totalPages;

    // Default constructor
    public AssignmentHistoryResponseDto() {}

    // Constructor with all fields
    public AssignmentHistoryResponseDto(Long customerId, String customerName, String customerPhone, 
                                       String customerEmail, List<AssignmentHistoryDto> assignmentHistory,
                                       Long totalRecords, Integer currentPage, Integer pageSize, Integer totalPages) {
        this.customerId = customerId;
        this.customerName = customerName;
        this.customerPhone = customerPhone;
        this.customerEmail = customerEmail;
        this.assignmentHistory = assignmentHistory;
        this.totalRecords = totalRecords;
        this.currentPage = currentPage;
        this.pageSize = pageSize;
        this.totalPages = totalPages;
    }

    // Getters and Setters
    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCustomerPhone() {
        return customerPhone;
    }

    public void setCustomerPhone(String customerPhone) {
        this.customerPhone = customerPhone;
    }

    public String getCustomerEmail() {
        return customerEmail;
    }

    public void setCustomerEmail(String customerEmail) {
        this.customerEmail = customerEmail;
    }

    public List<AssignmentHistoryDto> getAssignmentHistory() {
        return assignmentHistory;
    }

    public void setAssignmentHistory(List<AssignmentHistoryDto> assignmentHistory) {
        this.assignmentHistory = assignmentHistory;
    }

    public Long getTotalRecords() {
        return totalRecords;
    }

    public void setTotalRecords(Long totalRecords) {
        this.totalRecords = totalRecords;
    }

    public Integer getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(Integer currentPage) {
        this.currentPage = currentPage;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getTotalPages() {
        return totalPages;
    }

    public void setTotalPages(Integer totalPages) {
        this.totalPages = totalPages;
    }

    @Override
    public String toString() {
        return "AssignmentHistoryResponseDto{" +
                "customerId=" + customerId +
                ", customerName='" + customerName + '\'' +
                ", customerPhone='" + customerPhone + '\'' +
                ", customerEmail='" + customerEmail + '\'' +
                ", assignmentHistory=" + assignmentHistory +
                ", totalRecords=" + totalRecords +
                ", currentPage=" + currentPage +
                ", pageSize=" + pageSize +
                ", totalPages=" + totalPages +
                '}';
    }
}
