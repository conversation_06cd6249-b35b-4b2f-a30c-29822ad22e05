# SimpleRole Employee Count Enhancement

## Overview

This document describes the enhancement to the `searchRoles` API endpoint in `SimpleRoleController` to include employee count statistics for each role in the search results.

## Implementation Summary

### 1. Database Relationship Analysis

**SimpleRole to Employee Relationship:**
- **SimpleRole** entity maps to `roles` table
- **Employee** entity has `role_id` field (Integer) that references `roles.id`
- **Relationship**: One-to-Many (one SimpleRole can have many Employees)
- **Employee Status**: Only active employees (`status = 'active'`) and non-deleted (`deleted_at IS NULL`) are counted

### 2. Enhanced Components

#### A. SimpleRole Entity Enhancement
**File**: `agis-core-base/src/main/java/vn/agis/crm/base/jpa/entity/SimpleRole.java`

**Changes:**
- Added `@Transient` field `totalEmployees` (Long) to hold employee count statistics
- This field is populated at runtime and not persisted to database

```java
// Transient field for employee count statistics
@Transient
private Long totalEmployees;
```

#### B. EmployeeRepository Enhancement
**File**: `agis-crm-be/src/main/java/vn/agis/crm/repository/EmployeeRepository.java`

**New Methods:**
1. **Individual Query Method:**
   ```java
   @Query("SELECT COUNT(e) FROM Employee e WHERE e.roleId = :roleId AND e.deletedAt IS NULL AND e.status = 'active'")
   long countActiveEmployeesByRoleId(@Param("roleId") Integer roleId);
   ```

2. **Batch Query Method (Performance Optimized):**
   ```java
   @Query("SELECT e.roleId, COUNT(e) FROM Employee e WHERE e.roleId IN :roleIds AND e.deletedAt IS NULL AND e.status = 'active' GROUP BY e.roleId")
   List<Object[]> countActiveEmployeesByRoleIds(@Param("roleIds") List<Integer> roleIds);
   ```

#### C. SimpleRoleService Enhancement
**File**: `agis-crm-be/src/main/java/vn/agis/crm/service/SimpleRoleService.java`

**Changes:**
1. **Added EmployeeRepository dependency**
2. **Enhanced search method** to include employee count statistics
3. **Added helper method** `enhanceRolesWithEmployeeCount()` for batch processing

**Key Implementation Details:**
- Uses batch query for performance optimization
- Converts Long role IDs to Integer for Employee.roleId compatibility
- Creates lookup map for O(1) employee count assignment
- Handles empty result sets gracefully

### 3. API Response Enhancement

#### Before Enhancement:
```json
{
  "content": [
    {
      "id": 1,
      "name": "Admin",
      "description": "Administrator role",
      "isActive": true,
      "createdAt": "2024-01-15T10:30:00Z"
    }
  ],
  "totalElements": 1,
  "totalPages": 1
}
```

#### After Enhancement:
```json
{
  "content": [
    {
      "id": 1,
      "name": "Admin", 
      "description": "Administrator role",
      "isActive": true,
      "createdAt": "2024-01-15T10:30:00Z",
      "totalEmployees": 5
    }
  ],
  "totalElements": 1,
  "totalPages": 1
}
```

### 4. Performance Considerations

#### Batch Query Optimization:
- **Single Query**: Retrieves employee counts for all roles in one database call
- **Time Complexity**: O(1) database calls regardless of result set size
- **Memory Efficiency**: Uses HashMap for O(1) lookup during count assignment

#### Query Performance:
```sql
-- Optimized batch query
SELECT e.role_id, COUNT(e) 
FROM employees e 
WHERE e.role_id IN (1,2,3,4,5) 
  AND e.deleted_at IS NULL 
  AND e.status = 'active' 
GROUP BY e.role_id;
```

**Database Indexes Recommended:**
- `employees(role_id, deleted_at, status)` - Composite index for optimal query performance
- `employees(status, deleted_at)` - Alternative index if role_id selectivity is low

### 5. Backward Compatibility

#### Maintained Compatibility:
- ✅ **API Endpoint**: Same URL `/simple-roles` with same parameters
- ✅ **Request Structure**: No changes to request parameters or format
- ✅ **Response Structure**: Only adds new `totalEmployees` field
- ✅ **Existing Fields**: All existing response fields remain unchanged
- ✅ **Pagination**: Same pagination behavior maintained
- ✅ **Filtering**: Same name and isActive filtering maintained

#### Client Impact:
- **Existing Clients**: Will continue to work without modification
- **New Clients**: Can utilize the new `totalEmployees` field for enhanced functionality
- **Field Type**: `totalEmployees` is Long type, handles large employee counts

### 6. Architecture Consistency

#### AGIS Pattern Compliance:
- ✅ **AMQP Messaging**: Uses existing AMQP communication between agis-http-api and agis-crm-be
- ✅ **Service Layer**: Business logic implemented in SimpleRoleService (agis-crm-be)
- ✅ **Repository Pattern**: Uses JPA repository with custom queries
- ✅ **DTO Pattern**: Enhances existing SimpleRole entity with transient field
- ✅ **Error Handling**: Maintains existing error handling patterns

#### Code Quality:
- **Single Responsibility**: Each method has a clear, focused purpose
- **Performance Optimized**: Batch queries minimize database calls
- **Null Safety**: Handles empty results and null values gracefully
- **Type Safety**: Proper type conversions between Long and Integer

### 7. Testing Scenarios

#### Functional Tests:
1. **Empty Result Set**: Returns empty list with totalEmployees = 0
2. **Single Role**: Returns role with correct employee count
3. **Multiple Roles**: Returns all roles with respective employee counts
4. **Inactive Employees**: Only counts active employees
5. **Deleted Employees**: Excludes soft-deleted employees
6. **Large Dataset**: Performance test with 1000+ roles and 10000+ employees

#### Edge Cases:
- Roles with zero employees (totalEmployees = 0)
- Roles with very large employee counts (>10000)
- Database connection issues (graceful degradation)
- Invalid role ID types (proper type conversion)

### 8. Deployment Considerations

#### Database Requirements:
- No schema changes required
- Existing indexes on `employees` table recommended for performance
- Compatible with both MySQL and PostgreSQL

#### Application Requirements:
- No configuration changes required
- Backward compatible with existing API consumers
- No additional dependencies introduced

#### Monitoring:
- Monitor query performance for large datasets
- Track API response times for search operations
- Monitor memory usage during batch processing

## Conclusion

The SimpleRole employee count enhancement successfully adds valuable employee statistics to the search results while maintaining full backward compatibility and following AGIS architectural patterns. The implementation uses performance-optimized batch queries and provides a foundation for future statistical enhancements.

### Key Benefits:
- **Enhanced User Experience**: Provides immediate insight into role usage
- **Performance Optimized**: Single batch query for all employee counts
- **Backward Compatible**: Existing clients continue to work unchanged
- **Scalable**: Handles large datasets efficiently
- **Maintainable**: Clean, well-documented code following established patterns
