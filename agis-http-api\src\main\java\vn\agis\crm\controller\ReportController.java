package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.data.domain.Page;
import org.springframework.http.*;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.CreateEmailGroupReq;
import vn.agis.crm.base.jpa.dto.req.FilterParamValueRequest;
import vn.agis.crm.base.jpa.dto.req.ReportSearchDTO;
import vn.agis.crm.base.jpa.dto.req.SearchReceivingGroupReq;
import vn.agis.crm.base.jpa.dto.resp.ReceivingGroupNameResponse;
import vn.agis.crm.base.jpa.dto.resp.SearchReportRespone;
import vn.agis.crm.base.jpa.entity.EmailGroup;
import vn.agis.crm.base.jpa.entity.Report;
import vn.agis.crm.base.jpa.entity.ReportSending;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.service.ReportService;

import java.util.List;


@RestController
@RequestMapping("/report")
public class ReportController extends CrudController<Report, Long> {
    private final Logger logger = LoggerFactory.getLogger(ReportController.class);
    ReportService reportService;


    @Autowired
    public ReportController(ReportService service) {
        super(service);
        this.reportService = service;
        this.baseUrl = "/report";
    }

    @GetMapping("/search")
    public ResponseEntity<Page<SearchReportRespone>> searchDevice(
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "status", required = false, defaultValue = " ") Integer status,
            @RequestParam(name = "fromDate", required = false, defaultValue = " ") Long fromDate,
            @RequestParam(name = "toDate", required = false, defaultValue = " ") Long toDate,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "createdDate,desc") String sortBy) {
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        ReportSearchDTO reportSearchDTO = new ReportSearchDTO(name, status, fromDate, toDate, page, size, sortBy);
        Page<SearchReportRespone> reportRespones = reportService.searchReport(reportSearchDTO, listRequest.getPageable());
        logger.info("Search Report End");
        return ResponseEntity.ok().body(reportRespones);
    }

    @GetMapping("/{id}")
    public ResponseEntity<Report> getDetailReport(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        Report report = reportService.get(id);
        return ResponseEntity.ok().body(report);
    }

    @PostMapping("")
    public ResponseEntity<Report> createReport(@RequestBody Report entity, HttpServletRequest httpServletRequest) {
        Report report = reportService.create(entity);
        return ResponseEntity.ok().body(report);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Report> updateReport(@PathVariable Long id, @RequestBody Report entity, HttpServletRequest httpServletRequest) {
        Report report = reportService.update(id, entity);
        return ResponseEntity.ok().body(report);
    }

    @PutMapping("/schedule/{id}")
    public ResponseEntity<Report> updateReportSChedule(@PathVariable Long id, @RequestBody Report entity, HttpServletRequest httpServletRequest) {
        Report report = reportService.updateReportSchedule(id, entity);
        return ResponseEntity.ok().body(report);
    }

    @PutMapping("/sending/{id}")
    public ResponseEntity<ReportSending> updateReportSending(@PathVariable Long id, @RequestBody ReportSending entity, HttpServletRequest httpServletRequest) {
        ReportSending report = reportService.updateReportSending(id, entity);
        return ResponseEntity.ok().body(report);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ResponseBase> deleteReport(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = reportService.deleteById(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @PutMapping("/changeStatus/{id}")
    public ResponseEntity<Report> changeStatusReport(@PathVariable Long id, @RequestParam Integer status, HttpServletRequest httpServletRequest) {
        Report entity = new Report();
        entity.setId(id);
        entity.setStatus(status);
        Report report = reportService.changeStatusReport(entity);
        return ResponseEntity.ok().body(report);
    }

    @GetMapping("/email-group/{id}")
    @PreAuthorize("hasAnyAuthority('getRptRecvGrp')")
    public ResponseEntity<EmailGroup> getEmailGroup(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        EmailGroup emailGroup = reportService.getEmailGroup(id);
        return ResponseEntity.ok().body(emailGroup);
    }

    @GetMapping("/email-group/checkName")
    public ResponseEntity<Long> checkExistName(@RequestParam(value = "name", required = true) String name, HttpServletRequest request) {
        try {
            return new ResponseEntity<>(reportService.countEmailName(name), HttpStatus.OK);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    @PostMapping("/email-group")
    @PreAuthorize("hasAnyAuthority('createRptRecvGrp')")
    public ResponseEntity<EmailGroup> createEmailGroup(@RequestBody CreateEmailGroupReq entity, HttpServletRequest httpServletRequest) {
        EmailGroup emailGroup = reportService.createEmailGroup(entity);
        return ResponseEntity.ok().body(emailGroup);
    }

    @PutMapping("/email-group/{id}")
    @PreAuthorize("hasAnyAuthority('updateRptRecvGrp')")
    public ResponseEntity<EmailGroup> updateEmailGroup(@RequestBody CreateEmailGroupReq entity, @PathVariable Long id, HttpServletRequest httpServletRequest) {
        EmailGroup emailGroup = reportService.updateEmailGroup(entity, id);
        return ResponseEntity.ok().body(emailGroup);
    }

    @DeleteMapping("/email-group/{id}")
    @PreAuthorize("hasAnyAuthority('deleteRptRecvGrp')")
    public ResponseEntity<ResponseBase> deleteEmailGroup(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = reportService.deleteEmailGroup(id);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @GetMapping("/email-group/search")
    @Operation(description = "Danh sách Email Group")
    @PreAuthorize("hasAnyAuthority('searchRptRecvGrp')")
    public ResponseEntity<Page<ReceivingGroupNameResponse>> searchEmailGroup(
            @RequestParam(name = "name", required = false, defaultValue = " ") String name,
            @RequestParam(name = "description", required = false, defaultValue = " ") String description,
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
            @RequestParam(name = "sort", required = false, defaultValue = "name,desc") String sortBy) {
        logger.info("--- Execute searchEmailGroup method: Start --");
        ListRequest listRequest = new ListRequest(size, page, sortBy);
        SearchReceivingGroupReq searchDTO = new SearchReceivingGroupReq(name, description, page, size, sortBy);
        Page<ReceivingGroupNameResponse> emailGroups = reportService.searchEmailGroup(searchDTO, listRequest.getPageable());
        logger.info("--- Execute searchEmailGroup method: End --");
        return ResponseEntity.ok().body(emailGroups);
    }

    @GetMapping("/email-group/all")
//    @PreAuthorize("hasAnyAuthority('searchRptRecvGrp')")
    public ResponseEntity<List<EmailGroup>> listAllEmailGroup() {
        logger.info("--- Execute listAllEmailGroup method: Start --");
        List<EmailGroup> emailGroups = reportService.listAllEmailGroup();
        logger.info("--- Execute listAllEmailGroup method: End --");
        return ResponseEntity.ok().body(emailGroups);
    }

    @PostMapping("/email-group/delete-many")
    @PreAuthorize("hasAnyAuthority('deleteRptRecvGrp')")
    public ResponseEntity<ResponseBase> deleteManyAlert(@RequestBody List<Long> ids, HttpServletRequest httpServletRequest) {
        ResponseBase result = new ResponseBase();
        result = reportService.deleteManyEmailGroup(ids);
        return new ResponseEntity<>(result, HttpStatusCode.valueOf(result.getErrorCode()));
    }

    @GetMapping("/checkExits")
    public ResponseEntity<Long> checkExistNameReport(@RequestParam(value = "name", required = true) String name, HttpServletRequest request) {
        try {
            return new ResponseEntity<>(service.count(name), HttpStatus.OK);
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }


    @PostMapping("/preview")
    public ResponseEntity<Object> previewReport(@RequestBody FilterParamValueRequest filterParamValueRequest) {
        return ResponseEntity.ok(reportService.previewReport(filterParamValueRequest));
    }

    @PostMapping("/export")
    public ResponseEntity<InputStreamResource>  exportReport(@RequestBody FilterParamValueRequest filterParamValueRequest, HttpServletResponse r) throws Exception {

        InputStreamResource file = new InputStreamResource(reportService.exportReport(filterParamValueRequest));
//        ExportReportResponse exportReportResponse = reportService.exportReport(filterParamValueRequest, r);
//        String filename = exportReportResponse.getFilename();
        String filename = "report.xlsx";

        HttpHeaders headers = new HttpHeaders();
        headers.set(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename);
        headers.set(HttpHeaders.CONTENT_TYPE, "application/octet-stream");
        return new ResponseEntity<>(file, headers, HttpStatus.OK);

//        String encodedFileName = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
//        r.setHeader("Content-Description", "File Transfer");
//        r.setContentType("application/octet-stream");
//        r.setCharacterEncoding("UTF-8");
//        r.setHeader("Content-Disposition", "attachment; filename=" + filename  );
//        r.setHeader("Content-Transfer-Encoding", "binary");
//        r.setHeader("Connection", "Keep-Alive");
//        r.setContentLength(exportReportResponse.getBytes().length);
//        OutputStream os = r.getOutputStream();
//        try {
//            os.write(exportReportResponse.getBytes(), 0, exportReportResponse.getBytes().length);
//        } catch (Exception excp) {
//            //handle error
//        } finally {
//            os.close();
//            os.flush();
//        }
    }

    @GetMapping("/all/permission")
    public ResponseEntity<List<Report>> getAllReportByPermission(){
        List<Report> reports = reportService.getAllReportByPermission();
        return ResponseEntity.ok().body(reports);
    }
}
