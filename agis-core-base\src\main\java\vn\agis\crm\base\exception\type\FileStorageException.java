/**
 * <AUTHOR> on 20/01/2021
 */

package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

public class FileStorageException extends BaseException {

    /**
	 *
	 */
	private static final long serialVersionUID = 8992677737429751067L;

	public FileStorageException(String title, String entityName, String field, String errorCode) {
		super(title, entityName, Collections.singletonList(field), errorCode);
	}

	public FileStorageException(String title, String entityName, List<String> field, String errorCode) {
		super(title, entityName, field, errorCode);
	}
}
