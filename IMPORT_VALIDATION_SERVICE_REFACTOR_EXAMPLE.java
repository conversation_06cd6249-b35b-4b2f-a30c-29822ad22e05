// Alternative Solution: Refactor to Service Class with @Autowired

package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.dto.DryRunResultDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.util.ImportDataValidator;

import java.util.*;

/**
 * OPTION 1: Convert ImportDataValidator to Service Class
 * 
 * ✅ Advantages:
 * - Clean @Autowired dependency injection
 * - Better performance (no bean lookup overhead)
 * - Clear dependency declaration
 * - Better testability with @MockBean
 * - IDE support for refactoring
 * 
 * ❌ Trade-offs:
 * - Need to inject service into other classes
 * - More complex for simple utility functions
 * - Requires Spring context for all usage
 */
@Service
@Transactional(readOnly = true)
public class ImportValidationService {

    @Autowired
    private CustomerRepository customerRepository;

    /**
     * Validate import data with enhanced rules
     */
    public List<ValidationResultDto> validateImportData(List<LinkedHashMap<String, String>> rowsData, Long jobId) {
        List<ValidationResultDto> results = new ArrayList<>();
        
        // Load existing phones once for batch validation
        Set<String> existingPhones = loadExistingPhonesFromDatabase();
        Set<String> filePhones = new HashSet<>();
        
        for (LinkedHashMap<String, String> rowData : rowsData) {
            ValidationResultDto validation = validateSingleRow(rowData, jobId, existingPhones, filePhones);
            results.add(validation);
        }
        
        return results;
    }

    /**
     * Validate single row with all enhanced rules
     */
    public ValidationResultDto validateSingleRow(Map<String, String> rowData, Long jobId, 
                                                Set<String> existingPhones, Set<String> filePhones) {
        Integer rowNumber = Integer.parseInt(rowData.getOrDefault("__ROW_NUMBER__", "0"));
        ValidationResultDto result = new ValidationResultDto(rowNumber);
        
        // Use static utility methods for actual validation logic
        ImportDataValidator.validateRequiredFields(rowData, jobId, rowNumber, result);
        validatePhoneWithRepository(rowData, jobId, rowNumber, result, existingPhones, filePhones);
        ImportDataValidator.validateEmailEnhanced(rowData, jobId, rowNumber, result);
        ImportDataValidator.validateBirthDate(rowData, jobId, rowNumber, result);
        ImportDataValidator.validateMaritalStatus(rowData, jobId, rowNumber, result);
        
        return result;
    }

    /**
     * Enhanced phone validation using injected repository
     */
    private void validatePhoneWithRepository(Map<String, String> rowData, Long jobId, Integer rowNumber, 
                                           ValidationResultDto result, Set<String> existingPhones, Set<String> filePhones) {
        String phone = getPhoneFromRowData(rowData);
        
        if (phone == null || phone.trim().isEmpty()) {
            result.addError(createPhoneRequiredError(jobId, rowNumber));
            return;
        }
        
        // Format validation
        if (!isValidVietnamesePhone(phone)) {
            result.addError(createPhoneFormatError(jobId, rowNumber, phone));
            return;
        }
        
        String normalizedPhone = normalizeVietnamesePhone(phone);
        
        // Check duplicate in system using injected repository
        if (existingPhones.contains(normalizedPhone)) {
            result.addError(createPhoneDuplicateSystemError(jobId, rowNumber, phone));
            return;
        }
        
        // Check duplicate in file
        if (filePhones.contains(normalizedPhone)) {
            result.addError(createPhoneDuplicateFileError(jobId, rowNumber, phone));
            return;
        }
        
        filePhones.add(normalizedPhone);
    }

    /**
     * Load existing phones using injected repository (better performance)
     */
    private Set<String> loadExistingPhonesFromDatabase() {
        try {
            // Direct repository access - no bean lookup overhead
            List<Customers> customers = customerRepository.findAll();
            Set<String> existingPhones = new HashSet<>();
            
            for (Customers customer : customers) {
                if (customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                    String normalizedPhone = normalizeVietnamesePhone(customer.getPhone());
                    if (normalizedPhone != null) {
                        existingPhones.add(normalizedPhone);
                    }
                }
            }
            
            return existingPhones;
        } catch (Exception e) {
            logger.error("Error loading existing phones: {}", e.getMessage(), e);
            return new HashSet<>();
        }
    }

    // Helper methods...
    private String getPhoneFromRowData(Map<String, String> rowData) {
        // Implementation...
    }
    
    private boolean isValidVietnamesePhone(String phone) {
        // Implementation...
    }
    
    private String normalizeVietnamesePhone(String phone) {
        // Implementation...
    }
}

/**
 * OPTION 2: Hybrid Approach - Service for Repository Access, Static for Validation Logic
 */
@Service
@Transactional(readOnly = true)
public class ImportDataRepositoryService {

    @Autowired
    private CustomerRepository customerRepository;

    /**
     * Repository operations only - validation logic stays in static utilities
     */
    public Set<String> loadExistingPhones() {
        List<Customers> customers = customerRepository.findAll();
        Set<String> existingPhones = new HashSet<>();
        
        for (Customers customer : customers) {
            if (customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                String normalizedPhone = ImportDataValidator.normalizeVietnamesePhone(customer.getPhone());
                if (normalizedPhone != null) {
                    existingPhones.add(normalizedPhone);
                }
            }
        }
        
        return existingPhones;
    }

    public boolean phoneExistsInDatabase(String phone) {
        String normalizedPhone = ImportDataValidator.normalizeVietnamesePhone(phone);
        return customerRepository.existsByPhone(normalizedPhone);
    }
}

/**
 * Updated ImportDataValidator - Static validation logic only
 */
public class ImportDataValidator {

    // Keep all static validation methods
    public static ValidationResultDto validateRow(Map<String, String> rowData, Long jobId, 
                                                Set<String> existingPhones, Set<String> filePhones) {
        // Static validation logic - no repository access
        // Use pre-loaded existingPhones set
    }

    // All validation methods stay static
    public static void validatePhoneEnhanced(...) { }
    public static void validateEmailEnhanced(...) { }
    public static void validateBirthDate(...) { }
    public static void validateMaritalStatus(...) { }
}

/**
 * Updated ImportDryRunProcessor - Uses service for repository access
 */
public class ImportDryRunProcessor {

    public static DryRunResultDto processDryRun(ImportJob job, String filePath) {
        // Get repository service from Spring context
        ImportDataRepositoryService repoService = SpringContextUtils.getBean(ImportDataRepositoryService.class);
        
        // Load existing phones once
        Set<String> existingPhones = repoService != null ? 
            repoService.loadExistingPhones() : new HashSet<>();
        
        // Use static validation methods
        List<ValidationResultDto> results = validateData(rowsData, job.getId(), existingPhones);
        
        return ImportStatisticsCalculator.calculateStatistics(job.getId(), job.getFileName(), results, startTime, new Date());
    }

    private static List<ValidationResultDto> validateData(List<LinkedHashMap<String, String>> rowsData, 
                                                        Long jobId, Set<String> existingPhones) {
        List<ValidationResultDto> results = new ArrayList<>();
        Set<String> filePhones = new HashSet<>();
        
        for (LinkedHashMap<String, String> rowData : rowsData) {
            ValidationResultDto validation = ImportDataValidator.validateRow(rowData, jobId, existingPhones, filePhones);
            results.add(validation);
        }
        
        return results;
    }
}

/**
 * OPTION 3: Dependency Injection via Constructor (for testability)
 */
public class ImportDataValidator {

    private final CustomerRepository customerRepository;

    // Constructor injection for testing
    public ImportDataValidator(CustomerRepository customerRepository) {
        this.customerRepository = customerRepository;
    }

    // Default constructor uses SpringContextUtils for backward compatibility
    public ImportDataValidator() {
        this.customerRepository = SpringContextUtils.getBean(CustomerRepository.class);
    }

    public ValidationResultDto validateRow(Map<String, String> rowData, Long jobId) {
        // Use this.customerRepository for database operations
        Set<String> existingPhones = loadExistingPhones();
        // ... validation logic
    }

    private Set<String> loadExistingPhones() {
        if (customerRepository == null) {
            return new HashSet<>();
        }
        // ... implementation
    }
}

/**
 * COMPARISON OF APPROACHES
 */
class ApproachComparison {
    
    public static void main(String[] args) {
        System.out.println("=== Import Validation Refactoring Options ===");
        
        System.out.println("\n🏆 OPTION 1: Full Service Class");
        System.out.println("✅ Clean @Autowired injection");
        System.out.println("✅ Best performance");
        System.out.println("✅ Easy testing with @MockBean");
        System.out.println("❌ Need to inject service everywhere");
        System.out.println("❌ More complex for simple utilities");
        
        System.out.println("\n⚖️ OPTION 2: Hybrid Approach (RECOMMENDED)");
        System.out.println("✅ Repository operations in service");
        System.out.println("✅ Validation logic stays static");
        System.out.println("✅ Minimal code changes");
        System.out.println("✅ Good performance");
        System.out.println("⚠️ Still uses SpringContextUtils for service access");
        
        System.out.println("\n🔧 OPTION 3: Constructor Injection");
        System.out.println("✅ Testable with constructor injection");
        System.out.println("✅ Backward compatible");
        System.out.println("❌ More complex instantiation");
        System.out.println("❌ Still needs SpringContextUtils fallback");
        
        System.out.println("\n🎯 CURRENT IMPLEMENTATION (SpringContextUtils)");
        System.out.println("✅ Works with static methods");
        System.out.println("✅ Minimal code changes");
        System.out.println("✅ Graceful fallback");
        System.out.println("⚠️ Runtime dependency resolution");
        System.out.println("⚠️ Hidden dependencies");
        
        System.out.println("\n📊 RECOMMENDATION FOR AGIS CRM:");
        System.out.println("Keep current SpringContextUtils approach because:");
        System.out.println("1. Minimal performance impact with proper caching");
        System.out.println("2. Works well with existing static utility pattern");
        System.out.println("3. Graceful degradation when Spring context unavailable");
        System.out.println("4. Easy to maintain and understand");
        System.out.println("5. Follows established patterns in the codebase");
    }
}
