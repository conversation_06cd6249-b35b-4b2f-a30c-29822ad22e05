-- Migration Script: Add Unit Detail Fields to units table
-- Description: Add floor_area, floor_number, and unit_number fields to units table
-- Date: 2024-01-18
-- Author: AGIS CRM Enhancement

-- Add floor_area field (construction area in square meters)
ALTER TABLE units 
ADD COLUMN floor_area DECIMAL(10,2) NULL 
COMMENT 'Floor construction area in square meters (dien_tich_san_xay_dung/dien_tich_thong_thuy)';

-- Add floor_number field (floor where the unit is located)
ALTER TABLE units 
ADD COLUMN floor_number VARCHAR(50) NULL 
COMMENT 'Floor number where the unit is located (tang)';

-- Add unit_number field (unit/apartment number)
ALTER TABLE units 
ADD COLUMN unit_number VARCHAR(50) NULL 
COMMENT 'Unit number/apartment number (can_so)';

-- Create indexes for better search performance
CREATE INDEX idx_units_floor_area ON units (floor_area);
CREATE INDEX idx_units_floor_number ON units (floor_number);
CREATE INDEX idx_units_unit_number ON units (unit_number);

-- Sample data updates (commented out - uncomment if needed for testing)
/*
-- Update sample units with new field values
UPDATE units SET 
    floor_area = 85.50,
    floor_number = '5',
    unit_number = 'A501'
WHERE id = 1;

UPDATE units SET 
    floor_area = 120.75,
    floor_number = '10',
    unit_number = 'B1002'
WHERE id = 2;

UPDATE units SET 
    floor_area = 95.25,
    floor_number = 'G',
    unit_number = 'G001'
WHERE id = 3;
*/

-- Verification queries
-- Check if columns were added successfully
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'units' 
AND COLUMN_NAME IN ('floor_area', 'floor_number', 'unit_number')
ORDER BY COLUMN_NAME;

-- Check indexes were created
SELECT 
    INDEX_NAME,
    COLUMN_NAME,
    TABLE_NAME
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_NAME = 'units' 
AND INDEX_NAME IN ('idx_units_floor_area', 'idx_units_floor_number', 'idx_units_unit_number')
ORDER BY INDEX_NAME, SEQ_IN_INDEX;

-- Sample query to verify data structure
SELECT 
    id,
    name,
    product_type,
    floor_area,
    floor_number,
    unit_number,
    created_at
FROM units 
LIMIT 5;

-- Performance test query
EXPLAIN SELECT * FROM units 
WHERE floor_area BETWEEN 80.0 AND 120.0 
AND floor_number IN ('5', '10', 'G')
AND unit_number LIKE 'A%';
