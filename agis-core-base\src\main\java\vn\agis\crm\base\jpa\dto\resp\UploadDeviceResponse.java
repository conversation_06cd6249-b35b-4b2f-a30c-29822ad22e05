package vn.agis.crm.base.jpa.dto.resp;

import lombok.Data;
import vn.agis.crm.base.jpa.dto.req.ImportDeviceRowItem;

import java.util.List;

@Data
public class UploadDeviceResponse {
    private Integer errorCode;
    private String errorMsg;
    private List<ImportDeviceRowItem> errorList;
//    private List<Device> deviceNotExits;
//    private List<Device> deviceAssign;
//    private List<Device> deviceEmptyMsisdn;
//    public Integer getErrorCode() {
//        return errorCode;
//    }
//
//    public void setErrorCode(Integer errorCode) {
//        this.errorCode = errorCode;
//    }
//
//    public String getErrorMsg() {
//        return errorMsg;
//    }
//
//    public void setErrorMsg(String errorMsg) {
//        this.errorMsg = errorMsg;
//    }
//
//    public List<Device> getDeviceNotExits() {
//        return deviceNotExits;
//    }
//
//    public void setDeviceNotExits(List<Device> deviceNotExits) {
//        this.deviceNotExits = deviceNotExits;
//    }
//
//    public List<Device> getDeviceAssign() {
//        return deviceAssign;
//    }
//
//    public void setDeviceAssign(List<Device> deviceAssign) {
//        this.deviceAssign = deviceAssign;
//    }
//
//    public List<Device> getDeviceEmptyMsisdn() {
//        return deviceEmptyMsisdn;
//    }
//
//    public void setDeviceEmptyMsisdn(List<Device> deviceEmptyMsisdn) {
//        this.deviceEmptyMsisdn = deviceEmptyMsisdn;
//    }
}
