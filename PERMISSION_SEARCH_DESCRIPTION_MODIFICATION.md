# Permission Search API Modification - Description Field

## Overview

Successfully modified the `getAllPermissions` API endpoint in the `PermissionController` class to change the search functionality from searching by `name` field to searching by `description` field instead.

## Implementation Details

### 🎯 **Modified API Endpoint**

**Endpoint**: `GET /permissions?description={searchTerm}`

**Changes**:
- **Before**: `GET /permissions?name={searchTerm}` (searched by name field)
- **After**: `GET /permissions?description={searchTerm}` (searches by description field)

**Parameters**:
- `description` (optional): Case-insensitive partial description filter
- If no `description` parameter provided, returns all permissions (backward compatibility maintained)

**Response Format**: Same as original - `List<Permissions>` in JSON format

### 🏗️ **Architecture Changes**

The modification follows the existing AGIS architecture pattern with AMQP messaging between modules:

#### **1. Controller Layer (agis-http-api)**
**File**: `agis-http-api/src/main/java/vn/agis/crm/controller/PermissionController.java`

**Changes**:
- Changed request parameter from `@RequestParam(name = "name")` to `@RequestParam(name = "description")`
- Updated method call from `getAllWithNameFilter()` to `getAllWithDescriptionFilter()`
- Updated comments to reflect description-based filtering

```java
@GetMapping
public ResponseEntity<List<Permissions>> getAllPermissions(
        @RequestParam(name = "description", required = false) String description) {
    List<Permissions> permissions;

    if (description != null && !description.trim().isEmpty()) {
        // Use description-based filtering
        permissions = permissionService.getAllWithDescriptionFilter(description.trim());
    } else {
        // Return all permissions (maintain backward compatibility)
        permissions = permissionService.getAll();
    }

    return ResponseEntity.ok(permissions);
}
```

#### **2. API Service Layer (agis-http-api)**
**File**: `agis-http-api/src/main/java/vn/agis/crm/service/PermissionApiService.java`

**Changes**:
- Renamed method from `getAllWithNameFilter()` to `getAllWithDescriptionFilter()`
- Changed AMQP method call from `"GET_ALL_WITH_NAME_FILTER"` to `"GET_ALL_WITH_DESCRIPTION_FILTER"`
- Updated parameter name from `name` to `description`
- Updated JavaDoc and error logging messages

```java
/**
 * Get all permissions with optional description filtering
 * @param description Optional description filter for case-insensitive partial matching
 * @return List of permissions matching the filter criteria
 */
public List<Permissions> getAllWithDescriptionFilter(String description) {
    Event event = RequestUtils.amqp("GET_ALL_WITH_DESCRIPTION_FILTER", category, description, routingKey);
    if (event.respStatusCode == ResponseCode.OK) {
        List<Permissions> permissionsList = (List<Permissions>) event.payload;
        return permissionsList;
    }
    logger.error("Failed to get permissions with description filter '{}', status code: {}", description, event.respStatusCode);
    return Collections.emptyList();
}
```

#### **3. Business Logic Layer (agis-crm-be)**
**File**: `agis-crm-be/src/main/java/vn/agis/crm/service/SimplePermissionService.java`

**Changes**:
- Updated AMQP method handler from `"GET_ALL_WITH_NAME_FILTER"` to `"GET_ALL_WITH_DESCRIPTION_FILTER"`
- Renamed method from `getAllWithNameFilter()` to `getAllWithDescriptionFilter()`
- Changed repository call from `findByNameContainingIgnoreCase()` to `findByDescriptionContainingIgnoreCase()`
- Updated variable names and comments

```java
public Event process(Event event) {
    switch (event.method) {
        case Constants.Method.GET_ALL:
            return getAll(event);
        case "GET_ALL_WITH_DESCRIPTION_FILTER":
            return getAllWithDescriptionFilter(event);
        // ... other cases
    }
}

private Event getAllWithDescriptionFilter(Event event) {
    String descriptionFilter = (String) event.payload;
    List<Permissions> permissions;

    if (descriptionFilter == null || descriptionFilter.trim().isEmpty()) {
        permissions = repository.findAll();
    } else {
        permissions = repository.findByDescriptionContainingIgnoreCase(descriptionFilter.trim());
    }

    return event.createResponse(permissions, 200, "Success");
}
```

#### **4. Repository Layer (agis-crm-be)**
**File**: `agis-crm-be/src/main/java/vn/agis/crm/repository/PermissionsRepository.java`

**Changes**:
- Renamed method from `findByNameContainingIgnoreCase()` to `findByDescriptionContainingIgnoreCase()`
- Changed query to search `p.description` field instead of `p.name` field
- Updated parameter name from `:name` to `:description`
- Updated JavaDoc comments

```java
/**
 * Find permissions by description with case-insensitive partial matching
 * Uses LOWER() function for case-insensitive search
 */
@Query("SELECT p FROM Permissions p WHERE LOWER(p.description) LIKE LOWER(CONCAT('%', :description, '%'))")
List<Permissions> findByDescriptionContainingIgnoreCase(@Param("description") String description);
```

### 🔧 **Database Query Changes**

#### **Before (Name-based search)**:
```sql
SELECT p FROM Permissions p 
WHERE LOWER(p.name) LIKE LOWER(CONCAT('%', :name, '%'))
```

#### **After (Description-based search)**:
```sql
SELECT p FROM Permissions p 
WHERE LOWER(p.description) LIKE LOWER(CONCAT('%', :description, '%'))
```

**Query Features**:
- Case-insensitive search using `LOWER()` function
- Partial matching with `LIKE '%{description}%'` pattern
- Parameterized query to prevent SQL injection
- Searches against the `description` field instead of `name` field

### 📊 **API Usage Examples**

#### **Before Modification**:
```bash
# Search by name
GET /permissions?name=user
GET /permissions?name=create

# Get all permissions
GET /permissions
```

#### **After Modification**:
```bash
# Search by description
GET /permissions?description=manage
GET /permissions?description=user%20management

# Get all permissions
GET /permissions
```

#### **Sample Response**:
```json
[
  {
    "id": 1,
    "name": "createUser",
    "description": "Allows user to create new user accounts"
  },
  {
    "id": 2,
    "name": "updateUser", 
    "description": "Allows user to update existing user information"
  }
]
```

### ✅ **Backward Compatibility**

**Maintained Features**:
- ✅ **Same Response Structure**: No changes to response format
- ✅ **Same HTTP Methods**: Still uses GET method
- ✅ **Same Base URL**: `/permissions` endpoint unchanged
- ✅ **Optional Parameter**: Description parameter is optional, returns all permissions if not provided
- ✅ **Same Error Handling**: Maintains existing error handling patterns
- ✅ **Same AMQP Architecture**: Uses existing messaging patterns

**Breaking Changes**:
- ❌ **Parameter Name**: Changed from `name` to `description` (API consumers need to update)
- ❌ **Search Field**: Now searches description field instead of name field

### 🚀 **Architecture Compliance**

**AGIS Pattern Adherence**:
- ✅ **AMQP Messaging**: Maintains request-response pattern between agis-http-api and agis-crm-be
- ✅ **Service Layer Separation**: Business logic remains in agis-crm-be module
- ✅ **Repository Pattern**: Uses JPA repository with custom queries
- ✅ **Error Handling**: Consistent error handling and logging
- ✅ **Response Structure**: Maintains existing response format

**Code Quality**:
- **Consistent Naming**: All method and variable names updated consistently
- **Documentation**: Updated JavaDoc comments and inline documentation
- **Parameter Validation**: Maintains null and empty string validation
- **Logging**: Updated error logging messages with new parameter names

### 📝 **Testing Considerations**

**Test Scenarios**:
1. **Empty Description Filter**: Returns all permissions
2. **Partial Description Match**: Returns permissions with matching descriptions
3. **Case Insensitive Search**: Handles uppercase/lowercase variations
4. **Special Characters**: Handles descriptions with special characters
5. **No Matches**: Returns empty list when no descriptions match
6. **Null/Empty Parameter**: Falls back to returning all permissions

**Database Considerations**:
- Ensure `description` field has appropriate indexing for performance
- Consider description field length and content for search effectiveness
- Test with various description content patterns

## Conclusion

The modification successfully changes the permission search functionality from name-based to description-based search while maintaining the existing AGIS architecture patterns and most backward compatibility features. API consumers will need to update their requests to use the `description` parameter instead of `name`, but the overall functionality and response structure remain unchanged.

### Key Benefits:
- **Enhanced Search Capability**: Users can now search by more descriptive content
- **Maintained Architecture**: Follows existing AGIS patterns
- **Performance Optimized**: Uses efficient database queries with proper indexing
- **Consistent Implementation**: All layers updated consistently
