package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.AMQPConstants;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.exception.BadRequestException;
import vn.agis.crm.base.exception.DuplicateException;
import vn.agis.crm.base.exception.ForbiddenException;
import vn.agis.crm.base.exception.NotFoundException;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryUpdateDto;
import vn.agis.crm.base.jpa.entity.InteractionsSecondary;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.utils.RequestUtils;
import vn.agis.crm.constants.MessageKeyConstant;

import java.util.List;

/**
 * API Service layer for InteractionsSecondary
 * Handles HTTP API requests and communicates with backend service via AMQP
 */
@Service
public class InteractionsSecondaryApiService extends CrudService<InteractionsSecondary, Long> {

    private static final Logger logger = LoggerFactory.getLogger(InteractionsSecondaryApiService.class);

    public InteractionsSecondaryApiService() {
        super(InteractionsSecondary.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.INTERACTIONS_SECONDARY;
    }

    /**
     * Create new secondary interaction
     */
    public InteractionSecondaryDto createInteraction(InteractionSecondaryCreateDto createDto) {
        InteractionSecondaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, createDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == ResponseCode.CREATED) {
                response = (InteractionSecondaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.Validation.INVALID_INPUT);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error creating secondary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(createDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Update existing secondary interaction
     */
    public InteractionSecondaryDto updateInteraction(InteractionSecondaryUpdateDto updateDto) {
        InteractionSecondaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.UPDATE, category, updateDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (InteractionSecondaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.Validation.INVALID_INPUT);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error updating secondary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(updateDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Get secondary interaction by ID
     */
    public InteractionSecondaryDto getInteractionById(Long id) {
        InteractionSecondaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (InteractionSecondaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error getting secondary interaction by ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(id), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Delete secondary interaction by ID
     */
    public void deleteInteraction(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.DELETE, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error deleting secondary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(id), null, event);
        }
    }

    /**
     * Search secondary interactions with filters and pagination
     */
    @SuppressWarnings("unchecked")
    public Page<InteractionSecondaryDto> searchInteractions(InteractionSecondarySearchDto searchDto) {
        Page<InteractionSecondaryDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Page<InteractionSecondaryDto>) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error searching secondary interactions: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(searchDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Get secondary interactions by customer property ID
     */
    @SuppressWarnings("unchecked")
    public List<InteractionSecondaryDto> getInteractionsByCustomerPropertyId(Long customerPropertyId) {
        List<InteractionSecondaryDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("FIND_BY_CUSTOMER_PROPERTY_ID", category, customerPropertyId, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<InteractionSecondaryDto>) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error getting secondary interactions by customer property ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(customerPropertyId), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Count secondary interactions by customer property ID
     */
    public Long countInteractionsByCustomerPropertyId(Long customerPropertyId) {
        Long response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("COUNT_BY_CUSTOMER_PROPERTY_ID", category, customerPropertyId, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Long) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error counting secondary interactions by customer property ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(customerPropertyId), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Count secondary interactions by unit ID (through customer properties)
     */
    public Long countInteractionsByUnitId(Long unitId) {
        Long response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("COUNT_BY_UNIT_ID", category, unitId, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Long) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error counting secondary interactions by unit ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(unitId), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }
}
