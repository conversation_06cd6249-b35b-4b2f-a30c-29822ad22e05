package vn.agis.crm.base.jpa.dto.req;

import lombok.Getter;
import lombok.Setter;
import vn.agis.crm.base.utils.SqlUtils;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Objects;

@Getter
@Setter
public class ReportSearchDTO {
    private String name;
    private Integer status;
    private String fromDate;
    private String toDate;
    private Integer page;
    private Integer size;
    private String sortBy;
    public ReportSearchDTO(
            String name,
            Integer status,
            Long fromDate,
            Long toDate,
            Integer page,
            Integer size,
            String sortBy) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        this.name = Objects.isNull(name) ? " " : SqlUtils.optimizeSearchLike(name);
        this.status = Objects.isNull(status) ? -1 : status;
        this.fromDate = Objects.isNull(fromDate) ? " " : formatter.format(new Date(fromDate));
        this.toDate = Objects.isNull(toDate) ? " " : formatter.format(new Date(toDate));
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}
