package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.SimpleRole;

import java.util.Optional;

@Repository
public interface SimpleRoleRepository extends JpaRepository<SimpleRole, Long> {

    boolean existsByName(String name);

    SimpleRole findFirstByName(String name);

    @Query("SELECT r FROM SimpleRole r WHERE (:name IS NULL OR LOWER(r.name) LIKE LOWER(CONCAT('%', :name, '%'))) AND (:isActive IS NULL OR r.isActive = :isActive)")
    Page<SimpleRole> search(@Param("name") String name,
                            @Param("isActive") Boolean isActive,
                            Pageable pageable);

    Optional<SimpleRole> findById(Long ids);
}

