package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

/**
 * 400
 * Whren throw new this class, the error returned will contain an error code is 400
 */
public class BadRequestException extends BaseException {
    /**
     *
     */
    private static final long serialVersionUID = 942045642638174548L;

    public BadRequestException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, Collections.singletonList(field), errorCode);
    }

    public BadRequestException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }
}
