package vn.agis.crm.service.chatbot;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import dev.langchain4j.data.embedding.Embedding;
import dev.langchain4j.model.openai.OpenAiEmbeddingModel;
import dev.langchain4j.store.embedding.EmbeddingMatch;
import dev.langchain4j.store.embedding.inmemory.InMemoryEmbeddingStore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import vn.agis.crm.exception.OpenAIKeyNotFoundException;
import vn.agis.crm.exception.ParameterNotFoundException;
import vn.agis.crm.service.ChatVirtualAssistantService;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


@Service
public class SemanticSearchService {
    private final Logger logger = LoggerFactory.getLogger(SemanticSearchService.class);

    private final OpenAiEmbeddingModel embeddingModel;
    private final InMemoryEmbeddingStore<String> store;
    private final List<SqlTemplate> templates;

    private final dev.langchain4j.model.openai.OpenAiChatModel chatModel;

    public SemanticSearchService(@Value("${openai.api.key:123}") String apiKey) throws IOException {
        this.embeddingModel = OpenAiEmbeddingModel.builder()
                .apiKey(apiKey)
                .modelName("text-embedding-3-small") // model thông minh hơn
                .build();

        this.chatModel = dev.langchain4j.model.openai.OpenAiChatModel.builder()
                .apiKey(apiKey)
                .modelName("gpt-4o-mini") // hoặc gpt-4o nếu cần chính xác hơn
                .build();

        this.store = new InMemoryEmbeddingStore<>();
        this.templates = loadTemplates();
        indexTemplates();
    }

    private List<SqlTemplate> loadTemplates() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            InputStream is = getClass().getClassLoader().getResourceAsStream("templates.json");
            return mapper.readValue(is, new TypeReference<List<SqlTemplate>>() {
            });
        } catch (Exception e) {
            logger.error("Init template error", e);
            return Collections.emptyList();
        }
    }

    private void indexTemplates() {
        for (SqlTemplate tpl : templates) {
            List<String> texts = new ArrayList<>();
            texts.add(tpl.getDescription());
            if (tpl.getExamples() != null) texts.addAll(tpl.getExamples());

            for (String text : texts) {
                Embedding emb = embeddingModel.embed(text).content();
                store.add(emb, tpl.getId());
            }
        }
    }

    public String buildQuery(String question) throws Exception {
        // 1. Semantic search để chọn template
        Embedding emb = embeddingModel.embed(question).content();
        List<EmbeddingMatch<String>> matches = store.findRelevant(emb, 1);
        String templateId = matches.get(0).embedded();

        SqlTemplate tpl = templates.stream()
                .filter(t -> t.getId().equals(templateId))
                .findFirst()
                .orElseThrow();

        // 2. NLP extraction + SQL generation
        String prompt = """
                Bạn là một hệ thống NLP hỗ trợ sinh câu SQL.
                Nhiệm vụ:
                1. Đọc câu hỏi người dùng.
                2. Hiểu template SQL sau: %s
                3. Tự động trích xuất tham số cần thiết từ câu hỏi.
                4. Trả về trực tiếp câu SQL cuối cùng, KHÔNG có giải thích, KHÔNG có text thừa.
                Câu hỏi: "%s"
                """.formatted(tpl.getSql(), question);

        String sql = chatModel.generate(prompt).trim();
        logger.info("Final SQL: {}", sql);

        return sql;
    }
}
