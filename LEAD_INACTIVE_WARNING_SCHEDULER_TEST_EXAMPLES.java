// Test examples and usage scenarios for LeadInactiveWarningNotificationScheduler
// This demonstrates how to test and validate the lead inactive warning functionality

package vn.agis.crm.scheduler;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@SpringBootTest
@Transactional
public class LeadInactiveWarningNotificationSchedulerTest {

    @Autowired
    private LeadInactiveWarningNotificationScheduler scheduler;
    
    @Autowired
    private ConfigRepository configRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private EmployeeRepository employeeRepository;
    
    @Autowired
    private NotificationRepository notificationRepository;
    
    @Autowired
    private CustomerOfferRepository customerOfferRepository;
    
    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Test
    public void testInactiveLeadWarningWith7DaysConfig() {
        // Setup: Enable 7-day inactive warning notifications
        Config config = createInactiveWarningConfig("7");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer created 10 days ago with no interactions (should trigger warning)
        Customers leadCustomer = createTestLeadWithCreationDate("Inactive Lead", "0901234567", 10);
        leadCustomer.setCurrentStaffId(employee.getId());
        customerRepository.save(leadCustomer);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: Warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getType().equals(4); // LeadInactiveWarning
        assert notification.getTitle().equals("Cảnh báo lead không hoạt động");
        assert notification.getContent().contains("Inactive Lead");
        assert notification.getContent().contains("0901234567");
        assert notification.getContent().contains("được tạo từ ngày");
        assert notification.getContent().contains("chưa có tương tác nào");
        assert notification.getTargetCustomerId().equals(leadCustomer.getId());

        System.out.println("✅ 7-day inactive lead warning test passed");
    }

    @Test
    public void testInactiveLeadWithLastInteraction() {
        // Setup: Enable 5-day inactive warning notifications
        Config config = createInactiveWarningConfig("5");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer created 20 days ago
        Customers leadCustomer = createTestLeadWithCreationDate("Lead With Interaction", "0901234567", 20);
        leadCustomer.setCurrentStaffId(employee.getId());
        customerRepository.save(leadCustomer);

        // Setup: Create customer offer and interaction 8 days ago (should trigger warning)
        CustomerOffers offer = createCustomerOffer(leadCustomer.getId(), 1L);
        customerOfferRepository.save(offer);

        InteractionsPrimary interaction = createPrimaryInteraction(offer.getId(), 8); // 8 days ago
        interactionsPrimaryRepository.save(interaction);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: Warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getType().equals(4); // LeadInactiveWarning
        assert notification.getContent().contains("Lead With Interaction");
        assert notification.getContent().contains("đã không có tương tác nào từ ngày");
        assert notification.getContent().contains("Hãy liên hệ để duy trì mối quan hệ");

        System.out.println("✅ Inactive lead with last interaction test passed");
    }

    @Test
    public void testNoWarningForRecentlyActiveLeads() {
        // Setup: Enable 7-day inactive warning notifications
        Config config = createInactiveWarningConfig("7");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create lead customer with recent interaction (should NOT trigger warning)
        Customers leadCustomer = createTestLeadWithCreationDate("Active Lead", "0901234567", 10);
        leadCustomer.setCurrentStaffId(employee.getId());
        customerRepository.save(leadCustomer);

        // Setup: Create recent interaction (3 days ago)
        CustomerOffers offer = createCustomerOffer(leadCustomer.getId(), 1L);
        customerOfferRepository.save(offer);

        InteractionsPrimary interaction = createPrimaryInteraction(offer.getId(), 3); // 3 days ago
        interactionsPrimaryRepository.save(interaction);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: No warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ No warning for recently active leads test passed");
    }

    @Test
    public void testNoWarningForNonLeadCustomers() {
        // Setup: Enable 5-day inactive warning notifications
        Config config = createInactiveWarningConfig("5");
        configRepository.save(config);

        // Setup: Create test employee
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        // Setup: Create non-lead customer (Data source) created 10 days ago (should NOT trigger warning)
        Customers dataCustomer = createTestCustomerWithCreationDate("Data Customer", "0901234567", "Data", 10);
        dataCustomer.setCurrentStaffId(employee.getId());
        customerRepository.save(dataCustomer);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: No warning notification was created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(employee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ No warning for non-lead customers test passed");
    }

    @Test
    public void testDisabledConfiguration() {
        // Setup: No configuration (disabled)
        // Don't create any config

        // Setup: Create test employee and inactive lead
        Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
        employeeRepository.save(employee);

        Customers leadCustomer = createTestLeadWithCreationDate("Test Lead", "0901234567", 10);
        leadCustomer.setCurrentStaffId(employee.getId());
        customerRepository.save(leadCustomer);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: No notifications were created
        List<Notifications> allNotifications = notificationRepository.findAll();
        assert allNotifications.size() == 0;

        System.out.println("✅ Disabled configuration test passed");
    }

    @Test
    public void testInvalidConfiguration() {
        // Setup: Invalid configuration values
        Config invalidConfig1 = createInactiveWarningConfig("invalid");
        Config invalidConfig2 = createInactiveWarningConfig("-1");
        Config invalidConfig3 = createInactiveWarningConfig("999");

        // Test each invalid config
        for (Config config : List.of(invalidConfig1, invalidConfig2, invalidConfig3)) {
            configRepository.deleteAll();
            configRepository.save(config);

            // Setup: Create test employee and inactive lead
            Employee employee = createTestEmployee("Test Employee", "<EMAIL>", 2);
            employeeRepository.save(employee);

            Customers leadCustomer = createTestLeadWithCreationDate("Test Lead", "0901234567", 10);
            leadCustomer.setCurrentStaffId(employee.getId());
            customerRepository.save(leadCustomer);

            // Execute: Run the scheduler job
            scheduler.processLeadInactiveWarningNotifications();

            // Verify: No notifications were created due to invalid config
            List<Notifications> notifications = notificationRepository.findAll();
            assert notifications.size() == 0;

            // Cleanup
            customerRepository.deleteAll();
            employeeRepository.deleteAll();
            notificationRepository.deleteAll();
        }

        System.out.println("✅ Invalid configuration test passed");
    }

    @Test
    public void testMultipleInactiveLeads() {
        // Setup: Enable 5-day inactive warning notifications
        Config config = createInactiveWarningConfig("5");
        configRepository.save(config);

        // Setup: Create multiple employees
        Employee employee1 = createTestEmployee("Employee 1", "<EMAIL>", 2);
        Employee employee2 = createTestEmployee("Employee 2", "<EMAIL>", 2);
        employeeRepository.saveAll(List.of(employee1, employee2));

        // Setup: Create multiple inactive leads
        Customers lead1 = createTestLeadWithCreationDate("Inactive Lead 1", "0901111111", 10);
        lead1.setCurrentStaffId(employee1.getId());

        Customers lead2 = createTestLeadWithCreationDate("Inactive Lead 2", "0902222222", 15);
        lead2.setCurrentStaffId(employee1.getId());

        Customers lead3 = createTestLeadWithCreationDate("Inactive Lead 3", "0903333333", 8);
        lead3.setCurrentManagerId(employee2.getId());

        customerRepository.saveAll(List.of(lead1, lead2, lead3));

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: All employees got appropriate notifications
        List<Notifications> emp1Notifications = notificationRepository.findByTargetEmployeeId(employee1.getId());
        List<Notifications> emp2Notifications = notificationRepository.findByTargetEmployeeId(employee2.getId());

        assert emp1Notifications.size() == 2; // Lead 1 and Lead 2
        assert emp2Notifications.size() == 1; // Lead 3

        // Verify notification content is lead-specific
        for (Notifications notification : emp1Notifications) {
            assert notification.getContent().contains("Inactive Lead 1") || 
                   notification.getContent().contains("Inactive Lead 2");
            assert notification.getType().equals(4); // LeadInactiveWarning
        }

        System.out.println("✅ Multiple inactive leads test passed");
    }

    @Test
    public void testLeadWithoutAssignedEmployee() {
        // Setup: Enable 5-day inactive warning notifications
        Config config = createInactiveWarningConfig("5");
        configRepository.save(config);

        // Setup: Create inactive lead without assigned employee
        Customers leadCustomer = createTestLeadWithCreationDate("Unassigned Lead", "0901234567", 10);
        // Don't assign any employee
        customerRepository.save(leadCustomer);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: No notification was created (no assigned employee)
        List<Notifications> notifications = notificationRepository.findAll();
        assert notifications.size() == 0;

        System.out.println("✅ Lead without assigned employee test passed");
    }

    @Test
    public void testInactiveEmployeeHandling() {
        // Setup: Enable 5-day inactive warning notifications
        Config config = createInactiveWarningConfig("5");
        configRepository.save(config);

        // Setup: Create inactive employee
        Employee inactiveEmployee = createTestEmployee("Inactive Employee", "<EMAIL>", 2);
        inactiveEmployee.setStatus(Employee.Status.inactive);
        employeeRepository.save(inactiveEmployee);

        // Setup: Create inactive lead assigned to inactive employee
        Customers leadCustomer = createTestLeadWithCreationDate("Test Lead", "0901234567", 10);
        leadCustomer.setCurrentStaffId(inactiveEmployee.getId());
        customerRepository.save(leadCustomer);

        // Execute: Run the scheduler job
        scheduler.processLeadInactiveWarningNotifications();

        // Verify: No notification was created for inactive employee
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(inactiveEmployee.getId());
        assert notifications.size() == 0;

        System.out.println("✅ Inactive employee handling test passed");
    }

    // Helper methods for test setup

    private Config createInactiveWarningConfig(String value) {
        Config config = new Config();
        config.setConfigKey("NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS");
        config.setConfigValue(value);
        config.setConfigType(1);
        config.setDescription("Test lead inactive warning configuration");
        config.setCreatedAt(new Date());
        config.setCreatedBy(1L);
        return config;
    }

    private Employee createTestEmployee(String name, String email, Integer roleId) {
        Employee employee = new Employee();
        employee.setEmployeeCode("EMP_" + System.currentTimeMillis());
        employee.setFullName(name);
        employee.setEmail(email);
        employee.setPhone("0900000000");
        employee.setPassword("password");
        employee.setRoleId(roleId);
        employee.setStatus(Employee.Status.active);
        employee.setCreatedAt(new Date());
        employee.setCreatedBy(1L);
        return employee;
    }

    private Customers createTestLeadWithCreationDate(String name, String phone, int daysAgo) {
        return createTestCustomerWithCreationDate(name, phone, "Leads", daysAgo);
    }

    private Customers createTestCustomerWithCreationDate(String name, String phone, String sourceType, int daysAgo) {
        Customers customer = new Customers();
        customer.setFullName(name);
        customer.setPhone(phone);
        customer.setEmail("<EMAIL>");
        customer.setSourceType(sourceType);
        
        // Set creation date to daysAgo in the past
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -daysAgo);
        customer.setCreatedAt(cal.getTime());
        
        customer.setCreatedBy(1L);
        return customer;
    }

    private CustomerOffers createCustomerOffer(Long customerId, Long projectId) {
        CustomerOffers offer = new CustomerOffers();
        offer.setCustomerId(customerId);
        offer.setProjectId(projectId);
        offer.setStatus("OPEN");
        offer.setCreatedAt(new Date());
        offer.setCreatedBy(1L);
        return offer;
    }

    private InteractionsPrimary createPrimaryInteraction(Long customerOfferId, int daysAgo) {
        InteractionsPrimary interaction = new InteractionsPrimary();
        interaction.setCustomerOfferId(customerOfferId);
        interaction.setResult("Called");
        
        // Set interaction date to daysAgo in the past
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -daysAgo);
        interaction.setHappenedAt(cal.getTime());
        
        interaction.setNotes("Test interaction");
        interaction.setCreatedAt(new Date());
        interaction.setCreatedBy(1L);
        return interaction;
    }
}

// Example manual testing and configuration scenarios
class LeadInactiveWarningSchedulerManualTest {
    
    public static void main(String[] args) {
        System.out.println("⏰ Lead Inactive Warning Notification Scheduler - Manual Test Scenarios");
        
        // Scenario 1: Enable 7-day inactive warning notifications
        System.out.println("\n📋 Scenario 1: Enable 7-day inactive warning notifications");
        System.out.println("SQL: INSERT INTO configs (config_key, config_value, config_type, description) VALUES ('NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS', '7', 1, 'Send lead inactive warnings after 7 days');");
        System.out.println("Expected: Leads with no interactions for 7+ days will trigger warnings");
        
        // Scenario 2: Short-term warnings (3 days)
        System.out.println("\n📋 Scenario 2: Short-term warnings (3 days)");
        System.out.println("SQL: UPDATE configs SET config_value = '3' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';");
        System.out.println("Expected: Leads with no interactions for 3+ days will trigger warnings");
        
        // Scenario 3: Long-term warnings (30 days)
        System.out.println("\n📋 Scenario 3: Long-term warnings (30 days)");
        System.out.println("SQL: UPDATE configs SET config_value = '30' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';");
        System.out.println("Expected: Leads with no interactions for 30+ days will trigger warnings");
        
        // Scenario 4: Disable warnings
        System.out.println("\n📋 Scenario 4: Disable warnings");
        System.out.println("SQL: DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';");
        System.out.println("Expected: No lead inactive warnings will be sent");
        
        System.out.println("\n🔍 Monitoring Commands:");
        System.out.println("-- Check recent lead inactive warnings");
        System.out.println("SELECT * FROM notifications WHERE type = 4 AND created_at >= CURDATE() ORDER BY created_at DESC;");
        
        System.out.println("\n-- Check inactive leads (manual query)");
        System.out.println("SELECT c.id, c.full_name, c.phone, c.created_at, c.current_staff_id, c.current_manager_id");
        System.out.println("FROM customers c");
        System.out.println("WHERE c.source_type = 'Leads' AND c.deleted_at IS NULL");
        System.out.println("  AND c.created_at <= DATE_SUB(CURDATE(), INTERVAL 7 DAY)");
        System.out.println("ORDER BY c.created_at ASC;");
        
        System.out.println("\n-- Check configuration");
        System.out.println("SELECT * FROM configs WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';");
        
        System.out.println("\n-- Check inactive warning statistics");
        System.out.println("SELECT DATE(created_at) as date, COUNT(*) as count FROM notifications WHERE type = 4 GROUP BY DATE(created_at) ORDER BY date DESC LIMIT 7;");
    }
}
