package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import java.util.Date;

@Data
public class ImportErrorDto {
    private Long id;
    private Long importJobId;
    private Integer rowNumber;
    private String columnName;
    private String originalValue;
    private String errorType;
    private String errorDescription;
    private String severity; // ERROR, WARNING, INFO
    private Date createdAt;
    
    // Additional context
    private String customerPhone; // For easier identification
    private String customerName;
    private String suggestedFix;
    
    public ImportErrorDto() {}
    
    public ImportErrorDto(Long importJobId, Integer rowNumber, String columnName, 
                         String originalValue, String errorType, String errorDescription, String severity) {
        this.importJobId = importJobId;
        this.rowNumber = rowNumber;
        this.columnName = columnName;
        this.originalValue = originalValue;
        this.errorType = errorType;
        this.errorDescription = errorDescription;
        this.severity = severity;
        this.createdAt = new Date();
    }
}
