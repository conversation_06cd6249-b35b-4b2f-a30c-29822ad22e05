package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;

@Table(name = "areas")
@Entity
@Data
public class Areas extends AbstractEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    
    @Column(name = "NAME", nullable = false, length = 255)
    private String name;
    
    @Column(name = "DESCRIPTION", columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "PARENT_ID")
    private Long parentId;

    @Column(name = "STATUS", nullable = false)
    private Integer status = 1;

    @Column(name = "TYPE", nullable = false)
    private Integer type;

    @Column(name = "UPDATED_AT")
    private Date updatedAt;

    @Column(name = "UPDATED_BY")
    private Long updatedBy;
}
