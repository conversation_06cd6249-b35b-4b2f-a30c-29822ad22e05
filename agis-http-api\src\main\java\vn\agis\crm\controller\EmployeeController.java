package vn.agis.crm.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import jakarta.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.utils.StringUtils;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.base.jpa.dto.req.CreateEmployeeReq;
import vn.agis.crm.base.jpa.dto.EmployeeSearchDto;
import vn.agis.crm.base.jpa.dto.req.UpdateEmployeeReq;
import vn.agis.crm.service.EmployeeApiService;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.constants.Constants;


import java.util.List;

@RestController
@RequestMapping("/employee-mgmt")
public class EmployeeController extends CrudController<Employee, Long> {

    private final Logger log = LoggerFactory.getLogger(EmployeeController.class);

    private EmployeeApiService employeeService;

    @Autowired
    public EmployeeController(EmployeeApiService service) {
        super(service);
        this.employeeService = service;
        this.baseUrl = "/employee-mgmt";
    }

    @GetMapping("/search")
    @Operation(description = "Search employees with pagination and filters (code/name/phone/email)")
    public ResponseEntity<Page<Employee>> search(
            @RequestParam(name = "employeeCode", required = false, defaultValue = "") String employeeCode,
            @RequestParam(name = "fullName", required = false, defaultValue = "") String fullName,
            @RequestParam(name = "phone", required = false, defaultValue = "") String phone,
            @RequestParam(name = "email", required = false, defaultValue = "") String email,
            @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
            @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
            @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
            @RequestParam(name = "size", required = false, defaultValue = "20") Integer size,
            @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
            @RequestParam(name = "sort", required = false, defaultValue = "createdAt,desc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        EmployeeSearchDto dto = new EmployeeSearchDto(
                employeeCode.trim().equals("") ? null : employeeCode,
                fullName.trim().equals("") ? null : fullName,
                phone.trim().equals("") ? null : phone,
                email.trim().equals("") ? null : email,
                page,
                size,
                sortBy
        );

        Page<Employee> pageResult = employeeService.search(dto, listRequest.getPageable());
        return ResponseEntity.ok().body(pageResult);
    }

    @GetMapping("/{id}")
    @Operation(description = "Get employee by ID")
    public ResponseEntity<Employee> getEmployee(@PathVariable Long id) {
        Employee employee = employeeService.get(id);
        return ResponseEntity.ok().body(employee);
    }

    @PostMapping()
    @Operation(description = "Create new employee")
    public ResponseEntity<Employee> createEmployee(@Valid @RequestBody CreateEmployeeReq request, HttpServletRequest httpServletRequest) {
        Employee entity = new Employee();
        entity.setEmployeeCode(request.getEmployeeCode());
        entity.setFullName(request.getFullName());
        entity.setPhone(request.getPhone());
        entity.setEmail(request.getEmail());
        entity.setEncryptedPassword(request.getPassword());
        entity.setRoleId(request.getRoleId());

        var req = new CreateEmployeeReq();
        req.setEmployeeCode(request.getEmployeeCode());
        req.setFullName(request.getFullName());
        req.setPhone(request.getPhone());
        req.setEmail(request.getEmail());
        req.setPassword(request.getPassword());
        req.setRoleId(request.getRoleId());
        Employee employee = employeeService.createEmployee(req);
        return ResponseEntity.ok().body(employee);
    }

    @PutMapping("/{id}")
    @Operation(description = "Update employee by ID")
    public ResponseEntity<Employee> updateEmployee(@PathVariable Long id, @Valid @RequestBody UpdateEmployeeReq request,
                                             HttpServletRequest httpServletRequest) {
        var req = new UpdateEmployeeReq();
        req.setId(id);
        req.setEmployeeCode(request.getEmployeeCode());
        req.setFullName(request.getFullName());
        req.setPhone(request.getPhone());
        req.setEmail(request.getEmail());
        req.setPassword(request.getPassword());
        req.setRoleId(request.getRoleId());
        req.setStatus(request.getStatus());

        Employee updated = employeeService.updateEmployee(req);
        return ResponseEntity.ok().body(updated);
    }

    @PutMapping("/change-status/{id}")
    @Operation(description = "Change employee status (active/inactive)")
    public ResponseEntity<Void> changeStatus(@PathVariable Long id) {
        employeeService.changeStatus(id);
        return ResponseEntity.ok().build();
    }

    @DeleteMapping("/{id}")
    @Operation(description = "Delete employee by ID")
    public ResponseEntity<Void> deleteEmployee(@PathVariable Long id, HttpServletRequest httpServletRequest) {
        employeeService.deleteById(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/all")
    @Operation(description = "Get all employees without pagination")
    public ResponseEntity<List<Employee>> getAll() {
        List<Employee> list = employeeService.getAll();
        return ResponseEntity.ok().body(list);
    }

    @GetMapping("/check-exists")
    @Operation(description = "Check if employee exists by a specific field (key=employeeCode|email|phone)")
    public ResponseEntity<Boolean> checkExists(@RequestParam String key, @RequestParam String value) {
        boolean exists = employeeService.checkExists(key, value);
        return ResponseEntity.ok().body(exists);
    }

    @GetMapping(path = "/current")
    @Operation(description = "Get current employee (with roles & authorities) from Authorization header")
    public Employee currentEmployee(HttpServletRequest request) {
        String authorization = request.getHeader(Constants.CRMService.AUTH_HEADER_STRING);
        if (authorization != null && authorization.startsWith(Constants.CRMService.AUTH_TOKEN_PREFIX)) {
            String token = authorization.substring(Constants.CRMService.AUTH_TOKEN_PREFIX.length());
            if (token.isEmpty()) return null;
            return employeeService.currentEmployee(token);
        } else if (authorization != null && authorization.startsWith(Constants.CRMService.AUTH_CODE_PREFIX)) {
            String authCode = authorization.substring(Constants.CRMService.AUTH_CODE_PREFIX.length());
            if (authCode.isEmpty()) return null;
            return employeeService.currentEmployee(authCode);
        } else {
            return null;
        }
    }
}

