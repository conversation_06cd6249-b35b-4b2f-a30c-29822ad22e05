-- Lead Inactive Warning Notification Scheduler Setup
-- This script sets up the configuration for automatic lead inactive warning notifications

-- =====================================================
-- CONFIGURATION SETUP
-- =====================================================

-- Enable 7-day lead inactive warning notifications (recommended default)
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS', '7', 1, 'Số ngày cảnh báo lead không hoạt động', NOW(), 1)
ON DUPLICATE KEY UPDATE 
    config_value = '7',
    description = 'Số ngày cảnh báo lead không hoạt động',
    updated_at = NOW(),
    updated_by = 1;

-- =====================================================
-- ALTERNATIVE CONFIGURATIONS
-- =====================================================

-- Short-term warnings (3 days)
-- UPDATE configs SET config_value = '3' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Medium-term warnings (14 days)
-- UPDATE configs SET config_value = '14' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Long-term warnings (30 days)
-- UPDATE configs SET config_value = '30' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Disable lead inactive warnings
-- DELETE FROM configs WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';
-- OR
-- UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check current configuration
SELECT 
    config_key,
    config_value,
    config_type,
    description,
    created_at,
    updated_at
FROM configs 
WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS';

-- Check leads that would trigger inactive warnings with current configuration (7 days)
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.email,
    c.created_at,
    c.current_staff_id,
    c.current_manager_id,
    DATEDIFF(CURDATE(), DATE(c.created_at)) as days_since_creation,
    (SELECT MAX(ip.happened_at) 
     FROM customer_offers co 
     JOIN interactions_primary ip ON co.id = ip.customer_offer_id 
     WHERE co.customer_id = c.id) as last_primary_interaction,
    (SELECT MAX(is_table.happened_at) 
     FROM customer_properties cp 
     JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id 
     WHERE cp.customer_id = c.id) as last_secondary_interaction
FROM customers c
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND (
    -- No interactions and created more than 7 days ago
    (NOT EXISTS (SELECT 1 FROM customer_offers co JOIN interactions_primary ip ON co.id = ip.customer_offer_id WHERE co.customer_id = c.id)
     AND NOT EXISTS (SELECT 1 FROM customer_properties cp JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id WHERE cp.customer_id = c.id)
     AND c.created_at <= DATE_SUB(CURDATE(), INTERVAL 7 DAY))
    OR
    -- Has interactions but last interaction was more than 7 days ago
    (GREATEST(
       COALESCE((SELECT MAX(ip.happened_at) FROM customer_offers co JOIN interactions_primary ip ON co.id = ip.customer_offer_id WHERE co.customer_id = c.id), '1900-01-01'),
       COALESCE((SELECT MAX(is_table.happened_at) FROM customer_properties cp JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id WHERE cp.customer_id = c.id), '1900-01-01')
     ) <= DATE_SUB(CURDATE(), INTERVAL 7 DAY))
  )
ORDER BY c.created_at ASC;

-- Check recent lead inactive warning notifications
SELECT 
    n.id,
    n.target_employee_id,
    n.target_customer_id,
    n.title,
    n.content,
    n.is_read,
    n.created_at,
    c.full_name as customer_name,
    c.phone as customer_phone,
    e.full_name as employee_name
FROM notifications n
LEFT JOIN customers c ON n.target_customer_id = c.id
LEFT JOIN employees e ON n.target_employee_id = e.id
WHERE n.type = 4  -- LeadInactiveWarning type
  AND n.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY n.created_at DESC;

-- Check inactive warning notification statistics by date
SELECT 
    DATE(created_at) as notification_date,
    COUNT(*) as warning_count,
    COUNT(DISTINCT target_customer_id) as unique_leads,
    COUNT(DISTINCT target_employee_id) as unique_employees
FROM notifications 
WHERE type = 4  -- LeadInactiveWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY notification_date DESC;

-- =====================================================
-- LEAD ACTIVITY ANALYSIS
-- =====================================================

-- Check all lead customers with their last interaction dates
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.created_at,
    c.current_staff_id,
    c.current_manager_id,
    COALESCE(
        GREATEST(
            COALESCE(last_primary.max_happened_at, '1900-01-01'),
            COALESCE(last_secondary.max_happened_at, '1900-01-01')
        ),
        c.created_at
    ) as reference_date,
    CASE 
        WHEN GREATEST(
            COALESCE(last_primary.max_happened_at, '1900-01-01'),
            COALESCE(last_secondary.max_happened_at, '1900-01-01')
        ) > '1900-01-01' THEN 'Has Interactions'
        ELSE 'No Interactions'
    END as interaction_status,
    DATEDIFF(CURDATE(), 
        COALESCE(
            GREATEST(
                COALESCE(last_primary.max_happened_at, '1900-01-01'),
                COALESCE(last_secondary.max_happened_at, '1900-01-01')
            ),
            c.created_at
        )
    ) as days_since_last_activity
FROM customers c
LEFT JOIN (
    SELECT co.customer_id, MAX(ip.happened_at) as max_happened_at
    FROM customer_offers co
    JOIN interactions_primary ip ON co.id = ip.customer_offer_id
    GROUP BY co.customer_id
) last_primary ON c.id = last_primary.customer_id
LEFT JOIN (
    SELECT cp.customer_id, MAX(is_table.happened_at) as max_happened_at
    FROM customer_properties cp
    JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
    GROUP BY cp.customer_id
) last_secondary ON c.id = last_secondary.customer_id
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
ORDER BY days_since_last_activity DESC;

-- Check leads by inactivity period
SELECT 
    CASE 
        WHEN days_inactive = 0 THEN 'Active today'
        WHEN days_inactive = 1 THEN '1 day inactive'
        WHEN days_inactive <= 3 THEN '2-3 days inactive'
        WHEN days_inactive <= 7 THEN '4-7 days inactive'
        WHEN days_inactive <= 14 THEN '1-2 weeks inactive'
        WHEN days_inactive <= 30 THEN '2-4 weeks inactive'
        ELSE 'Over 1 month inactive'
    END as inactivity_period,
    COUNT(*) as lead_count
FROM (
    SELECT 
        c.id,
        DATEDIFF(CURDATE(), 
            COALESCE(
                GREATEST(
                    COALESCE(last_primary.max_happened_at, '1900-01-01'),
                    COALESCE(last_secondary.max_happened_at, '1900-01-01')
                ),
                c.created_at
            )
        ) as days_inactive
    FROM customers c
    LEFT JOIN (
        SELECT co.customer_id, MAX(ip.happened_at) as max_happened_at
        FROM customer_offers co
        JOIN interactions_primary ip ON co.id = ip.customer_offer_id
        GROUP BY co.customer_id
    ) last_primary ON c.id = last_primary.customer_id
    LEFT JOIN (
        SELECT cp.customer_id, MAX(is_table.happened_at) as max_happened_at
        FROM customer_properties cp
        JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
        GROUP BY cp.customer_id
    ) last_secondary ON c.id = last_secondary.customer_id
    WHERE c.source_type = 'Leads' 
      AND c.deleted_at IS NULL
) lead_activity
GROUP BY 
    CASE 
        WHEN days_inactive = 0 THEN 'Active today'
        WHEN days_inactive = 1 THEN '1 day inactive'
        WHEN days_inactive <= 3 THEN '2-3 days inactive'
        WHEN days_inactive <= 7 THEN '4-7 days inactive'
        WHEN days_inactive <= 14 THEN '1-2 weeks inactive'
        WHEN days_inactive <= 30 THEN '2-4 weeks inactive'
        ELSE 'Over 1 month inactive'
    END
ORDER BY 
    CASE 
        WHEN inactivity_period = 'Active today' THEN 1
        WHEN inactivity_period = '1 day inactive' THEN 2
        WHEN inactivity_period = '2-3 days inactive' THEN 3
        WHEN inactivity_period = '4-7 days inactive' THEN 4
        WHEN inactivity_period = '1-2 weeks inactive' THEN 5
        WHEN inactivity_period = '2-4 weeks inactive' THEN 6
        ELSE 7
    END;

-- =====================================================
-- EMPLOYEE WORKLOAD ANALYSIS
-- =====================================================

-- Check employee workload for inactive leads
SELECT 
    e.id,
    e.full_name as employee_name,
    e.email,
    e.status,
    COUNT(CASE WHEN c.current_staff_id = e.id THEN 1 END) as staff_assignments,
    COUNT(CASE WHEN c.current_manager_id = e.id THEN 1 END) as manager_assignments,
    COUNT(c.id) as total_lead_assignments,
    COUNT(CASE WHEN inactive_leads.customer_id IS NOT NULL THEN 1 END) as inactive_leads_count
FROM employees e
LEFT JOIN customers c ON (c.current_staff_id = e.id OR c.current_manager_id = e.id)
    AND c.source_type = 'Leads' AND c.deleted_at IS NULL
LEFT JOIN (
    -- Subquery to identify inactive leads (7+ days)
    SELECT DISTINCT c.id as customer_id
    FROM customers c
    LEFT JOIN (
        SELECT co.customer_id, MAX(ip.happened_at) as max_happened_at
        FROM customer_offers co
        JOIN interactions_primary ip ON co.id = ip.customer_offer_id
        GROUP BY co.customer_id
    ) last_primary ON c.id = last_primary.customer_id
    LEFT JOIN (
        SELECT cp.customer_id, MAX(is_table.happened_at) as max_happened_at
        FROM customer_properties cp
        JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
        GROUP BY cp.customer_id
    ) last_secondary ON c.id = last_secondary.customer_id
    WHERE c.source_type = 'Leads' 
      AND c.deleted_at IS NULL
      AND COALESCE(
          GREATEST(
              COALESCE(last_primary.max_happened_at, '1900-01-01'),
              COALESCE(last_secondary.max_happened_at, '1900-01-01')
          ),
          c.created_at
      ) <= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
) inactive_leads ON c.id = inactive_leads.customer_id
WHERE e.status = 'active' 
  AND e.deleted_at IS NULL
GROUP BY e.id, e.full_name, e.email, e.status
HAVING total_lead_assignments > 0
ORDER BY inactive_leads_count DESC, total_lead_assignments DESC;

-- =====================================================
-- INTERACTION PATTERN ANALYSIS
-- =====================================================

-- Check interaction frequency for leads
SELECT 
    'Primary Interactions' as interaction_type,
    COUNT(*) as total_interactions,
    COUNT(DISTINCT co.customer_id) as unique_customers,
    AVG(DATEDIFF(CURDATE(), ip.happened_at)) as avg_days_since_interaction
FROM customer_offers co
JOIN interactions_primary ip ON co.id = ip.customer_offer_id
JOIN customers c ON co.customer_id = c.id
WHERE c.source_type = 'Leads' AND c.deleted_at IS NULL

UNION ALL

SELECT 
    'Secondary Interactions' as interaction_type,
    COUNT(*) as total_interactions,
    COUNT(DISTINCT cp.customer_id) as unique_customers,
    AVG(DATEDIFF(CURDATE(), is_table.happened_at)) as avg_days_since_interaction
FROM customer_properties cp
JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
JOIN customers c ON cp.customer_id = c.id
WHERE c.source_type = 'Leads' AND c.deleted_at IS NULL;

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Check notification creation performance (recent inactive warnings)
SELECT 
    DATE(created_at) as date,
    HOUR(created_at) as hour,
    COUNT(*) as warnings_created,
    MIN(created_at) as first_warning,
    MAX(created_at) as last_warning,
    TIMESTAMPDIFF(SECOND, MIN(created_at), MAX(created_at)) as duration_seconds
FROM notifications 
WHERE type = 4  -- LeadInactiveWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), HOUR(created_at)
ORDER BY date DESC, hour DESC;

-- Check for duplicate inactive warnings (should be minimal)
SELECT 
    target_employee_id,
    target_customer_id,
    DATE(created_at) as notification_date,
    COUNT(*) as duplicate_count
FROM notifications 
WHERE type = 4  -- LeadInactiveWarning type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY target_employee_id, target_customer_id, DATE(created_at)
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, notification_date DESC;

-- =====================================================
-- TROUBLESHOOTING QUERIES
-- =====================================================

-- Find inactive leads that should trigger warnings but don't have notifications
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.created_at,
    c.current_staff_id,
    c.current_manager_id,
    COALESCE(
        GREATEST(
            COALESCE(last_primary.max_happened_at, '1900-01-01'),
            COALESCE(last_secondary.max_happened_at, '1900-01-01')
        ),
        c.created_at
    ) as reference_date,
    DATEDIFF(CURDATE(), 
        COALESCE(
            GREATEST(
                COALESCE(last_primary.max_happened_at, '1900-01-01'),
                COALESCE(last_secondary.max_happened_at, '1900-01-01')
            ),
            c.created_at
        )
    ) as days_inactive
FROM customers c
LEFT JOIN (
    SELECT co.customer_id, MAX(ip.happened_at) as max_happened_at
    FROM customer_offers co
    JOIN interactions_primary ip ON co.id = ip.customer_offer_id
    GROUP BY co.customer_id
) last_primary ON c.id = last_primary.customer_id
LEFT JOIN (
    SELECT cp.customer_id, MAX(is_table.happened_at) as max_happened_at
    FROM customer_properties cp
    JOIN interactions_secondary is_table ON cp.id = is_table.customer_property_id
    GROUP BY cp.customer_id
) last_secondary ON c.id = last_secondary.customer_id
LEFT JOIN notifications n ON (
    n.target_customer_id = c.id 
    AND n.type = 4 
    AND DATE(n.created_at) = CURDATE()
)
WHERE c.source_type = 'Leads' 
  AND c.deleted_at IS NULL
  AND (c.current_staff_id IS NOT NULL OR c.current_manager_id IS NOT NULL)
  AND COALESCE(
      GREATEST(
          COALESCE(last_primary.max_happened_at, '1900-01-01'),
          COALESCE(last_secondary.max_happened_at, '1900-01-01')
      ),
      c.created_at
  ) <= DATE_SUB(CURDATE(), INTERVAL 7 DAY)  -- Adjust based on config
  AND n.id IS NULL;

-- =====================================================
-- SCHEDULER STATUS CHECK
-- =====================================================

-- Check if scheduler is running (look for recent lead inactive warnings)
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'RUNNING - Recent lead inactive warnings found'
        ELSE 'UNKNOWN - No recent warnings (may be normal if no inactive leads)'
    END as scheduler_status,
    COUNT(*) as recent_warnings,
    MAX(created_at) as last_warning
FROM notifications 
WHERE type = 4 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Check configuration status
SELECT 
    CASE 
        WHEN config_value IS NULL THEN 'DISABLED - Configuration not found'
        WHEN config_value = '' THEN 'DISABLED - Empty configuration value'
        WHEN config_value REGEXP '^[0-9]+$' AND CAST(config_value AS UNSIGNED) BETWEEN 1 AND 365 THEN 
            CONCAT('ENABLED - ', config_value, ' days inactive warning period')
        ELSE 'INVALID - Invalid configuration value'
    END as config_status,
    config_value,
    updated_at as last_updated
FROM configs 
WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS'
UNION ALL
SELECT 
    'DISABLED - Configuration not found' as config_status,
    NULL as config_value,
    NULL as last_updated
WHERE NOT EXISTS (
    SELECT 1 FROM configs WHERE config_key = 'NOTIFICATION_LEAD_INACTIVE_WARNING_DAYS'
);
