package vn.agis.crm.base.event.amqp;

import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.*;
import com.rabbitmq.stream.Environment;
import org.springframework.amqp.rabbit.AsyncRabbitTemplate;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.amqp.RabbitProperties;
import org.springframework.rabbit.stream.producer.RabbitStreamTemplate;
import vn.agis.crm.base.configuration.SpringContext;
import vn.agis.crm.base.event.constants.AMQPConstants;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by tiemnd on 12/14/19.
 */
public abstract class AMQPAbstractConfiguration {
    public static final String DEFAULT_EXCHANGE_NAME = "";
    protected String exchangeName;
    protected Queue defaultReceiveQueue;

    protected static CachingConnectionFactory CONNECTION_FACTORY;

    public synchronized ConnectionFactory connectionFactory() {
        return CONNECTION_FACTORY;
    }

    public Environment streamEnv;

    public boolean isSuperStream = false;

    public abstract void configureRabbitTemplate(RabbitTemplate template);

    static Map<String, AsyncRabbitTemplate> asyncRabbitTemplateMap = new ConcurrentHashMap<>();
    static Map<String, RabbitTemplate> rabbitTemplateMap = new ConcurrentHashMap<>();
    static Map<String, RabbitStreamTemplate> rabbitStreamTemplateMap = new ConcurrentHashMap<>();

    public RabbitStreamTemplate rabbitStreamTemplate() {
        RabbitStreamTemplate rabbitTemplate;
        if ((rabbitTemplate = rabbitStreamTemplateMap.get(exchangeName)) == null) {
            rabbitTemplate = new RabbitStreamTemplate(streamEnv, exchangeName);
            if (isSuperStream)
                rabbitTemplate.setSuperStreamRouting(message -> exchangeName);
        }

        rabbitStreamTemplateMap.put(exchangeName, rabbitTemplate);
        return rabbitTemplate;
    }

    public RabbitTemplate rabbitTemplate() {
        RabbitTemplate rabbitTemplate;
        if ((rabbitTemplate = rabbitTemplateMap.get(exchangeName)) == null) {
            rabbitTemplate = new RabbitTemplate(connectionFactory());
            rabbitTemplate.setMessageConverter(jsonMessageConverter());
            configureRabbitTemplate(rabbitTemplate);
            rabbitTemplate.setUserCorrelationId(true);
        }
//        if (isRpc) {
//                rabbitTemplate.setDefaultReceiveQueue(getDefaultReceiveQueue(rabbitTemplate).getName());
//                rabbitTemplate.setUserCorrelationId(true);
//                rabbitTemplate.setUseDirectReplyToContainer(false);
//                rabbitTemplate.setUseTemporaryReplyQueues(false);
//                rabbitTemplate.setReplyAddress(getDefaultReceiveQueue(rabbitTemplate).getName());
//                rabbitTemplate.expectedQueueNames();
//                try {
//                    Field field = RabbitTemplate.class.getDeclaredField("evaluatedFastReplyTo");
//                    field.setAccessible(true);
//                    field.set(rabbitTemplate, true);
//                } catch (Exception e) {
//                    e.printStackTrace();
//                }
//        }
        rabbitTemplateMap.put(exchangeName, rabbitTemplate);
        return rabbitTemplate;
    }

    private Queue createQueue(String queueName, RabbitTemplate template) {
        Queue queue = new Queue(queueName, false, false, false);
        queue.getArguments().put("x-expires", 172800000L);
        /** Set TTL for all message of RPC queue of API to prevent full queue**/
        if (SpringContext.getX_message_ttl() != null)
            queue.getArguments().put("x-message-ttl", SpringContext.getX_message_ttl());
        RabbitAdmin rabbitAdmin = new RabbitAdmin(template);
        try {
            if (SpringContext.isPurgeAllRpcQueue())
                rabbitAdmin.purgeQueue(queue.getName());
        } catch (Exception e) {
            System.out.println("Error when purge queue" + queue.getName());
            e.printStackTrace();
        }
        rabbitAdmin.declareQueue(queue);
        return queue;
    }

    public AsyncRabbitTemplate asyncRabbitTemplate(String replyQueue) {
        AsyncRabbitTemplate asyncRabbitTemplate;
        if ((asyncRabbitTemplate = asyncRabbitTemplateMap.get(exchangeName)) == null) {
            RabbitTemplate template = new RabbitTemplate(connectionFactory());
            template.setMessageConverter(jsonMessageConverter());
            configureRabbitTemplate(template);
            if (replyQueue.isBlank()) {
                asyncRabbitTemplate = new AsyncRabbitTemplate(template);
            } else {
                /** With rpc queue, it not need durable so should be not use quorum queue and use x-expires for RPC queue **/
                SimpleMessageListenerContainer container = new SimpleMessageListenerContainer(connectionFactory());
                Queue queue;
                if (replyQueue.equals(getDefaultReplyQueue())) queue = getDefaultReceiveQueue(template);
                else queue = createQueue(replyQueue, template);
                /** Set TTL for all message of RPC queue of API to prevent full queue**/
                if (SpringContext.getX_message_ttl() != null)
                    queue.getArguments().put("x-message-ttl", SpringContext.getX_message_ttl());
                RabbitProperties rabbitProperties = SpringContext.getBean(RabbitProperties.class);
                RabbitProperties.SimpleContainer simpleContainer = rabbitProperties!= null?rabbitProperties.getListener().getSimple():null;
                if (simpleContainer != null && simpleContainer.getConcurrency() != null) {
                    container.setConcurrentConsumers(simpleContainer.getConcurrency());
                }
                if (simpleContainer != null && simpleContainer.getPrefetch() != null) {
                    container.setPrefetchCount(simpleContainer.getPrefetch());
                }
                container.setQueues(queue);
                container.afterPropertiesSet();
                asyncRabbitTemplate = new AsyncRabbitTemplate(template, container, null);
            }
            asyncRabbitTemplate.start();
            asyncRabbitTemplateMap.put(exchangeName, asyncRabbitTemplate);
        }
        return asyncRabbitTemplate;
    }

    public MessageConverter jsonMessageConverter() {
        return new JsonMessageConverter();
    }

    public AmqpAdmin amqpAdmin() {
        return new RabbitAdmin(connectionFactory());
    }

    public TopicExchange topicExchange(String topicExchangeName) {
        return new TopicExchange(topicExchangeName);
    }

    public DirectExchange directExchange(String directExchange) {
        return new DirectExchange(exchangeName);
    }

    public CustomExchange xDelayedMessageExchange(String xDelayedExchangeName) {
        Map<String, Object> args = new HashMap<String, Object>();
        args.put("x-delayed-type", AMQPConstants.ExchangeType.DIRECT);
        return new CustomExchange(xDelayedExchangeName, AMQPConstants.ExchangeType.X_DELAY_MESSAGE, true, false, args);
    }

    public CustomExchange customExchange(String exchangeName, String exchangeType) {
        return new CustomExchange(exchangeName, exchangeType, true, false, null);
    }

    private String defaultReplyQueue;
    public String getDefaultReplyQueue() {

        if (defaultReplyQueue == null) {
            defaultReplyQueue = SpringContext.getApplicationId() + ".rpc";
        }
        return defaultReplyQueue;
    }

    private Queue getDefaultReceiveQueue(RabbitTemplate template) {
        if (defaultReceiveQueue == null) {
            defaultReceiveQueue = createQueue(getDefaultReplyQueue(), template);
        }
        return defaultReceiveQueue;
    }

    void validateStreamRetentionMaxAge(String maxAge) {
        String unit = maxAge.substring(maxAge.length() - 1);
        if (!(unit.equals("Y") || unit.equals("M") || unit.equals("D") ||
                unit.equals("h") || unit.equals("m") || unit.equals("s"))) {
            throw new RuntimeException("Max age retention for rabbitmq stream must be unit Y, M, D, h, m, s, current value " + maxAge);
        }
        String numberStr = maxAge.substring(0, maxAge.length() - 1);
        try {
            Integer number = Integer.valueOf(numberStr);
        } catch (NumberFormatException e) {
            throw new RuntimeException("Max age retention for rabbitmq stream must be a integer of unit Y, M, D, h, m, s, current value " + maxAge);
        }
    }

    Map<String, Object> getArgumentForStream(String streamName) {
        // Retention for stream, config via systemproperty : java -DstreamMaxAgeDefault=7D -DstreamMaxLengthBytesDefault=500000 -DstreamMaxSegmentSizeBytesDefault=500000000
        // -D<streamName>MaxAge=5D -D<streamName>MaxLengthBytes=400000 -D<streamName>MaxSegmentSizeBytes=100000000
        String streamMaxAgeDefault = SpringContext.getEnvironmentProperty("streamMaxAgeDefault", String.class, "2D");
        Long streamMaxLengthBytesDefault = SpringContext.getEnvironmentProperty("streamMaxLengthBytesDefault", Long.class, null);
        Long streamMaxSegmentSizeBytesDefault = SpringContext.getEnvironmentProperty("streamMaxSegmentSizeBytesDefault", Long.class, 500000000L);
        String maxAgeForStream = SpringContext.getEnvironmentProperty(streamName + "MaxAge", String.class, streamMaxAgeDefault);
        validateStreamRetentionMaxAge(maxAgeForStream);
        Long maxLengthBytesForStream = SpringContext.getEnvironmentProperty(streamName + "MaxLengthBytes", Long.class, streamMaxLengthBytesDefault);
        Long maxSegmentSizeBytesForStream = SpringContext.getEnvironmentProperty(streamName + "MaxSegmentSizeBytes", Long.class, streamMaxSegmentSizeBytesDefault);
        Map<String, Object> argumentForStream = new HashMap();
        if (StringUtils.isNotBlank(maxAgeForStream)) argumentForStream.put("x-max-age", maxAgeForStream);
        if (maxLengthBytesForStream != null) argumentForStream.put("x-max-length-bytes", maxLengthBytesForStream);
        if (maxSegmentSizeBytesForStream != null) argumentForStream.put("x-stream-max-segment-size-bytes", maxSegmentSizeBytesForStream);
        return argumentForStream;
    }
}
