package vn.agis.crm.controller;

import jakarta.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.JpaSort;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.entity.AbstractEntity;
import vn.agis.crm.service.CrudService;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public abstract class CrudController <T extends AbstractEntity, ID extends Serializable>{
    private static Logger logger = LoggerFactory.getLogger(CrudController.class);

    protected CrudService<T,ID> service;

    public String baseUrl;

    public CrudController(CrudService service) {
        this.service = service;
    }

    public ResponseEntity<T> get(@PathVariable(value = "id") ID id, HttpServletRequest request){
        return new ResponseEntity<T>(service.get(id), HttpStatus.OK);
    }

    public ResponseEntity<T> create(@RequestBody T entity, HttpServletRequest request) {
        return new ResponseEntity<T>(service.create(entity), HttpStatus.OK);
    }

    public ResponseEntity<T> update(@PathVariable(value = "id") ID id, @RequestBody T entity, HttpServletRequest request){
        return new ResponseEntity<T>(service.update(id, entity), HttpStatus.OK);
    }

    public ResponseEntity delete(@PathVariable(value = "id") ID id, HttpServletRequest request){
        service.deleteById(id);
        return new ResponseEntity(HttpStatus.OK);
    }

    public ResponseEntity<List<ID>> batchDelete(@RequestBody Set<ID> listIDs, HttpServletRequest request) {
        return new ResponseEntity<>(service.batchDelete(listIDs), HttpStatus.OK);
    }

    @RequestMapping(path = "/checkExist", method = RequestMethod.GET)
    public ResponseEntity<Long> checkExist(@RequestParam(value = "query", required = true) String query, HttpServletRequest request) {
        try {
            return new ResponseEntity<>(service.count(query), HttpStatus.OK);
        }
        catch (Exception e){
            logger.error(e.getMessage(), e);
            return new ResponseEntity<>(null, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

//
//    protected String getToken(String authorization) {
//        String token = null;
//        if (authorization != null && authorization.startsWith(Constants.AUTH_TOKEN_PREFIX)) {
//            token = authorization.substring(Constants.AUTH_TOKEN_PREFIX.length());
//        }
//        return token;
//    }

    private static final String STR_REGEXP_SORT = "^[A-z0-9]+,(asc|desc|ASC|DESC)$";
    public static class ListRequest {
        private final Integer page;
        private final Integer size;
        private final String sort;

        public ListRequest(Integer size, Integer page, String sort) {
            if (sort != null && !sort.matches(STR_REGEXP_SORT)) {
            }
            this.size = size == null ? 10 : (size < 0) ? 10 : size;
            this.page = page == null ? 0 : (page < 0) ? 0 : page;
            this.sort = (sort != null) ? sort : "id,asc";
        }

        public Pageable getPageable() {
            String[] part = sort.split(",", 2);
            Sort sortable = Sort.by(Sort.Direction.valueOf(part[1].toUpperCase()), part[0]);
            return PageRequest.of(page, size, sortable);
        }

        public Pageable getPageable(Sort sort) {
            return PageRequest.of(page, size, sort);
        }
    }

    @RequestMapping(path = "/getByKey", method = RequestMethod.GET)
    public List<T> getByKey(@RequestParam(name = "key") String key,
        @RequestParam(name = "value") String value){
        return service.getByKey(key, value);
    }

    public static class MultiSortListRequest {
        private final Integer page;
        private final Integer size;
        private final String sort;

        private static final String STR_REGEXP_SORT = "(\\w+,(asc|desc))(;\\w+,(asc|desc))*"; // Ví dụ: "field1,asc;field2,desc"

        public MultiSortListRequest(Integer size, Integer page, String sort) {
            if (sort != null && !sort.matches(STR_REGEXP_SORT)) {
                throw new IllegalArgumentException("Invalid sort format. Expected: field1,asc;field2,desc");
            }
            this.size = size == null ? 10 : (size < 0) ? 10 : size;
            this.page = page == null ? 0 : (page < 0) ? 0 : page;
            this.sort = (sort != null) ? sort : "id,asc";
        }

        public Pageable getPageable() {
            Sort sortable = parseSort(sort);
            return PageRequest.of(page, size, sortable);
        }

        public Pageable getPageable(Sort sort) {
            return PageRequest.of(page, size, sort);
        }

        private Sort parseSort(String sortString) {
            if (sortString == null || sortString.isEmpty()) {
                return Sort.by("id").ascending();
            }
            // Tách từng điều kiện sort, ví dụ: "field1,asc;field2,desc"
            String[] sortFields = sortString.split(";");
            List<Sort.Order> orders = new ArrayList<>();

            for (String fieldSort : sortFields) {
                String[] part = fieldSort.split(",", 2);
                if (part.length != 2) {
                    throw new IllegalArgumentException("Invalid sort field: " + fieldSort);
                }
                String field = part[0].trim();
                String direction = part[1].trim().toUpperCase();

                Sort.Order order;
                if (!field.toLowerCase().contains("date")) {
                    order = Sort.Order.by(String.valueOf(JpaSort.unsafe("NLSSORT(UPPER(" + field + "), 'nls_sort = Vietnamese')"))).with(Sort.Direction.valueOf(direction));
                } else {
                    order = new Sort.Order(Sort.Direction.valueOf(direction), field);
                }
                orders.add(order);
            }

            return Sort.by(orders);
        }
    }
}
