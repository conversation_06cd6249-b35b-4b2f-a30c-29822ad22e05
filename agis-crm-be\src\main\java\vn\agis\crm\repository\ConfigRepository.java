package vn.agis.crm.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.repositories.CustomJpaRepository;

import java.util.Optional;

@Repository
public interface ConfigRepository extends CustomJpaRepository<Config, Long> {

    Config findOneByConfigKeyIgnoreCase(String configKey);

    Optional<Config> findByConfigKey(String configKey);

    @Query("SELECT c FROM Config c WHERE "+
            "(:configKey IS NULL OR UPPER(c.configKey) LIKE CONCAT('%', UPPER(:configKey), '%')) AND "+
            "(:configType = -1 OR c.configType = :configType) AND "+
            "(:description IS NULL OR UPPER(c.description) LIKE CONCAT('%', UPPER(:description), '%'))")
    Page<Config> search(@Param("configKey") String configKey,
                        @Param("configType") Integer configType,
                        @Param("description") String description,
                        Pageable pageable);
}

