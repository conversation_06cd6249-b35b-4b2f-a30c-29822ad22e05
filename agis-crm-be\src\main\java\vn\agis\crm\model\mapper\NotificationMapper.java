package vn.agis.crm.model.mapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.EmployeeDto;
import vn.agis.crm.base.jpa.dto.NotificationDto;
import vn.agis.crm.base.jpa.dto.req.CustomerDto;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.repository.EmployeeRepository;

@Component
public class NotificationMapper {

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private CustomerRepository customerRepository;

    public NotificationDto toDto(Notifications entity) {
        if (entity == null) return null;
        
        NotificationDto dto = new NotificationDto();
        dto.setId(entity.getId());
        dto.setTargetEmployeeId(entity.getTargetEmployeeId());
        dto.setTargetCustomerId(entity.getTargetCustomerId());
        dto.setType(entity.getType());
        dto.setTitle(entity.getTitle());
        dto.setContent(entity.getContent());
        dto.setIsRead(entity.getIsRead());
        dto.setReadAt(entity.getReadAt());
        dto.setCreatedAt(entity.getCreatedAt());
        dto.setCreatedBy(entity.getCreatedBy());

        return dto;
    }

    public NotificationDto toDtoWithDetails(Notifications entity) {
        if (entity == null) return null;
        
        NotificationDto dto = toDto(entity);

        // Load target employee details
        if (entity.getTargetEmployeeId() != null) {
            employeeRepository.findById(entity.getTargetEmployeeId()).ifPresent(employee -> {
                dto.setTargetEmployee(toEmployeeDto(employee));
            });
        }

        // Load target customer details (if exists)
        if (entity.getTargetCustomerId() != null) {
            customerRepository.findById(entity.getTargetCustomerId()).ifPresent(customer -> {
                dto.setTargetCustomer(toCustomerDto(customer));
            });
        }

        // Load created by employee details (if exists)
        if (entity.getCreatedBy() != null) {
            employeeRepository.findById(entity.getCreatedBy()).ifPresent(employee -> {
                dto.setCreatedByEmployee(toEmployeeDto(employee));
            });
        }

        return dto;
    }

    private EmployeeDto toEmployeeDto(Employee employee) {
        if (employee == null) return null;
        
        EmployeeDto dto = new EmployeeDto();
        dto.setId(employee.getId());
        dto.setEmployeeCode(employee.getEmployeeCode());
        dto.setFullName(employee.getFullName());
        dto.setPhone(employee.getPhone());
        dto.setEmail(employee.getEmail());
//        dto.setPosition(employee.getPosition());
//        dto.setDepartment(employee.getDepartment());
//        dto.setIsActive(employee.getIsActive());
        
        return dto;
    }

    private CustomerDto toCustomerDto(Customers customer) {
        if (customer == null) return null;
        
        CustomerDto dto = new CustomerDto();
        dto.setId(customer.getId());
        dto.setFullName(customer.getFullName());
        dto.setPhone(customer.getPhone());
        dto.setEmail(customer.getEmail());
        dto.setCccd(customer.getCccd());
        dto.setAdditionalPhones(customer.getAdditionalPhones());
        dto.setAdditionalEmails(customer.getAdditionalEmails());
        dto.setAdditionalCccds(customer.getAdditionalCccds());
        dto.setInterests(customer.getInterests());
        dto.setBirthDate(customer.getBirthDate());
        dto.setAddressContact(customer.getAddressContact());
        dto.setAddressPermanent(customer.getAddressPermanent());
        dto.setNationality(customer.getNationality());
        dto.setMaritalStatus(customer.getMaritalStatus());
        dto.setTotalAsset(customer.getTotalAsset());
        dto.setBusinessField(customer.getBusinessField());
        dto.setAvatarUrl(customer.getAvatarUrl());
        dto.setZaloStatus(customer.getZaloStatus());
        dto.setFacebookLink(customer.getFacebookLink());
        dto.setSourceType(customer.getSourceType());
        dto.setSourceDetail(customer.getSourceDetail());
        dto.setNotes(customer.getNotes());
//        dto.setIsActive(customer.getIsActive());
        
        return dto;
    }
}
