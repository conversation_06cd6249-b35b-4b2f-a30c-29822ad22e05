package vn.agis.crm.verification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.InteractionPrimarySearchDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondaryDto;
import vn.agis.crm.base.jpa.dto.InteractionSecondarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionSecondaryCreateDto;
import vn.agis.crm.service.InteractionsPrimaryApiService;
import vn.agis.crm.service.InteractionsSecondaryApiService;

import java.math.BigDecimal;

/**
 * Verification script to test the Interactions CRUD API implementation
 * Run this to verify all components are working correctly
 */
@Component
public class InteractionsCrudApiVerificationScript implements CommandLineRunner {

    @Autowired
    private InteractionsPrimaryApiService primaryApiService;

    @Autowired
    private InteractionsSecondaryApiService secondaryApiService;

    @Override
    public void run(String... args) throws Exception {
        System.out.println("=".repeat(80));
        System.out.println("🚀 INTERACTIONS CRUD API VERIFICATION SCRIPT");
        System.out.println("=".repeat(80));

        try {
            // Test Primary Interactions
            testPrimaryInteractions();
            
            // Test Secondary Interactions
            testSecondaryInteractions();
            
            System.out.println("✅ ALL VERIFICATION TESTS COMPLETED SUCCESSFULLY!");
            
        } catch (Exception e) {
            System.err.println("❌ VERIFICATION FAILED: " + e.getMessage());
            e.printStackTrace();
        }
        
        System.out.println("=".repeat(80));
    }

    private void testPrimaryInteractions() {
        System.out.println("\n📋 TESTING PRIMARY INTERACTIONS API");
        System.out.println("-".repeat(50));

        try {
            // Test 1: Create Primary Interaction
            System.out.println("1️⃣  Testing Create Primary Interaction...");
            InteractionPrimaryCreateDto createDto = new InteractionPrimaryCreateDto();
            createDto.setCustomerOfferId(1L);
            createDto.setResult("Verification test - successful contact");
            createDto.setHappenedAt("15/01/2024");
            createDto.setNotes("This is a verification test for primary interaction creation");

            InteractionPrimaryDto created = primaryApiService.createInteraction(createDto);
            System.out.println("   ✅ Created primary interaction with ID: " + created.getId());
            System.out.println("   📝 Result: " + created.getResult());
            System.out.println("   📅 Happened At: " + created.getHappenedAt());

            // Test 2: Get Primary Interaction by ID
            System.out.println("\n2️⃣  Testing Get Primary Interaction by ID...");
            InteractionPrimaryDto retrieved = primaryApiService.getInteractionById(created.getId());
            System.out.println("   ✅ Retrieved primary interaction: " + retrieved.getId());
            System.out.println("   📝 Result: " + retrieved.getResult());
            System.out.println("   👤 Created By: " + retrieved.getCreatedBy());
            System.out.println("   👤 Created Name: " + retrieved.getCreatedName());

            // Test 3: Search Primary Interactions
            System.out.println("\n3️⃣  Testing Search Primary Interactions...");
            InteractionPrimarySearchDto searchDto = new InteractionPrimarySearchDto();
            searchDto.setCustomerOfferId(1L);
            searchDto.setResult("verification");
            searchDto.setPage(0);
            searchDto.setSize(10);
            searchDto.setSortBy("happenedAt,desc");

            var searchResults = primaryApiService.searchInteractions(searchDto);
            System.out.println("   ✅ Search completed successfully");
            System.out.println("   📊 Total elements: " + searchResults.getTotalElements());
            System.out.println("   📄 Current page: " + searchResults.getNumber());
            System.out.println("   📏 Page size: " + searchResults.getSize());

            // Test 4: Get by Customer Offer ID
            System.out.println("\n4️⃣  Testing Get by Customer Offer ID...");
            var offerInteractions = primaryApiService.getInteractionsByCustomerOfferId(1L);
            System.out.println("   ✅ Retrieved interactions for customer offer 1");
            System.out.println("   📊 Count: " + offerInteractions.size());

            // Test 5: Count by Customer Offer ID
            System.out.println("\n5️⃣  Testing Count by Customer Offer ID...");
            Long count = primaryApiService.countInteractionsByCustomerOfferId(1L);
            System.out.println("   ✅ Count for customer offer 1: " + count);

            // Test 6: Delete Primary Interaction
            System.out.println("\n6️⃣  Testing Delete Primary Interaction...");
            primaryApiService.deleteInteraction(created.getId());
            System.out.println("   ✅ Deleted primary interaction: " + created.getId());

            System.out.println("\n✅ PRIMARY INTERACTIONS API - ALL TESTS PASSED!");

        } catch (Exception e) {
            System.err.println("❌ PRIMARY INTERACTIONS TEST FAILED: " + e.getMessage());
            throw e;
        }
    }

    private void testSecondaryInteractions() {
        System.out.println("\n📋 TESTING SECONDARY INTERACTIONS API");
        System.out.println("-".repeat(50));

        try {
            // Test 1: Create Secondary Interaction
            System.out.println("1️⃣  Testing Create Secondary Interaction...");
            InteractionSecondaryCreateDto createDto = new InteractionSecondaryCreateDto();
            createDto.setCustomerPropertyId(1L);
            createDto.setExpectedSellPrice(new BigDecimal("5000000000"));
            createDto.setExpectedRentPrice(new BigDecimal("50000000"));
            createDto.setResult("Verification test - property valuation");
            createDto.setHappenedAt("15/01/2024");
            createDto.setNotes("This is a verification test for secondary interaction creation");

            InteractionSecondaryDto created = secondaryApiService.createInteraction(createDto);
            System.out.println("   ✅ Created secondary interaction with ID: " + created.getId());
            System.out.println("   📝 Result: " + created.getResult());
            System.out.println("   💰 Expected Sell Price: " + created.getExpectedSellPrice());
            System.out.println("   🏠 Expected Rent Price: " + created.getExpectedRentPrice());

            // Test 2: Get Secondary Interaction by ID
            System.out.println("\n2️⃣  Testing Get Secondary Interaction by ID...");
            InteractionSecondaryDto retrieved = secondaryApiService.getInteractionById(created.getId());
            System.out.println("   ✅ Retrieved secondary interaction: " + retrieved.getId());
            System.out.println("   📝 Result: " + retrieved.getResult());
            System.out.println("   👤 Created By: " + retrieved.getCreatedBy());
            System.out.println("   👤 Created Name: " + retrieved.getCreatedName());

            // Test 3: Search Secondary Interactions
            System.out.println("\n3️⃣  Testing Search Secondary Interactions...");
            InteractionSecondarySearchDto searchDto = new InteractionSecondarySearchDto();
            searchDto.setCustomerPropertyId(1L);
            searchDto.setExpectedSellPriceFrom(new BigDecimal("1000000000"));
            searchDto.setExpectedSellPriceTo(new BigDecimal("10000000000"));
            searchDto.setPage(0);
            searchDto.setSize(10);
            searchDto.setSortBy("happenedAt,desc");

            var searchResults = secondaryApiService.searchInteractions(searchDto);
            System.out.println("   ✅ Search completed successfully");
            System.out.println("   📊 Total elements: " + searchResults.getTotalElements());
            System.out.println("   📄 Current page: " + searchResults.getNumber());
            System.out.println("   📏 Page size: " + searchResults.getSize());

            // Test 4: Get by Customer Property ID
            System.out.println("\n4️⃣  Testing Get by Customer Property ID...");
            var propertyInteractions = secondaryApiService.getInteractionsByCustomerPropertyId(1L);
            System.out.println("   ✅ Retrieved interactions for customer property 1");
            System.out.println("   📊 Count: " + propertyInteractions.size());

            // Test 5: Count by Customer Property ID
            System.out.println("\n5️⃣  Testing Count by Customer Property ID...");
            Long propertyCount = secondaryApiService.countInteractionsByCustomerPropertyId(1L);
            System.out.println("   ✅ Count for customer property 1: " + propertyCount);

            // Test 6: Count by Unit ID
            System.out.println("\n6️⃣  Testing Count by Unit ID...");
            Long unitCount = secondaryApiService.countInteractionsByUnitId(1L);
            System.out.println("   ✅ Count for unit 1: " + unitCount);

            // Test 7: Delete Secondary Interaction
            System.out.println("\n7️⃣  Testing Delete Secondary Interaction...");
            secondaryApiService.deleteInteraction(created.getId());
            System.out.println("   ✅ Deleted secondary interaction: " + created.getId());

            System.out.println("\n✅ SECONDARY INTERACTIONS API - ALL TESTS PASSED!");

        } catch (Exception e) {
            System.err.println("❌ SECONDARY INTERACTIONS TEST FAILED: " + e.getMessage());
            throw e;
        }
    }

    /**
     * Additional verification methods for edge cases and error handling
     */
    private void testErrorHandling() {
        System.out.println("\n🔍 TESTING ERROR HANDLING");
        System.out.println("-".repeat(50));

        // Test invalid customer offer ID
        try {
            System.out.println("1️⃣  Testing invalid customer offer ID...");
            InteractionPrimaryCreateDto invalidDto = new InteractionPrimaryCreateDto();
            invalidDto.setCustomerOfferId(999999L); // Non-existent ID
            invalidDto.setResult("Test");
            invalidDto.setHappenedAt("15/01/2024");
            
            primaryApiService.createInteraction(invalidDto);
            System.err.println("   ❌ Should have thrown exception for invalid customer offer ID");
        } catch (Exception e) {
            System.out.println("   ✅ Correctly handled invalid customer offer ID: " + e.getMessage());
        }

        // Test invalid date format
        try {
            System.out.println("2️⃣  Testing invalid date format...");
            InteractionPrimaryCreateDto invalidDateDto = new InteractionPrimaryCreateDto();
            invalidDateDto.setCustomerOfferId(1L);
            invalidDateDto.setResult("Test");
            invalidDateDto.setHappenedAt("2024-01-15"); // Wrong format
            
            primaryApiService.createInteraction(invalidDateDto);
            System.err.println("   ❌ Should have thrown exception for invalid date format");
        } catch (Exception e) {
            System.out.println("   ✅ Correctly handled invalid date format: " + e.getMessage());
        }

        // Test negative price
        try {
            System.out.println("3️⃣  Testing negative price...");
            InteractionSecondaryCreateDto negativePrice = new InteractionSecondaryCreateDto();
            negativePrice.setCustomerPropertyId(1L);
            negativePrice.setExpectedSellPrice(new BigDecimal("-1000000"));
            negativePrice.setResult("Test");
            negativePrice.setHappenedAt("15/01/2024");
            
            secondaryApiService.createInteraction(negativePrice);
            System.err.println("   ❌ Should have thrown exception for negative price");
        } catch (Exception e) {
            System.out.println("   ✅ Correctly handled negative price: " + e.getMessage());
        }

        System.out.println("\n✅ ERROR HANDLING TESTS COMPLETED!");
    }

    /**
     * Performance verification for large datasets
     */
    private void testPerformance() {
        System.out.println("\n⚡ TESTING PERFORMANCE");
        System.out.println("-".repeat(50));

        try {
            long startTime = System.currentTimeMillis();
            
            // Test pagination performance
            InteractionPrimarySearchDto searchDto = new InteractionPrimarySearchDto();
            searchDto.setPage(0);
            searchDto.setSize(100);
            searchDto.setSortBy("happenedAt,desc");
            
            var results = primaryApiService.searchInteractions(searchDto);
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            System.out.println("   ✅ Search with pagination completed in " + duration + "ms");
            System.out.println("   📊 Retrieved " + results.getNumberOfElements() + " elements");
            System.out.println("   📄 Total pages: " + results.getTotalPages());
            
            if (duration < 1000) {
                System.out.println("   🚀 Performance: EXCELLENT (< 1 second)");
            } else if (duration < 3000) {
                System.out.println("   ✅ Performance: GOOD (< 3 seconds)");
            } else {
                System.out.println("   ⚠️  Performance: NEEDS OPTIMIZATION (> 3 seconds)");
            }
            
        } catch (Exception e) {
            System.err.println("   ❌ Performance test failed: " + e.getMessage());
        }
    }

    /**
     * Integration verification with related entities
     */
    private void testIntegration() {
        System.out.println("\n🔗 TESTING INTEGRATION");
        System.out.println("-".repeat(50));

        try {
            // Test relationship integrity
            System.out.println("1️⃣  Testing relationship integrity...");
            
            // Create primary interaction
            InteractionPrimaryCreateDto primaryDto = new InteractionPrimaryCreateDto();
            primaryDto.setCustomerOfferId(1L);
            primaryDto.setResult("Integration test");
            primaryDto.setHappenedAt("15/01/2024");
            primaryDto.setNotes("Testing integration");
            
            InteractionPrimaryDto primary = primaryApiService.createInteraction(primaryDto);
            
            // Create secondary interaction
            InteractionSecondaryCreateDto secondaryDto = new InteractionSecondaryCreateDto();
            secondaryDto.setCustomerPropertyId(1L);
            secondaryDto.setExpectedSellPrice(new BigDecimal("2000000000"));
            secondaryDto.setResult("Integration test secondary");
            secondaryDto.setHappenedAt("15/01/2024");
            secondaryDto.setNotes("Testing secondary integration");
            
            InteractionSecondaryDto secondary = secondaryApiService.createInteraction(secondaryDto);
            
            System.out.println("   ✅ Created related interactions successfully");
            System.out.println("   🔗 Primary ID: " + primary.getId());
            System.out.println("   🔗 Secondary ID: " + secondary.getId());
            
            // Cleanup
            primaryApiService.deleteInteraction(primary.getId());
            secondaryApiService.deleteInteraction(secondary.getId());
            
            System.out.println("   🧹 Cleanup completed");
            
        } catch (Exception e) {
            System.err.println("   ❌ Integration test failed: " + e.getMessage());
        }
    }
}
