package vn.agis.crm.rmi;

import com.zaxxer.hikari.HikariDataSource;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.rmi.RemoteException;
import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;
import java.rmi.server.UnicastRemoteObject;
import java.sql.*;
import java.util.*;

@Component
public class RmiMessengerServiceImpl implements RmiMessengerService {

    @Autowired
    ApplicationContext context;
//    @Autowired
//    @Qualifier("coremgmt")
//    HikariDataSource ds;
//    @Autowired
//    @Qualifier("reportmgmt")
//    HikariDataSource dsReport;

    // typeSql: 0 - select, 1 - insert, update, delete, 2 - anomyous block
    @Override
    public String processQuery(String typeSql, String dataSourceStr, String query) {
        System.out.println("Receive RMI request: typeSql " + typeSql + ", dataSource " + dataSourceStr + ", query ----" + query);
        if (!StringUtils.equals(typeSql, "0") && StringUtils.equals(typeSql, "1") && StringUtils.equals(typeSql, "2")) return "type sql invalid";
        String rs = "";
        try {
            DataSource dataSource = getDataSource(dataSourceStr);
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(query)) {
                if (typeSql.equals("0")) {
                    ResultSet resultSet = stmt.executeQuery();
                    JSONArray jsonArray = resultSetToJson(resultSet);
                    rs = jsonArray.toString(2);
                } else if (typeSql.equals("1")) {
                    int i = stmt.executeUpdate();
                    rs = "result execute: " + i;
                } else {
                    stmt.executeQuery();
                    rs = "OK";
                }
            } catch (SQLException throwables) {
                throwables.printStackTrace();
                rs = throwables.getMessage();
            }
        } catch (Exception e) {
            e.printStackTrace();
            rs = e.getMessage();
        }

        return rs;
    }

    private DataSource getDataSource(String dataSource) throws SQLException {
        if (dataSource.contains("@")) {
            String[] arr = dataSource.split("/");
            if (arr == null || arr.length != 2)
                throw new RuntimeException("Connection string to database is wrong syntax : username/password@connect_identifier");
            String username = arr[0];
            arr = dataSource.substring(username.length() + 1).split("@");
            if (arr.length < 2)
                throw new RuntimeException("Connection string to database is wrong syntax : username/password@connect_identifier");
            String password = arr[0];
            String jdbcUrl = dataSource.substring(username.length() + password.length() + 2);
            return DataSourceBuilder.create().driverClassName("oracle.jdbc.OracleDriver")
                    .username(username)
                    .password(password)
                    .url(jdbcUrl)
                    .build();
        }
        Map<String, HikariDataSource> mapDS = context.getBeansOfType(HikariDataSource.class);
        return mapDS.get(dataSource);
    }

    private static JSONArray resultSetToJson(ResultSet rs) throws SQLException {
        JSONArray jsonArray = new JSONArray();
        ResultSetMetaData rsmd = rs.getMetaData();
        int columnCount = rsmd.getColumnCount();

        while (rs.next()) {
            JSONObject jsonObject = new JSONObject();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rsmd.getColumnName(i);
                Object columnValue = rs.getObject(i);
                jsonObject.put(columnName, columnValue);
            }
            jsonArray.put(jsonObject);
        }
        return jsonArray;
    }


    @PostConstruct
    public void createStubAndBind() throws RemoteException {
        RmiMessengerService stub = (RmiMessengerService) UnicastRemoteObject.exportObject((RmiMessengerService) this, 0);
        Registry registry = LocateRegistry.createRegistry(1099);
        registry.rebind("RmiMessengerService", stub);
    }
}
