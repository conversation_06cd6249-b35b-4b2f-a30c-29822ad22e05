package vn.agis.crm.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Created by huyvv
 * Date: 05/08/2021
 * Time: 8:53 AM
 * for all issues, contact me: <EMAIL>
 **/
public class AesCrypt {
    private static final Logger logger = LoggerFactory.getLogger(AesCrypt.class);
    private static final String secretPass = "1qazxsw-o6o12oii";
    private static final String secretIV = "2wsxzaq-4g0l0nhc";

    private final SecretKeySpec keySpec;
    private final IvParameterSpec ivParameterSpec;

    public AesCrypt() {
        keySpec = new SecretKeySpec(secretPass.getBytes(), "AES");
        ivParameterSpec = new IvParameterSpec(secretIV.getBytes());
    }

    public String encrypt(String strToEncrypt) {
        try {
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(1, keySpec, ivParameterSpec);
            return Base64.getEncoder().encodeToString(cipher.doFinal(strToEncrypt.getBytes("UTF-8")));
        } catch (Exception ex) {
            logger.error("encrypt error with input #{}, detail: #{}", strToEncrypt, ex.getMessage());
            return strToEncrypt;
        }
    }

    public String decrypt(String strToDecrypt) {
        try {
            Base64.Decoder decoder = Base64.getDecoder();
            byte[] encrypted1 = decoder.decode(strToDecrypt);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");
            cipher.init(2, keySpec, ivParameterSpec);
            byte[] original = cipher.doFinal(encrypted1);
            return new String(original);
        } catch (Exception ex) {
            logger.error("decrypt error with input #{}, detail: #{}", strToDecrypt, ex.getMessage());
            return strToDecrypt;
        }
    }
}
