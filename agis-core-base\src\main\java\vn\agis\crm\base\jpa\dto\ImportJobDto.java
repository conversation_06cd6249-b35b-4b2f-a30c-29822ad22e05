package vn.agis.crm.base.jpa.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import vn.agis.crm.base.domain.imports.ImportJobMode;
import vn.agis.crm.base.domain.imports.ImportJobSource;
import vn.agis.crm.base.domain.imports.ImportJobStatus;

import java.util.Date;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ImportJobDto {
    
    private Long id;
    private String fileName;
    private String fileChecksum;
    private ImportJobSource source;
    private String sourceLink;
    private ImportJobMode mode;
    private Integer totalRows;
    private Integer validRows;
    private Integer errorRows;
    private ImportJobStatus status;
    private String options;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date updatedAt;
    
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date finishedAt;
    
    private WarningsDto warnings;
    
    @Data
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class WarningsDto {
        private Boolean duplicateFileDetected;
        private List<Long> relatedJobIds;
    }
}
