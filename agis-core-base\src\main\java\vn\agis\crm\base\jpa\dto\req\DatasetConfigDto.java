package vn.agis.crm.base.jpa.dto.req;

import lombok.Data;

import java.util.List;

@Data
public class DatasetConfigDto {
    private Long id;
    private String type;
    private String datasetName;
    private String schema;
    private String query;
    private String typeQuery;
    private String keyLabel;
    private String keyValue;
    private String keyValueX;
    private String keyValueY;
    private String keyValueR;
    private String keyDataset;
    private String typeAnimation;
    private List<String> backgroundColors;
    private List<String> borderColors;
    private int borderWidth;
    private List<String> hoverBackgroundColors;
    private List<String> hoverBorderColors;
    private int hoverBorderWidth;
    private List<String> pointColors;
    private List<String> pointBorderColors;
    private int pointBorderWidth;
    private int barPercentage;
    private int barThickness;
    private int base;
    private int barRadius;
    private int categoryPercentage;
    private String xAxisID;
    private String yAxisID;
    private String stack;
    private boolean isLineDash;
    private String borderCapStyle;
    private String borderJoinStyle;
    private boolean fill;
    private int tension;
    private String pointStyle;
    private int weight;

}
