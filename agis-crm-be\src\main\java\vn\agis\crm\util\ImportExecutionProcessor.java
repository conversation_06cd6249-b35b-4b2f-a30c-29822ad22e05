package vn.agis.crm.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ImportResultDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.base.jpa.entity.*;
import vn.agis.crm.repository.*;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Main processor for import execution
 * Handles the actual database operations for customer import
 */
@Component
public class ImportExecutionProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(ImportExecutionProcessor.class);
    
    // Configuration constants
    private static final int DEFAULT_BATCH_SIZE = 100;
    private static final int PROGRESS_UPDATE_INTERVAL = 50; // Update progress every 50 rows
    private static final SimpleDateFormat sdf = new SimpleDateFormat("E MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);

    /**
     * Process import execution for an import job
     */
    public static ImportResultDto processImportExecution(ImportJob job, String filePath, Long userId, ProjectRepository projectRepository,
                                                         UnitRepository unitRepository,
                                                         CustomerPropertyRepository customerPropertyRepository,
                                                         CustomerRepository customerRepository,
                                                         CustomerRelativeRepository customerRelativeRepository,
                                                         EmployeeRepository employeeRepository,
                                                         InteractionsSecondaryRepository interactionsSecondaryRepository,
                                                         CustomerOfferRepository customerOfferRepository) {
        Date startTime = new Date();
        
        try {
            logger.info("Starting import execution for job {} with file {}", job.getId(), job.getFileName());
            
            // Parse file data (reuse from dry-run)
            List<LinkedHashMap<String, String>> rowsData = parseFile(filePath, job.getFileName(), job.getOptions());
            
            // Validate data (reuse validation logic)
            List<ValidationResultDto> validationResults = validateData(rowsData, job.getId());
            
            // Execute import with database operations
            ImportResultDto result = executeImport(job, validationResults, startTime, userId, projectRepository,unitRepository,customerPropertyRepository,customerRepository,customerRelativeRepository,employeeRepository,interactionsSecondaryRepository, customerOfferRepository);
            
            logger.info("Completed import execution for job {}. Total rows: {}, Successful: {}, Failed: {}", 
                       job.getId(), result.getTotalRows(), result.getSuccessfulRows(), result.getFailedRows());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Error processing import execution for job " + job.getId(), e);
            
            // Return error result
            ImportResultDto errorResult = new ImportResultDto(job.getId(), job.getFileName(), "FAILED");
            errorResult.setStartedAt(startTime);
            errorResult.setFinishedAt(new Date());
            errorResult.setTotalRows(0);
            errorResult.setSuccessfulRows(0);
            errorResult.setFailedRows(0);
            errorResult.calculateProcessingTime();
            
            return errorResult;
        }
    }
    
    /**
     * Execute the actual import with database operations
     */
    private static ImportResultDto executeImport(ImportJob job, List<ValidationResultDto> validationResults, Date startTime, Long userId, ProjectRepository projectRepository,
                                                 UnitRepository unitRepository,
                                                 CustomerPropertyRepository customerPropertyRepository,
                                                 CustomerRepository customerRepository,
                                                 CustomerRelativeRepository customerRelativeRepository,
                                                 EmployeeRepository employeeRepository, InteractionsSecondaryRepository interactionsSecondaryRepository,
    CustomerOfferRepository customerOfferRepository) {
        ImportResultDto result = new ImportResultDto(job.getId(), job.getFileName(), "SUCCESS");
        result.setStartedAt(startTime);
        
        int totalRows = validationResults.size();
        int successfulRows = 0;
        int failedRows = 0;
        int processedRows = 0;
        
        // Database operations tracking
        ImportResultDto.DatabaseOperationsDto dbOps = new ImportResultDto.DatabaseOperationsDto();
        dbOps.setCustomersCreated(0);
        dbOps.setCustomersUpdated(0);
        dbOps.setCustomersSkipped(0);
        dbOps.setTransactionsCommitted(0);
        dbOps.setTransactionsRolledBack(0);
        
        List<String> rollbackReasons = new ArrayList<>();
        
        try {
            // Process in batches for better performance
            int batchSize = getBatchSize(job.getOptions());
            int totalBatches = (int) Math.ceil((double) totalRows / batchSize);
            
            logger.info("Processing {} rows in {} batches of size {}", totalRows, totalBatches, batchSize);
            
            for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                int startIndex = batchIndex * batchSize;
                int endIndex = Math.min(startIndex + batchSize, totalRows);
                
                List<ValidationResultDto> batchData = validationResults.subList(startIndex, endIndex);
                
                // Process batch
                BatchResult batchResult = processBatch(job, batchData, batchIndex + 1, totalBatches,userId,projectRepository,unitRepository,customerPropertyRepository,customerRepository,customerRelativeRepository,employeeRepository,interactionsSecondaryRepository, customerOfferRepository);
                
                // Update counters
                successfulRows += batchResult.successfulRows;
                failedRows += batchResult.failedRows;
                processedRows += batchData.size();
                
                // Update database operations
                dbOps.setCustomersCreated(dbOps.getCustomersCreated() + batchResult.customersCreated);
                dbOps.setCustomersUpdated(dbOps.getCustomersUpdated() + batchResult.customersUpdated);
                dbOps.setTransactionsCommitted(dbOps.getTransactionsCommitted() + batchResult.transactionsCommitted);
                dbOps.setTransactionsRolledBack(dbOps.getTransactionsRolledBack() + batchResult.transactionsRolledBack);
                
                if (batchResult.rollbackReasons != null) {
                    rollbackReasons.addAll(batchResult.rollbackReasons);
                }
                
                // Check for cancellation (placeholder - would check job status in real implementation)
                if (isImportCancelled(job.getId())) {
                    logger.info("Import cancelled for job {}", job.getId());
                    result.setStatus("CANCELLED");
                    result.setWasCancelled(true);
                    result.setCancelledAt(new Date());
                    break;
                }
                
                // Log progress
                if (batchIndex % 5 == 0 || batchIndex == totalBatches - 1) {
                    double progress = ((double) processedRows / totalRows) * 100;
                    logger.info("Import progress for job {}: {}/{} rows processed ({:.1f}%)", 
                               job.getId(), processedRows, totalRows, progress);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error during import execution", e);
            result.setStatus("FAILED");
            rollbackReasons.add("Thực thi import thất bại: " + e.getMessage());
        }
        
        // Finalize result
        result.setFinishedAt(new Date());
        result.setTotalRows(totalRows);
        result.setProcessedRows(processedRows);
        result.setSuccessfulRows(successfulRows);
        result.setFailedRows(failedRows);
        result.calculateSuccessRate();
        result.calculateProcessingTime();
        
        // Set database operations
        dbOps.setRollbackReasons(rollbackReasons);
        result.setDatabaseOperations(dbOps);
        
        // Set error summary
        result.setTotalErrors(failedRows);
        result.setTotalWarnings(0); // TODO: Implement warning tracking
        
        return result;
    }
    
    /**
     * Process a single batch of data
     */
    private static BatchResult processBatch(ImportJob job, List<ValidationResultDto> batchData, 
                                          int batchNumber, int totalBatches, Long userId,
                                            ProjectRepository projectRepository,
                                            UnitRepository unitRepository,
                                            CustomerPropertyRepository customerPropertyRepository,
                                            CustomerRepository customerRepository,
                                            CustomerRelativeRepository customerRelativeRepository,
                                            EmployeeRepository employeeRepository,InteractionsSecondaryRepository interactionsSecondaryRepository,
                                            CustomerOfferRepository customerOfferRepository) {
        BatchResult result = new BatchResult();
        
        logger.debug("Processing batch {}/{} with {} rows for job {}", 
                    batchNumber, totalBatches, batchData.size(), job.getId());
        
        for (ValidationResultDto validationResult : batchData) {
            try {
                // Skip rows with validation errors
                if (validationResult.hasErrors()) {
                    result.failedRows++;
                    result.transactionsRolledBack++;
                    if (result.rollbackReasons == null) {
                        result.rollbackReasons = new ArrayList<>();
                    }
                    result.rollbackReasons.add("Dòng " + validationResult.getRowNumber() + ": Lỗi xác thực dữ liệu");
                    continue;
                }
                
                // Process valid row (placeholder - would implement actual database operations)
                boolean success = processCustomerRow(validationResult,userId,projectRepository,unitRepository,customerPropertyRepository,customerRepository,customerRelativeRepository,employeeRepository,interactionsSecondaryRepository,customerOfferRepository);
                
                if (success) {
                    result.successfulRows++;
                    result.customersCreated++; // Simplified - would track creates vs updates
                    result.transactionsCommitted++;
                } else {
                    result.failedRows++;
                    result.transactionsRolledBack++;
                    if (result.rollbackReasons == null) {
                        result.rollbackReasons = new ArrayList<>();
                    }
                    result.rollbackReasons.add("Dòng " + validationResult.getRowNumber() + ": Thao tác cơ sở dữ liệu thất bại");
                }
                
            } catch (Exception e) {
                logger.error("Error processing row {}", validationResult.getRowNumber(), e);
                result.failedRows++;
                result.transactionsRolledBack++;
                if (result.rollbackReasons == null) {
                    result.rollbackReasons = new ArrayList<>();
                }
                result.rollbackReasons.add("Dòng " + validationResult.getRowNumber() + ": " + e.getMessage());
            }
        }
        
        return result;
    }
    
    /**
     * Process a single customer row (placeholder implementation)
     */
    static ObjectMapper mapper = new ObjectMapper();
    private static boolean processCustomerRow(ValidationResultDto validationResult, Long userId,
                                              ProjectRepository projectRepository,
                                              UnitRepository unitRepository,
                                              CustomerPropertyRepository customerPropertyRepository,
                                              CustomerRepository customerRepository,
                                              CustomerRelativeRepository customerRelativeRepository,
                                              EmployeeRepository employeeRepository,
                                              InteractionsSecondaryRepository interactionsSecondaryRepository,
                                                CustomerOfferRepository customerOfferRepository) {
        // TODO: Implement actual database operations
        // This would include:
        // 1. Create/update customer record
        // 2. Create customer relatives
        // 3. Create customer properties
        // 4. Create customer offers
        // 5. Create interactions
        // 6. Create assignments
        
        // For now, simulate processing with a small delay
        try {
            LinkedHashMap<String,String> map = validationResult.getOriginalRowData();

            Iterator<String> it = map.values().iterator();

            // Project validation and creation logic
            String projectName = it.next(); // First field is project name (TÊN DỰ ÁN)
            Projects pSave = validateAndCreateProject(projectName, userId, projectRepository);

            // Unit validation, creation, and update logic
            String sector = it.next(); //PHÂN KHU
            String unitCode = it.next();
            String floorNumber = it.next(); //TẦNG/DÃY SỐ
            String unitNumber = it.next(); //CĂN SỐ
            String productType = it.next(); //LOẠI SẢN PHẨM
            String areaStr = it.next(); //DIỆN TÍCH ĐẤT/DIỆN TÍCH TIM TƯỜNG
            String floorAreaStr = it.next();
            String doorDirection = it.next();
            String view = it.next();
            String contractPriceStr = it.next();  // GIÁ GỐC TRÊN HỢP ĐỒNG

            Units unitsSave = validateAndCreateOrUpdateUnit(
                pSave.getId(), unitCode, sector, floorNumber, unitNumber,
                productType, areaStr, floorAreaStr, doorDirection, view,
                contractPriceStr, userId, unitRepository, validationResult);

            //Giao dịch ngày (customer_properties.transactionDate)
            Date transactionDate = sdf.parse(it.next());

            // Customer identification and data merging logic
            String sourceType = it.next();
            String sourceDetail = it.next();
            // pháp lý (customer_properties.legal_status)
            String legalStatus = it.next();

            String avatarUrl = it.next();
            String fullName = it.next();
            Date birthDate = sdf.parse(it.next());
            String phone = it.next();
            String cccd = it.next();
            String email = it.next();
            String addressContact = it.next();
            String addressPermanent = it.next();
            String nationality = it.next();
            String maritalStatus = it.next();
            String interests = it.next();
            String totalAssetStr = it.next();
            String businessField = it.next();
            String zaloStatus = it.next();
            String facebookLink = it.next(); //AE

            Customers customersSave = identifyAndProcessCustomer(
                phone, cccd, email, fullName, birthDate, avatarUrl, addressContact,
                addressPermanent, nationality, maritalStatus, interests, totalAssetStr,
                businessField, zaloStatus, facebookLink, sourceType, sourceDetail,
                userId, customerRepository, validationResult);

            //TODO: Bước 4: Đọc thông tin người thân của khách hàng
            String relationType = it.next();
            String fullNameOfRelative = it.next();
            String yearOfBirth = it.next();
            String relativePhone = it.next();
            if (relationType != null || fullNameOfRelative != null || yearOfBirth != null || relativePhone != null) {
                CustomerRelatives cr = new CustomerRelatives();
                cr.setCustomerId(customersSave.getId());
                cr.setRelationType(relationType);
                cr.setFullName(fullNameOfRelative);
                if (yearOfBirth != null) {
                    cr.setYearOfBirth(Integer.parseInt(yearOfBirth));
                }
                // Clean Excel formatting artifacts from relative's phone
                if (relativePhone != null && !relativePhone.trim().isEmpty()) {
                    relativePhone = removeExcelFormattingArtifacts(relativePhone);
                }
                cr.setPhone(relativePhone);

                cr.setNotes(it.next());
                cr.setCreatedBy(userId);
                cr.setCreatedAt(new java.util.Date());
                CustomerRelatives customerRelativesSave = customerRelativeRepository.save(cr);
            }


            //TODO: Bước 5: Đọc thông tin thứ cấp chuyển nhượng
            String firstInteraction = it.next();
            String lastInteraction = it.next();
            CustomerProperties customerPropertiesSave = new CustomerProperties();
            if (firstInteraction != null || lastInteraction != null) {
                CustomerProperties cp = new CustomerProperties();
                cp.setCustomerId(customersSave.getId());
                cp.setProjectId(pSave.getId());
                cp.setUnitId(unitsSave.getId());
                cp.setFirstInteraction(sdf.parse(firstInteraction)); // TƯƠNG TÁC LẦN ĐẦU (THỨ CẤP)
                cp.setLastInteraction(sdf.parse(lastInteraction)); // TƯƠNG TÁC GẦN NHẤT (THỨ CẤP)
                cp.setCreatedBy(userId);
                cp.setCreatedAt(new java.util.Date());
                cp.setTransactionDate(transactionDate);
                cp.setLegalStatus(legalStatus);
                customerPropertiesSave = customerPropertyRepository.save(cp);
            }


            // TODO: DỰ ÁN ĐANG CHÀO, KẾT QUẢ (SƠ CẤP)
            //interactions_secondary.result
            String resultInteractionsSecondary = it.next(); //"KÉT QUẢ(THỨ CẤP)"
            //expected_sell_price
            String expectedSellPrice = it.next(); //GIÁ BÁN KỲ VỌNG (THỨ CẤP)
            String expectedRentPrice = it.next(); //GIÁ Cho Thuê KỲ VỌNG (THỨ CẤP)
            String noteInteractionsSecondary = it.next(); //AP


            InteractionsSecondary is = new InteractionsSecondary();
            is.setResult(resultInteractionsSecondary);
            is.setExpectedSellPrice(new BigDecimal(expectedSellPrice));
            is.setExpectedRentPrice(new BigDecimal(expectedRentPrice));
            is.setNotes(noteInteractionsSecondary);
            is.setHappenedAt(null); //TODO : interactions_secondary.happened_at
            is.setCustomerPropertyId(customerPropertiesSave.getId());
            InteractionsSecondary interactionsSecondarySave = interactionsSecondaryRepository.save(is);

            // TODO: Cột TÊN ĐẠI LÝ, TÊN SALE NGOÀI, SỐ PHONE SALE NGOÀI, EMAIL SALE NGOÀI
//            customer_offers
            CustomerOffers co = new CustomerOffers();
            co.setFirstInteraction(sdf.parse(it.next()));
            co.setLastInteraction(sdf.parse(it.next()));
            String projectNameOffer = it.next(); //"DỰ ÁN ĐANG CHÀO (SƠ CẤP)"
            Projects pOfferSave = validateAndCreateProject(projectNameOffer, userId, projectRepository);
            co.setProjectId(pOfferSave.getId());
            co.setCustomerId(customersSave.getId());
            CustomerOffers customerOffersSave = customerOfferRepository.save(co);

            InteractionsPrimary ip = new InteractionsPrimary();
            ip.setCustomerOfferId(customerOffersSave.getId());
            ip.setResult(it.next());
            ip.setNotes(it.next()); // GHI CHÚ (SƠ CẤP) cột AU


            /////////////////////////////////////////////////////////////
            customerPropertiesSave.setExternalAgencyName(it.next());
            customerPropertiesSave.setExternalSaleName(it.next());
            customerPropertiesSave.setExternalSalePhone(it.next());
            customerPropertiesSave.setExternalSaleEmail(it.next());
            customerPropertyRepository.save(customerPropertiesSave);


            Employee e = new Employee();
            e.setEmployeeCode(it.next());
            e.setFullName(it.next());
            e.setPhone(it.next());
            e.setEmail(it.next());
            e.setPassword("****");
            e.setRoleId(3);
            e.setCreatedBy(userId);
            e.setCreatedAt(new java.util.Date());
            Employee employeeSave = employeeRepository.save(e);

            // Map lại id employee
            // TODO: Cột THỜI GIAN NHẬN ĐƯỢC LEAD ĐỂ CHĂM
            CustomerAssignments ca= new CustomerAssignments();
            ca.setCustomerId(customersSave.getId());
            ca.setEmployeeId(employeeSave.getId());
            ca.setRoleType(2);
            ca.setAssignedFrom(sdf.parse(it.next()));
            ca.setCreatedBy(userId);
            ca.setCreatedAt(new Date());

            return true; // Simulate success
        } catch (Exception e) {
            logger.error("Exception : {}", e.getMessage(), e);
            return false;
        }
    }


    /**
     * Parse file data (reuse from dry-run processor)
     */
    private static List<LinkedHashMap<String, String>> parseFile(String filePath, String fileName, String options) throws Exception {
        return ImportDryRunProcessor.parseFile(filePath, fileName, options);
    }
    
    /**
     * Validate data (reuse from dry-run processor)
     */
    private static List<ValidationResultDto> validateData(List<LinkedHashMap<String, String>> rowsData, Long jobId) {
        return ImportDryRunProcessor.validateData(rowsData, jobId);
    }
    
    /**
     * Get batch size from options
     */
    private static int getBatchSize(String options) {
        // TODO: Parse batch size from options
        return DEFAULT_BATCH_SIZE;
    }
    
    /**
     * Check if import is cancelled (placeholder)
     */
    private static boolean isImportCancelled(Long jobId) {
        // TODO: Implement cancellation check by querying job status
        return false;
    }
    
    /**
     * Validate and create project if needed
     * Implements case-insensitive project name checking to prevent duplicates
     *
     * @param projectName The project name from import data
     * @param userId The user ID creating the project
     * @param projectRepository The project repository for database operations
     * @return The existing or newly created project
     * @throws RuntimeException if project creation fails
     */
    private static Projects validateAndCreateProject(String projectName, Long userId, ProjectRepository projectRepository) {
        if (projectName == null || projectName.trim().isEmpty()) {
            throw new IllegalArgumentException("Tên dự án không được để trống");
        }

        String trimmedProjectName = projectName.trim();

        try {
            // Check if project already exists (case-insensitive)
            Projects existingProject = projectRepository.findFirstByNameIgnoreCase(trimmedProjectName);

            if (existingProject != null) {
                logger.debug("Found existing project: {} (ID: {})", existingProject.getName(), existingProject.getId());
                return existingProject;
            }

            // Project doesn't exist, create new one
            logger.info("Creating new project: {}", trimmedProjectName);

            Projects newProject = new Projects();
            newProject.setName(trimmedProjectName);
            newProject.setCreatedBy(userId);
            newProject.setCreatedAt(new java.util.Date());
            newProject.setActive(true); // Set as active by default

            Projects savedProject = projectRepository.save(newProject);
            logger.info("Successfully created new project: {} (ID: {})", savedProject.getName(), savedProject.getId());

            return savedProject;

        } catch (Exception e) {
            logger.error("Error validating/creating project '{}': {}", trimmedProjectName, e.getMessage(), e);
            throw new RuntimeException("Lỗi khi xử lý dự án '" + trimmedProjectName + "': " + e.getMessage(), e);
        }
    }

    /**
     * Validate and create or update unit with comprehensive field comparison
     * Implements case-insensitive unit code checking and field-by-field update logic
     *
     * @param projectId The project ID for the unit
     * @param unitCode The unit code from import data
     * @param sector The sector from import data
     * @param floorNumber The floor number from import data
     * @param unitNumber The unit number from import data
     * @param productType The product type from import data
     * @param areaStr The area from import data (as string)
     * @param floorAreaStr The floor area from import data (as string)
     * @param doorDirection The door direction from import data
     * @param view The view from import data
     * @param contractPriceStr The contract price from import data (as string)
     * @param userId The user ID for audit trail
     * @param unitRepository The unit repository for database operations
     * @param validationResult The validation result to add warnings to
     * @return The existing or newly created unit
     * @throws RuntimeException if unit processing fails
     */
    private static Units validateAndCreateOrUpdateUnit(Long projectId, String unitCode, String sector,
                                                      String floorNumber, String unitNumber, String productType,
                                                      String areaStr, String floorAreaStr, String doorDirection,
                                                      String view, String contractPriceStr, Long userId,
                                                      UnitRepository unitRepository, ValidationResultDto validationResult) {
        if (unitCode == null || unitCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Mã căn không được để trống");
        }

        String trimmedUnitCode = unitCode.trim();

        try {
            // Check if unit already exists (case-insensitive)
            Units existingUnit = unitRepository.findFirstByProjectIdAndCodeIgnoreCase(projectId, trimmedUnitCode);

            if (existingUnit != null) {
                logger.debug("Found existing unit: {} in project {} (ID: {})",
                    existingUnit.getCode(), projectId, existingUnit.getId());

                // Update existing unit and compare fields
                return updateExistingUnit(existingUnit, sector, floorNumber, unitNumber, productType,
                    areaStr, floorAreaStr, doorDirection, view, contractPriceStr, userId, unitRepository, validationResult);
            } else {
                // Unit doesn't exist, create new one
                logger.info("Creating new unit: {} in project {}", trimmedUnitCode, projectId);

                return createNewUnit(projectId, trimmedUnitCode, sector, floorNumber, unitNumber, productType,
                    areaStr, floorAreaStr, doorDirection, view, contractPriceStr, userId, unitRepository);
            }

        } catch (Exception e) {
            logger.error("Error validating/processing unit '{}' in project {}: {}",
                trimmedUnitCode, projectId, e.getMessage(), e);
            throw new RuntimeException("Lỗi khi xử lý mã căn '" + trimmedUnitCode + "': " + e.getMessage(), e);
        }
    }

    /**
     * Create a new unit with all provided data
     */
    private static Units createNewUnit(Long projectId, String unitCode, String sector, String floorNumber,
                                      String unitNumber, String productType, String areaStr, String floorAreaStr,
                                      String doorDirection, String view, String contractPriceStr, Long userId,
                                      UnitRepository unitRepository) {
        Units newUnit = new Units();
        newUnit.setProjectId(projectId);
        newUnit.setCode(unitCode);
        newUnit.setSector(trimOrNull(sector));
        newUnit.setFloorNumber(trimOrNull(floorNumber));
        newUnit.setUnitNumber(trimOrNull(unitNumber));
        newUnit.setProductType(trimOrNull(productType));
        newUnit.setDoorDirection(trimOrNull(doorDirection));
        newUnit.setView(trimOrNull(view));

        // Parse numeric fields with error handling
        try {
            if (areaStr != null && !areaStr.trim().isEmpty()) {
                newUnit.setArea(new BigDecimal(areaStr.trim()));
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid area value '{}' for unit {}, setting to 0", areaStr, unitCode);
            newUnit.setArea(BigDecimal.ZERO);
        }

        try {
            if (floorAreaStr != null && !floorAreaStr.trim().isEmpty()) {
                newUnit.setFloorArea(new BigDecimal(floorAreaStr.trim()));
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid floor area value '{}' for unit {}, setting to null", floorAreaStr, unitCode);
            newUnit.setFloorArea(null);
        }

        try {
            if (contractPriceStr != null && !contractPriceStr.trim().isEmpty()) {
                newUnit.setContractPrice(new BigDecimal(contractPriceStr.trim()));
            } else {
                newUnit.setContractPrice(BigDecimal.ZERO);
            }
        } catch (NumberFormatException e) {
            logger.warn("Invalid contract price value '{}' for unit {}, setting to 0", contractPriceStr, unitCode);
            newUnit.setContractPrice(BigDecimal.ZERO);
        }

        newUnit.setCreatedBy(userId);
        newUnit.setCreatedAt(new java.util.Date());
        newUnit.setIsActive(true);

        Units savedUnit = unitRepository.save(newUnit);
        logger.info("Successfully created new unit: {} (ID: {})", savedUnit.getCode(), savedUnit.getId());

        return savedUnit;
    }

    /**
     * Update existing unit with field-by-field comparison and warning generation
     */
    private static Units updateExistingUnit(Units existingUnit, String sector, String floorNumber,
                                           String unitNumber, String productType, String areaStr, String floorAreaStr,
                                           String doorDirection, String view, String contractPriceStr, Long userId,
                                           UnitRepository unitRepository, ValidationResultDto validationResult) {
        boolean hasUpdates = false;
        String unitCode = existingUnit.getCode();

        // Compare and update sector (Phân khu)
        String newSector = trimOrNull(sector);
        if (shouldUpdateField(existingUnit.getSector(), newSector)) {
            if (existingUnit.getSector() != null && !existingUnit.getSector().equals(newSector)) {
                addFieldConflictWarning(validationResult, "Phân khu", unitCode, newSector, existingUnit.getSector());
            } else {
                existingUnit.setSector(newSector);
                hasUpdates = true;
            }
        }

        // Compare and update product type (Loại sản phẩm)
        String newProductType = trimOrNull(productType);
        if (shouldUpdateField(existingUnit.getProductType(), newProductType)) {
            if (existingUnit.getProductType() != null && !existingUnit.getProductType().equals(newProductType)) {
                addFieldConflictWarning(validationResult, "Loại sản phẩm", unitCode, newProductType, existingUnit.getProductType());
            } else {
                existingUnit.setProductType(newProductType);
                hasUpdates = true;
            }
        }

        // Compare and update floor number (Số tầng)
        String newFloorNumber = trimOrNull(floorNumber);
        if (shouldUpdateField(existingUnit.getFloorNumber(), newFloorNumber)) {
            if (existingUnit.getFloorNumber() != null && !existingUnit.getFloorNumber().equals(newFloorNumber)) {
                addFieldConflictWarning(validationResult, "Số tầng", unitCode, newFloorNumber, existingUnit.getFloorNumber());
            } else {
                existingUnit.setFloorNumber(newFloorNumber);
                hasUpdates = true;
            }
        }

        // Compare and update unit number (Số căn)
        String newUnitNumber = trimOrNull(unitNumber);
        if (shouldUpdateField(existingUnit.getUnitNumber(), newUnitNumber)) {
            if (existingUnit.getUnitNumber() != null && !existingUnit.getUnitNumber().equals(newUnitNumber)) {
                addFieldConflictWarning(validationResult, "Số căn", unitCode, newUnitNumber, existingUnit.getUnitNumber());
            } else {
                existingUnit.setUnitNumber(newUnitNumber);
                hasUpdates = true;
            }
        }

        // Compare and update door direction (Hướng cửa)
        String newDoorDirection = trimOrNull(doorDirection);
        if (shouldUpdateField(existingUnit.getDoorDirection(), newDoorDirection)) {
            if (existingUnit.getDoorDirection() != null && !existingUnit.getDoorDirection().equals(newDoorDirection)) {
                addFieldConflictWarning(validationResult, "Hướng cửa", unitCode, newDoorDirection, existingUnit.getDoorDirection());
            } else {
                existingUnit.setDoorDirection(newDoorDirection);
                hasUpdates = true;
            }
        }

        // Compare and update view (View)
        String newView = trimOrNull(view);
        if (shouldUpdateField(existingUnit.getView(), newView)) {
            if (existingUnit.getView() != null && !existingUnit.getView().equals(newView)) {
                addFieldConflictWarning(validationResult, "View", unitCode, newView, existingUnit.getView());
            } else {
                existingUnit.setView(newView);
                hasUpdates = true;
            }
        }

        // Compare and update area (Diện tích)
        if (areaStr != null && !areaStr.trim().isEmpty()) {
            try {
                BigDecimal newArea = new BigDecimal(areaStr.trim());
                if (existingUnit.getArea() == null || existingUnit.getArea().compareTo(BigDecimal.ZERO) == 0) {
                    existingUnit.setArea(newArea);
                    hasUpdates = true;
                } else if (existingUnit.getArea().compareTo(newArea) != 0) {
                    addFieldConflictWarning(validationResult, "Diện tích", unitCode,
                        newArea.toString(), existingUnit.getArea().toString());
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid area value '{}' for existing unit {}", areaStr, unitCode);
            }
        }

        // Compare and update floor area (Diện tích sàn)
        if (floorAreaStr != null && !floorAreaStr.trim().isEmpty()) {
            try {
                BigDecimal newFloorArea = new BigDecimal(floorAreaStr.trim());
                if (existingUnit.getFloorArea() == null || existingUnit.getFloorArea().compareTo(BigDecimal.ZERO) == 0) {
                    existingUnit.setFloorArea(newFloorArea);
                    hasUpdates = true;
                } else if (existingUnit.getFloorArea().compareTo(newFloorArea) != 0) {
                    addFieldConflictWarning(validationResult, "Diện tích sàn", unitCode,
                        newFloorArea.toString(), existingUnit.getFloorArea().toString());
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid floor area value '{}' for existing unit {}", floorAreaStr, unitCode);
            }
        }

        // Compare and update contract price (Giá hợp đồng)
        if (contractPriceStr != null && !contractPriceStr.trim().isEmpty()) {
            try {
                BigDecimal newContractPrice = new BigDecimal(contractPriceStr.trim());
                if (existingUnit.getContractPrice() == null || existingUnit.getContractPrice().compareTo(BigDecimal.ZERO) == 0) {
                    existingUnit.setContractPrice(newContractPrice);
                    hasUpdates = true;
                } else if (existingUnit.getContractPrice().compareTo(newContractPrice) != 0) {
                    addFieldConflictWarning(validationResult, "Giá hợp đồng", unitCode,
                        newContractPrice.toString(), existingUnit.getContractPrice().toString());
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid contract price value '{}' for existing unit {}", contractPriceStr, unitCode);
            }
        }

        // Save if there are updates
        if (hasUpdates) {
            existingUnit.setUpdatedBy(userId);
            existingUnit.setUpdatedAt(new java.util.Date());
            Units savedUnit = unitRepository.save(existingUnit);
            logger.info("Updated existing unit: {} (ID: {})", savedUnit.getCode(), savedUnit.getId());
            return savedUnit;
        } else {
            logger.debug("No updates needed for existing unit: {} (ID: {})", existingUnit.getCode(), existingUnit.getId());
            return existingUnit;
        }
    }

    /**
     * Utility method to trim string or return null if empty
     */
    private static String trimOrNull(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        return value.trim();
    }

    /**
     * Determine if a field should be updated based on existing and new values
     * Update if existing is null/empty and new has value
     */
    private static boolean shouldUpdateField(String existingValue, String newValue) {
        return (existingValue == null || existingValue.trim().isEmpty()) &&
               (newValue != null && !newValue.trim().isEmpty());
    }

    /**
     * Add a field conflict warning to the validation result
     */
    private static void addFieldConflictWarning(ValidationResultDto validationResult, String fieldName,
                                               String unitCode, String importValue, String systemValue) {
        String warningMessage = String.format(
            "Thông tin %s của mã căn %s không khớp với dữ liệu trên hệ thống\n" +
            "Trong file xls: %s\n" +
            "Trên hệ thống: %s",
            fieldName, unitCode,
            importValue != null ? importValue : "(trống)",
            systemValue != null ? systemValue : "(trống)"
        );

        ImportErrorDto warning = new ImportErrorDto(
            null, // jobId - will be set by caller if needed
            validationResult.getRowNumber(),
            "MÃ CĂN", // column name
            unitCode, // value
            ImportErrorType.DATA_INCONSISTENCY.getCode(), // error type
            warningMessage,
            ImportSeverity.WARNING.getCode()
        );

        validationResult.addWarning(warning);
        logger.warn("Field conflict for unit {}: {} - Import: '{}', System: '{}'",
            unitCode, fieldName, importValue, systemValue);
    }

    /**
     * Identify and process customer with comprehensive identification and update logic
     */
    private static Customers identifyAndProcessCustomer(String phone, String cccd, String email,
                                                       String fullName, Date birthDate, String avatarUrl,
                                                       String addressContact, String addressPermanent,
                                                       String nationality, String maritalStatus, String interests,
                                                       String totalAssetStr, String businessField, String zaloStatus,
                                                       String facebookLink, String sourceType, String sourceDetail,
                                                       Long userId, CustomerRepository customerRepository,
                                                       ValidationResultDto validationResult) {
        try {
            // Step 1: Customer identification using phone, CCCD, and email
            Customers existingCustomer = identifyExistingCustomer(phone, cccd, email, customerRepository);

            if (existingCustomer != null) {
                // Step 2: Update existing customer
                logger.info("Found existing customer ID {} for row {}", existingCustomer.getId(), validationResult.getRowNumber());
                return updateExistingCustomer(existingCustomer, phone, cccd, email, fullName, birthDate,
                    avatarUrl, addressContact, addressPermanent, nationality, maritalStatus, interests,
                    totalAssetStr, businessField, zaloStatus, facebookLink, sourceType, sourceDetail,
                    userId, customerRepository, validationResult);
            } else {
                // Step 3: Create new customer
                logger.info("Creating new customer for row {}", validationResult.getRowNumber());
                return createNewCustomer(phone, cccd, email, fullName, birthDate, avatarUrl,
                    addressContact, addressPermanent, nationality, maritalStatus, interests,
                    totalAssetStr, businessField, zaloStatus, facebookLink, sourceType, sourceDetail,
                    userId, customerRepository);
            }

        } catch (Exception e) {
            logger.error("Error processing customer for row {}: {}", validationResult.getRowNumber(), e.getMessage(), e);
            throw new RuntimeException("Lỗi xử lý thông tin khách hàng: " + e.getMessage(), e);
        }
    }

    /**
     * Identify existing customer using phone, CCCD, and email
     */
    private static Customers identifyExistingCustomer(String phone, String cccd, String email,
                                                     CustomerRepository customerRepository) {
        // Normalize inputs
        String normalizedPhone = normalizePhone(phone);
        String normalizedCccd = normalizeCccd(cccd);
        String normalizedEmail = normalizeEmail(email);

        // Search by phone (including additional phones)
        if (normalizedPhone != null && !normalizedPhone.trim().isEmpty()) {
            Customers customer = customerRepository.findFirstByPhoneIncludingAdditional(normalizedPhone);
            if (customer != null) {
                logger.debug("Customer found by phone: {}", normalizedPhone);
                return customer;
            }
        }

        // Search by CCCD (including additional CCCDs)
        if (normalizedCccd != null && !normalizedCccd.trim().isEmpty()) {
            Customers customer = customerRepository.findFirstByCccdIncludingAdditional(normalizedCccd);
            if (customer != null) {
                logger.debug("Customer found by CCCD: {}", normalizedCccd);
                return customer;
            }
        }

        // Search by email (including additional emails)
        if (normalizedEmail != null && !normalizedEmail.trim().isEmpty()) {
            Customers customer = customerRepository.findFirstByEmailIncludingAdditional(normalizedEmail);
            if (customer != null) {
                logger.debug("Customer found by email: {}", normalizedEmail);
                return customer;
            }
        }

        return null; // No existing customer found
    }

    /**
     * Update existing customer with import data
     */
    private static Customers updateExistingCustomer(Customers existingCustomer, String phone, String cccd, String email,
                                                   String fullName, Date birthDate, String avatarUrl,
                                                   String addressContact, String addressPermanent, String nationality,
                                                   String maritalStatus, String interests, String totalAssetStr,
                                                   String businessField, String zaloStatus, String facebookLink,
                                                   String sourceType, String sourceDetail, Long userId,
                                                   CustomerRepository customerRepository, ValidationResultDto validationResult) {
        boolean hasUpdates = false;

        // Handle identifying information (phone, CCCD, email) with special logic
        hasUpdates |= updateIdentifyingInformation(existingCustomer, phone, cccd, email, validationResult);

        // Handle other information fields
        hasUpdates |= updateOtherInformation(existingCustomer, fullName, birthDate, avatarUrl, addressContact,
            addressPermanent, nationality, maritalStatus, interests, totalAssetStr, businessField,
            zaloStatus, facebookLink, sourceType, sourceDetail, validationResult);

        // Save if there are updates
        if (hasUpdates) {
            existingCustomer.setUpdatedBy(userId);
            existingCustomer.setUpdatedAt(new Date());
            return customerRepository.save(existingCustomer);
        } else {
            return existingCustomer;
        }
    }

    /**
     * Update identifying information (phone, CCCD, email) with special logic
     */
    private static boolean updateIdentifyingInformation(Customers existingCustomer, String phone, String cccd,
                                                       String email, ValidationResultDto validationResult) {
        boolean hasUpdates = false;

        // Normalize inputs
        String normalizedPhone = normalizePhone(phone);
        String normalizedCccd = normalizeCccd(cccd);
        String normalizedEmail = normalizeEmail(email);

        // Handle phone
        if (normalizedPhone != null && !normalizedPhone.trim().isEmpty()) {
            if (isEmptyOrNull(existingCustomer.getPhone())) {
                // Primary phone is empty, update it
                existingCustomer.setPhone(normalizedPhone);
                hasUpdates = true;
            } else if (!existingCustomer.getPhone().equals(normalizedPhone)) {
                // Primary phone differs, add to additional phones if not already present
                addToAdditionalPhones(existingCustomer, normalizedPhone);
                hasUpdates = true;
            }
        }

        // Handle CCCD
        if (normalizedCccd != null && !normalizedCccd.trim().isEmpty()) {
            if (isEmptyOrNull(existingCustomer.getCccd())) {
                // Primary CCCD is empty, update it
                existingCustomer.setCccd(normalizedCccd);
                hasUpdates = true;
            } else if (!existingCustomer.getCccd().equals(normalizedCccd)) {
                // Primary CCCD differs, add to additional CCCDs if not already present
                addToAdditionalCccds(existingCustomer, normalizedCccd);
                hasUpdates = true;
            }
        }

        // Handle email
        if (normalizedEmail != null && !normalizedEmail.trim().isEmpty()) {
            if (isEmptyOrNull(existingCustomer.getEmail())) {
                // Primary email is empty, update it
                existingCustomer.setEmail(normalizedEmail);
                hasUpdates = true;
            } else if (!existingCustomer.getEmail().equalsIgnoreCase(normalizedEmail)) {
                // Primary email differs, add to additional emails if not already present
                addToAdditionalEmails(existingCustomer, normalizedEmail);
                hasUpdates = true;
            }
        }

        return hasUpdates;
    }

    /**
     * Update other information fields with conflict detection
     */
    private static boolean updateOtherInformation(Customers existingCustomer, String fullName, Date birthDate,
                                                 String avatarUrl, String addressContact, String addressPermanent,
                                                 String nationality, String maritalStatus, String interests,
                                                 String totalAssetStr, String businessField, String zaloStatus,
                                                 String facebookLink, String sourceType, String sourceDetail,
                                                 ValidationResultDto validationResult) {
        boolean hasUpdates = false;

        // Full Name
        if (shouldUpdateField(existingCustomer.getFullName(), fullName)) {
            if (existingCustomer.getFullName() != null && !existingCustomer.getFullName().equals(fullName)) {
                addFieldConflictWarning(validationResult, "Họ và tên", fullName, existingCustomer.getFullName());
            } else {
                existingCustomer.setFullName(fullName);
                hasUpdates = true;
            }
        }

        // Birth Date
        if (birthDate != null) {
            if (existingCustomer.getBirthDate() == null) {
                existingCustomer.setBirthDate(birthDate);
                hasUpdates = true;
            } else if (!existingCustomer.getBirthDate().equals(birthDate)) {
                addFieldConflictWarning(validationResult, "Ngày sinh",
                    sdf.format(birthDate), sdf.format(existingCustomer.getBirthDate()));
            }
        }

        // Avatar URL
        if (shouldUpdateField(existingCustomer.getAvatarUrl(), avatarUrl)) {
            if (existingCustomer.getAvatarUrl() != null && !existingCustomer.getAvatarUrl().equals(avatarUrl)) {
                addFieldConflictWarning(validationResult, "Avatar URL", avatarUrl, existingCustomer.getAvatarUrl());
            } else {
                existingCustomer.setAvatarUrl(avatarUrl);
                hasUpdates = true;
            }
        }

        // Address Contact
        if (shouldUpdateField(existingCustomer.getAddressContact(), addressContact)) {
            if (existingCustomer.getAddressContact() != null && !existingCustomer.getAddressContact().equals(addressContact)) {
                addFieldConflictWarning(validationResult, "Địa chỉ liên hệ", addressContact, existingCustomer.getAddressContact());
            } else {
                existingCustomer.setAddressContact(addressContact);
                hasUpdates = true;
            }
        }

        // Address Permanent
        if (shouldUpdateField(existingCustomer.getAddressPermanent(), addressPermanent)) {
            if (existingCustomer.getAddressPermanent() != null && !existingCustomer.getAddressPermanent().equals(addressPermanent)) {
                addFieldConflictWarning(validationResult, "Địa chỉ thường trú", addressPermanent, existingCustomer.getAddressPermanent());
            } else {
                existingCustomer.setAddressPermanent(addressPermanent);
                hasUpdates = true;
            }
        }

        // Continue with other fields...
        hasUpdates |= updateRemainingFields(existingCustomer, nationality, maritalStatus, interests, totalAssetStr,
            businessField, zaloStatus, facebookLink, sourceType, sourceDetail, validationResult);

        return hasUpdates;
    }

    /**
     * Update remaining customer fields
     */
    private static boolean updateRemainingFields(Customers existingCustomer, String nationality, String maritalStatus,
                                                String interests, String totalAssetStr, String businessField,
                                                String zaloStatus, String facebookLink, String sourceType,
                                                String sourceDetail, ValidationResultDto validationResult) {
        boolean hasUpdates = false;

        // Nationality
        if (shouldUpdateField(existingCustomer.getNationality(), nationality)) {
            if (existingCustomer.getNationality() != null && !existingCustomer.getNationality().equals(nationality)) {
                addFieldConflictWarning(validationResult, "Quốc tịch", nationality, existingCustomer.getNationality());
            } else {
                existingCustomer.setNationality(nationality);
                hasUpdates = true;
            }
        }

        // Marital Status
        if (shouldUpdateField(existingCustomer.getMaritalStatus(), maritalStatus)) {
            if (existingCustomer.getMaritalStatus() != null && !existingCustomer.getMaritalStatus().equals(maritalStatus)) {
                addFieldConflictWarning(validationResult, "Tình trạng hôn nhân", maritalStatus, existingCustomer.getMaritalStatus());
            } else {
                existingCustomer.setMaritalStatus(maritalStatus);
                hasUpdates = true;
            }
        }

        // Interests
        if (shouldUpdateField(existingCustomer.getInterests(), interests)) {
            try {
                String formattedInterests = mapper.writeValueAsString(Arrays.asList(interests.split(",")));
                if (existingCustomer.getInterests() != null && !existingCustomer.getInterests().equals(formattedInterests)) {
                    addFieldConflictWarning(validationResult, "Sở thích", interests, existingCustomer.getInterests());
                } else {
                    existingCustomer.setInterests(formattedInterests);
                    hasUpdates = true;
                }
            } catch (Exception e) {
                logger.warn("Error formatting interests for customer update: {}", e.getMessage());
            }
        }

        // Total Asset
        if (totalAssetStr != null && !totalAssetStr.trim().isEmpty()) {
            try {
                BigDecimal totalAsset = new BigDecimal(totalAssetStr.trim());
                if (existingCustomer.getTotalAsset() == null || existingCustomer.getTotalAsset().compareTo(BigDecimal.ZERO) == 0) {
                    existingCustomer.setTotalAsset(totalAsset);
                    hasUpdates = true;
                } else if (existingCustomer.getTotalAsset().compareTo(totalAsset) != 0) {
                    addFieldConflictWarning(validationResult, "Tổng tài sản", totalAssetStr, existingCustomer.getTotalAsset().toString());
                }
            } catch (NumberFormatException e) {
                logger.warn("Invalid total asset format: {}", totalAssetStr);
            }
        }

        // Business Field
        if (shouldUpdateField(existingCustomer.getBusinessField(), businessField)) {
            if (existingCustomer.getBusinessField() != null && !existingCustomer.getBusinessField().equals(businessField)) {
                addFieldConflictWarning(validationResult, "Lĩnh vực kinh doanh", businessField, existingCustomer.getBusinessField());
            } else {
                existingCustomer.setBusinessField(businessField);
                hasUpdates = true;
            }
        }

        // Zalo Status
        if (shouldUpdateField(existingCustomer.getZaloStatus(), zaloStatus)) {
            if (existingCustomer.getZaloStatus() != null && !existingCustomer.getZaloStatus().equals(zaloStatus)) {
                addFieldConflictWarning(validationResult, "Trạng thái Zalo", zaloStatus, existingCustomer.getZaloStatus());
            } else {
                existingCustomer.setZaloStatus(zaloStatus);
                hasUpdates = true;
            }
        }

        // Facebook Link
        if (shouldUpdateField(existingCustomer.getFacebookLink(), facebookLink)) {
            if (existingCustomer.getFacebookLink() != null && !existingCustomer.getFacebookLink().equals(facebookLink)) {
                addFieldConflictWarning(validationResult, "Link Facebook", facebookLink, existingCustomer.getFacebookLink());
            } else {
                existingCustomer.setFacebookLink(facebookLink);
                hasUpdates = true;
            }
        }

        // Source Type
        if (shouldUpdateField(existingCustomer.getSourceType(), sourceType)) {
            if (existingCustomer.getSourceType() != null && !existingCustomer.getSourceType().equals(sourceType)) {
                addFieldConflictWarning(validationResult, "Loại nguồn", sourceType, existingCustomer.getSourceType());
            } else {
                existingCustomer.setSourceType(sourceType);
                hasUpdates = true;
            }
        }

        // Source Detail
        if (shouldUpdateField(existingCustomer.getSourceDetail(), sourceDetail)) {
            if (existingCustomer.getSourceDetail() != null && !existingCustomer.getSourceDetail().equals(sourceDetail)) {
                addFieldConflictWarning(validationResult, "Chi tiết nguồn", sourceDetail, existingCustomer.getSourceDetail());
            } else {
                existingCustomer.setSourceDetail(sourceDetail);
                hasUpdates = true;
            }
        }

        return hasUpdates;
    }

    /**
     * Create new customer with import data
     */
    private static Customers createNewCustomer(String phone, String cccd, String email, String fullName,
                                              Date birthDate, String avatarUrl, String addressContact,
                                              String addressPermanent, String nationality, String maritalStatus,
                                              String interests, String totalAssetStr, String businessField,
                                              String zaloStatus, String facebookLink, String sourceType,
                                              String sourceDetail, Long userId, CustomerRepository customerRepository) {
        Customers newCustomer = new Customers();

        // Set identifying information
        newCustomer.setPhone(normalizePhone(phone));
        newCustomer.setCccd(normalizeCccd(cccd));
        newCustomer.setEmail(normalizeEmail(email));

        // Set personal information
        newCustomer.setFullName(fullName);
        newCustomer.setBirthDate(birthDate);
        newCustomer.setAvatarUrl(avatarUrl);
        newCustomer.setAddressContact(addressContact);
        newCustomer.setAddressPermanent(addressPermanent);
        newCustomer.setNationality(nationality);
        newCustomer.setMaritalStatus(maritalStatus);

        // Set interests
        if (interests != null && !interests.trim().isEmpty()) {
            try {
                newCustomer.setInterests(mapper.writeValueAsString(Arrays.asList(interests.split(","))));
            } catch (Exception e) {
                logger.warn("Error formatting interests for new customer: {}", e.getMessage());
                newCustomer.setInterests(interests);
            }
        }

        // Set total asset
        if (totalAssetStr != null && !totalAssetStr.trim().isEmpty()) {
            try {
                newCustomer.setTotalAsset(new BigDecimal(totalAssetStr.trim()));
            } catch (NumberFormatException e) {
                logger.warn("Invalid total asset format: {}", totalAssetStr);
                newCustomer.setTotalAsset(BigDecimal.ZERO);
            }
        } else {
            newCustomer.setTotalAsset(BigDecimal.ZERO);
        }

        // Set business information
        newCustomer.setBusinessField(businessField);
        newCustomer.setZaloStatus(zaloStatus);
        newCustomer.setFacebookLink(facebookLink);
        newCustomer.setSourceType(sourceType != null ? sourceType : "Data");
        newCustomer.setSourceDetail(sourceDetail);

        // Set creation metadata
        newCustomer.setCreatedBy(userId);
        newCustomer.setCreatedAt(new Date());

        return customerRepository.save(newCustomer);
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Remove Excel formatting artifacts from phone numbers
     */
    private static String removeExcelFormattingArtifacts(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }

        String cleaned = phone.trim();

        // Remove leading single quotes that Excel adds to force text interpretation
        if (cleaned.startsWith("'")) {
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    }

    /**
     * Normalize phone number
     */
    private static String normalizePhone(String phone) {
        if (phone == null) return null;

        // Remove Excel formatting artifacts first
        String normalized = removeExcelFormattingArtifacts(phone.trim());
        normalized = normalized.replaceAll("\\s+", "");
        if (normalized.isEmpty()) return null;

        // Convert +84 to 0
        if (normalized.startsWith("+84")) {
            normalized = "0" + normalized.substring(3);
        } else if (normalized.startsWith("84") && normalized.length() > 10) {
            normalized = "0" + normalized.substring(2);
        }

        return normalized;
    }

    /**
     * Normalize CCCD
     */
    private static String normalizeCccd(String cccd) {
        if (cccd == null) return null;
        String normalized = cccd.trim().replaceAll("\\s+", "");
        return normalized.isEmpty() ? null : normalized;
    }

    /**
     * Normalize email
     */
    private static String normalizeEmail(String email) {
        if (email == null) return null;
        String normalized = email.trim().toLowerCase();
        return normalized.isEmpty() ? null : normalized;
    }

    /**
     * Check if field is empty or null
     */
    private static boolean isEmptyOrNull(String value) {
        return value == null || value.trim().isEmpty();
    }

    /**
     * Add field conflict warning
     */
    private static void addFieldConflictWarning(ValidationResultDto validationResult, String fieldName,
                                               String excelValue, String systemValue) {
        String customerName = ""; // Could be extracted from validation result if available
        String warningMessage = String.format(
            "Thông tin %s của khách hàng %s không khớp với dữ liệu trên hệ thống\n" +
            "Trong file xls: %s\n" +
            "Trên hệ thống: %s",
            fieldName, customerName, excelValue, systemValue
        );

        ImportErrorDto warning = new ImportErrorDto(
            null, validationResult.getRowNumber(), fieldName, excelValue,
            "DATA_INCONSISTENCY", warningMessage, "WARNING"
        );
        validationResult.addWarning(warning);
    }

    /**
     * Add phone to additional phones if not already present
     */
    private static void addToAdditionalPhones(Customers customer, String phone) {
        if (customer.getAdditionalPhones() == null) {
            customer.setAdditionalPhones(new ArrayList<>());
        }

        // Check if phone already exists in additional phones
        boolean exists = customer.getAdditionalPhones().stream()
            .anyMatch(existingPhone -> existingPhone.equals(phone));

        if (!exists) {
            customer.getAdditionalPhones().add(phone);
        }
    }

    /**
     * Add CCCD to additional CCCDs if not already present
     */
    private static void addToAdditionalCccds(Customers customer, String cccd) {
        if (customer.getAdditionalCccds() == null) {
            customer.setAdditionalCccds(new ArrayList<>());
        }

        // Check if CCCD already exists in additional CCCDs
        boolean exists = customer.getAdditionalCccds().stream()
            .anyMatch(existingCccd -> existingCccd.equals(cccd));

        if (!exists) {
            customer.getAdditionalCccds().add(cccd);
        }
    }

    /**
     * Add email to additional emails if not already present
     */
    private static void addToAdditionalEmails(Customers customer, String email) {
        if (customer.getAdditionalEmails() == null) {
            customer.setAdditionalEmails(new ArrayList<>());
        }

        // Check if email already exists in additional emails (case-insensitive)
        boolean exists = customer.getAdditionalEmails().stream()
            .anyMatch(existingEmail -> existingEmail.equalsIgnoreCase(email));

        if (!exists) {
            customer.getAdditionalEmails().add(email);
        }
    }

    /**
     * Result of processing a batch
     */
    private static class BatchResult {
        int successfulRows = 0;
        int failedRows = 0;
        int customersCreated = 0;
        int customersUpdated = 0;
        int transactionsCommitted = 0;
        int transactionsRolledBack = 0;
        List<String> rollbackReasons;
    }
}
