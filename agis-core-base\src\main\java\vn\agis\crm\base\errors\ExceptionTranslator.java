package vn.agis.crm.base.errors;

import com.fasterxml.jackson.databind.exc.InvalidFormatException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MaxUploadSizeExceededException;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseStatusConst;
import vn.agis.crm.base.exception.ApiError;
import vn.agis.crm.base.exception.type.*;
import vn.agis.crm.base.utils.ResponseDataConfiguration;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * Controller advice to translate the server side exceptions to client-friendly json structures.
 * The error response follows RFC7807 - Problem Details for HTTP APIs (https://tools.ietf.org/html/rfc7807).
 */

@RestControllerAdvice
@Slf4j
public class ExceptionTranslator {

    public static final String EXCEPTION_MESS = "Exception: {}";

    @Autowired
    private MessageSource messageSource;

    /**
     * @param ex ResourceNotFoundException
     * @return HttpStatus - 404
     */
    @ExceptionHandler(value = ResourceNotFoundException.class)
    @ResponseStatus(code = HttpStatus.NOT_FOUND)
    public ResponseEntity<ApiError> handleResourceNotFoundException(ResourceNotFoundException ex,
                                                                    NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.NOT_FOUND);
    }

    /**
     * @param ex DuplicateException
     * @return HttpStatus - 409
     */
    @ExceptionHandler(value = DuplicateException.class)
    @ResponseStatus(code = HttpStatus.CONFLICT)
    public ResponseEntity<ApiError> handleDuplicateException(DuplicateException ex,
                                                             NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.CONFLICT);
    }

    /**
     * @param ex BadRequestException
     * @return HttpStatus - 400
     */
    @ExceptionHandler(value = BadRequestException.class)
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiError> handleBadRequestException(BadRequestException ex,
                                                              NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.BAD_REQUEST);
    }

    /**
     * Xu ly loi 500
     *
     * @param ex InternalServerException
     * @return HttpStatus - 500
     */
    @ExceptionHandler(value = InternalServerException.class)
    @ResponseStatus(code = HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ApiError> handleInternalServerException(InternalServerException ex,
                                                                  NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Xu ly loi validate du lieu
     *
     * @param ex MethodArgumentNotValidException
     * @return HttpStatus - 400
     */
    @ExceptionHandler(value = {
            ConstraintViolationException.class
    })
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiError> handleConstraintViolationException(ConstraintViolationException ex,
                                                                       NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        ApiError apiError = new ApiError();
        apiError.setMessage("Validation error");
        for (ConstraintViolation<?> objectError : ex.getConstraintViolations()) {
            HashMap<String, Object> map = new HashMap<>(objectError.getConstraintDescriptor().getAttributes());
            map.remove(null);
            map.remove("message");
            String message = messageSource.getMessage(Objects.requireNonNull(objectError.getMessageTemplate()),
                    map.values().toArray(), LocaleContextHolder.getLocale());
            apiError.addValidationError(objectError, message);
        }
        return ResponseDataConfiguration.error(ResponseStatusConst.ERROR, apiError, HttpStatus.BAD_REQUEST);
    }


    /**
     * Xu ly loi validate du lieu
     *
     * @param ex MethodArgumentNotValidException
     * @return HttpStatus - 400
     */
    @ExceptionHandler(value = {
            MethodArgumentNotValidException.class,
            org.hibernate.exception.ConstraintViolationException.class
    })
    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    public ResponseEntity<ApiError> handleMethodArgumentNotValid(MethodArgumentNotValidException ex,
                                                                 NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        List<ObjectError> errors = ex.getAllErrors();

        ApiError apiError = new ApiError();
        for (ObjectError objectError : errors) {
            FieldError field = (FieldError) objectError;
            String message = messageSource.getMessage(
                    Objects.requireNonNull(field.getDefaultMessage()),
                    field.getArguments(),
                    LocaleContextHolder.getLocale()
            );
            apiError.addSubError(
                    new ApiError.ApiValidationError(field.getObjectName(),
                            field.getField(),
                            field.getRejectedValue(),
                            message,
                            field.getDefaultMessage()));
        }
        return ResponseDataConfiguration.error(ResponseStatusConst.ERROR, apiError, HttpStatus.BAD_REQUEST);
    }

    public ApiError setException(BaseException ex) {
        ApiError apiError = new ApiError();
        apiError.setMessage(ex.getTitle());
        apiError.setField(ex.getField().toString());
        apiError.setObject(ex.getEntityName());
        apiError.setErrorCode(ex.getErrorCode());
        return apiError;
    }

    @ExceptionHandler(value = FileStorageException.class)
    @ResponseStatus(code = HttpStatus.CONFLICT)
    public ResponseEntity<ApiError> handleFileUploadException(FileStorageException ex,
                                                              NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = MaxUploadSizeExceededException.class)
    @ResponseStatus(code = HttpStatus.CONFLICT)
    public ResponseEntity<ApiError> handleFileUploadException(MaxUploadSizeExceededException ex,
                                                              NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, new ApiError(), HttpStatus.CONFLICT);
    }

    @ExceptionHandler(value = DataConstrainException.class)
    @ResponseStatus(code = HttpStatus.NOT_ACCEPTABLE)
    public ResponseEntity<ApiError> handleFileUploadException(DataConstrainException ex,
                                                              NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.NOT_ACCEPTABLE);
    }

    @ExceptionHandler(value = InvalidFormatException.class)
    public ResponseEntity<Object> handleFormat(InvalidFormatException ex, NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        ApiError apiError = new ApiError();
        String message = messageSource.getMessage(MessageKeyConstant.Validation.DATA_FORMAT, null, LocaleContextHolder.getLocale());
        apiError.setMessage(message);
        apiError.setField(ex.getPath().get(0).getFieldName());
        return ResponseDataConfiguration.error(null, apiError, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public ResponseEntity<Object> handleMethodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex, NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        ApiError apiError = new ApiError();
        String message = messageSource.getMessage(MessageKeyConstant.Validation.DATA_FORMAT, null, LocaleContextHolder.getLocale());
        apiError.setMessage(message);
        return ResponseDataConfiguration.error(null, apiError, HttpStatus.BAD_REQUEST);
    }


    /**
     * @param ex NotFoundException
     * @return HttpStatus - 404
     */
    @ExceptionHandler(value = NotFoundException.class)
    @ResponseStatus(code = HttpStatus.NOT_FOUND)
    public ResponseEntity<ApiError> handleNotFoundException(NotFoundException ex,
                                                            NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.NOT_FOUND);
    }

    @ResponseStatus(code = HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = DataInvalidException.class)
    public ResponseEntity<ApiError> handleDataInvalidException(DataInvalidException ex,
                                                               NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex.getMessage());
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.BAD_REQUEST);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value = ForbiddenException.class)
    public ResponseEntity<ApiError> forbiddenException(ForbiddenException ex,
                                                       NativeWebRequest request) {
        log.error(EXCEPTION_MESS, ex);
        return ResponseDataConfiguration.error(null, setException(ex), HttpStatus.FORBIDDEN);
    }

}
