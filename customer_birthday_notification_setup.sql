-- Customer Birthday Notification Scheduler Setup
-- This script sets up the configuration for automatic customer birthday notifications

-- =====================================================
-- CONFIGURATION SETUP
-- =====================================================

-- Enable 3-day advance birthday notifications (recommended default)
INSERT INTO configs (config_key, config_value, config_type, description, created_at, created_by) 
VALUES ('NOTIFICATION_BIRTHDAY_DAYS_BEFORE', '3', 1, 'Số ngày trước sinh nhật để gửi thông báo nhắc nhở', NOW(), 1)
ON DUPLICATE KEY UPDATE 
    config_value = '3',
    description = 'Số ngày trước sinh nhật để gửi thông báo nhắc nhở',
    updated_at = NOW(),
    updated_by = 1;

-- =====================================================
-- ALTERNATIVE CONFIGURATIONS
-- =====================================================

-- Same-day notifications only
-- UPDATE configs SET config_value = '0' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- 1-day advance notifications
-- UPDATE configs SET config_value = '1' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- 1-week advance notifications
-- UPDATE configs SET config_value = '7' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- 1-month advance notifications
-- UPDATE configs SET config_value = '30' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- Disable birthday notifications
-- DELETE FROM configs WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';
-- OR
-- UPDATE configs SET config_value = '' WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check current configuration
SELECT 
    config_key,
    config_value,
    config_type,
    description,
    created_at,
    updated_at
FROM configs 
WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE';

-- Check customers with birthdays today
SELECT 
    id,
    full_name,
    phone,
    birth_date,
    current_staff_id,
    current_manager_id,
    source_type
FROM customers 
WHERE DAY(birth_date) = DAY(CURDATE()) 
  AND MONTH(birth_date) = MONTH(CURDATE())
  AND deleted_at IS NULL
ORDER BY full_name;

-- Check customers with birthdays in 3 days (adjust based on your config)
SELECT 
    id,
    full_name,
    phone,
    birth_date,
    current_staff_id,
    current_manager_id,
    source_type
FROM customers 
WHERE DAY(birth_date) = DAY(DATE_ADD(CURDATE(), INTERVAL 3 DAY))
  AND MONTH(birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL 3 DAY))
  AND deleted_at IS NULL
ORDER BY full_name;

-- Check recent birthday notifications
SELECT 
    n.id,
    n.target_employee_id,
    n.target_customer_id,
    n.title,
    n.content,
    n.is_read,
    n.created_at,
    c.full_name as customer_name,
    e.full_name as employee_name
FROM notifications n
LEFT JOIN customers c ON n.target_customer_id = c.id
LEFT JOIN employees e ON n.target_employee_id = e.id
WHERE n.type = 2  -- CustomerBirthday type
  AND n.created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY n.created_at DESC;

-- Check notification statistics by date
SELECT 
    DATE(created_at) as notification_date,
    COUNT(*) as notification_count,
    COUNT(DISTINCT target_customer_id) as unique_customers,
    COUNT(DISTINCT target_employee_id) as unique_employees
FROM notifications 
WHERE type = 2  -- CustomerBirthday type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(created_at)
ORDER BY notification_date DESC;

-- =====================================================
-- EMPLOYEE ASSIGNMENT ANALYSIS
-- =====================================================

-- Check customers without employee assignments (will use admin fallback)
SELECT 
    id,
    full_name,
    phone,
    birth_date,
    current_staff_id,
    current_manager_id,
    source_type
FROM customers 
WHERE (current_staff_id IS NULL AND current_manager_id IS NULL)
  AND deleted_at IS NULL
  AND birth_date IS NOT NULL
ORDER BY full_name;

-- Check active employees by role (for admin fallback analysis)
SELECT 
    role_id,
    COUNT(*) as employee_count
FROM employees 
WHERE status = 'active' 
  AND deleted_at IS NULL
GROUP BY role_id
ORDER BY role_id;

-- Check employees with role_id = 1 (admin fallback candidates)
SELECT 
    id,
    employee_code,
    full_name,
    email,
    role_id,
    status
FROM employees 
WHERE role_id = 1 
  AND status = 'active' 
  AND deleted_at IS NULL
ORDER BY full_name;

-- =====================================================
-- BIRTHDAY DISTRIBUTION ANALYSIS
-- =====================================================

-- Birthday distribution by month
SELECT 
    MONTH(birth_date) as birth_month,
    MONTHNAME(birth_date) as month_name,
    COUNT(*) as customer_count
FROM customers 
WHERE birth_date IS NOT NULL 
  AND deleted_at IS NULL
GROUP BY MONTH(birth_date), MONTHNAME(birth_date)
ORDER BY birth_month;

-- Birthday distribution by day of month
SELECT 
    DAY(birth_date) as birth_day,
    COUNT(*) as customer_count
FROM customers 
WHERE birth_date IS NOT NULL 
  AND deleted_at IS NULL
GROUP BY DAY(birth_date)
ORDER BY birth_day;

-- Customers with birthdays in the next 30 days
SELECT 
    id,
    full_name,
    phone,
    birth_date,
    CONCAT(DAY(birth_date), '/', MONTH(birth_date)) as birthday,
    current_staff_id,
    current_manager_id,
    DATEDIFF(
        DATE(CONCAT(YEAR(CURDATE()), '-', MONTH(birth_date), '-', DAY(birth_date))),
        CURDATE()
    ) as days_until_birthday
FROM customers 
WHERE birth_date IS NOT NULL 
  AND deleted_at IS NULL
  AND (
    (MONTH(birth_date) = MONTH(CURDATE()) AND DAY(birth_date) >= DAY(CURDATE())) OR
    (MONTH(birth_date) = MONTH(DATE_ADD(CURDATE(), INTERVAL 1 MONTH)) AND DAY(birth_date) <= DAY(LAST_DAY(CURDATE())))
  )
ORDER BY MONTH(birth_date), DAY(birth_date);

-- =====================================================
-- PERFORMANCE MONITORING
-- =====================================================

-- Check notification creation performance (recent notifications)
SELECT 
    DATE(created_at) as date,
    HOUR(created_at) as hour,
    COUNT(*) as notifications_created,
    MIN(created_at) as first_notification,
    MAX(created_at) as last_notification,
    TIMESTAMPDIFF(SECOND, MIN(created_at), MAX(created_at)) as duration_seconds
FROM notifications 
WHERE type = 2  -- CustomerBirthday type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
GROUP BY DATE(created_at), HOUR(created_at)
ORDER BY date DESC, hour DESC;

-- Check for duplicate notifications (should be minimal)
SELECT 
    target_employee_id,
    target_customer_id,
    DATE(created_at) as notification_date,
    COUNT(*) as duplicate_count
FROM notifications 
WHERE type = 2  -- CustomerBirthday type
  AND created_at >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY target_employee_id, target_customer_id, DATE(created_at)
HAVING COUNT(*) > 1
ORDER BY duplicate_count DESC, notification_date DESC;

-- =====================================================
-- TROUBLESHOOTING QUERIES
-- =====================================================

-- Find customers with birthdays but no notifications (potential issues)
SELECT 
    c.id,
    c.full_name,
    c.phone,
    c.birth_date,
    c.current_staff_id,
    c.current_manager_id,
    CONCAT(DAY(c.birth_date), '/', MONTH(c.birth_date)) as birthday
FROM customers c
LEFT JOIN notifications n ON (
    n.target_customer_id = c.id 
    AND n.type = 2 
    AND DATE(n.created_at) = CURDATE()
)
WHERE c.birth_date IS NOT NULL 
  AND c.deleted_at IS NULL
  AND DAY(c.birth_date) = DAY(CURDATE())
  AND MONTH(c.birth_date) = MONTH(CURDATE())
  AND n.id IS NULL;

-- Check for inactive employees assigned to customers
SELECT 
    c.id as customer_id,
    c.full_name as customer_name,
    c.current_staff_id,
    c.current_manager_id,
    es.status as staff_status,
    es.deleted_at as staff_deleted,
    em.status as manager_status,
    em.deleted_at as manager_deleted
FROM customers c
LEFT JOIN employees es ON c.current_staff_id = es.id
LEFT JOIN employees em ON c.current_manager_id = em.id
WHERE c.deleted_at IS NULL
  AND (
    (c.current_staff_id IS NOT NULL AND (es.status != 'active' OR es.deleted_at IS NOT NULL)) OR
    (c.current_manager_id IS NOT NULL AND (em.status != 'active' OR em.deleted_at IS NOT NULL))
  );

-- =====================================================
-- MAINTENANCE QUERIES
-- =====================================================

-- Clean up old birthday notifications (older than 90 days)
-- WARNING: This will permanently delete old notifications
-- DELETE FROM notifications 
-- WHERE type = 2 
--   AND created_at < DATE_SUB(CURDATE(), INTERVAL 90 DAY);

-- Mark old birthday notifications as read (alternative to deletion)
-- UPDATE notifications 
-- SET is_read = true, read_at = NOW()
-- WHERE type = 2 
--   AND is_read = false
--   AND created_at < DATE_SUB(CURDATE(), INTERVAL 30 DAY);

-- =====================================================
-- SCHEDULER STATUS CHECK
-- =====================================================

-- Check if scheduler is running (look for recent birthday notifications)
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN 'RUNNING - Recent birthday notifications found'
        ELSE 'UNKNOWN - No recent birthday notifications (may be normal if no birthdays)'
    END as scheduler_status,
    COUNT(*) as recent_notifications,
    MAX(created_at) as last_notification
FROM notifications 
WHERE type = 2 
  AND created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR);

-- Check configuration status
SELECT 
    CASE 
        WHEN config_value IS NULL THEN 'DISABLED - Configuration not found'
        WHEN config_value = '' THEN 'DISABLED - Empty configuration value'
        WHEN config_value REGEXP '^[0-9]+$' AND CAST(config_value AS UNSIGNED) BETWEEN 0 AND 365 THEN 
            CONCAT('ENABLED - ', config_value, ' days advance notice')
        ELSE 'INVALID - Invalid configuration value'
    END as config_status,
    config_value,
    updated_at as last_updated
FROM configs 
WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE'
UNION ALL
SELECT 
    'DISABLED - Configuration not found' as config_status,
    NULL as config_value,
    NULL as last_updated
WHERE NOT EXISTS (
    SELECT 1 FROM configs WHERE config_key = 'NOTIFICATION_BIRTHDAY_DAYS_BEFORE'
);
