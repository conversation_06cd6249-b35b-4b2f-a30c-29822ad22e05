package vn.agis.crm.base.exception.type;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;
import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

@ResponseStatus(HttpStatus.FORBIDDEN)
public class ForbiddenException extends BaseException {

    public ForbiddenException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, Collections.singletonList(field), errorCode);
    }

    public ForbiddenException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }
}
