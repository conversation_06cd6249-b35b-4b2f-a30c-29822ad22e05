package vn.agis.crm.base.jpa.dto;

import lombok.Data;

@Data
public class InteractionPrimaryDto {
    private Long id;
    private String result;
    // Accepted as dd/MM/yyyy in request; response may render as ISO by serializer
    private String happenedAt;
    private String notes;
    private Boolean deleted;

    // NEW: Audit fields for tracking who created the interaction
    private Long createdBy;
    private String createdName;
}

