package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.Column;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import lombok.Data;

import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@Table(name = "DASHBOARD_CONFIG")
@Entity
@Data
public class DashboardConfig extends AbstractEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "CHART_ID")
    private Long chartId;

    @Column(name = "USER_ID")
    private Long userId;

    @Column(name = "THRESHOLD")
    private String threshold;

    @Column(name = "STATUS")
    private Integer status;

    @Column(name = "POSITION_CONFIG")
    private String positionConfig;

    @Column(name = "LAST_FILTER")
    private String lastFilter;
}
