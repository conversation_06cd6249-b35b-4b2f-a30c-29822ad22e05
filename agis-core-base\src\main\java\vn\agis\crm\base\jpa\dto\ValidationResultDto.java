package vn.agis.crm.base.jpa.dto;

import lombok.Data;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Data
public class ValidationResultDto {
    private boolean isValid;
    private List<ImportErrorDto> errors;
    private List<ImportErrorDto> warnings;
    private Map<String, Object> validatedData;
    private String customerPhone; // For duplicate checking
    private String customerName;
    
    // Validation context
    private Integer rowNumber;
    private LinkedHashMap<String, String> originalRowData;
    
    public ValidationResultDto() {
        this.isValid = true;
    }
    
    public ValidationResultDto(Integer rowNumber) {
        this.rowNumber = rowNumber;
        this.isValid = true;
    }
    
    public void addError(ImportErrorDto error) {
        if (errors == null) {
            errors = new java.util.ArrayList<>();
        }
        errors.add(error);
        this.isValid = false;
    }
    
    public void addWarning(ImportErrorDto warning) {
        if (warnings == null) {
            warnings = new java.util.ArrayList<>();
        }
        warnings.add(warning);
    }
    
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
}
