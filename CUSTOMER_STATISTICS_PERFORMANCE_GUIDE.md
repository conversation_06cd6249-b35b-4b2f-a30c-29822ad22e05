# Customer Statistics Performance Optimization Guide

## Overview

This document outlines the performance optimizations implemented for the enhanced `getPageProject` API that includes customer statistics (`totalCustomersPurchased` and `totalCustomersWithOffers`).

## Performance Considerations

### 1. Query Optimization

#### Individual Queries (Small Datasets)
For small result sets (≤5 projects), individual queries are used:
```sql
-- For each project
SELECT COUNT(DISTINCT cp.customer_id) FROM customer_properties cp WHERE cp.project_id = ?
SELECT COUNT(DISTINCT co.customer_id) FROM customer_offers co WHERE co.project_id = ? AND co.status != 'CANCELLED'
```

#### Batch Queries (Large Datasets)
For larger result sets (>5 projects), batch queries are used:
```sql
-- Single query for all projects
SELECT cp.project_id, COUNT(DISTINCT cp.customer_id) 
FROM customer_properties cp 
WHERE cp.project_id IN (?, ?, ?, ...) 
GROUP BY cp.project_id

SELECT co.project_id, COUNT(DISTINCT co.customer_id) 
FROM customer_offers co 
WHERE co.project_id IN (?, ?, ?, ...) AND co.status != 'CANCELLED' 
GROUP BY co.project_id
```

### 2. Database Indexes

Ensure the following indexes exist for optimal performance:

```sql
-- Customer Properties
CREATE INDEX idx_customer_properties_project_id ON customer_properties(project_id);
CREATE INDEX idx_customer_properties_customer_project ON customer_properties(customer_id, project_id);

-- Customer Offers
CREATE INDEX idx_customer_offers_project_id ON customer_offers(project_id);
CREATE INDEX idx_customer_offers_status_project ON customer_offers(status, project_id);
CREATE INDEX idx_customer_offers_customer_project ON customer_offers(customer_id, project_id);
```

### 3. Caching Strategy

For frequently accessed project statistics, consider implementing caching:

#### Application-Level Caching
```java
@Cacheable(value = "projectStats", key = "#projectId")
public ProjectWithStatsDto getProjectWithStats(Long projectId) {
    // Implementation
}
```

#### Database-Level Caching
- Use Redis for caching frequently accessed statistics
- Implement cache invalidation when customer data changes

### 4. Performance Metrics

#### Expected Performance
- **Small datasets (1-5 projects)**: ~50-100ms
- **Medium datasets (6-20 projects)**: ~100-200ms  
- **Large datasets (21-100 projects)**: ~200-500ms

#### Monitoring
Monitor the following metrics:
- Query execution time
- Memory usage during batch processing
- API response time
- Database connection pool usage

## Implementation Details

### 1. Batch Processing Logic

```java
private List<ProjectWithStatsDto> batchEnhanceProjectsWithStats(List<Projects> projects) {
    if (projects.size() > 5) {
        // Use batch queries for better performance
        return processBatchQueries(projects);
    } else {
        // Use individual queries for small datasets
        return processIndividualQueries(projects);
    }
}
```

### 2. Repository Methods

#### Individual Query Methods
```java
@Query("SELECT COUNT(DISTINCT cp.customerId) FROM CustomerProperties cp WHERE cp.projectId = :projectId")
long countUniqueCustomersByProjectId(@Param("projectId") Long projectId);
```

#### Batch Query Methods
```java
@Query("SELECT cp.projectId, COUNT(DISTINCT cp.customerId) FROM CustomerProperties cp WHERE cp.projectId IN :projectIds GROUP BY cp.projectId")
List<Object[]> countUniqueCustomersByProjectIds(@Param("projectIds") List<Long> projectIds);
```

### 3. Memory Management

- Use streaming for very large datasets
- Implement pagination at the database level
- Clear temporary collections after processing

## Testing and Benchmarking

### 1. Performance Tests

Create performance tests for different dataset sizes:

```java
@Test
void testPerformanceWithLargeDataset() {
    // Test with 100+ projects
    List<Projects> largeDataset = createTestProjects(100);
    
    long startTime = System.currentTimeMillis();
    List<ProjectWithStatsDto> result = projectService.batchEnhanceProjectsWithStats(largeDataset);
    long endTime = System.currentTimeMillis();
    
    assertTrue(endTime - startTime < 1000); // Should complete within 1 second
    assertEquals(100, result.size());
}
```

### 2. Load Testing

Use tools like JMeter or Gatling to test API performance:
- Concurrent users: 10-50
- Request rate: 10-100 requests/second
- Dataset sizes: 1, 10, 50, 100 projects

### 3. Database Performance Testing

Monitor database performance:
```sql
-- Check query execution plans
EXPLAIN SELECT cp.project_id, COUNT(DISTINCT cp.customer_id) 
FROM customer_properties cp 
WHERE cp.project_id IN (1,2,3,4,5) 
GROUP BY cp.project_id;

-- Monitor slow queries
SHOW PROCESSLIST;
```

## Optimization Recommendations

### 1. Short-term Optimizations
- Implement the batch query approach
- Add necessary database indexes
- Monitor query performance

### 2. Medium-term Optimizations
- Implement application-level caching
- Add database connection pooling optimization
- Consider read replicas for statistics queries

### 3. Long-term Optimizations
- Implement materialized views for frequently accessed statistics
- Consider denormalization for critical statistics
- Implement asynchronous statistics calculation

## Configuration

### Application Properties
```properties
# Database connection pool
spring.datasource.hikari.maximum-pool-size=20
spring.datasource.hikari.minimum-idle=5

# JPA/Hibernate optimization
spring.jpa.properties.hibernate.jdbc.batch_size=25
spring.jpa.properties.hibernate.order_inserts=true
spring.jpa.properties.hibernate.order_updates=true

# Query timeout
spring.jpa.properties.hibernate.query.timeout=30000
```

### JVM Tuning
```bash
# Memory settings
-Xms512m -Xmx2g

# Garbage collection
-XX:+UseG1GC -XX:MaxGCPauseMillis=200
```

## Monitoring and Alerting

### 1. Application Metrics
- API response time percentiles (P50, P95, P99)
- Database query execution time
- Memory usage during batch processing
- Error rates

### 2. Database Metrics
- Connection pool utilization
- Query execution time
- Index usage statistics
- Lock wait time

### 3. Alerts
Set up alerts for:
- API response time > 1 second
- Database query time > 500ms
- Memory usage > 80%
- Error rate > 1%

## Troubleshooting

### Common Issues

1. **Slow Query Performance**
   - Check if indexes are being used
   - Analyze query execution plan
   - Consider query optimization

2. **Memory Issues**
   - Monitor heap usage during batch processing
   - Implement streaming for very large datasets
   - Adjust JVM memory settings

3. **Database Connection Issues**
   - Monitor connection pool usage
   - Adjust pool size settings
   - Check for connection leaks

### Debug Queries

```sql
-- Check index usage
SHOW INDEX FROM customer_properties;
SHOW INDEX FROM customer_offers;

-- Analyze query performance
EXPLAIN ANALYZE SELECT cp.project_id, COUNT(DISTINCT cp.customer_id) 
FROM customer_properties cp 
WHERE cp.project_id IN (1,2,3,4,5) 
GROUP BY cp.project_id;
```

## Conclusion

The implemented optimizations provide significant performance improvements for the customer statistics feature:

- **Batch processing** reduces database round trips
- **Intelligent query selection** optimizes for different dataset sizes
- **Proper indexing** ensures fast query execution
- **Monitoring and alerting** help maintain performance over time

Regular performance testing and monitoring are essential to maintain optimal performance as the dataset grows.
