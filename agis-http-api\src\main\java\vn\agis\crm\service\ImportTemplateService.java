package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.Constants.Category;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.dto.ImportTemplateDto;
import vn.agis.crm.util.RequestUtils;

@Service
public class ImportTemplateService {

    private static final Logger logger = LoggerFactory.getLogger(ImportTemplateService.class);
    private final String routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
    private final String category = Category.IMPORT_TEMPLATE;

    /**
     * Get the import template file
     */
    public Resource getTemplate() {
        try {
            Event event = RequestUtils.amqp(Constants.Method.GET_IMPORT_TEMPLATE, category, null, routingKey);

            if (event.respStatusCode == ResponseCode.OK) {
                ImportTemplateDto response = (ImportTemplateDto) event.payload;
                return response.getResource();
            } else if (event.respStatusCode == 404) {
                throw new ResourceNotFoundException("Template file not found", category, "template", "Template file not found");
            } else {
                throw new RuntimeException("Error retrieving template: " + event.respErrorDesc);
            }
        } catch (Exception e) {
            logger.error("Error in getTemplate: {}", e.getMessage(), e);
            throw new RuntimeException("Error retrieving template", e);
        }
    }

    /**
     * Get template filename for download
     */
    public String getTemplateFilename() {
        return "DATA-FORMAT-V2.csv";
    }

    /**
     * Get template content type
     */
    public String getTemplateContentType() {
        return "text/csv";
    }
}
