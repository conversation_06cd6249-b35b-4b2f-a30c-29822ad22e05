package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.io.Serializable;

@Entity
@Table(name = "role_permissions")
@Data
@IdClass(RolePermissions.RolePermissionId.class)
public class RolePermissions {

    @Id
    @Column(name = "role_id")
    private Long roleId;

    @Id
    @Column(name = "permission_id")
    private Long permissionId;

    @Data
    public static class RolePermissionId implements Serializable {
        private Long roleId;
        private Long permissionId;
    }
}

