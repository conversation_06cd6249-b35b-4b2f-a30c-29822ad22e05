package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;

/**
 * 404
 * Whren throw new this class, the error returned will contain an error code is 404
 */

public class ResourceNotFoundException extends BaseException {
    /**
     *
     */
    private static final long serialVersionUID = -5803702402883889153L;

    public ResourceNotFoundException(String title, String entityName, List<String> field, String errorCode) {
        super(title, entityName, field, errorCode);
    }

    public ResourceNotFoundException(String title, String entityName, String field, String errorCode) {
        super(title, entityName, Collections.singletonList(field), errorCode);
    }
}
