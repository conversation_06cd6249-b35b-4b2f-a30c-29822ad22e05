package vn.agis.crm.service;

import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.jpa.entity.Permission;
import vn.agis.crm.util.RequestUtils;

@Service
public class PermissionService {

    private Logger logger = LoggerFactory.getLogger(PermissionService.class);
    private String routingKey;
    private String category;

    private static final String objectKey = "Permission";

    public PermissionService() {
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.PERMISSION;
    }

    public List<Permission> getAllPermission() {
        List<Permission> response = new ArrayList<>();
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.GET_PERMISSION_BY_USER, category, null, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return response = (List<Permission>) event.payload;
            } else {
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, timeResponse,
//                null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }
}
