package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.DuplicateException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.ResourceNotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.ConfigSearchDto;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.CreateConfigReq;
import vn.agis.crm.base.jpa.dto.req.UpdateConfigReq;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.util.RequestUtils;

import java.util.List;

@Service
public class ConfigApiService extends CrudService<Config, Long> {

    private static final Logger logger = LoggerFactory.getLogger(ConfigApiService.class);

    public ConfigApiService() {
        super(Config.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.CONFIG;
    }

    public Page<Config> search(ConfigSearchDto searchDTO, Pageable pageable) {
        List<Config> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, category, searchDTO, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), Config.class);
                return new PageImpl<>(response, pageable, pageInfo.getTotalCount());
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            } else {
                throw new ForbiddenException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
        } catch (Exception e) {
            logger.error("Error in search configs: {}", e.getMessage(), e);
            return Page.empty(pageable);
        } finally {
            writeLog(timeRequest, timeResponse, searchDTO.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Config createConfig(CreateConfigReq req) {
        Config response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.CREATE, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == 201) {
                response = (Config) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } catch (Exception ex) {
            logger.error("Error in createConfig: {}", ex.getMessage(), ex);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Config updateConfig(UpdateConfigReq req) {
        Config response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.UPDATE, category, req, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Config) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.CONFLICT || event.respStatusCode == 409) {
                throw new DuplicateException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.Validation.DUPLICATE_NAME);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(MessageKeyConstant.FORBIDDEN, category, (List<String>) event.payload, MessageKeyConstant.FORBIDDEN);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, (List<String>) event.payload, MessageKeyConstant.NOT_FOUND);
            }
            return null;
        } catch (Exception ex) {
            logger.error("Error in updateConfig: {}", ex.getMessage(), ex);
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, req.toString(), response != null ? response.toString() : null, event);
        }
    }

    public Config getByKey(String configKey) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(JpaConstants.Method.GET_BY_KEY, category, configKey, routingKey);
            timeResponse = System.currentTimeMillis();
            if (event.respStatusCode == ResponseCode.OK) {
                return (Config) event.payload;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new ResourceNotFoundException(event.respErrorDesc, category, configKey, MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, configKey, MessageKeyConstant.FORBIDDEN);
            }
            return null;
        } finally {
            writeLog(timeRequest, timeResponse, configKey, event != null && event.payload != null ? event.payload.toString() : null, event);
        }
    }
}


