package vn.agis.crm.base.jpa.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@MappedSuperclass
public class AbstractEntity implements Serializable {

    @Column(name = "created_at")
    protected Date createdAt;

    @Column(name = "created_by")
    protected Long createdBy;
}
