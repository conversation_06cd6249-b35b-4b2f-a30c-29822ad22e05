// Unit Validation Verification Script
// Quick verification script to test the enhanced unit validation, creation, and update logic

package vn.agis.crm.verification;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.jpa.entity.Units;
import vn.agis.crm.base.jpa.entity.Projects;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;
import vn.agis.crm.repository.UnitRepository;
import vn.agis.crm.repository.ProjectRepository;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * Verification script to test the enhanced unit validation, creation, and update logic
 * Run this to verify that the implementation works correctly
 */
@SpringBootApplication
public class UnitValidationVerificationScript implements CommandLineRunner {

    @Autowired
    private UnitRepository unitRepository;
    
    @Autowired
    private ProjectRepository projectRepository;

    public static void main(String[] args) {
        SpringApplication.run(UnitValidationVerificationScript.class, args);
    }

    @Override
    @Transactional
    public void run(String... args) throws Exception {
        System.out.println("🏗️ Unit Validation Verification Script");
        System.out.println("======================================");
        System.out.println();

        // Setup test project
        Projects testProject = setupTestProject();
        
        // Test data - various unit scenarios
        System.out.println("📋 Testing Unit Validation Logic");
        System.out.println("=================================");

        // Test 1: New unit creation
        testUnitCreation(testProject.getId(), "A1.01", "A", "Căn hộ", "85.5", "75.0");
        
        // Test 2: Case insensitive matching
        testUnitCaseInsensitive(testProject.getId(), "a1.01", "A", "Căn hộ", "85.5", "75.0");
        
        // Test 3: Different unit in same project
        testUnitCreation(testProject.getId(), "B2.05", "B", "Penthouse", "120.0", "100.0");
        
        // Test 4: Same unit code in different project
        Projects anotherProject = setupAnotherTestProject();
        testUnitCreation(anotherProject.getId(), "A1.01", "A", "Studio", "45.0", "40.0");
        
        // Test 5: Unit update with empty fields
        testUnitUpdateEmptyFields(testProject.getId(), "C3.10");
        
        // Test 6: Unit update with conflicting data
        testUnitUpdateConflictingData(testProject.getId(), "A1.01");

        System.out.println();
        System.out.println("📊 Final Database State");
        System.out.println("=======================");
        
        List<Units> allUnits = unitRepository.findAll();
        System.out.println("Total units in database: " + allUnits.size());
        
        for (Units unit : allUnits) {
            System.out.println(String.format("Project: %d | Code: '%s' | Type: %s | Area: %s | Active: %s", 
                unit.getProjectId(), 
                unit.getCode(), 
                unit.getProductType(),
                unit.getArea(),
                unit.getActive()));
        }

        System.out.println();
        System.out.println("✅ Verification Complete!");
        System.out.println("Expected Results:");
        System.out.println("- No duplicate units for same code with different cases within same project");
        System.out.println("- Same unit code can exist in different projects");
        System.out.println("- Empty fields updated, conflicting fields generate warnings");
        System.out.println("- Unit codes properly trimmed and normalized");
    }

    private Projects setupTestProject() {
        Projects project = new Projects();
        project.setName("Test Project Vinhomes");
        project.setCreatedBy(1L);
        project.setCreatedAt(new java.util.Date());
        project.setActive(true);
        return projectRepository.save(project);
    }
    
    private Projects setupAnotherTestProject() {
        Projects project = new Projects();
        project.setName("Test Project Masteri");
        project.setCreatedBy(1L);
        project.setCreatedAt(new java.util.Date());
        project.setActive(true);
        return projectRepository.save(project);
    }

    private void testUnitCreation(Long projectId, String unitCode, String sector, String productType, 
                                 String areaStr, String floorAreaStr) {
        System.out.println(String.format("\n🔍 Testing Unit Creation: Project %d, Code '%s'", projectId, unitCode));
        
        try {
            ValidationResultDto validationResult = new ValidationResultDto(1);
            Units result = simulateValidateAndCreateOrUpdateUnit(
                projectId, unitCode, sector, "1", "01", productType, 
                areaStr, floorAreaStr, "Đông", "Hồ bơi", "2500000000", 1L, validationResult);
            
            System.out.println(String.format("✅ Result: ID=%d, Code='%s', Type='%s', Area=%s", 
                result.getId(), result.getCode(), result.getProductType(), result.getArea()));
                
            if (validationResult.hasWarnings()) {
                System.out.println("⚠️ Warnings generated:");
                validationResult.getWarnings().forEach(w -> 
                    System.out.println("   - " + w.getErrorDescription()));
            }
                
        } catch (Exception e) {
            System.out.println(String.format("❌ Error: %s", e.getMessage()));
        }
    }
    
    private void testUnitCaseInsensitive(Long projectId, String unitCode, String sector, String productType, 
                                        String areaStr, String floorAreaStr) {
        System.out.println(String.format("\n🔍 Testing Case Insensitive: Project %d, Code '%s'", projectId, unitCode));
        
        try {
            ValidationResultDto validationResult = new ValidationResultDto(1);
            Units result = simulateValidateAndCreateOrUpdateUnit(
                projectId, unitCode, sector, "1", "01", productType, 
                areaStr, floorAreaStr, "Đông", "Hồ bơi", "2500000000", 1L, validationResult);
            
            System.out.println(String.format("✅ Found existing: ID=%d, Code='%s' (original case preserved)", 
                result.getId(), result.getCode()));
                
        } catch (Exception e) {
            System.out.println(String.format("❌ Error: %s", e.getMessage()));
        }
    }
    
    private void testUnitUpdateEmptyFields(Long projectId, String unitCode) {
        System.out.println(String.format("\n🔍 Testing Empty Field Update: Project %d, Code '%s'", projectId, unitCode));
        
        // First create a unit with some empty fields
        Units unit = new Units();
        unit.setProjectId(projectId);
        unit.setCode(unitCode);
        unit.setSector("C");
        unit.setProductType(null); // Empty
        unit.setArea(new BigDecimal("95.0"));
        unit.setFloorArea(null); // Empty
        unit.setDoorDirection(""); // Empty
        unit.setView(null); // Empty
        unit.setContractPrice(BigDecimal.ZERO); // Empty
        unit.setCreatedBy(1L);
        unit.setCreatedAt(new java.util.Date());
        unit.setActive(true);
        Units savedUnit = unitRepository.save(unit);
        
        System.out.println(String.format("   Created unit with empty fields: ID=%d", savedUnit.getId()));
        
        // Now test update with import data
        try {
            ValidationResultDto validationResult = new ValidationResultDto(1);
            Units result = simulateValidateAndCreateOrUpdateUnit(
                projectId, unitCode, "C", "3", "10", "Penthouse", 
                "95.0", "85.0", "Nam", "Thành phố", "3500000000", 1L, validationResult);
            
            System.out.println(String.format("✅ Updated: Type='%s', FloorArea=%s, Direction='%s', View='%s', Price=%s", 
                result.getProductType(), result.getFloorArea(), result.getDoorDirection(), 
                result.getView(), result.getContractPrice()));
                
            if (validationResult.hasWarnings()) {
                System.out.println("⚠️ Unexpected warnings for empty field updates:");
                validationResult.getWarnings().forEach(w -> 
                    System.out.println("   - " + w.getErrorDescription()));
            } else {
                System.out.println("✅ No warnings for empty field updates (as expected)");
            }
                
        } catch (Exception e) {
            System.out.println(String.format("❌ Error: %s", e.getMessage()));
        }
    }
    
    private void testUnitUpdateConflictingData(Long projectId, String unitCode) {
        System.out.println(String.format("\n🔍 Testing Conflicting Data: Project %d, Code '%s'", projectId, unitCode));
        
        try {
            // This should find the existing A1.01 unit and generate conflicts
            ValidationResultDto validationResult = new ValidationResultDto(1);
            Units result = simulateValidateAndCreateOrUpdateUnit(
                projectId, unitCode, "A", "1", "01", "Penthouse", // Different product type
                "100.0", "90.0", "Tây", "Sông", "3000000000", 1L, validationResult); // Different values
            
            System.out.println(String.format("✅ Processed: ID=%d, Type='%s' (original preserved)", 
                result.getId(), result.getProductType()));
                
            if (validationResult.hasWarnings()) {
                System.out.println("⚠️ Conflict warnings generated (as expected):");
                validationResult.getWarnings().forEach(w -> 
                    System.out.println("   - " + w.getErrorDescription().split("\n")[0])); // First line only
            } else {
                System.out.println("❌ Expected warnings for conflicting data but none generated");
            }
                
        } catch (Exception e) {
            System.out.println(String.format("❌ Error: %s", e.getMessage()));
        }
    }

    /**
     * Simulated version of the validateAndCreateOrUpdateUnit method
     * This mimics the actual implementation in ImportExecutionProcessor
     */
    private Units simulateValidateAndCreateOrUpdateUnit(Long projectId, String unitCode, String sector, 
                                                       String floorNumber, String unitNumber, String productType,
                                                       String areaStr, String floorAreaStr, String doorDirection,
                                                       String view, String contractPriceStr, Long userId,
                                                       ValidationResultDto validationResult) {
        if (unitCode == null || unitCode.trim().isEmpty()) {
            throw new IllegalArgumentException("Mã căn không được để trống");
        }
        
        String trimmedUnitCode = unitCode.trim();
        
        try {
            // Check if unit already exists (case-insensitive)
            Units existingUnit = unitRepository.findFirstByProjectIdAndCodeIgnoreCase(projectId, trimmedUnitCode);
            
            if (existingUnit != null) {
                System.out.println(String.format("   📁 Found existing unit: %s in project %d (ID: %d)", 
                    existingUnit.getCode(), projectId, existingUnit.getId()));
                
                // Update existing unit and compare fields
                return simulateUpdateExistingUnit(existingUnit, sector, floorNumber, unitNumber, productType,
                    areaStr, floorAreaStr, doorDirection, view, contractPriceStr, userId, validationResult);
            } else {
                // Unit doesn't exist, create new one
                System.out.println(String.format("   🆕 Creating new unit: %s in project %d", trimmedUnitCode, projectId));
                
                return simulateCreateNewUnit(projectId, trimmedUnitCode, sector, floorNumber, unitNumber, productType,
                    areaStr, floorAreaStr, doorDirection, view, contractPriceStr, userId);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("Lỗi khi xử lý mã căn '" + trimmedUnitCode + "': " + e.getMessage(), e);
        }
    }
    
    private Units simulateCreateNewUnit(Long projectId, String unitCode, String sector, String floorNumber,
                                       String unitNumber, String productType, String areaStr, String floorAreaStr,
                                       String doorDirection, String view, String contractPriceStr, Long userId) {
        Units newUnit = new Units();
        newUnit.setProjectId(projectId);
        newUnit.setCode(unitCode);
        newUnit.setSector(trimOrNull(sector));
        newUnit.setFloorNumber(trimOrNull(floorNumber));
        newUnit.setUnitNumber(trimOrNull(unitNumber));
        newUnit.setProductType(trimOrNull(productType));
        newUnit.setDoorDirection(trimOrNull(doorDirection));
        newUnit.setView(trimOrNull(view));
        
        // Parse numeric fields with error handling
        try {
            if (areaStr != null && !areaStr.trim().isEmpty()) {
                newUnit.setArea(new BigDecimal(areaStr.trim()));
            }
        } catch (NumberFormatException e) {
            newUnit.setArea(BigDecimal.ZERO);
        }
        
        try {
            if (floorAreaStr != null && !floorAreaStr.trim().isEmpty()) {
                newUnit.setFloorArea(new BigDecimal(floorAreaStr.trim()));
            }
        } catch (NumberFormatException e) {
            newUnit.setFloorArea(null);
        }
        
        try {
            if (contractPriceStr != null && !contractPriceStr.trim().isEmpty()) {
                newUnit.setContractPrice(new BigDecimal(contractPriceStr.trim()));
            } else {
                newUnit.setContractPrice(BigDecimal.ZERO);
            }
        } catch (NumberFormatException e) {
            newUnit.setContractPrice(BigDecimal.ZERO);
        }
        
        newUnit.setCreatedBy(userId);
        newUnit.setCreatedAt(new java.util.Date());
        newUnit.setActive(true);
        
        return unitRepository.save(newUnit);
    }
    
    private Units simulateUpdateExistingUnit(Units existingUnit, String sector, String floorNumber,
                                            String unitNumber, String productType, String areaStr, String floorAreaStr,
                                            String doorDirection, String view, String contractPriceStr, Long userId,
                                            ValidationResultDto validationResult) {
        boolean hasUpdates = false;
        String unitCode = existingUnit.getCode();
        
        // Compare and update product type (Loại sản phẩm)
        String newProductType = trimOrNull(productType);
        if (shouldUpdateField(existingUnit.getProductType(), newProductType)) {
            if (existingUnit.getProductType() != null && !existingUnit.getProductType().equals(newProductType)) {
                addSimulatedFieldConflictWarning(validationResult, "Loại sản phẩm", unitCode, newProductType, existingUnit.getProductType());
            } else {
                existingUnit.setProductType(newProductType);
                hasUpdates = true;
            }
        }
        
        // Compare and update area (Diện tích)
        if (areaStr != null && !areaStr.trim().isEmpty()) {
            try {
                BigDecimal newArea = new BigDecimal(areaStr.trim());
                if (existingUnit.getArea() == null || existingUnit.getArea().compareTo(BigDecimal.ZERO) == 0) {
                    existingUnit.setArea(newArea);
                    hasUpdates = true;
                } else if (existingUnit.getArea().compareTo(newArea) != 0) {
                    addSimulatedFieldConflictWarning(validationResult, "Diện tích", unitCode, 
                        newArea.toString(), existingUnit.getArea().toString());
                }
            } catch (NumberFormatException e) {
                // Invalid area value, skip
            }
        }
        
        // Add more field comparisons for testing...
        if (floorAreaStr != null && !floorAreaStr.trim().isEmpty()) {
            try {
                BigDecimal newFloorArea = new BigDecimal(floorAreaStr.trim());
                if (existingUnit.getFloorArea() == null || existingUnit.getFloorArea().compareTo(BigDecimal.ZERO) == 0) {
                    existingUnit.setFloorArea(newFloorArea);
                    hasUpdates = true;
                } else if (existingUnit.getFloorArea().compareTo(newFloorArea) != 0) {
                    addSimulatedFieldConflictWarning(validationResult, "Diện tích sàn", unitCode, 
                        newFloorArea.toString(), existingUnit.getFloorArea().toString());
                }
            } catch (NumberFormatException e) {
                // Invalid floor area value, skip
            }
        }
        
        // Save if there are updates
        if (hasUpdates) {
            existingUnit.setUpdatedBy(userId);
            existingUnit.setUpdatedAt(new java.util.Date());
            return unitRepository.save(existingUnit);
        } else {
            return existingUnit;
        }
    }
    
    private String trimOrNull(String value) {
        if (value == null || value.trim().isEmpty()) {
            return null;
        }
        return value.trim();
    }
    
    private boolean shouldUpdateField(String existingValue, String newValue) {
        return (existingValue == null || existingValue.trim().isEmpty()) && 
               (newValue != null && !newValue.trim().isEmpty());
    }
    
    private void addSimulatedFieldConflictWarning(ValidationResultDto validationResult, String fieldName, 
                                                 String unitCode, String importValue, String systemValue) {
        String warningMessage = String.format(
            "Thông tin %s của mã căn %s không khớp với dữ liệu trên hệ thống\n" +
            "Trong file xls: %s\n" +
            "Trên hệ thống: %s",
            fieldName, unitCode, 
            importValue != null ? importValue : "(trống)",
            systemValue != null ? systemValue : "(trống)"
        );
        
        // Simulate adding warning to validation result
        validationResult.addWarning(new vn.agis.crm.base.jpa.dto.ImportErrorDto(
            null, validationResult.getRowNumber(), "MÃ CĂN", unitCode, 
            "DATA_INCONSISTENCY", warningMessage, "WARNING"
        ));
    }
}

/**
 * Manual Testing Instructions
 * ===========================
 * 
 * 1. **Compile and Run:**
 *    ```bash
 *    mvn compile exec:java -Dexec.mainClass="vn.agis.crm.verification.UnitValidationVerificationScript"
 *    ```
 * 
 * 2. **Expected Output:**
 *    - First occurrence of each unique unit code within project should create new unit
 *    - Subsequent occurrences with different cases should find existing unit
 *    - Same unit code in different projects should create separate units
 *    - Empty fields should be updated, conflicting fields should generate warnings
 *    - Unit codes should be trimmed of leading/trailing whitespace
 * 
 * 3. **Database Verification:**
 *    ```sql
 *    SELECT u.id, u.project_id, u.code, u.product_type, u.area, u.is_active, 
 *           u.created_at, u.updated_at, p.name as project_name
 *    FROM units u 
 *    JOIN projects p ON u.project_id = p.id
 *    WHERE u.deleted_at IS NULL 
 *    ORDER BY u.project_id, u.code;
 *    ```
 * 
 * 4. **Import Testing:**
 *    Create a test CSV file with unit codes in different cases and projects:
 *    ```csv
 *    TÊN DỰ ÁN,MÃ CĂN,HỌ VÀ TÊN KHÁCH HÀNG,PHONE
 *    Vinhomes Grand Park,A1.01,Nguyen Van A,0901234567
 *    Vinhomes Grand Park,a1.01,Tran Thi B,0901234568
 *    Vinhomes Grand Park,A1.02,Le Van C,0901234569
 *    Masteri Thao Dien,A1.01,Pham Thi D,0901234570
 *    Masteri Thao Dien,B2.05,Hoang Van E,0901234571
 *    ```
 * 
 * 5. **Verification Checklist:**
 *    ✅ No duplicate units created for same code with different cases within same project
 *    ✅ Same unit code can exist in different projects as separate units
 *    ✅ New units created automatically when they don't exist
 *    ✅ Empty fields in existing units are filled with import data
 *    ✅ Conflicting data generates Vietnamese warning messages
 *    ✅ Unit IDs correctly used in customer properties
 *    ✅ Proper error handling for invalid unit codes
 *    ✅ Transaction consistency maintained
 *    ✅ Performance acceptable for bulk imports
 */
