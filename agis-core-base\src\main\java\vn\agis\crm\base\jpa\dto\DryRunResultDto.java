package vn.agis.crm.base.jpa.dto;

import lombok.Data;
import java.util.Date;
import java.util.List;

@Data
public class DryRunResultDto {
    private Long jobId;
    private String fileName;
    private String status;
    private Date startedAt;
    private Date finishedAt;
    private Long processingTimeMs;
    
    // Statistics
    private Integer totalRows;
    private Integer validRows;
    private Integer errorRows;
    private Integer warningRows;
    private Integer duplicateRows;
    
    // Estimation
    private EstimationDto estimation;
    
    // Summary by error type
    private List<ErrorSummaryDto> errorSummary;
    
    // Warnings
    private List<String> warnings;
    
    @Data
    public static class EstimationDto {
        private Integer customersToCreate;
        private Integer customersToUpdate;
        private Integer relativesToCreate;
        private Integer propertiesToCreate;
        private Integer offersToCreate;
        private Integer assignmentsToCreate;
        private Long estimatedProcessingTimeMs;
        private String estimatedProcessingTime; // Human readable format
    }
    
    @Data
    public static class ErrorSummaryDto {
        private Integer row;
        private String column;
        private String errType;
        private String description;
        private String severity;

        // Legacy fields for backward compatibility
        @Deprecated
        private String errorType;
        @Deprecated
        private String errorDescription;
        @Deprecated
        private Integer count;
        @Deprecated
        private List<Integer> affectedRows;

        // Constructor for new format
        public ErrorSummaryDto(Integer row, String column, String errType, String description, String severity) {
            this.row = row;
            this.column = column;
            this.errType = errType;
            this.description = description;
            this.severity = severity;
        }

        // Constructor for legacy format
        @Deprecated
        public ErrorSummaryDto(String errorType, String errorDescription, Integer count, List<Integer> affectedRows) {
            this.errorType = errorType;
            this.errorDescription = errorDescription;
            this.count = count;
            this.affectedRows = affectedRows;
        }

        public ErrorSummaryDto() {}
    }
}
