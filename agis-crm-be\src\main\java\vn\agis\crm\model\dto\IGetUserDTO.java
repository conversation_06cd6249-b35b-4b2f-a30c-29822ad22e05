package vn.agis.crm.model.dto;

public interface IGetUserDTO {
    Long getId();
    Long getUserManageId();
    String getUsername();
    Integer getType();
    String getEmail();
    Integer getStatus();
    String getName();
    String getDescription();
    String getRoleNames();
    String getRoleIds();
    String getPhone();
    String getAddressContact();
    String getTaxCode();
    String getRepresentativeName();
    String getAddressHeadOffice();
    String getProvinceAddressName();
    String getWardAddressName();
    Integer getProvinceCodeAddress();
    Integer getWardCodeAddress();
    String getProvinceOfficeName();
    String getWardOfficeName();
    Integer getProvinceCodeOffice();
    Integer getWardCodeOffice();
    Integer getApartment();
}
