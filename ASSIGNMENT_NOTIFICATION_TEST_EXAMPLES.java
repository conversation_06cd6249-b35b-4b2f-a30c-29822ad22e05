// Test examples demonstrating the enhanced AssignmentService with notification functionality
// This would typically be in a test class or used for integration testing

package vn.agis.crm.service;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.req.ManualAssignmentRequest;
import vn.agis.crm.base.jpa.entity.Config;
import vn.agis.crm.base.jpa.entity.Customers;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.repository.ConfigRepository;
import vn.agis.crm.repository.CustomerRepository;
import vn.agis.crm.repository.NotificationRepository;

import java.util.Arrays;
import java.util.List;

@SpringBootTest
@Transactional
public class AssignmentServiceNotificationTest {

    @Autowired
    private AssignmentService assignmentService;
    
    @Autowired
    private ConfigRepository configRepository;
    
    @Autowired
    private CustomerRepository customerRepository;
    
    @Autowired
    private NotificationRepository notificationRepository;

    @Test
    public void testManagerAssignmentWithNotificationsEnabled() {
        // Setup: Enable manager notifications
        Config managerConfig = new Config();
        managerConfig.setConfigKey("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        managerConfig.setConfigValue("ON");
        managerConfig.setConfigType(1);
        managerConfig.setDescription("Enable notifications for new manager lead assignments");
        configRepository.save(managerConfig);

        // Setup: Create test customers
        Customers customer1 = createTestCustomer("Nguyễn Văn A", "0987654321");
        Customers customer2 = createTestCustomer("Trần Thị B", "0912345678");
        customerRepository.saveAll(Arrays.asList(customer1, customer2));

        // Test: Assign customers to manager
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(Arrays.asList(customer1.getId(), customer2.getId()));
        request.setManagerId(100L);
        request.setStaffId(null);
        request.setDryRun(false);

        Event event = new Event();
        event.payload = request;
        event.userId = 999L;
        event.method = "MANUAL_ASSIGN";

        // Execute assignment
        Event response = assignmentService.process(event);

        // Verify assignment success
        assert response.respStatusCode == 200;
        assert response.payload != null;

        // Verify notifications were created
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(100L);
        assert notifications.size() == 2;

        for (Notifications notification : notifications) {
            assert notification.getType().equals(1); // LeadAssigned
            assert notification.getTitle().equals("Lead mới được phân công");
            assert notification.getContent().contains("Manager");
            assert notification.getIsRead().equals(false);
            assert notification.getCreatedBy().equals(999L);
        }

        System.out.println("✅ Manager assignment with notifications test passed");
    }

    @Test
    public void testStaffAssignmentWithNotificationsDisabled() {
        // Setup: Disable staff notifications
        Config staffConfig = new Config();
        staffConfig.setConfigKey("ON_NOTIFICATION_NEW_STAFF_LEAD");
        staffConfig.setConfigValue("OFF");
        staffConfig.setConfigType(1);
        staffConfig.setDescription("Disable notifications for new staff lead assignments");
        configRepository.save(staffConfig);

        // Setup: Create test customer
        Customers customer = createTestCustomer("Lê Văn C", "0901234567");
        customerRepository.save(customer);

        // Test: Assign customer to staff
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(Arrays.asList(customer.getId()));
        request.setManagerId(null);
        request.setStaffId(200L);
        request.setDryRun(false);

        Event event = new Event();
        event.payload = request;
        event.userId = 999L;
        event.method = "MANUAL_ASSIGN";

        // Execute assignment
        Event response = assignmentService.process(event);

        // Verify assignment success
        assert response.respStatusCode == 200;

        // Verify no notifications were created (disabled)
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(200L);
        assert notifications.size() == 0;

        System.out.println("✅ Staff assignment with notifications disabled test passed");
    }

    @Test
    public void testMixedAssignmentWithPartialNotifications() {
        // Setup: Enable manager notifications, disable staff notifications
        Config managerConfig = new Config();
        managerConfig.setConfigKey("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        managerConfig.setConfigValue("ON");
        managerConfig.setConfigType(1);
        configRepository.save(managerConfig);

        Config staffConfig = new Config();
        staffConfig.setConfigKey("ON_NOTIFICATION_NEW_STAFF_LEAD");
        staffConfig.setConfigValue("OFF");
        staffConfig.setConfigType(1);
        configRepository.save(staffConfig);

        // Setup: Create test customers
        Customers customer1 = createTestCustomer("Phạm Thị D", "0934567890");
        Customers customer2 = createTestCustomer("Hoàng Văn E", "0945678901");
        customerRepository.saveAll(Arrays.asList(customer1, customer2));

        // Test: Assign customers to both manager and staff
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(Arrays.asList(customer1.getId(), customer2.getId()));
        request.setManagerId(300L);
        request.setStaffId(400L);
        request.setDryRun(false);

        Event event = new Event();
        event.payload = request;
        event.userId = 999L;
        event.method = "MANUAL_ASSIGN";

        // Execute assignment
        Event response = assignmentService.process(event);

        // Verify assignment success
        assert response.respStatusCode == 200;

        // Verify manager notifications were created
        List<Notifications> managerNotifications = notificationRepository.findByTargetEmployeeId(300L);
        assert managerNotifications.size() == 2;

        // Verify staff notifications were NOT created
        List<Notifications> staffNotifications = notificationRepository.findByTargetEmployeeId(400L);
        assert staffNotifications.size() == 0;

        System.out.println("✅ Mixed assignment with partial notifications test passed");
    }

    @Test
    public void testDryRunDoesNotCreateNotifications() {
        // Setup: Enable notifications
        Config config = new Config();
        config.setConfigKey("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        config.setConfigValue("ON");
        config.setConfigType(1);
        configRepository.save(config);

        // Setup: Create test customer
        Customers customer = createTestCustomer("Test Customer", "0999999999");
        customerRepository.save(customer);

        // Test: Dry run assignment
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(Arrays.asList(customer.getId()));
        request.setManagerId(500L);
        request.setDryRun(true); // DRY RUN

        Event event = new Event();
        event.payload = request;
        event.userId = 999L;
        event.method = "MANUAL_ASSIGN";

        // Execute dry run
        Event response = assignmentService.process(event);

        // Verify dry run success
        assert response.respStatusCode == 200;
        assert response.payload.toString().contains("affectedCount");

        // Verify no notifications were created (dry run)
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(500L);
        assert notifications.size() == 0;

        System.out.println("✅ Dry run does not create notifications test passed");
    }

    @Test
    public void testNotificationContentAccuracy() {
        // Setup: Enable manager notifications
        Config config = new Config();
        config.setConfigKey("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        config.setConfigValue("ON");
        config.setConfigType(1);
        configRepository.save(config);

        // Setup: Create test customer with specific data
        Customers customer = createTestCustomer("Nguyễn Thị Hoa", "0987123456");
        customerRepository.save(customer);

        // Test: Assign customer to manager
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(Arrays.asList(customer.getId()));
        request.setManagerId(600L);
        request.setDryRun(false);

        Event event = new Event();
        event.payload = request;
        event.userId = 888L;
        event.method = "MANUAL_ASSIGN";

        // Execute assignment
        Event response = assignmentService.process(event);

        // Verify assignment success
        assert response.respStatusCode == 200;

        // Verify notification content
        List<Notifications> notifications = notificationRepository.findByTargetEmployeeId(600L);
        assert notifications.size() == 1;

        Notifications notification = notifications.get(0);
        assert notification.getTitle().equals("Lead mới được phân công");
        assert notification.getContent().contains("Nguyễn Thị Hoa");
        assert notification.getContent().contains("0987123456");
        assert notification.getContent().contains("Manager");
        assert notification.getContent().contains("24 giờ");
        assert notification.getTargetEmployeeId().equals(600L);
        assert notification.getTargetCustomerId().equals(customer.getId());
        assert notification.getType().equals(1);
        assert notification.getCreatedBy().equals(888L);

        System.out.println("✅ Notification content accuracy test passed");
    }

    @Test
    public void testNoNotificationsForDuplicateAssignments() {
        // Setup: Enable notifications
        Config config = new Config();
        config.setConfigKey("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        config.setConfigValue("ON");
        config.setConfigType(1);
        configRepository.save(config);

        // Setup: Create test customer
        Customers customer = createTestCustomer("Test Duplicate", "0888888888");
        customerRepository.save(customer);

        // First assignment
        ManualAssignmentRequest request1 = new ManualAssignmentRequest();
        request1.setCustomerIds(Arrays.asList(customer.getId()));
        request1.setManagerId(700L);
        request1.setDryRun(false);

        Event event1 = new Event();
        event1.payload = request1;
        event1.userId = 999L;
        event1.method = "MANUAL_ASSIGN";

        assignmentService.process(event1);

        // Verify first notification created
        List<Notifications> firstNotifications = notificationRepository.findByTargetEmployeeId(700L);
        assert firstNotifications.size() == 1;

        // Second assignment (same customer, same manager)
        ManualAssignmentRequest request2 = new ManualAssignmentRequest();
        request2.setCustomerIds(Arrays.asList(customer.getId()));
        request2.setManagerId(700L); // Same manager
        request2.setDryRun(false);

        Event event2 = new Event();
        event2.payload = request2;
        event2.userId = 999L;
        event2.method = "MANUAL_ASSIGN";

        assignmentService.process(event2);

        // Verify no additional notification created (duplicate assignment)
        List<Notifications> secondNotifications = notificationRepository.findByTargetEmployeeId(700L);
        assert secondNotifications.size() == 1; // Still only 1 notification

        System.out.println("✅ No notifications for duplicate assignments test passed");
    }

    private Customers createTestCustomer(String fullName, String phone) {
        Customers customer = new Customers();
        customer.setFullName(fullName);
        customer.setPhone(phone);
        customer.setEmail(fullName.toLowerCase().replace(" ", ".") + "@test.com");
        customer.setSourceType("Data");
        return customer;
    }
}

// Example usage in a real service integration
@Service
@Transactional
class CustomerManagementService {
    
    @Autowired
    private AssignmentService assignmentService;
    
    @Autowired
    private ConfigRepository configRepository;
    
    public void assignNewLeadsToManager(List<Long> customerIds, Long managerId) {
        System.out.println("🎯 Assigning " + customerIds.size() + " new leads to manager " + managerId);
        
        // Check if notifications are enabled
        Config config = configRepository.findOneByConfigKeyIgnoreCase("ON_NOTIFICATION_NEW_MANAGER_LEAD");
        boolean notificationsEnabled = config != null && "ON".equalsIgnoreCase(config.getConfigValue());
        
        System.out.println("📧 Manager notifications: " + (notificationsEnabled ? "ENABLED" : "DISABLED"));
        
        // Create assignment request
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(customerIds);
        request.setManagerId(managerId);
        request.setStaffId(null);
        request.setDryRun(false);
        
        // Create event
        Event event = new Event();
        event.payload = request;
        event.userId = getCurrentUserId();
        event.method = "MANUAL_ASSIGN";
        
        // Execute assignment
        Event response = assignmentService.process(event);
        
        if (response.respStatusCode == 200) {
            System.out.println("✅ Successfully assigned leads to manager " + managerId);
            if (notificationsEnabled) {
                System.out.println("📧 Notifications sent to manager for new lead assignments");
            }
        } else {
            System.err.println("❌ Failed to assign leads: " + response.respErrorDesc);
        }
    }
    
    public void assignNewLeadsToStaff(List<Long> customerIds, Long staffId) {
        System.out.println("🎯 Assigning " + customerIds.size() + " new leads to staff " + staffId);
        
        // Check if notifications are enabled
        Config config = configRepository.findOneByConfigKeyIgnoreCase("ON_NOTIFICATION_NEW_STAFF_LEAD");
        boolean notificationsEnabled = config != null && "ON".equalsIgnoreCase(config.getConfigValue());
        
        System.out.println("📧 Staff notifications: " + (notificationsEnabled ? "ENABLED" : "DISABLED"));
        
        // Create assignment request
        ManualAssignmentRequest request = new ManualAssignmentRequest();
        request.setCustomerIds(customerIds);
        request.setManagerId(null);
        request.setStaffId(staffId);
        request.setDryRun(false);
        
        // Create event
        Event event = new Event();
        event.payload = request;
        event.userId = getCurrentUserId();
        event.method = "MANUAL_ASSIGN";
        
        // Execute assignment
        Event response = assignmentService.process(event);
        
        if (response.respStatusCode == 200) {
            System.out.println("✅ Successfully assigned leads to staff " + staffId);
            if (notificationsEnabled) {
                System.out.println("📧 Notifications sent to staff for new lead assignments");
            }
        } else {
            System.err.println("❌ Failed to assign leads: " + response.respErrorDesc);
        }
    }
    
    private Long getCurrentUserId() {
        // Implementation to get current user ID
        return 1L; // Placeholder
    }
}
