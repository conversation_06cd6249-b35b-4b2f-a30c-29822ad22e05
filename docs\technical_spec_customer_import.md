# Technical Specification: Customer Data Import System

**Version:** 1.0  
**Date:** 2025-09-11  
**Author:** Cascade AI

---

## 1. Executive Summary

This document outlines the technical solution for building a robust and scalable customer data import system for the AGIS CRM platform. The solution is designed to meet the requirements specified in section **5.1.4** of the system requirements document.

The system will feature a background job processing architecture, exposed via a RESTful API, to handle file uploads from both local machines and Google Drive. Key features include a **dry-run** mode for pre-validation, flexible duplicate handling (**upsert by phone** or **skip**), idempotent file processing via checksums, and detailed error reporting. The architecture is designed to be resilient, scalable, and maintainable, integrating seamlessly with the existing MariaDB database schema.

---

## 2. System Analysis

This section summarizes the key findings from analyzing the project artifacts.

### 2.1. Database Schema (`agis_crm.sql`)

The database is well-structured with clear relationships. Key tables relevant to the import process are:

-   **`customers`**: The central entity for customer data. Uniqueness is enforced by the `phone` column (`uq_customer_phone`). It includes fields for personal details, source tracking, and direct links (`current_manager_id`, `current_staff_id`) to the assigned employees for performance.
-   **`customer_assignments`**: A historical log of employee assignments to customers. An active assignment is denoted by `assigned_to IS NULL`.
-   **`customer_relatives`**: Stores information about a customer's relatives.
-   **`customer_properties`** & **`customer_offers`**: Manages real estate properties a customer owns or is being offered, respectively.
-   **`interactions_primary`** & **`interactions_secondary`**: Logs interactions related to offers and owned properties.
-   **`import_jobs`** & **`import_job_errors`**: A dedicated schema for managing the import process itself, tracking each job's metadata, status, and row-level errors.
-   **`projects`**, **`units`**, **`employees`**: Master data tables that will be used for lookups and validation during the import.

### 2.2. Import Template (`DATA-FORMAT-V2.csv`)

The CSV template is a denormalized, wide-format file containing multiple entities in a single row. This requires a sophisticated parsing and mapping logic.

-   **Structure**: The file combines information for Customer, Relatives, Owned Properties, Offered Properties, Interactions, and Assignments.
-   **Data Format**: Contains varied data formats, including different date styles (`dd-Mon-yy`, `Day, Month dd, yyyy`), formatted numbers (`25,000,000,000`), and free text.
-   **Mapping Challenge**: A single row in the CSV can translate to multiple records across various database tables (e.g., one `customers` record, multiple `customer_relatives` records, multiple `customer_properties` records, etc.).

### 2.3. System Requirements (`Đặc tả yêu cầu giai đoạn 1.md` - Section 5.1.4)

The requirements for the "Trang Import dữ liệu khách hàng" are clear and detailed:

-   **Dual Source**: Support for file uploads and Google Drive.
-   **File Formats**: `.xlsx`, `.xls`, `.csv` (UTF-8).
-   **Three-Step Workflow**:
    1.  **Select Source**: Choose file, with options for sheet selection and template download.
    2.  **Dry-run**: A pre-check step that validates the file without writing to the database, providing a summary of valid/invalid rows and detailed errors.
    3.  **Confirm & Run**: Execute the import as a background job, with options for handling duplicates and errors.
-   **Idempotency**: Use a file checksum to detect and warn against re-importing the same file.
-   **Duplicate Handling**: Provide options to **upsert** existing records (based on phone number) or **skip** duplicates.

---

## 3. Technical Architecture

### 3.1. High-Level Design

The system will be built as a microservice or a distinct module within the existing application. It will consist of three main components:

1.  **API Layer**: A set of RESTful endpoints for initiating, monitoring, and managing import jobs.
2.  **Service Layer**: Contains the core business logic for parsing, validating, and transforming the data.
3.  **Background Job Processor**: A queue-based system (e.g., using RabbitMQ, Redis, or a simple database polling mechanism) to execute the import jobs asynchronously.

### 3.2. Data Flow Diagram

```mermaid
sequenceDiagram
    participant User
    participant Frontend
    participant ImportAPI
    participant JobQueue
    participant ImportWorker
    participant Database

    User->>Frontend: Uploads file & selects options
    Frontend->>ImportAPI: POST /api/imports (mode=DRY_RUN)
    ImportAPI->>Database: Creates `import_jobs` record (status: PENDING)
    ImportAPI->>JobQueue: Enqueues Dry-run Job
    Frontend-->>User: Shows Job ID and pending status

    JobQueue->>ImportWorker: Dispatches Job
    ImportWorker->>Database: Updates job status (RUNNING)
    ImportWorker->>ImportWorker: Parses & Validates File
    ImportWorker->>Database: Writes errors to `import_job_errors`
    ImportWorker->>Database: Updates `import_jobs` with results (status: SUCCESS/FAILED)

    User->>Frontend: Reviews dry-run results & confirms
    Frontend->>ImportAPI: POST /api/imports/{id}/run
    ImportAPI->>Database: Updates job mode to RUN, status to PENDING
    ImportAPI->>JobQueue: Enqueues Run Job

    JobQueue->>ImportWorker: Dispatches Job
    ImportWorker->>Database: Updates job status (RUNNING)
    loop For each row in file
        ImportWorker->>Database: BEGIN TRANSACTION
        ImportWorker->>Database: Upsert `customers`, Insert related data...
        ImportWorker->>Database: COMMIT / ROLLBACK
    end
    ImportWorker->>Database: Updates `import_jobs` with final results (status: SUCCESS/FAILED)
```

### 3.3. Database Integration

-   **Transactions**: Each row from the source file (representing a single customer and their related data) will be processed within a single database transaction. This ensures data integrity. If any part of the row processing fails (e.g., invalid relative data), the entire set of changes for that customer is rolled back.
-   **Lookups**: The system will perform efficient lookups on `projects`, `units`, and `employees` tables. These lookups should be case-insensitive for user-friendliness and cached where possible to improve performance.
-   **Concurrency**: The `import_jobs` table will use pessimistic locking (`SELECT ... FOR UPDATE`) when a worker picks up a job to prevent race conditions where multiple workers might try to process the same job.

---

## 4. Implementation Details

### 4.1. Field Mappings

A detailed mapping from CSV columns to database fields is crucial. Below are key examples:

| CSV Header                  | Database Table & Column                           | Notes                                                                 |
| --------------------------- | ------------------------------------------------- | --------------------------------------------------------------------- |
| `HỌ VÀ TÊN KHÁCH HÀNG`      | `customers.full_name`                             | Required.                                                             |
| `PHONE`                     | `customers.phone`                                 | Required, Unique. Normalize to E.164 format.                          |
| `NGÀY THÁNG NĂM SINH`       | `customers.birth_date`                            | Parse multiple date formats.                                          |
| `TÊN DỰ ÁN` & `MÃ CĂN`      | `customer_properties.unit_id` (via lookups)       | Requires lookup on `projects` and `units`.                            |
| `GIAO DỊCH NGÀY`            | `customer_properties.transaction_date`            |                                                                       |
| `GIÁ BÁN KỲ VỌNG`           | `interactions_secondary.expected_sell_price`      | Linked to a `customer_properties` record.                             |
| `DỰ ÁN ĐANG CHÀO`           | `customer_offers.project_id`                      |                                                                       |
| `MÃ SỐ NHÂN VIÊN`           | `customer_assignments.employee_id` (via lookup)   | Creates an active assignment. Updates `customers.current_staff_id`.   |
| `MỐI QUAN HỆ` & `HỌ VÀ TÊN` | `customer_relatives.relation_type`, `.full_name`  | A single customer can have multiple relative rows.                    |

### 4.2. Validation Rules

-   **Required Fields**: `full_name` and `phone` are mandatory for a customer record to be valid.
-   **Uniqueness**: `phone` must be unique in the `customers` table. The import process will handle this based on the chosen duplicate strategy.
-   **Data Types & Formats**:
    -   **Phone**: Must be a valid phone number, normalized to E.164.
    -   **Email**: Must match a standard email regex.
    -   **Dates**: Must be parsable into a valid date. The system will attempt parsing using a configurable list of formats.
    -   **Numbers**: Must be parsable into decimal values, stripping currency symbols and thousands separators.
-   **Referential Integrity**: Values like `TÊN DỰ ÁN` or `MÃ SỐ NHÂN VIÊN` must exist in their respective master tables (`projects`, `employees`).

### 4.3. Error Handling Strategy

Errors will be categorized and stored in the `import_job_errors` table.

| Error Type Code                 | Description                                                                 |
| ------------------------------- | --------------------------------------------------------------------------- |
| `MISSING_REQUIRED_FIELD`        | A mandatory field (e.g., `phone`) is missing.                               |
| `INVALID_FORMAT`                | The data is in an incorrect format (e.g., unparseable date).                 |
| `DUPLICATE_IN_SYSTEM`           | The phone number already exists in the DB (used when `skip` strategy is on).|
| `DUPLICATE_IN_FILE`             | The same phone number appears multiple times in the file.                   |
| `FK_NOT_FOUND`                  | A referenced entity (e.g., Project, Employee) was not found.                |
| `DB_TRANSACTION_ERROR`          | A generic database error occurred during the transaction.                   |

---

## 5. Development Phases

The implementation can be broken down into the following incremental phases:

1.  **Phase 1: Core Job & API Infrastructure**
    -   Set up `import_jobs` and `import_job_errors` tables.
    -   Build the basic API endpoints for creating and viewing jobs.
    -   Implement the background job worker infrastructure (polling or queue-based).

2.  **Phase 2: File Parsing & Dry-run Logic**
    -   Implement parsers for CSV and Excel files.
    -   Develop the complete validation logic for the dry-run mode.
    -   Implement the logic to populate the `import_job_errors` table.

3.  **Phase 3: Database Write Logic (Run Mode)**
    -   Implement the transactional database write logic (`upsert` and `insert`).
    -   Handle the `UPSERT_BY_PHONE` and `SKIP` strategies.
    -   Integrate all field mappings for `customers`, `relatives`, `properties`, etc.

4.  **Phase 4: Frontend Integration & Advanced Features**
    -   Develop the UI for the 3-step import flow.
    -   Integrate Google Drive API for file picking.
    -   Implement file checksum logic for idempotency.

---

## 6. API Specifications

### `POST /api/imports`
-   **Description**: Creates a new import job.
-   **Request Body** (`multipart/form-data`):
    -   `file`: The uploaded file.
    -   `source`: `WEB_UPLOAD` or `GOOGLE_DRIVE`.
    -   `source_link`: URL for Google Drive file.
    -   `options`: JSON string with import configurations (see below).
-   **`options` JSON Object**:
    ```json
    {
      "mode": "DRY_RUN", // or "RUN"
      "upsert_strategy": "UPSERT_BY_PHONE", // or "SKIP_DUP"
      "stop_on_error": false,
      "sheet_name": "Sheet1"
    }
    ```
-   **Response** (201 Created):
    ```json
    {
      "id": 123,
      "status": "PENDING",
      "mode": "DRY_RUN",
      "file_checksum": "sha256:...",
      "created_at": "2025-09-11T10:30:00Z"
    }
    ```

### `GET /api/imports/{id}`
-   **Description**: Retrieves the status and results of an import job.
-   **Response** (200 OK):
    ```json
    {
      "id": 123,
      "file_name": "customer_data.csv",
      "status": "SUCCESS",
      "mode": "DRY_RUN",
      "total_rows": 100,
      "valid_rows": 95,
      "error_rows": 5,
      "created_at": "...",
      "finished_at": "..."
    }
    ```

### `GET /api/imports/{id}/errors`
-   **Description**: Gets a paginated list of errors for a job.
-   **Query Params**: `page`, `limit`.
-   **Response** (200 OK):
    ```json
    {
      "data": [
        {
          "row_num": 10,
          "column_name": "PHONE",
          "error_type": "INVALID_FORMAT",
          "description": "Phone number is not in a valid format."
        }
      ],
      "pagination": { ... }
    }
    ```

### `POST /api/imports/{id}/run`
-   **Description**: Confirms and transitions a completed DRY_RUN job to RUN mode.
-   **Response** (202 Accepted):
    ```json
    {
      "id": 123,
      "status": "PENDING",
      "mode": "RUN"
    }
    ```

---

## 7. Testing Strategy

-   **Unit Tests**: Test individual components like file parsers, data validators, and field normalizers in isolation.
-   **Integration Tests**: Test the entire workflow from API call to database write, using a test database with pre-seeded data.
-   **E2E Tests**: Simulate user actions on the frontend to test the complete flow.
-   **Test Data**: Create a comprehensive set of test files (`.csv`, `.xlsx`) covering all success and failure scenarios (e.g., files with missing columns, invalid data, duplicates, etc.).

---

## 8. Data Processing Workflow

### 8.1. Per-Row Processing Logic (Pseudo-code)

```javascript
async function processCustomerRow(row, options) {
  const transaction = await db.beginTransaction();

  try {
    // 1. Normalize and validate core customer data
    const customerData = {
      phone: normalizePhone(row['PHONE']),
      full_name: row['HỌ VÀ TÊN KHÁCH HÀNG'],
      email: row['EMAIL'],
      birth_date: parseDate(row['NGÀY THÁNG NĂM SINH']),
      // ... other fields
    };

    validateRequired(customerData, ['phone', 'full_name']);

    // 2. Handle customer upsert based on strategy
    let customer;
    if (options.upsert_strategy === 'UPSERT_BY_PHONE') {
      customer = await upsertCustomer(customerData);
    } else {
      customer = await insertCustomerIfNotExists(customerData);
    }

    // 3. Process relatives
    if (row['HỌ VÀ TÊN NGƯỜI THÂN']) {
      await insertCustomerRelative({
        customer_id: customer.id,
        relation_type: row['MỐI QUAN HỆ'],
        full_name: row['HỌ VÀ TÊN NGƯỜI THÂN'],
        // ... other relative fields
      });
    }

    // 4. Process owned properties
    if (row['TÊN DỰ ÁN'] && row['MÃ CĂN']) {
      const project = await findProjectByName(row['TÊN DỰ ÁN']);
      const unit = await findUnitByCode(project.id, row['MÃ CĂN']);

      const property = await insertCustomerProperty({
        customer_id: customer.id,
        project_id: project.id,
        unit_id: unit.id,
        transaction_date: parseDate(row['GIAO DỊCH NGÀY']),
        contract_price: parseNumber(row['GIÁ GỐC TRÊN HỢP ĐỒNG']),
        // ... external sale info
      });

      // Process secondary interactions
      if (row['KẾT QUẢ (THỨ CẤP)']) {
        await insertSecondaryInteraction({
          customer_property_id: property.id,
          result: row['KẾT QUẢ (THỨ CẤP)'],
          expected_sell_price: parseNumber(row['GÍA BÁN KỲ VỌNG']),
          expected_rent_price: parseNumber(row['GIÁ CHO THUÊ KỲ VỌNG']),
          // ...
        });
      }
    }

    // 5. Process offers and primary interactions
    if (row['DỰ ÁN ĐANG CHÀO (SƠ CẤP)']) {
      const offerProject = await findProjectByName(row['DỰ ÁN ĐANG CHÀO (SƠ CẤP)']);
      const offer = await insertCustomerOffer({
        customer_id: customer.id,
        project_id: offerProject.id,
        // ...
      });

      if (row['KẾT QUẢ (SƠ CẤP)']) {
        await insertPrimaryInteraction({
          customer_offer_id: offer.id,
          result: row['KẾT QUẢ (SƠ CẤP)'],
          // ...
        });
      }
    }

    // 6. Process staff assignment
    if (row['MÃ SỐ NHÂN VIÊN']) {
      const employee = await findEmployeeByCode(row['MÃ SỐ NHÂN VIÊN']);
      await assignStaffToCustomer(customer.id, employee.id, {
        assigned_from: parseDateTime(row['THỜI GIAN NHẬN ĐƯỢC LEAD ĐỂ CHĂM'])
      });
    }

    await transaction.commit();
    return { success: true, customer_id: customer.id };

  } catch (error) {
    await transaction.rollback();
    return { success: false, error: error.message };
  }
}
```

### 8.2. Batch Processing Strategy

For optimal performance and memory usage:

1. **Stream Processing**: Read and process the file in chunks of 500-1000 rows
2. **Connection Pooling**: Use database connection pools to handle concurrent transactions
3. **Progress Tracking**: Update `import_jobs.updated_at` and store progress in `options.progress`
4. **Memory Management**: Clear processed row data from memory after each batch

---

## 9. Configuration Management

### 9.1. Import Options Schema

```json
{
  "mode": "DRY_RUN | RUN",
  "upsert_strategy": "UPSERT_BY_PHONE | SKIP_DUP",
  "stop_on_error": false,
  "run_only_valid_rows": true,
  "force_reimport": false,
  "sheet_name": "Sheet1",
  "date_formats": [
    "d-MMM-yy",
    "EEEE, MMMM d, yyyy",
    "d/M/yyyy",
    "yyyy-MM-dd"
  ],
  "number_locale": "vi-VN",
  "source_type_mapping": {
    "Data": "Data",
    "Marketing": "Leads",
    "Network": "Refer",
    "Event": "Event"
  },
  "marital_status_mapping": {
    "ĐỘC THÂN": "single",
    "ĐÃ LẬP GIA ĐÌNH": "married",
    "LY THÂN": "divorced"
  },
  "result_mappings": {
    "secondary": {
      "MUỐN BÁN": "want_to_sell",
      "ĐÃ CHO THUÊ": "rented_out",
      "KHÔNG BÁN": "not_selling"
    },
    "primary": {
      "QUAN TÂM": "interested",
      "KO QUAN TÂM": "not_interested",
      "KO BẮT MÁY": "no_answer"
    }
  },
  "assignment_policy": {
    "identify_employee_by": "employee_code",
    "missing_employee": "WARN_SKIP",
    "role": "STAFF"
  },
  "project_unit_policy": {
    "require_existing_project": true,
    "require_existing_unit": true
  }
}
```

### 9.2. System Configuration

Store system-wide import configurations in the `configs` table:

```sql
INSERT INTO configs (config_key, config_type, config_value, description) VALUES
('import.max_file_size_mb', 1, '10', 'Maximum file size for imports in MB'),
('import.max_rows_per_file', 1, '10000', 'Maximum number of rows per import file'),
('import.supported_formats', 2, '["csv","xlsx","xls"]', 'Supported file formats'),
('import.default_date_formats', 2, '["d-MMM-yy","EEEE, MMMM d, yyyy","d/M/yyyy"]', 'Default date parsing formats');
```

---

## 10. Error Handling & Recovery

### 10.1. Error Classification Matrix

| Severity | Error Type | Action | Recovery |
|----------|------------|--------|----------|
| **CRITICAL** | `DB_CONNECTION_FAILED` | Stop job immediately | Retry after connection restored |
| **ERROR** | `MISSING_REQUIRED_FIELD` | Skip row, log error | Manual data correction required |
| **ERROR** | `FK_NOT_FOUND` | Skip row, log error | Add missing master data |
| **WARNING** | `INVALID_FORMAT` | Use default/skip field | Data cleaning recommended |
| **INFO** | `DUPLICATE_IN_FILE` | Merge or skip based on strategy | No action needed |

### 10.2. Recovery Mechanisms

1. **Job Restart**: Failed jobs can be restarted from the last successful batch
2. **Partial Success**: Jobs that complete with errors still update valid records
3. **Rollback Strategy**: Critical errors trigger full job rollback
4. **Manual Intervention**: Provide tools for admins to fix data and re-run specific rows

---

## 11. Security & Access Control

### 11.1. Authentication & Authorization

- **Role-Based Access**: Only users with `Admin` role can access import endpoints
- **API Authentication**: Use JWT tokens or session-based authentication
- **Audit Trail**: Log all import activities in `audit_logs` table

### 11.2. File Security

```javascript
// File validation middleware
async function validateUploadedFile(file) {
  // 1. File size check
  if (file.size > MAX_FILE_SIZE) {
    throw new Error('File too large');
  }

  // 2. File type validation
  const allowedTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
  if (!allowedTypes.includes(file.mimetype)) {
    throw new Error('Invalid file type');
  }

  // 3. Virus scanning (if available)
  await scanFileForMalware(file.path);

  // 4. Content validation
  await validateFileStructure(file.path);
}
```

---

## 12. Monitoring & Observability

### 12.1. Metrics to Track

- **Job Success Rate**: Percentage of jobs that complete successfully
- **Processing Time**: Average time per job and per row
- **Error Rates**: Breakdown by error type and frequency
- **Data Quality**: Percentage of valid vs invalid rows

### 12.2. Logging Strategy

```javascript
// Structured logging example
logger.info('Import job started', {
  job_id: 123,
  file_name: 'customers.csv',
  total_rows: 1000,
  user_id: 456,
  timestamp: new Date().toISOString()
});

logger.error('Row processing failed', {
  job_id: 123,
  row_number: 42,
  error_type: 'FK_NOT_FOUND',
  error_details: 'Project "INVALID_PROJECT" not found',
  customer_phone: '+84901234567'
});
```

---

## 13. Performance Optimization

### 13.1. Database Optimizations

```sql
-- Essential indexes for import performance
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_projects_name_lower ON projects(LOWER(name));
CREATE INDEX idx_units_project_code ON units(project_id, code);
CREATE INDEX idx_employees_code ON employees(employee_code);
CREATE INDEX idx_import_jobs_status ON import_jobs(status);
CREATE INDEX idx_import_job_errors_job_id ON import_job_errors(import_job_id);
```

### 13.2. Caching Strategy

```javascript
// In-memory cache for frequently accessed master data
class ImportCache {
  constructor() {
    this.projects = new Map();
    this.employees = new Map();
    this.units = new Map();
  }

  async getProject(name) {
    const key = name.toLowerCase().trim();
    if (!this.projects.has(key)) {
      const project = await db.findProjectByName(name);
      this.projects.set(key, project);
    }
    return this.projects.get(key);
  }

  // Similar methods for employees and units...
}
```

---

## 14. Testing & Quality Assurance

### 14.1. Test Data Sets

Create comprehensive test files covering:

1. **Happy Path**: Valid data with all fields populated
2. **Edge Cases**: Empty fields, boundary values, special characters
3. **Error Scenarios**: Invalid formats, missing required fields, duplicates
4. **Large Files**: Performance testing with 10k+ rows
5. **Malformed Files**: Corrupted CSV, invalid Excel files

### 14.2. Test Implementation Example

```javascript
describe('Customer Import Service', () => {
  beforeEach(async () => {
    await setupTestDatabase();
    await seedMasterData();
  });

  test('should successfully import valid customer data', async () => {
    const file = createTestFile('valid_customers.csv');
    const job = await importService.createJob(file, { mode: 'DRY_RUN' });

    await importService.processJob(job.id);

    const result = await importService.getJobResult(job.id);
    expect(result.status).toBe('SUCCESS');
    expect(result.valid_rows).toBe(10);
    expect(result.error_rows).toBe(0);
  });

  test('should handle duplicate phone numbers correctly', async () => {
    // Test implementation...
  });
});
```

---

## 15. Deployment Considerations

### 15.1. Infrastructure Requirements

- **Application Server**: Node.js/Python/Java application with at least 2GB RAM
- **Database**: MariaDB with sufficient storage for import jobs and error logs
- **Queue System**: Redis or RabbitMQ for background job processing
- **File Storage**: Local filesystem or cloud storage (AWS S3, Google Cloud Storage)

### 15.2. Deployment Checklist

- [ ] Database migrations applied
- [ ] Required indexes created
- [ ] Configuration values set
- [ ] Background worker service configured
- [ ] File upload directory permissions set
- [ ] Monitoring and logging configured
- [ ] Security scanning enabled
- [ ] Performance benchmarks established

---

## 16. Future Enhancements

### 16.1. Phase 2 Features

1. **Real-time Progress Updates**: WebSocket connections for live job status
2. **Advanced Validation Rules**: Custom business rules engine
3. **Data Transformation**: Built-in data cleaning and normalization
4. **Scheduled Imports**: Automated imports from external sources
5. **Multi-tenant Support**: Isolated imports for different organizations

### 16.2. Integration Opportunities

- **CRM Webhooks**: Notify external systems of successful imports
- **Data Quality Dashboard**: Visual analytics for import success rates
- **Machine Learning**: Automated data quality scoring and suggestions

---

## Conclusion

This technical specification provides a comprehensive blueprint for implementing a robust customer data import system that meets all requirements specified in section 5.1.4. The modular design allows for incremental development and future enhancements while maintaining data integrity and system performance.

The solution balances user-friendliness with technical robustness, providing clear error reporting and flexible configuration options while ensuring data consistency and security throughout the import process.

