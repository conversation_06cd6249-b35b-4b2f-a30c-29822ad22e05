{"info": {"_postman_id": "3a9e9df3-2c1b-4b69-9f4f-1a8e0bb2c8d7", "name": "AGIS HTTP API - EmployeeController", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Postman collection for EmployeeController endpoints in agis-http-api"}, "variable": [{"key": "base_url", "value": "http://localhost:8080"}, {"key": "token", "value": ""}, {"key": "employeeCode", "value": ""}, {"key": "fullName", "value": ""}, {"key": "phone", "value": ""}, {"key": "email", "value": ""}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "sort", "value": "createdAt,desc"}, {"key": "id", "value": "1"}, {"key": "existsValue", "value": "E001"}], "item": [{"name": "GET /employee-mgmt/search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/search?employeeCode={{employeeCode}}&fullName={{fullName}}&phone={{phone}}&email={{email}}&page={{page}}&size={{size}}&sort={{sort}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "search"], "query": [{"key": "employeeCode", "value": "{{employeeCode}}"}, {"key": "fullName", "value": "{{fullName}}"}, {"key": "phone", "value": "{{phone}}"}, {"key": "email", "value": "{{email}}"}, {"key": "page", "value": "{{page}}"}, {"key": "size", "value": "{{size}}"}, {"key": "sort", "value": "{{sort}}"}]}, "description": "Search employees by code/name/phone/email with pagination"}}, {"name": "GET /employee-mgmt/{id}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/{{id}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "{{id}}"]}, "description": "Get employee by ID"}}, {"name": "POST /employee-mgmt", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/employee-mgmt", "host": ["{{base_url}}"], "path": ["employee-mgmt"]}, "body": {"mode": "raw", "raw": "{\n  \"employeeCode\": \"E001\",\n  \"fullName\": \"<PERSON>uy<PERSON>\",\n  \"phone\": \"0900000000\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Passw0rd!\",\n  \"roleId\": 2\n}"}, "description": "Create a new employee (CreateEmployeeReq)"}}, {"name": "PUT /employee-mgmt/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/employee-mgmt/{{id}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "{{id}}"]}, "body": {"mode": "raw", "raw": "{\n  \"employeeCode\": \"E001\",\n  \"fullName\": \"<PERSON><PERSON><PERSON> (Updated)\",\n  \"phone\": \"0900000001\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Passw0rd!\",\n  \"roleId\": 3,\n  \"status\": \"active\"\n}"}, "description": "Update an employee by ID (UpdateEmployeeReq)"}}, {"name": "PUT /employee-mgmt/change-status/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/change-status/{{id}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "change-status", "{{id}}"]}, "description": "Toggle employee status active/inactive"}}, {"name": "DELETE /employee-mgmt/{id}", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/{{id}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "{{id}}"]}, "description": "Delete an employee by ID"}}, {"name": "GET /employee-mgmt/all", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/all", "host": ["{{base_url}}"], "path": ["employee-mgmt", "all"]}, "description": "Get all employees (no pagination)"}}, {"name": "GET /employee-mgmt/check-exists", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/check-exists?key={{existsKey}}&value={{existsValue}}", "host": ["{{base_url}}"], "path": ["employee-mgmt", "check-exists"], "query": [{"key": "key", "value": "{{<PERSON><PERSON><PERSON>}}"}, {"key": "value", "value": "{{<PERSON>V<PERSON><PERSON>}}"}]}, "description": "Check if employee exists by a specific field (key=employeeCode|email|phone)"}}, {"name": "GET /employee-mgmt/current", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/employee-mgmt/current", "host": ["{{base_url}}"], "path": ["employee-mgmt", "current"]}, "description": "Get current employee (includes roles & authorities)"}}]}