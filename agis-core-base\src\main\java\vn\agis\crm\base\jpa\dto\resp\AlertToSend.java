package vn.agis.crm.base.jpa.dto.resp;

import jakarta.persistence.Column;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlertToSend {
    private Long alertId;
    private Long alertListId;
    private String alertName;
    private Integer severity;
    private Integer status;
    private Long customerId;
    private Long imsi;
    private String description;
    private Date raisedDate;
    private Integer flag;
    private Long msisdn;
    private Integer simStatus;
    private String ratingPlanName;
    private Integer eventType;
    private Long value;
//    private String emailSubject;
    private String emailContent;
    private String smsContent;
    private String notificationUrl;
    private String alertEmails;
    private String alertMsisdns;
    private Integer actionType;
    private Long smsThreshold;
    private Long dataThreshold;
    private String receivingGroupEmails;
    private String receivingGroupMsisdns;
    private String datapoolPkgCode;
}
