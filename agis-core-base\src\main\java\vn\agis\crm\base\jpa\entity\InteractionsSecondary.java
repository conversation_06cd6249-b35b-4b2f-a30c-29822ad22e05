package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "interactions_secondary")
public class InteractionsSecondary {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "customer_property_id", nullable = false)
    private Long customerPropertyId;

    @Column(name = "expected_sell_price")
    private BigDecimal expectedSellPrice;

    @Column(name = "expected_rent_price")
    private BigDecimal expectedRentPrice;

    @Column(name = "result", nullable = false, length = 100)
    private String result;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "happened_at", nullable = false)
    private Date happenedAt;

    @Lob
    @Column(name = "notes")
    private String notes;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "created_at")
    private Date createdAt;

    @Column(name = "created_by")
    private Long createdBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "updated_at")
    private Date updatedAt;

    @Column(name = "updated_by")
    private Long updatedBy;
}

