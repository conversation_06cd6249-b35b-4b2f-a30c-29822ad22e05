package vn.agis.crm.service;

import jakarta.transaction.Transactional;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants.RoleType;
import vn.agis.crm.base.constants.Constants.UserType;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.RoleSearchDTO;
import vn.agis.crm.base.jpa.dto.resp.SearchRoleRespone;
import vn.agis.crm.base.jpa.dto.resp.SearchRoleResponeDTO;
import vn.agis.crm.base.jpa.entity.Role;
import vn.agis.crm.base.jpa.entity.RolePermission;
import vn.agis.crm.base.jpa.services.CrudService;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.repository.RolePermissionRepository;
import vn.agis.crm.repository.RoleRepository;
import vn.agis.crm.repository.UserRepository;
import vn.agis.crm.util.BaseController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class RoleService extends CrudService<Role, Long> {
    public static final String SUPER_ADMIN_ROLENAME = "SUPER_ADMIN";
    private RoleRepository roleRepository;
    private RolePermissionRepository rolePermissionRepository;

    @Autowired
    UserRepository userRepository;
    public RoleService(RoleRepository roleRepository, RolePermissionRepository rolePermissionRepository) {
        super(Role.class);
        this.repository = this.roleRepository = roleRepository;
        this.rolePermissionRepository = rolePermissionRepository;
    }

    @Override
    public Event process(Event event) {
        Event response = null;
        switch (event.method) {
            case JpaConstants.Method.GET_ONE -> response = processGetOne(event);
            case JpaConstants.Method.UPDATE -> response = processUpdate(event);
            case JpaConstants.Method.UPDATE_ONE -> response = processUpdateStatus(event);
            case JpaConstants.Method.GET_ALL -> response = processGetAll(event);
            case JpaConstants.Method.CREATE -> response = processCreate(event);
            case JpaConstants.Method.SEARCH -> response = processSearch(event);
            case JpaConstants.Method.DELETE -> response = processDeleteRole(event);
            case JpaConstants.Method.CHECK_EXITS -> response = processCheckExits(event);
        }
        return response;
    }

    @Override
    public Event processGetOne(Event event) {
        Long roleId = (Long) event.payload;
        Role role;
        if (roleRepository.findById(roleId).isPresent()) {
            role = roleRepository.findById(roleId).get();
        }
        else {
            return event.createResponse("id", 404, "Not Found");
        }
        List<Long> permissionIds = roleRepository.getRolePermissions(roleId);
        role.setPermissionIds(permissionIds);
        return event.createResponse(role, 200, "OK");
    }

    @Override
    public Event processUpdate(Event event) {
        Role role = (Role) event.payload;
        Long roleId = role.getId();
        Role oldRole = roleRepository.findById(roleId).get();
        if(checkPermissionRole(event, oldRole)) {
            if (roleRepository.countByName(role.getName()) > 0 && !oldRole.getName().equals(role.getName())) {
                return event.createResponse("name", 409, "Role name is exist");
            }
            List<Long> permissionIds = role.getPermissionIds();

            oldRole.setName(role.getName());
            oldRole.setDescription(role.getDescription());
            oldRole.setStatus(role.getStatus());
            oldRole.setType(role.getType());
            rolePermissionRepository.deleteAllByRoleId(roleId);
            List<RolePermission> rolePermissions = new ArrayList<>();
            for (Long permissionId : permissionIds) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(permissionId);
                rolePermissions.add(rolePermission);
            }
            oldRole.setPermissionIds(permissionIds);
            rolePermissionRepository.saveAll(rolePermissions);
            try {
                roleRepository.save(oldRole);
            } catch (Exception e) {
                e.printStackTrace();
                return event.createResponse(null, 400, "Bad Request");
            }
            return event.createResponse(oldRole, 200, "OK");
        }
        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }
        public Event processCheckExits(Event event) {
        String name = (String) event.payload;
        long count = roleRepository.countByName(name);
        return event.createResponse(count, 200, "OK");
    }

    public Event processUpdateStatus(Event event) {
        Long roleId = (Long) event.payload;
        Role role = roleRepository.findById(roleId).get();
        if(checkPermissionRole(event, role)) {
            if (role.getStatus() == 1) {
                role.setStatus(0);
            } else {
                role.setStatus(1);
            }
            roleRepository.save(role);
            return event.createResponse(null, 200, "OK");
        }
        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }
    @Override
    public Event processCreate(Event event) {
        Role rolePermissionReq = (Role) event.payload;
        if(checkPermissionRole(event, rolePermissionReq)) {
            if (roleRepository.countByName(rolePermissionReq.getName()) > 0) {
                return event.createResponse("name", 409, "Role name is exist");
            }
            rolePermissionReq.setCreatedBy(event.userId);
            Role role = (Role) super.create(rolePermissionReq);
            Long roleId = role.getId();
            List<Long> permissionIds = rolePermissionReq.getPermissionIds();
            List<RolePermission> rolePermissions = new ArrayList<>();
            for (Long permissionId : permissionIds) {
                RolePermission rolePermission = new RolePermission();
                rolePermission.setRoleId(roleId);
                rolePermission.setPermissionId(permissionId);
                rolePermissions.add(rolePermission);
            }
            rolePermissionRepository.saveAll(rolePermissions);
            event.payload=role;
            return event.createResponse(role,200,"OK");
        }
        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public Event processSearch(Event event) {
        RoleSearchDTO roleSearchDTO = (RoleSearchDTO) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(roleSearchDTO.getSize(), roleSearchDTO.getPage(), roleSearchDTO.getSortBy());

        List<Long> userIdList = new ArrayList<>();
        userIdList.add(-1L); // tránh null
        if (event.userType == UserType.ENTERPRISE) {
            // thêm tài khoản con và chính nó vào list
//            userIdList = getListChildOfListUser(List.of(event.userId));
            userIdList = getListChildOfUser(event.userId);
            userIdList.add(event.userId);
        }
        Page<SearchRoleResponeDTO> roles = roleRepository.searchRole(roleSearchDTO, listRequest.getPageable(), userIdList, event.userType);
        List<SearchRoleRespone> entityList = roles.getContent()
                .stream()
                .map(dto -> {
                    SearchRoleRespone entity = new SearchRoleRespone();
                    BeanUtils.copyProperties(dto, entity);
                    return entity;
                })
                .collect(Collectors.toList());
        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(roles.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(entityList));
        return event.createResponse(pageInfo, 200, (String) null);
    }

    public Event processDeleteRole(Event event) {
        Long roleId = (Long) event.payload;
        Optional<Role> roleOptional = roleRepository.findById(roleId);
        if (!roleOptional.isPresent()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Role role = roleOptional.get();
        if (role.getCreatedBy() == 0) {
            return event.createResponse("id", ResponseCode.BAD_REQUEST, "Can not delete default role");
        }
        if(checkPermissionRole(event, role)) {
            rolePermissionRepository.deleteAllByRoleId(roleId);
            return processDelete(event);
        }
        return event.createResponse("id", ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    boolean checkPermissionRole(Event event, Role role) {
        if(event.userType == UserType.ADMIN) {
            return true;
        }else if(event.userType == UserType.ENTERPRISE) {
            // KH doanh nghiệp chỉ có quyền với cấp dưới (KH)
            if(role.getType().equals(RoleType.CUSTOMER)) {
                return true;
            }else {
                return false;
            }
        }else if(event.userType == UserType.CUSTOMER) {
            // KH chỉ có quyền với role KH
            if(role.getType().equals(RoleType.CUSTOMER)) {
                return true;
            }else {
                return false;
            }
        }
        return false;
    }


    private List<Long> getListChildOfUser(Long userId) {
        List<Long> listFinal = new ArrayList<>();
        listFinal.addAll(userRepository.findAllByUserManageOrUser(userId)
                .stream()
                .map(user -> user.getId())
                .collect(Collectors.toList()));;
        return listFinal;
    }
}
