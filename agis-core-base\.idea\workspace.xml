<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="4cc6f5a7-dd8f-4497-aac0-bb7985906410" name="Changes" comment="fix api dry-run" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="dev_import" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitLabMergeRequestFiltersHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPENED&quot;,
    &quot;assignee&quot;: {
      &quot;type&quot;: &quot;org.jetbrains.plugins.gitlab.mergerequest.ui.filters.GitLabMergeRequestsFiltersValue.MergeRequestsMemberFilterValue.MergeRequestsAssigneeFilterValue&quot;,
      &quot;username&quot;: &quot;hosyhaiha&quot;,
      &quot;fullname&quot;: &quot;hosyhaiha&quot;
    }
  }
}</component>
  <component name="GitLabMergeRequestsSettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;first&quot;: &quot;https://gitlab.com/agis6491334/agis-core-base.git&quot;,
    &quot;second&quot;: &quot;2cfce9aa-ecdd-4155-af73-b6317d6ca807&quot;
  }
}</component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenRunner">
    <option name="skipTests" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 5
}</component>
  <component name="ProjectId" id="31hE2j9OHSOFHGqNWsF0mfXLKv1" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.crm-core-base [clean,install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crm-core-base [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.crm-core-base [install].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ism-core-base [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.ism-core-base [install].executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Project&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.15&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration default="true" type="JetRunConfigurationType">
      <module name="agis-core-base" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration default="true" type="KotlinStandaloneScriptRunConfigurationType">
      <module name="agis-core-base" />
      <option name="filePath" />
      <method v="2" />
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fdfe4dae3a2d-intellij.indexing.shared.core-IU-243.22562.218" />
        <option value="bundled-js-predefined-d6986cc7102b-deb605915726-JavaScript-IU-243.22562.218" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="4cc6f5a7-dd8f-4497-aac0-bb7985906410" name="Changes" comment="" />
      <created>1755967699750</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1755967699750</updated>
      <workItem from="1755967702043" duration="7120000" />
      <workItem from="1756026191035" duration="5210000" />
      <workItem from="1756225692698" duration="3668000" />
      <workItem from="1756234753502" duration="53000" />
      <workItem from="1756301213128" duration="643000" />
      <workItem from="1756302097913" duration="16819000" />
      <workItem from="1756556783469" duration="2725000" />
      <workItem from="1756630155754" duration="8447000" />
      <workItem from="1756648979571" duration="872000" />
      <workItem from="1756652095227" duration="1572000" />
      <workItem from="1756655126694" duration="5156000" />
      <workItem from="1756732161550" duration="624000" />
      <workItem from="1756739133025" duration="2656000" />
      <workItem from="1756814312198" duration="114000" />
      <workItem from="1756814435500" duration="1608000" />
      <workItem from="1757171386337" duration="18004000" />
      <workItem from="1757431926032" duration="4885000" />
      <workItem from="1757514098302" duration="5097000" />
      <workItem from="1757550560824" duration="239000" />
      <workItem from="1757858417546" duration="1566000" />
      <workItem from="1757862071938" duration="28654000" />
      <workItem from="1758292430338" duration="2628000" />
      <workItem from="1758298564621" duration="876000" />
      <workItem from="1758334145885" duration="706000" />
      <workItem from="1758336323889" duration="2453000" />
      <workItem from="1758364262616" duration="22355000" />
      <workItem from="1758718552000" duration="831000" />
      <workItem from="1758808345920" duration="608000" />
      <workItem from="1758822440580" duration="1994000" />
    </task>
    <task id="LOCAL-00001" summary="refactor code base">
      <option name="closed" value="true" />
      <created>1756030643097</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1756030643097</updated>
    </task>
    <task id="LOCAL-00002" summary="EMPLOYEE">
      <option name="closed" value="true" />
      <created>1756234616348</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1756234616348</updated>
    </task>
    <task id="LOCAL-00003" summary="EMPLOYEE">
      <option name="closed" value="true" />
      <created>1756234762601</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1756234762601</updated>
    </task>
    <task id="LOCAL-00004" summary="sửa entity project">
      <option name="closed" value="true" />
      <created>1756318344469</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1756318344469</updated>
    </task>
    <task id="LOCAL-00005" summary="căn hộ">
      <option name="closed" value="true" />
      <created>1756321564346</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1756321564346</updated>
    </task>
    <task id="LOCAL-00006" summary="api khách hàng + đổi parse gson message">
      <option name="closed" value="true" />
      <created>1756352066373</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1756352066373</updated>
    </task>
    <task id="LOCAL-00007" summary="thêm api check trùng project và unit">
      <option name="closed" value="true" />
      <created>1756631697070</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1756631697070</updated>
    </task>
    <task id="LOCAL-00008" summary="role and permission">
      <option name="closed" value="true" />
      <created>1756649245367</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1756649245367</updated>
    </task>
    <task id="LOCAL-00009" summary="employee">
      <option name="closed" value="true" />
      <created>1756660362776</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1756660362777</updated>
    </task>
    <task id="LOCAL-00010" summary="fix api check trùng employee">
      <option name="closed" value="true" />
      <created>1756663186519</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1756663186519</updated>
    </task>
    <task id="LOCAL-00011" summary="config">
      <option name="closed" value="true" />
      <created>1756818760295</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1756818760295</updated>
    </task>
    <task id="LOCAL-00012" summary="bổ sung DTO trả ra cho assignment">
      <option name="closed" value="true" />
      <created>1757178081995</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1757178081995</updated>
    </task>
    <task id="LOCAL-00013" summary="bổ sung DTO trả ra cho customer">
      <option name="closed" value="true" />
      <created>1757181777093</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1757181777093</updated>
    </task>
    <task id="LOCAL-00014" summary="fix api customer">
      <option name="closed" value="true" />
      <created>1757266699901</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1757266699901</updated>
    </task>
    <task id="LOCAL-00015" summary="fix api customer">
      <option name="closed" value="true" />
      <created>1757346773163</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1757346773163</updated>
    </task>
    <task id="LOCAL-00016" summary="api upload">
      <option name="closed" value="true" />
      <created>1757349125540</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1757349125540</updated>
    </task>
    <task id="LOCAL-00017" summary="fix assign for customer">
      <option name="closed" value="true" />
      <created>1757515216630</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1757515216630</updated>
    </task>
    <task id="LOCAL-00018" summary="api for assign">
      <option name="closed" value="true" />
      <created>1757550735053</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1757550735053</updated>
    </task>
    <task id="LOCAL-00019" summary="fix bug 12 xóa dự án">
      <option name="closed" value="true" />
      <created>1757863177864</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1757863177864</updated>
    </task>
    <task id="LOCAL-00020" summary="fix bug 12 xóa dự án">
      <option name="closed" value="true" />
      <created>1757863873768</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1757863873768</updated>
    </task>
    <task id="LOCAL-00021" summary="fix bug 13 xem danh sách dự án">
      <option name="closed" value="true" />
      <created>1757867142829</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1757867142829</updated>
    </task>
    <task id="LOCAL-00022" summary="fix bug 16">
      <option name="closed" value="true" />
      <created>1757869319998</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1757869319998</updated>
    </task>
    <task id="LOCAL-00023" summary="fix bug 18">
      <option name="closed" value="true" />
      <created>1757873217668</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1757873217668</updated>
    </task>
    <task id="LOCAL-00024" summary="fix bug 12, 22">
      <option name="closed" value="true" />
      <created>1757943467389</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1757943467389</updated>
    </task>
    <task id="LOCAL-00025" summary="fix bug 33">
      <option name="closed" value="true" />
      <created>1757950483563</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1757950483563</updated>
    </task>
    <task id="LOCAL-00026" summary="fix bug 42">
      <option name="closed" value="true" />
      <created>1757963474928</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1757963474928</updated>
    </task>
    <task id="LOCAL-00027" summary="fix bug 32">
      <option name="closed" value="true" />
      <created>1757982403795</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1757982403795</updated>
    </task>
    <task id="LOCAL-00028" summary="fix bug 24, 47 , seach theo ngày sinh nhật khách hàng">
      <option name="closed" value="true" />
      <created>1758126043119</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1758126043119</updated>
    </task>
    <task id="LOCAL-00029" summary="fix bug 25 26">
      <option name="closed" value="true" />
      <created>1758211403573</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1758211403573</updated>
    </task>
    <task id="LOCAL-00030" summary="fix bug 34">
      <option name="closed" value="true" />
      <created>1758212817439</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1758212817439</updated>
    </task>
    <task id="LOCAL-00031" summary="fix bug 39 - noti">
      <option name="closed" value="true" />
      <created>1758216501828</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1758216501828</updated>
    </task>
    <task id="LOCAL-00032" summary="fix bug 48">
      <option name="closed" value="true" />
      <created>1758217915835</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1758217915835</updated>
    </task>
    <task id="LOCAL-00033" summary="fix api import">
      <option name="closed" value="true" />
      <created>1758364812239</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1758364812239</updated>
    </task>
    <task id="LOCAL-00034" summary="fix api dry-run">
      <option name="closed" value="true" />
      <created>1758423837485</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1758423837485</updated>
    </task>
    <option name="localTasksCounter" value="35" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="CHECK_CODE_SMELLS_BEFORE_PROJECT_COMMIT" value="false" />
    <option name="CHECK_NEW_TODO" value="false" />
    <MESSAGE value="role and permission" />
    <MESSAGE value="employee" />
    <MESSAGE value="fix api check trùng employee" />
    <MESSAGE value="config" />
    <MESSAGE value="bổ sung DTO trả ra cho assignment" />
    <MESSAGE value="bổ sung DTO trả ra cho customer" />
    <MESSAGE value="fix api customer" />
    <MESSAGE value="api upload" />
    <MESSAGE value="fix assign for customer" />
    <MESSAGE value="api for assign" />
    <MESSAGE value="fix bug 12 xóa dự án" />
    <MESSAGE value="fix bug 13 xem danh sách dự án" />
    <MESSAGE value="fix bug 16" />
    <MESSAGE value="fix bug 18" />
    <MESSAGE value="fix bug 12, 22" />
    <MESSAGE value="fix bug 33" />
    <MESSAGE value="fix bug 42" />
    <MESSAGE value="fix bug 32" />
    <MESSAGE value="fix bug 24, 47 , seach theo ngày sinh nhật khách hàng" />
    <MESSAGE value="fix bug 25 26" />
    <MESSAGE value="fix bug 34" />
    <MESSAGE value="fix bug 39 - noti" />
    <MESSAGE value="fix bug 48" />
    <MESSAGE value="fix api import" />
    <MESSAGE value="fix api dry-run" />
    <option name="LAST_COMMIT_MESSAGE" value="fix api dry-run" />
  </component>
</project>