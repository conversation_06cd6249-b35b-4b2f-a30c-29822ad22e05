server:
  port: 8082
  servlet:
    context-path: /cmp
spring:
  rabbitmq:
    addresses: ************:5672, ************:5672, ************:5672
    port: 5672
    username: admin
    password: ssdc_cmp
  datasource:
    jdbc-url: ***************************************
    url: ***************************************
    username: coremgmt
    password: core!cmP#23
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      data-source-properties:
        stringtype: unspecified
  datasource-core:
    jdbc-url: ***************************************
    url: ***************************************
    username: cmp
    password: ssdc_cmp
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      data-source-properties:
        stringtype: unspecified
  datasource-report:
    jdbc-url: ***************************************
    url: ***************************************
    username: reporting
    password: report!cmP#23
    driver-class-name: oracle.jdbc.OracleDriver
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    database-platform: org.hibernate.dialect.OracleDialect
    use-new-id-generator-mappings: false
    show-sql: true
    hibernate:
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none
  redis:
#    password: ssdc_cmp
#    sentinel:
#      master: mymaster
#      nodes: ************:26379, ************:26379, ************:26379
    #    Standalone, Sentinel, Cluster
    mode: Sentinel
    standalone:
      host: ************
      port: 6379
      username: aaaa
      password: oneiot@2020
    cluster:
      nodes: *********:6380,*********:6381,*********:6385
      username: default
      password:
    sentinel:
      password: ssdc_cmp
      master: mymaster
      nodes: ************:26379, ************:26379, ************:26379
    jedis:
      pool:
        max-active: 1000
        min-idle: 0
        max-wait: -1
        max-idle: 10
  mail:
    host: mail.vnpt-technology.vn
    port: 25
    username: oneiot
    password: One@2024%^&
    protocol: smtp
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: -1
jhipster:
  mail: # specific JHipster mail property, for standard properties see MailProperties
    from: <EMAIL>
    base-url: http://cmp.oneiot.com.vn:6080/#/
ccbs:
  url-api:
application:
  activation:
    expirePeriodActiveMail: 2592000 #seconds - 30days
    expirePeriodResetPassword: 86400 #seconds - 1day
    enableMail: true #true/false
  timeAfterSoftDelete: 2592000
  tokenTime:
    remember: 2592000 #seconds - 30days
    noRemember: 1800 # seconds - 0.5h
baseUrl: http://localhost:9090
#nodeName: nghiepnv-core
folder:
  storage: ./storage
management:
  endpoints:
    web:
      exposure:
        include: health
  health:
    mail:
      enabled: false
  endpoint:
    health:
      probes:
        enabled: true


