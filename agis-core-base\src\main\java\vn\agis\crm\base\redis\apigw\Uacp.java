package vn.agis.crm.base.redis.apigw;

import java.util.List;

public class Uacp {
    private Long id;
    private Long userId;
    private String protocol;
    private Long maxTps;
    private List<String> commands;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public Long getMaxTps() {
        return maxTps;
    }

    public void setMaxTps(Long maxTps) {
        this.maxTps = maxTps;
    }

    public List<String> getCommands() {
        return commands;
    }

    public void setCommands(List<String> commands) {
        this.commands = commands;
    }
}
