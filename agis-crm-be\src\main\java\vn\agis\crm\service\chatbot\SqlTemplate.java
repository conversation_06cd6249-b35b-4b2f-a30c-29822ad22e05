package vn.agis.crm.service.chatbot;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SqlTemplate {
    private String id;
    private String description;
    private String sql;
    private List<ParameterConfig> parameters;
    private List<String> examples;

    // getters & setters
}
