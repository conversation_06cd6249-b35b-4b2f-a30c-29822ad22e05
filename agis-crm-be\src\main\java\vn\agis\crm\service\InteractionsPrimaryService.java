package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants.Method;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.InteractionPrimarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryUpdateDto;
import vn.agis.crm.base.jpa.entity.Employee;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;
import vn.agis.crm.base.utils.DateUtils;
import vn.agis.crm.repository.CustomerOfferRepository;
import vn.agis.crm.repository.EmployeeRepository;
import vn.agis.crm.repository.InteractionsPrimaryRepository;
import vn.agis.crm.service.mapper.InteractionsPrimaryMapper;

import javax.persistence.criteria.Predicate;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * Service layer for InteractionsPrimary entity
 * Handles business logic, validation, and data transformation
 */
@Service
@Transactional
public class InteractionsPrimaryService {

    private static final Logger logger = LoggerFactory.getLogger(InteractionsPrimaryService.class);
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("dd/MM/yyyy");

    @Autowired
    private InteractionsPrimaryRepository interactionsPrimaryRepository;

    @Autowired
    private CustomerOfferRepository customerOfferRepository;

    @Autowired
    private EmployeeRepository employeeRepository;

    @Autowired
    private InteractionsPrimaryMapper interactionsPrimaryMapper;

    /**
     * Main event processing method following the established pattern
     */
    public Event process(Event event) {
        try {
            switch (event.method) {
                case Method.CREATE:
                    return create(event);
                case Method.UPDATE:
                    return update(event);
                case Method.DELETE:
                    return delete(event);
                case Method.FIND_BY_ID:
                    return findById(event);
                case Method.SEARCH:
                    return search(event);
                case "FIND_BY_CUSTOMER_OFFER_ID":
                    return findByCustomerOfferId(event);
                case "COUNT_BY_CUSTOMER_OFFER_ID":
                    return countByCustomerOfferId(event);
                default:
                    return event.createResponse(null, ResponseCode.NOT_FOUND, "Method not found");
            }
        } catch (Exception e) {
            logger.error("Error processing InteractionsPrimary event: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Internal server error: " + e.getMessage());
        }
    }

    /**
     * Create new primary interaction
     */
    private Event create(Event event) {
        try {
            InteractionPrimaryCreateDto createDto = (InteractionPrimaryCreateDto) event.payload;
            
            // Validate customer offer exists
            if (!customerOfferRepository.existsById(createDto.getCustomerOfferId())) {
                return event.createResponse(null, ResponseCode.BAD_REQUEST, "Customer offer not found");
            }

            // Convert DTO to entity
            InteractionsPrimary entity = interactionsPrimaryMapper.toEntity(createDto);
            
            // Set audit fields
            entity.setCreatedAt(new Date());
            entity.setCreatedBy(event.userId);
            entity.setUpdatedAt(new Date());
            entity.setUpdatedBy(event.userId);

            // Save entity
            InteractionsPrimary saved = interactionsPrimaryRepository.save(entity);
            
            // Convert to response DTO
            InteractionPrimaryDto responseDto = interactionsPrimaryMapper.toDto(saved);
            
            logger.info("Created primary interaction with ID: {}", saved.getId());
            return event.createResponse(responseDto, ResponseCode.CREATED, "Primary interaction created successfully");
            
        } catch (Exception e) {
            logger.error("Error creating primary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error creating primary interaction: " + e.getMessage());
        }
    }

    /**
     * Update existing primary interaction
     */
    private Event update(Event event) {
        try {
            InteractionPrimaryUpdateDto updateDto = (InteractionPrimaryUpdateDto) event.payload;
            
            // Find existing entity
            Optional<InteractionsPrimary> existingOpt = interactionsPrimaryRepository.findById(updateDto.getId());
            if (!existingOpt.isPresent()) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Primary interaction not found");
            }

            InteractionsPrimary existing = existingOpt.get();
            
            // Validate customer offer exists
            if (!customerOfferRepository.existsById(updateDto.getCustomerOfferId())) {
                return event.createResponse(null, ResponseCode.BAD_REQUEST, "Customer offer not found");
            }

            // Update entity from DTO
            interactionsPrimaryMapper.updateEntityFromDto(existing, updateDto);
            
            // Set audit fields
            existing.setUpdatedAt(new Date());
            existing.setUpdatedBy(event.userId);

            // Save updated entity
            InteractionsPrimary saved = interactionsPrimaryRepository.save(existing);
            
            // Convert to response DTO
            InteractionPrimaryDto responseDto = interactionsPrimaryMapper.toDto(saved);
            
            logger.info("Updated primary interaction with ID: {}", saved.getId());
            return event.createResponse(responseDto, ResponseCode.OK, "Primary interaction updated successfully");
            
        } catch (Exception e) {
            logger.error("Error updating primary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error updating primary interaction: " + e.getMessage());
        }
    }

    /**
     * Delete primary interaction
     */
    private Event delete(Event event) {
        try {
            Long id = (Long) event.payload;
            
            if (!interactionsPrimaryRepository.existsById(id)) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Primary interaction not found");
            }

            interactionsPrimaryRepository.deleteById(id);
            
            logger.info("Deleted primary interaction with ID: {}", id);
            return event.createResponse(null, ResponseCode.OK, "Primary interaction deleted successfully");
            
        } catch (Exception e) {
            logger.error("Error deleting primary interaction: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error deleting primary interaction: " + e.getMessage());
        }
    }

    /**
     * Find primary interaction by ID
     */
    private Event findById(Event event) {
        try {
            Long id = (Long) event.payload;
            
            Optional<InteractionsPrimary> entityOpt = interactionsPrimaryRepository.findById(id);
            if (!entityOpt.isPresent()) {
                return event.createResponse(null, ResponseCode.NOT_FOUND, "Primary interaction not found");
            }

            InteractionPrimaryDto responseDto = interactionsPrimaryMapper.toDto(entityOpt.get());
            
            return event.createResponse(responseDto, ResponseCode.OK, "Primary interaction found");
            
        } catch (Exception e) {
            logger.error("Error finding primary interaction by ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error finding primary interaction: " + e.getMessage());
        }
    }

    /**
     * Search primary interactions with filters and pagination
     */
    private Event search(Event event) {
        try {
            InteractionPrimarySearchDto searchDto = (InteractionPrimarySearchDto) event.payload;
            
            // Build specification for filtering
            Specification<InteractionsPrimary> spec = buildSearchSpecification(searchDto);
            
            // Create pageable
            Pageable pageable = org.springframework.data.domain.PageRequest.of(
                searchDto.getPage(), 
                searchDto.getSize(),
                org.springframework.data.domain.Sort.by(parseSortBy(searchDto.getSortBy()))
            );
            
            // Execute search
            Page<InteractionsPrimary> page = interactionsPrimaryRepository.findAll(spec, pageable);
            
            // Convert to DTOs
            Page<InteractionPrimaryDto> responsePage = page.map(entity -> interactionsPrimaryMapper.toDto(entity));
            
            return event.createResponse(responsePage, ResponseCode.OK, "Search completed successfully");
            
        } catch (Exception e) {
            logger.error("Error searching primary interactions: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error searching primary interactions: " + e.getMessage());
        }
    }

    /**
     * Find primary interactions by customer offer ID
     */
    private Event findByCustomerOfferId(Event event) {
        try {
            Long customerOfferId = (Long) event.payload;
            
            List<InteractionsPrimary> entities = interactionsPrimaryRepository.findByCustomerOfferId(customerOfferId);
            List<InteractionPrimaryDto> responseDtos = entities.stream()
                .map(entity -> interactionsPrimaryMapper.toDto(entity))
                .collect(java.util.stream.Collectors.toList());
            
            return event.createResponse(responseDtos, ResponseCode.OK, "Primary interactions found");
            
        } catch (Exception e) {
            logger.error("Error finding primary interactions by customer offer ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error finding primary interactions: " + e.getMessage());
        }
    }

    /**
     * Count primary interactions by customer offer ID
     */
    private Event countByCustomerOfferId(Event event) {
        try {
            Long customerOfferId = (Long) event.payload;
            
            long count = interactionsPrimaryRepository.countByCustomerOfferId(customerOfferId);
            
            return event.createResponse(count, ResponseCode.OK, "Count completed successfully");
            
        } catch (Exception e) {
            logger.error("Error counting primary interactions by customer offer ID: {}", e.getMessage(), e);
            return event.createResponse(null, ResponseCode.INTERNAL_SERVER_ERROR, "Error counting primary interactions: " + e.getMessage());
        }
    }

    /**
     * Build JPA Specification for search filtering
     */
    private Specification<InteractionsPrimary> buildSearchSpecification(InteractionPrimarySearchDto searchDto) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // Filter by customer offer ID
            if (searchDto.getCustomerOfferId() != null) {
                predicates.add(criteriaBuilder.equal(root.get("customerOfferId"), searchDto.getCustomerOfferId()));
            }

            // Filter by result (case-insensitive partial match)
            if (searchDto.getResult() != null && !searchDto.getResult().trim().isEmpty()) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("result")), 
                    "%" + searchDto.getResult().toLowerCase() + "%"
                ));
            }

            // Filter by happened at date range
            if (searchDto.getHappenedAtFrom() != null || searchDto.getHappenedAtTo() != null) {
                try {
                    if (searchDto.getHappenedAtFrom() != null && searchDto.getHappenedAtTo() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getHappenedAtFrom());
                        Date toDate = dateFormat.parse(searchDto.getHappenedAtTo());
                        predicates.add(criteriaBuilder.between(root.get("happenedAt"), fromDate, toDate));
                    } else if (searchDto.getHappenedAtFrom() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getHappenedAtFrom());
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("happenedAt"), fromDate));
                    } else {
                        Date toDate = dateFormat.parse(searchDto.getHappenedAtTo());
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("happenedAt"), toDate));
                    }
                } catch (Exception e) {
                    logger.warn("Invalid date format in search criteria: {}", e.getMessage());
                }
            }

            // Filter by created by
            if (searchDto.getCreatedBy() != null) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), searchDto.getCreatedBy()));
            }

            // Filter by created at date range
            if (searchDto.getCreatedAtFrom() != null || searchDto.getCreatedAtTo() != null) {
                try {
                    if (searchDto.getCreatedAtFrom() != null && searchDto.getCreatedAtTo() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getCreatedAtFrom());
                        Date toDate = dateFormat.parse(searchDto.getCreatedAtTo());
                        predicates.add(criteriaBuilder.between(root.get("createdAt"), fromDate, toDate));
                    } else if (searchDto.getCreatedAtFrom() != null) {
                        Date fromDate = dateFormat.parse(searchDto.getCreatedAtFrom());
                        predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("createdAt"), fromDate));
                    } else {
                        Date toDate = dateFormat.parse(searchDto.getCreatedAtTo());
                        predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("createdAt"), toDate));
                    }
                } catch (Exception e) {
                    logger.warn("Invalid date format in created at search criteria: {}", e.getMessage());
                }
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Parse sort by string to Sort.Order array
     */
    private org.springframework.data.domain.Sort.Order[] parseSortBy(String sortBy) {
        if (sortBy == null || sortBy.trim().isEmpty()) {
            return new org.springframework.data.domain.Sort.Order[]{
                org.springframework.data.domain.Sort.Order.desc("happenedAt")
            };
        }

        String[] parts = sortBy.split(",");
        if (parts.length == 2) {
            String property = parts[0].trim();
            String direction = parts[1].trim();
            
            if ("desc".equalsIgnoreCase(direction)) {
                return new org.springframework.data.domain.Sort.Order[]{
                    org.springframework.data.domain.Sort.Order.desc(property)
                };
            } else {
                return new org.springframework.data.domain.Sort.Order[]{
                    org.springframework.data.domain.Sort.Order.asc(property)
                };
            }
        }

        return new org.springframework.data.domain.Sort.Order[]{
            org.springframework.data.domain.Sort.Order.desc("happenedAt")
        };
    }
}
