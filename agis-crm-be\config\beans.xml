<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:task="http://www.springframework.org/schema/task"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
	http://www.springframework.org/schema/context
	http://www.springframework.org/schema/context/spring-context-3.1.xsd
	http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.0.xsd">

    <context:component-scan base-package="com.cimit"/>
    <bean id="thread-pool" class="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor" scope="singleton">
        <property name="corePoolSize" value="5"/>
        <property name="maxPoolSize" value="128"/>
        <property name="keepAliveSeconds" value="10"/>
        <property name="threadNamePrefix" value="cache"/>
        <property name="WaitForTasksToCompleteOnShutdown" value="true"/>
    </bean>


<!--    <bean id="configMinIO2-1" class="vn.agis.crm.base.jpa.dto.dump.MinIOSyncConfig2">-->
<!--        <property name="typeFile" value="info"/>-->
<!--        <property name="bucketName" value="m2msim"/>-->
<!--        <property name="remoteFile" value="M2M_{ddMMyyyy}.txt.zip"/>-->
<!--        <property name="interval" value="0"/>-->
<!--        <property name="timeType" value="day"/>-->
<!--        <property name="localDir" value="/ftp/download/"/>-->
<!--    </bean>-->

<!--    <bean id="configMinIO2-2" class="vn.agis.crm.base.jpa.dto.dump.MinIOSyncConfig2">-->
<!--        <property name="typeFile" value="data"/>-->
<!--        <property name="bucketName" value="m2msim"/>-->
<!--        <property name="remoteFile" value="M2M_DAILY_{ddMMyyyy}.txt.gz"/>-->
<!--        <property name="interval" value="-1"/>-->
<!--        <property name="timeType" value="day"/>-->
<!--        <property name="localDir" value="/ftp/download/"/>-->
<!--    </bean>-->

<!--    <bean id="localConfig" class="vn.agis.crm.base.jpa.dto.dump.LocalSyncConfig">-->
<!--        <property name="batchSize" value="500"/>-->
<!--        <property name="backupDir" value="/ftp/backup/"/>-->
<!--        <property name="errorDir" value="/ftp/error/"/>-->
<!--        <property name="separator" value="\|\|"/>-->
<!--        <property name="startWithRow" value="1"/>-->
<!--    </bean>-->


<!--    <bean id="compensationScanDto" class="vn.agis.crm.base.jpa.dto.dump.CompensationScanDto">-->
<!--        <property name="bucketName" value="m2msim"/>-->
<!--        <property name="prefixName" value="M2M_DAILY_"/>-->
<!--        <property name="pattern" value="ddMMyyyy"/>-->
<!--        <property name="endWithName" value=".txt.gz"/>-->
<!--        <property name="numberOfDay" value="3"/>-->
<!--        <property name="localDir" value="/ftp/download/"/>-->
<!--        <property name="backupDir" value="/ftp/backup"/>-->
<!--        <property name="typeFile" value="data"/>-->
<!--    </bean>-->
<!--    <bean id="jobGetFileMinIO2-1" class="vn.agis.crm.dump.GetFileMinIO2">-->
<!--        <property name="configMinIO2" ref="configMinIO2-1"/>-->
<!--    </bean>-->

<!--    <bean id="jobGetFileMinIO2-2" class="vn.agis.crm.dump.GetFileMinIO2">-->
<!--        <property name="configMinIO2" ref="configMinIO2-2"/>-->
<!--    </bean>-->
<!--    <bean id="jobMonitoringFile-1" class="vn.agis.crm.dump.MonitoringFile">-->
<!--        <property name="configMinIO2" ref="configMinIO2-1"/>-->
<!--    </bean>-->

<!--    <bean id="jobMonitoringFile-2" class="vn.agis.crm.dump.MonitoringFile">-->
<!--        <property name="configMinIO2" ref="configMinIO2-2"/>-->
<!--    </bean>-->

<!--    <bean id="jobImportFile-1" class="vn.agis.crm.dump.ImportData">-->
<!--        <property name="localSyncConfig" ref="localConfig"/>-->
<!--        <property name="minIOSyncConfig2" ref="configMinIO2-1"/>-->
<!--    </bean>-->

<!--    <bean id="jobImportFile-2" class="vn.agis.crm.dump.ImportData">-->
<!--        <property name="localSyncConfig" ref="localConfig"/>-->
<!--        <property name="minIOSyncConfig2" ref="configMinIO2-2"/>-->
<!--    </bean>-->
<!--    <bean id="jobUpdateData-1" class="vn.agis.crm.dump.UpdateData">-->
<!--        <property name="configMinIO2" ref="configMinIO2-1"/>-->
<!--    </bean>-->

<!--    <bean id="jobUpdateData-2" class="vn.agis.crm.dump.UpdateData">-->
<!--        <property name="configMinIO2" ref="configMinIO2-2"/>-->
<!--    </bean>-->

<!--    <bean id="jobCompensationScan" class="vn.agis.crm.dump.JobCompensationScan">-->
<!--        <property name="compensationScanDto" ref="compensationScanDto"/>-->
<!--    </bean>-->

<!--    <bean id="sqlInfo" class="vn.agis.crm.base.jpa.dto.dump.SqlInfo">-->
<!--        <property name="mergeCustomer" value="MERGE INTO COREMGMT.CUSTOMER C-->
<!--                USING (-->
<!--                SELECT CUSTOMER_CODE, CUSTOMER_NAME, CUSTOMER_EMAIL, CASE WHEN TELLER_PHONE IS NULL OR REGEXP_REPLACE(TRIM(TELLER_PHONE), '[^0-9,+]+', '') IS NULL THEN '84000000000' ELSE REGEXP_REPLACE(TRIM(TELLER_PHONE), '[^0-9,+]+', '') END TELLER_PHONE , TELLER_ADDRESS, TELLER_CENTER_CODE, PAYMENT_ROUTE, CUSTOMER_BIRTHDAY,PROVINCE_CODE FROM (select tmp.*, ROW_NUMBER() over (PARTITION BY CUSTOMER_CODE ORDER BY CONTRACT_DATE DESC) AS rn-->
<!--                	FROM (-->
<!--                	SELECT CUSTOMER_CODE, CUSTOMER_NAME, CUSTOMER_EMAIL, TELLER_PHONE, TELLER_ADDRESS, TELLER_CENTER_CODE, PAYMENT_ROUTE, CUSTOMER_BIRTHDAY,PROVINCE_CODE, CONTRACT_DATE FROM (SELECT CUSTOMER_CODE, CUSTOMER_NAME, CUSTOMER_EMAIL, TELLER_PHONE, TELLER_ADDRESS, TELLER_CENTER_CODE, PAYMENT_ROUTE, CUSTOMER_BIRTHDAY, SUBSTR(CONTRACT_CODE, 1, 3) PROVINCE_CODE, TRUNC(CONTRACT_DATE) CONTRACT_DATE FROM COREMGMT.SIM_DATA_DUMP) GROUP BY CUSTOMER_CODE, CUSTOMER_NAME, CUSTOMER_EMAIL, TELLER_PHONE, TELLER_ADDRESS, TELLER_CENTER_CODE, PAYMENT_ROUTE, CUSTOMER_BIRTHDAY,PROVINCE_CODE, CONTRACT_DATE-->
<!--                	) tmp ) where rn = 1) S-->
<!--                ON (C.CUSTOMER_CODE = S.CUSTOMER_CODE)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET C.CUSTOMER_NAME = S.CUSTOMER_NAME, C.EMAIL = S.CUSTOMER_EMAIL, C.BIRTHDAY = S.CUSTOMER_BIRTHDAY, C.PHONE = S.TELLER_PHONE, C.PROVINCE_CODE = S.PROVINCE_CODE WHERE C.CUSTOMER_CODE = S.CUSTOMER_CODE-->
<!--                WHEN NOT MATCHED THEN-->
<!--                INSERT (CREATED_BY, CUSTOMER_CODE, CUSTOMER_NAME, EMAIL, PHONE, STATUS, CUSTOMER_TYPE, NOTE, BIRTHDAY, CREATED_DATE, C.PROVINCE_CODE)-->
<!--                VALUES (1, S.CUSTOMER_CODE, S.CUSTOMER_NAME, S.CUSTOMER_EMAIL, S.TELLER_PHONE, 1, 2, CONCAT('Địa chỉ liên hệ : ', CONCAT(S.TELLER_ADDRESS, CONCAT(', mã trung tâm : ', CONCAT(S.TELLER_CENTER_CODE, CONCAT(', mã tuyến : ', S.PAYMENT_ROUTE))))), S.CUSTOMER_BIRTHDAY,SYSDATE, S.PROVINCE_CODE )"/>-->
<!--        &lt;!&ndash;        <property name="mergeCustomer" value="delete from coremgmt.log_dump where type_file = 'data'"/>&ndash;&gt;-->
<!--        <property name="mergeContract" value="MERGE INTO COREMGMT.CONTRACT CT-->
<!--                USING (SELECT CUSTOMER_ID, CONTRACT_CODE, TELLER_CENTER_CODE, CUSTOMER_NAME, CASE WHEN TELLER_PHONE IS NULL OR REGEXP_REPLACE(TRIM(TELLER_PHONE), '[^0-9,+]+', '') IS NULL THEN '84000000000' ELSE REGEXP_REPLACE(TRIM(TELLER_PHONE), '[^0-9,+]+', '') END TELLER_PHONE , TELLER_ADDRESS, CUSTOMER_BIRTHDAY, PAYMENT_NAME, PAYMENT_ADDRESS, PAYMENT_ROUTE, CONTRACT_DATE FROM (SELECT CUSTOMER_ID, CONTRACT_CODE, TELLER_CENTER_CODE, CUSTOMER_NAME, TELLER_PHONE, TELLER_ADDRESS, CUSTOMER_BIRTHDAY, PAYMENT_NAME, PAYMENT_ADDRESS, PAYMENT_ROUTE, CONTRACT_DATE, ROW_NUMBER()-->
<!--                   OVER (PARTITION BY CONTRACT_CODE ORDER BY CONTRACT_DATE DESC) AS rn-->
<!--                   FROM (SELECT CO.ID CUSTOMER_ID, SD.CONTRACT_CODE CONTRACT_CODE, SD.TELLER_CENTER_CODE, SD.CUSTOMER_NAME, SD.TELLER_PHONE , SD.TELLER_ADDRESS, SD.CUSTOMER_BIRTHDAY, SD.PAYMENT_NAME, SD.PAYMENT_ADDRESS, SD.PAYMENT_ROUTE, SD.CONTRACT_DATE-->
<!--                FROM COREMGMT.CUSTOMER CO JOIN COREMGMT.SIM_DATA_DUMP SD ON CO.CUSTOMER_CODE = SD.CUSTOMER_CODE-->
<!--                GROUP BY CO.ID, SD.CONTRACT_CODE, SD.TELLER_CENTER_CODE, SD.CUSTOMER_NAME, SD.TELLER_PHONE , SD.TELLER_ADDRESS, SD.CUSTOMER_BIRTHDAY, SD.PAYMENT_NAME, SD.PAYMENT_ADDRESS, SD.PAYMENT_ROUTE, SD.CONTRACT_DATE) )WHERE rn = 1) C-->
<!--                ON (CT.CONTRACT_CODE= C.CONTRACT_CODE)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET CT.CENTER_CODE = C.TELLER_CENTER_CODE, CT.CUSTOMER_NAME = C.CUSTOMER_NAME, CT.CONTACT_PHONE = C.TELLER_PHONE, CT.CONTACT_ADDRESS = C.TELLER_ADDRESS, CT.CONTACT_BIRTHDAY = C.CONTRACT_DATE, CT.PAYMENT_NAME = C.PAYMENT_NAME, CT.PAYMENT_ADDRESS = C.PAYMENT_ADDRESS, CT.ROUTE_CODE = C.PAYMENT_ROUTE, CT.CUSTOMER_ID = C.CUSTOMER_ID WHERE CT.CONTRACT_CODE= C.CONTRACT_CODE-->
<!--                WHEN NOT MATCHED THEN-->
<!--                INSERT (CONTRACT_CODE, CENTER_CODE, CUSTOMER_NAME, CONTACT_PHONE, CONTACT_ADDRESS, PAYMENT_NAME, PAYMENT_ADDRESS, ROUTE_CODE, CONTRACT_DATE, CUSTOMER_ID, CREATED_BY, CREATED_DATE)-->
<!--                VALUES (C.CONTRACT_CODE, C.TELLER_CENTER_CODE, C.CUSTOMER_NAME, C.TELLER_PHONE, C.TELLER_ADDRESS, C.PAYMENT_NAME, C.PAYMENT_ADDRESS, C.PAYMENT_ROUTE, C.CONTRACT_DATE, C.CUSTOMER_ID, 1, SYSDATE)-->

<!--                "/>-->
<!--        <property name="mergeRatingPlan" value="MERGE INTO COREMGMT.RATING_PLAN RP-->
<!--                USING (SELECT PKG_CODE, PKG_DOCUMENT, PKG_PRICE, PKG_NAME, PKG_CYCLE, DATACAPACITY, FREE_INTRANET_SMS, FREE_INTERNET_SMS-->
<!--                FROM (-->
<!--                	SELECT PKG_CODE, PKG_DOCUMENT, PKG_PRICE, PKG_NAME, PKG_CYCLE, DATACAPACITY, FREE_INTRANET_SMS, FREE_INTERNET_SMS , ROW_NUMBER()-->
<!--                   OVER (PARTITION BY PKG_CODE ORDER BY DATACAPACITY DESC)  AS RN-->
<!--                   FROM (SELECT PKG_CODE, PKG_DOCUMENT, PKG_PRICE, PKG_NAME, PKG_CYCLE, DATACAPACITY, FREE_INTRANET_SMS, FREE_INTERNET_SMS FROM COREMGMT.SIM_DATA_DUMP WHERE PKG_CODE IS NOT NULL AND PKG_NAME IS NOT NULL GROUP BY PKG_CODE, PKG_DOCUMENT, PKG_PRICE, PKG_NAME, PKG_CYCLE, DATACAPACITY, FREE_INTRANET_SMS, FREE_INTERNET_SMS)-->
<!--                ) WHERE RN = 1) SD-->
<!--                ON (RP.CODE = SD.PKG_CODE)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET RP.NAME = SD.PKG_NAME,  RP.CYCLE_INTERVAL = SD.PKG_CYCLE, RP.DISPATCH_CODE = SD.PKG_DOCUMENT, RP.SUBSCRIPTION_FEE = SD.PKG_PRICE, RP.LIMIT_DATA_USAGE = SD.DATACAPACITY, RP.LIMIT_SMS_INSIDE = SD.FREE_INTRANET_SMS, RP.LIMIT_SMS_OUTSIDE = SD.FREE_INTERNET_SMS WHERE RP.CODE = SD.PKG_CODE-->
<!--                WHEN NOT MATCHED THEN-->
<!--                INSERT (CODE, NAME, CYCLE_INTERVAL ,STATUS, CREATED_DATE, CREATED_BY, DISPATCH_CODE, SUBSCRIPTION_FEE, LIMIT_DATA_USAGE, LIMIT_SMS_INSIDE, LIMIT_SMS_OUTSIDE, RATING_SCOPE)-->
<!--                VALUES (SD.PKG_CODE, SD.PKG_NAME, SD.PKG_CYCLE, 3, SYSDATE, 1, PKG_DOCUMENT, PKG_PRICE, DATACAPACITY, FREE_INTRANET_SMS, FREE_INTERNET_SMS,0)-->
<!--                "/>-->
<!--        <property name="updateRatingPlanId" value="UPDATE COREMGMT.SIM_DATA_DUMP SD-->
<!--                SET SD.RATING_PLAN_ID = (SELECT-->
<!--                RP.ID-->
<!--                FROM-->
<!--                	COREMGMT.RATING_PLAN RP-->
<!--                WHERE-->
<!--                	RP.CODE = SD.PKG_CODE )-->
<!--                WHERE-->
<!--                	SD.RATING_PLAN_ID IS NULL"/>-->
<!--        <property name="mergeSim" value="MERGE INTO SIMMGMT.SIM S-->
<!--                USING (-->
<!--                SELECT MSISDN, IMSI, STATUS, RATING_PLAN_ID, PKG_NAME,  PROVINCE_CODE, CUSTOMER_NAME, CUSTOMER_CODE, CONTRACT_CODE, CONTRACT_DATE, PKG_CODE-->
<!--                    FROM (-->
<!--                        SELECT MSISDN, IMSI, STATUS, RATING_PLAN_ID, PKG_NAME, PROVINCE_CODE, CUSTOMER_NAME, CUSTOMER_CODE, CONTRACT_CODE, CONTRACT_DATE, PKG_CODE, ROW_NUMBER() OVER (PARTITION BY MSISDN ORDER BY CONTRACT_DATE DESC)  AS RN-->
<!--                       FROM (SELECT MSISDN, IMSI, STATUS, RATING_PLAN_ID, PKG_NAME, SUBSTR(CONTRACT_CODE, 1, 3) PROVINCE_CODE, CUSTOMER_NAME, CUSTOMER_CODE, CONTRACT_CODE, CONTRACT_DATE, PKG_CODE FROM COREMGMT.SIM_DATA_DUMP GROUP BY MSISDN, IMSI, STATUS, RATING_PLAN_ID, PKG_NAME, SUBSTR(CONTRACT_CODE, 1, 3) , CUSTOMER_NAME, CUSTOMER_CODE, CONTRACT_CODE, CONTRACT_DATE, PKG_CODE)-->
<!--                    ) WHERE RN = 1-->
<!--                ) SD ON (S.MSISDN = SD.MSISDN)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET S.IMSI=SD.IMSI, S.STATUS=SD.STATUS + 1, S.RATING_PLAN_ID=SD.RATING_PLAN_ID, S.RATING_PLAN_NAME=SD.PKG_NAME, S.UPDATED_BY = 1, S.PROVINCE_CODE=SD.PROVINCE_CODE, S.CUSTOMER_NAME=SD.CUSTOMER_NAME, S.CUSTOMER_CODE=SD.CUSTOMER_CODE,-->
<!--                S.CONTRACT_CODE=SD.CONTRACT_CODE, S.CONTRACT_DATE=SD.CONTRACT_DATE, S.RATING_PLAN_CODE = SD.PKG_CODE,-->
<!--                S.UPDATED_DATE = CASE WHEN SD.STATUS = 4 THEN S.UPDATED_DATE ELSE SYSDATE END-->
<!--                WHEN NOT MATCHED THEN-->
<!--                INSERT (S.IMSI, S.MSISDN, S.STATUS, S.RATING_PLAN_ID, S.RATING_PLAN_NAME, S.NEXT_RATING_PLAN_ID, S.UPDATED_DATE, S.UPDATED_BY, S.CREATED_DATE,S.CREATED_BY, S.PROVINCE_CODE, S.CUSTOMER_NAME, S.CUSTOMER_CODE, S.CONTRACT_CODE,S.CONTRACT_DATE, S.RATING_PLAN_CODE, S.HANDLE_STATUS)-->
<!--                VALUES (SD.IMSI, SD.MSISDN, SD.STATUS + 1, SD.RATING_PLAN_ID, SD.PKG_NAME, SD.RATING_PLAN_ID, SYSDATE, 1, SYSDATE, 1, SD.PROVINCE_CODE, SD.CUSTOMER_NAME, SD.CUSTOMER_CODE, SD.CONTRACT_CODE, SD.CONTRACT_DATE, SD.PKG_CODE, 0)-->
<!--                "/>-->
<!--        <property name="updateSimTicket"-->
<!--                  value="update COREMGMT.SIM_TICKET set STATUS = 2, ACTIVED_DATE = SYSDATE WHERE imsi in (select imsi from COREMGMT.SIM_DATA_DUMP WHERE STATUS = 1 or STATUS = 2)"/>-->
<!--    </bean>-->
<!--    &lt;!&ndash; bean cho stagging agis tech &ndash;&gt;-->

<!--        <bean id="sqlData" class="vn.agis.crm.base.jpa.dto.dump.SqlData">-->
<!--            <property name="updateSimDataDaily" value="MERGE INTO COREMGMT.SIM_DATA_DAILY SD1-->
<!--                    USING (SELECT MSISDN, CUSTOMER_CODE, CUSTOMER_NAME, PROVINCE_CODE, CONTRACT_CODE FROM SIMMGMT.SIM) SD2-->
<!--                    ON (SD1.MSISDN = SD2.MSISDN)-->
<!--                    WHEN MATCHED THEN-->
<!--                    UPDATE SET SD1.CUSTOMER_CODE = SD2.CUSTOMER_CODE, SD1.CUSTOMER_NAME = SD2.CUSTOMER_NAME, SD1.PROVINCE_CODE = SD2.PROVINCE_CODE, SD1.CONTRACT_CODE = SD2.CONTRACT_CODE-->
<!--                    "/>-->
<!--            <property name="updateDailyUsageReport" value="MERGE INTO REPORTING.DAILY_USAGE_REPORT R-->
<!--                    USING COREMGMT.SIM_DATA_DAILY@R_DB141 SD ON (R.MSISDN = SD.MSISDN AND TRUNC(R.DAY) = TRUNC(SD.DAY))-->
<!--                    WHEN MATCHED THEN-->
<!--                    UPDATE SET R.USAGE_DATA= SD.DAILY_DATA_USED, R.USAGE_SMS= SD.DAILY_SMS_USED, R.CUSTOMER_CODE= SD.CUSTOMER_CODE, R.CUSTOMER_NAME= SD.CUSTOMER_NAME, R.PROVINCE_CODE= SD.PROVINCE_CODE, R.CONTRACT_CODE= SD.CONTRACT_CODE  WHERE R.MSISDN = SD.MSISDN AND TRUNC(R.DAY) = TRUNC(SD.DAY)-->
<!--                    WHEN NOT MATCHED THEN-->
<!--                    INSERT (R.MSISDN, R.USAGE_DATA, R.USAGE_SMS, R.CUSTOMER_CODE, R.CUSTOMER_NAME, R.PROVINCE_CODE, R.DAY, R.CONTRACT_CODE)-->
<!--                    VALUES (SD.MSISDN, SD.DAILY_DATA_USED, SD.DAILY_SMS_USED, SD.CUSTOMER_CODE, SD.CUSTOMER_NAME, SD.PROVINCE_CODE, SD.DAY, SD.CONTRACT_CODE)-->
<!--                    "/>-->
<!--            <property name="updateDailyDataUsedInMonth" value="MERGE INTO COREMGMT.SIM_DATA_DAILY@R_DB141 SD-->
<!--                    USING (SELECT MSISDN, SUM(USAGE_DATA) DATA_INMONTH, SUM(USAGE_SMS) SMS_INMONTH FROM DAILY_USAGE_REPORT WHERE EXTRACT(MONTH FROM DAY) = EXTRACT(MONTH FROM (SYSDATE - 1))-->
<!--                                                    AND EXTRACT(YEAR FROM DAY) = EXTRACT(YEAR FROM (SYSDATE - 1)) GROUP BY MSISDN) TMP-->
<!--                                                    ON (SD.MSISDN = TMP.MSISDN)-->
<!--                                                    WHEN MATCHED THEN-->
<!--                                                    UPDATE SET SD.TOTAL_DATA_INMONTH = TMP.DATA_INMONTH, SD.TOTAL_SMS_INMONTH = TMP.SMS_INMONTH-->
<!--                    "/>-->
<!--            <property name="mergeSimData" value="MERGE INTO SIMMGMT.SIM S-->
<!--                    USING (SELECT MSISDN,TOTAL_DATA_INMONTH,TOTAL_SMS_INMONTH, ACTIVE_DATE FROM   COREMGMT.SIM_DATA_DAILY) SD-->
<!--                    ON (S.MSISDN = SD.MSISDN)-->
<!--                    WHEN MATCHED THEN-->
<!--                    UPDATE SET S.USAGED_DATA=SD.TOTAL_DATA_INMONTH, S.USAGED_SMS=SD.TOTAL_SMS_INMONTH, S.ACTIVATED_DATE = SD.ACTIVE_DATE"/>-->
<!--        </bean>-->



<!--    &lt;!&ndash; bean cho production &ndash;&gt;-->
<!--    <bean id="sqlData" class="vn.agis.crm.base.jpa.dto.dump.SqlData">-->
<!--        <property name="updateSimDataDaily" value="MERGE INTO COREMGMT.SIM_DATA_DAILY SD1-->
<!--                USING (SELECT MSISDN, CUSTOMER_CODE, CUSTOMER_NAME, PROVINCE_CODE, CONTRACT_CODE FROM SIMMGMT.SIM) SD2-->
<!--                ON (SD1.MSISDN = SD2.MSISDN)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET SD1.CUSTOMER_CODE = SD2.CUSTOMER_CODE, SD1.CUSTOMER_NAME = SD2.CUSTOMER_NAME, SD1.PROVINCE_CODE = SD2.PROVINCE_CODE, SD1.CONTRACT_CODE = SD2.CONTRACT_CODE-->
<!--                "/>-->
<!--        <property name="updateDailyUsageReport" value="MERGE INTO REPORTING.DAILY_USAGE_REPORT R-->
<!--                USING COREMGMT.SIM_DATA_DAILY SD ON (R.MSISDN = SD.MSISDN AND TRUNC(R.DAY) = TRUNC(SD.DAY))-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET R.USAGE_DATA= SD.DAILY_DATA_USED, R.USAGE_SMS= SD.DAILY_SMS_USED, R.CUSTOMER_CODE= SD.CUSTOMER_CODE, R.CUSTOMER_NAME= SD.CUSTOMER_NAME, R.PROVINCE_CODE= SD.PROVINCE_CODE, R.CONTRACT_CODE = SD.CONTRACT_CODE WHERE R.MSISDN = SD.MSISDN AND TRUNC(R.DAY) = TRUNC(SD.DAY)-->
<!--                WHEN NOT MATCHED THEN-->
<!--                INSERT (R.MSISDN, R.USAGE_DATA, R.USAGE_SMS, R.CUSTOMER_CODE, R.CUSTOMER_NAME, R.PROVINCE_CODE, R.DAY, R.CONTRACT_CODE)-->
<!--                VALUES (SD.MSISDN, SD.DAILY_DATA_USED, SD.DAILY_SMS_USED, SD.CUSTOMER_CODE, SD.CUSTOMER_NAME, SD.PROVINCE_CODE, SD.DAY, SD.CONTRACT_CODE)-->
<!--                "/>-->
<!--        <property name="updateDailyDataUsedInMonth" value="MERGE INTO COREMGMT.SIM_DATA_DAILY SD-->
<!--                USING (SELECT MSISDN, SUM(USAGE_DATA) DATA_INMONTH, SUM(USAGE_SMS) SMS_INMONTH FROM REPORTING.DAILY_USAGE_REPORT WHERE EXTRACT(MONTH FROM DAY) = EXTRACT(MONTH FROM (SYSDATE - 1))-->
<!--                                                AND EXTRACT(YEAR FROM DAY) = EXTRACT(YEAR FROM (SYSDATE - 1)) GROUP BY MSISDN) TMP-->
<!--                                                ON (SD.MSISDN = TMP.MSISDN)-->
<!--                                                WHEN MATCHED THEN-->
<!--                                                UPDATE SET SD.TOTAL_DATA_INMONTH = TMP.DATA_INMONTH, SD.TOTAL_SMS_INMONTH = TMP.SMS_INMONTH-->
<!--                "/>-->
<!--        <property name="mergeSimData" value="MERGE INTO SIMMGMT.SIM S-->
<!--                USING (SELECT MSISDN,TOTAL_DATA_INMONTH,TOTAL_SMS_INMONTH, ACTIVE_DATE FROM   COREMGMT.SIM_DATA_DAILY) SD-->
<!--                ON (S.MSISDN = SD.MSISDN)-->
<!--                WHEN MATCHED THEN-->
<!--                UPDATE SET S.USAGED_DATA=SD.TOTAL_DATA_INMONTH, S.USAGED_SMS=SD.TOTAL_SMS_INMONTH, S.ACTIVATED_DATE = SD.ACTIVE_DATE"/>-->
<!--    </bean>-->
</beans>
