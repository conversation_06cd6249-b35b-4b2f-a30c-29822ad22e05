package vn.agis.crm.base.jpa.entity;

import lombok.Data;


import jakarta.persistence.*;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import vn.agis.crm.base.utils.JsonArrayToStringDeserializer;

import com.fasterxml.jackson.annotation.JsonFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Entity
@Table(name = "subscription")
public class Subscription {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id1")
    private Long id1;

    @Column(name = "id")
    private Long id;

    @Column(name = "sme_id")
    private Long smeId;

    @Column(name = "service_type", length = 50)
    private String serviceType;

    @Column(name = "pricing_id")
    private Integer pricingId;

    @Column(name = "pricing_type")
    private Byte pricingType;

    @Column(name = "multi_plan_id")
    private Integer multiPlanId;

    @Column(name = "pricing_code", length = 50)
    private String pricingCode;

    @Column(name = "pricing_name", length = 100)
    private String pricingName;

    @Column(name = "period")
    private Integer period;

    @Column(name = "period_type", length = 20)
    private String periodType;

    @Column(name = "number_of_period")
    private Integer numberOfPeriod;

    @Column(name = "quantity")
    private Integer quantity;

    @Column(name = "unit_currency_id")
    private Integer unitCurrencyId;

    @Column(name = "unit_currency_name", length = 10)
    private String unitCurrencyName;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    @Column(name = "effective_time")
    private Date effectiveTime;

    @Column(name = "used_quantity")
    private Integer usedQuantity;

    @Column(name = "type", length = 20)
    private String type;

    @Column(name = "amount", precision = 15, scale = 2)
    private BigDecimal amount;

    @Column(name = "status", length = 20)
    private String status;

    @Column(name = "cancel_at", length = 50)
    private String cancelAt;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    @Column(name = "payment_date")
    private Date paymentDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "dd/MM/yyyy")
    @Column(name = "start_date")
    private Date startDate;

    @JsonDeserialize(using = JsonArrayToStringDeserializer.class)
    @Column(name = "addons")
    private String addons;

    @JsonDeserialize(using = JsonArrayToStringDeserializer.class)
    @Column(name = "coupons")
    private String coupons;

    @JsonDeserialize(using = JsonArrayToStringDeserializer.class)
    @Column(name = "tax")
    private String tax;

    @JsonDeserialize(using = JsonArrayToStringDeserializer.class)
    @Column(name = "econtract")
    private String econtract;

    @JsonDeserialize(using = JsonArrayToStringDeserializer.class)
    @Column(name = "custom_fields")
    private String customFields;

    @Column(name = "created_at")
    private Date createdAt;
}
