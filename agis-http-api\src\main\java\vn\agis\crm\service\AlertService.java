package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.errors.BaseException;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.constants.AMQPConstants;
import vn.agis.crm.base.exception.type.BadRequestException;
import vn.agis.crm.base.exception.type.ForbiddenException;
import vn.agis.crm.base.exception.type.NotFoundException;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.dto.req.*;
import vn.agis.crm.base.jpa.dto.resp.*;
import vn.agis.crm.base.jpa.entity.Alert;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.response.ResponseBase;
import vn.agis.crm.util.RequestUtils;

import java.util.ArrayList;
import java.util.List;

import static vn.agis.crm.base.constants.Constants.Method.*;

@Service
public class AlertService extends CrudService<Alert, Long> {
    private Logger logger = LoggerFactory.getLogger(AlertService.class);
    private static final String objectKey = "Alert";

    public AlertService() {
        super(Alert.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_RULE_MGMT;
        this.category = Constants.Category.RULE;
    }

    public Page<AlertFullNameResponse> searchAlert(SearchAlertReqDTO searchDTO, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<AlertFullNameResponse> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(JpaConstants.Method.SEARCH, category, searchDTO, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), AlertFullNameResponse.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), AlertFullNameResponse.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        } finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(searchDTO), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public DetailAlertResponse getAlertDetail(Long id) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        DetailAlertResponse response = new DetailAlertResponse();
        try {
            event = RequestUtils.amqp(DETAIL_ALERT, category, id, routingKey);
            if (event.respStatusCode == ResponseCode.OK && event.payload != null) {
                response = (DetailAlertResponse) event.payload;
            }
            else if(event.respStatusCode == ResponseCode.NOT_FOUND) throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
            else if(event.respStatusCode == ResponseCode.FORBIDDEN) throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.FORBIDDEN);
            return response;
        } finally {
//            TransactionLogger.writeLogITrans(id, response.getName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    null, ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public Alert createAlert(CreateAlertReq createAlertReq) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        Alert response = new Alert();
        try {
            event = RequestUtils.amqp(CREATED_ALERT, category, createAlertReq, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Alert) event.payload;
            }
            return response;
        } finally {
//            TransactionLogger.writeLogITrans(response.getId(), response.getAlertName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(createAlertReq), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public Alert update(UpdateAlertReq createAlertReq) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        Alert response = new Alert();
        try {
            event = RequestUtils.amqp(UPDATE_ALERT, category, createAlertReq, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Alert) event.payload;
            }
            else if(event.respStatusCode == ResponseCode.BAD_REQUEST)  throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
            return response;
        }finally {
//            TransactionLogger.writeLogITrans(response.getId(), response.getAlertName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(createAlertReq), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }

    public ResponseBase deleteAlert(Long id) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;
        Event event = RequestUtils.amqp(DELETE_ALERT, category, id, routingKey);
        ResponseBase responseBase = new ResponseBase();
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            Alert alert = (Alert) event.payload;
            responseBase.setErrorCode(event.respStatusCode);
            responseBase.setErrorMsg(event.respErrorDesc);
//            TransactionLogger.writeLogITrans(id, alert.getAlertName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    null, ObjectMapperUtil.toJsonString(responseBase), null, null, event);
            return responseBase;
        } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND) {
            exception =  new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(id, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    String.valueOf(id), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        }
        return null;
    }

    public ResponseBase changeStatusAlert(ChangeStatusAlertReqDTO changeStatusAlertReqDTO) {
        Long timeRequest = System.currentTimeMillis();
        BaseException exception;
        Event event = RequestUtils.amqp(CHANGE_STATUS_ALERT, category, changeStatusAlertReqDTO, routingKey);
        ResponseBase responseBase = new ResponseBase();
        if (event.respStatusCode.intValue() == ResponseCode.OK) {
            Alert alert = (Alert) event.payload;
            responseBase.setErrorCode(event.respStatusCode);
            responseBase.setErrorMsg(event.respErrorDesc);
//            TransactionLogger.writeLogITrans(changeStatusAlertReqDTO.getId(), alert.getAlertName(), objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(changeStatusAlertReqDTO), ObjectMapperUtil.toJsonString(responseBase), null, null, event);
            return responseBase;
        } else if (event.respStatusCode.intValue() == ResponseCode.NOT_FOUND) {
            exception =  new NotFoundException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.NOT_FOUND);
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(changeStatusAlertReqDTO), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        } else if (event.respStatusCode.intValue() == ResponseCode.BAD_REQUEST) {
            exception =  new BadRequestException(event.respErrorDesc, category, (String) event.payload, MessageKeyConstant.BAD_REQUEST);
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                ObjectMapperUtil.toJsonString(changeStatusAlertReqDTO), ObjectMapperUtil.toJsonString(ResponseDataConfiguration.error(null, new ExceptionTranslator().setException(exception), HttpStatus.NOT_FOUND).getBody()), null, null, event);
            throw exception;
        }
        return null;
    }

    public Integer checkExist(String name) {
        Event event = RequestUtils.amqp(CHECK_EXIST_ALERT, category, name, routingKey);
        if (event.respStatusCode == ResponseCode.OK) {
            return (Integer) event.payload;
        }
        return null;
    }

    public Page<AlertHistoryResponseDTO> searchHistory(SearchHistoryAlertRequest request, Pageable pageable) {
        Long timeRequest = System.currentTimeMillis();
        Event event = null;
        List<AlertHistoryResponseDTO> response = new ArrayList<>();
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH_ALERT_HISTORY, category, request, routingKey);
            if (event.respStatusCode == ResponseCode.OK) {
                PageInfo pageInfo = (PageInfo) event.payload;
                response = ObjectMapperUtil.listMapper(pageInfo.getData(), AlertHistoryResponseDTO.class);
                return new PageImpl<>(ObjectMapperUtil.listMapper(pageInfo.getData(), AlertHistoryResponseDTO.class), pageable, pageInfo.getTotalCount());
            }
            return null;
        }finally {
//            TransactionLogger.writeLogITrans(null, null, objectKey, null, timeRequest, System.currentTimeMillis(),
//                    ObjectMapperUtil.toJsonString(request), ObjectMapperUtil.toJsonString(response), null, null, event);
        }
    }
}
