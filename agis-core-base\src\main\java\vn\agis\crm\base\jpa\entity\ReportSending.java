package vn.agis.crm.base.jpa.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Table(name = "REPORT_SENDING")
@Entity
@Data
public class ReportSending extends AbstractEntity{
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;
    @Column(name = "REPORT_CONFIG_ID")
    private Long reportConfigId;
    @Column(name = "EMAIL_SUBJECT")
    private String emailSubject;
    @Column(name = "EMAILS")
    private String emails;
    @Column(name = "SCHEDULE")
    private String schedule;
    @Column(name = "UPDATED_DATE")
    private Date updatedDate;
    @Column(name = "UPDATED_BY")
    private Long updatedBy;
    @Transient
    private List<EmailGroup> emailGroups;

}
