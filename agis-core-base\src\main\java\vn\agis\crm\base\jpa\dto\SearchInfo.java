package vn.agis.crm.base.jpa.dto;

import org.springframework.data.domain.Pageable;

public class SearchInfo {
    private String query;
    private String resultField = "";
    private int pageNumber;
    private int pageSize;
    private String orders;

    public SearchInfo(){

    }

    public SearchInfo(String query, Pageable pageable){
        this.query = query;
        this.pageNumber = pageable.getPageNumber();
        this.pageSize = pageable.getPageSize();
        if(pageable != null && pageable.getSort() != null) {
            this.orders = pageable.getSort().toString();
        }else this.orders = "";
    }
    public SearchInfo(String query, String resultField, Pageable pageable){
        this.query = query;
        this.resultField = resultField;
        this.pageNumber = pageable.getPageNumber();
        this.pageSize = pageable.getPageSize();
        if(pageable != null && pageable.getSort() != null) {
            this.orders = pageable.getSort().toString();
        }else this.orders = "";
    }


    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public int getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(int pageNumber) {
        this.pageNumber = pageNumber;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public String getOrders() {
        return orders;
    }

    public void setOrders(String orders) {
        this.orders = orders;
    }

    public String getResultField() {
        return resultField;
    }

    public void setResultField(String resultField) {
        this.resultField = resultField;
    }
}
