package vn.agis.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import io.swagger.v3.oas.annotations.Parameter;
import vn.agis.crm.base.jpa.dto.NotificationDto;
import vn.agis.crm.base.jpa.dto.NotificationSearchDto;
import vn.agis.crm.base.jpa.entity.Notifications;
import vn.agis.crm.constant.SwaggerConstant;
import vn.agis.crm.service.NotificationService;

@RestController
@RequestMapping("/notification-mgmt")
public class NotificationController extends CrudController<Notifications, Long> {

    NotificationService notificationService;

    @Autowired
    public NotificationController(NotificationService service) {
        super(service);
        this.notificationService = service;
        this.baseUrl = "/notification-mgmt";
    }

    @GetMapping("/search")
    public ResponseEntity<Page<NotificationDto>> getPageNotifications(
        @RequestParam(name = "targetEmployeeId", required = false) Long targetEmployeeId,
        @RequestParam(name = "targetCustomerId", required = false) Long targetCustomerId,
        @RequestParam(name = "type", required = false) Integer type,
        @RequestParam(name = "title", required = false, defaultValue = "") String title,
        @RequestParam(name = "content", required = false, defaultValue = "") String content,
        @RequestParam(name = "isRead", required = false) Boolean isRead,
        @RequestParam(name = "readAt", required = false, defaultValue = "") String readAt,
        @Parameter(description = SwaggerConstant.Pagination.PAGE, example = SwaggerConstant.Example.PAGE)
        @RequestParam(name = "page", required = false, defaultValue = "0") Integer page,
        @Parameter(description = SwaggerConstant.Pagination.SIZE, example = SwaggerConstant.Example.SIZE)
        @RequestParam(name = "size", required = false, defaultValue = "10") Integer size,
        @Parameter(description = SwaggerConstant.Pagination.SORT, example = SwaggerConstant.Example.SORT)
        @RequestParam(name = "sort", required = false, defaultValue = "createdAt,desc") String sortBy) {

        ListRequest listRequest = new ListRequest(size, page, sortBy);
        NotificationSearchDto searchDto = new NotificationSearchDto(
            targetEmployeeId, targetCustomerId, type, title, content, isRead, readAt, 
            page, size, sortBy);
        
        Page<NotificationDto> list = notificationService.search(searchDto, listRequest.getPageable());
        return ResponseEntity.ok().body(list);
    }

    @GetMapping("/{id}")
    public NotificationDto getOneNotification(@PathVariable Long id) {
        return notificationService.getOneWithDetails(id);
    }

    @PutMapping("/mark-read/{id}")
    public ResponseEntity<NotificationDto> markAsRead(@PathVariable Long id) {
        NotificationDto result = notificationService.markAsRead(id);
        return ResponseEntity.ok(result);
    }

    @PutMapping("/mark-unread/{id}")
    public ResponseEntity<NotificationDto> markAsUnread(@PathVariable Long id) {
        NotificationDto result = notificationService.markAsUnread(id);
        return ResponseEntity.ok(result);
    }
}
