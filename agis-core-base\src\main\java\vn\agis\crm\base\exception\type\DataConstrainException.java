package vn.agis.crm.base.exception.type;

import vn.agis.crm.base.errors.BaseException;

import java.util.Collections;
import java.util.List;


public class DataConstrainException extends BaseException {
    /**
	 *
	 */
	private static final long serialVersionUID = -145644403398350138L;

	public DataConstrainException(String title, String entityName, String field, String errorCode) {
		super(title, entityName, Collections.singletonList(field), errorCode);
	}

	public DataConstrainException(String title, String entityName, List<String> field, String errorCode) {
		super(title, entityName, field, errorCode);
	}
}
