package vn.agis.crm.service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import jakarta.transaction.Transactional;
import vn.agis.crm.base.constants.Constants.UserType;
import vn.agis.crm.base.constants.MessageKeyConstant;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.constants.JpaConstants;
import vn.agis.crm.base.jpa.dto.AreasSearch;
import vn.agis.crm.base.jpa.dto.PageInfo;
import vn.agis.crm.base.jpa.entity.Areas;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.model.dto.AreasSearchDTO;
import vn.agis.crm.repository.AreasRepository;
import vn.agis.crm.repository.UserRepository;
import vn.agis.crm.util.BaseController;

@Service
@Transactional
public class AreasService {

    private AreasRepository areasRepository;

    @Autowired
    UserRepository userRepository;

    public AreasService(AreasRepository areasRepository) {
        this.areasRepository = areasRepository;
    }

    public Event process(Event event) {
        Event response = null;
        switch (event.method) {
            case JpaConstants.Method.GET_ONE -> response = processGetOne(event);
            case JpaConstants.Method.UPDATE -> response = processUpdate(event);
            case JpaConstants.Method.UPDATE_ONE -> response = processUpdateStatus(event);
            case JpaConstants.Method.GET_ALL -> response = processGetAll(event);
            case JpaConstants.Method.CREATE -> response = processCreate(event);
            case JpaConstants.Method.SEARCH -> response = processSearch(event);
            case JpaConstants.Method.DELETE -> response = processDelete(event);
            case JpaConstants.Method.CHECK_EXITS -> response = processCheckExits(event);
            case JpaConstants.Method.GET_AREA_HIERARCHY -> response = getAreaHierarchy(event);
        }
        return response;
    }

    public Event processGetOne(Event event) {
        Long areasId = (Long) event.payload;
        Areas areas;
        if (areasRepository.findById(areasId).isPresent()) {
            areas = areasRepository.findById(areasId).get();
        } else {
            return event.createResponse("id", 404, "Not Found");
        }
        return event.createResponse(areas, 200, "OK");
    }

    public Event processUpdate(Event event) {
        Areas areas = (Areas) event.payload;
        Long areasId = areas.getId();
        if (checkPermissionAreas(event, areas)) {
            Optional<Areas> optionalAreas = areasRepository.findById(areasId);
            if (!optionalAreas.isPresent()) {
                return event.createResponse("id", 404, "Not Found");
            }
            Areas oldAreas = optionalAreas.get();

            oldAreas.setName(areas.getName());
            oldAreas.setDescription(areas.getDescription());
            oldAreas.setStatus(areas.getStatus());
            oldAreas.setUpdatedBy(event.userId);

            try {
                areasRepository.save(oldAreas);
            } catch (Exception e) {
                e.printStackTrace();
                return event.createResponse(null, 400, "Bad Request");
            }
            return event.createResponse(oldAreas, 200, "OK");
        }
        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public Event processUpdateStatus(Event event) {
        Long areasId = (Long) event.payload;
        Areas areas = areasRepository.findById(areasId).get();
        if (checkPermissionAreas(event, areas)) {
            if (areas.getStatus() == 1) {
                areas.setStatus(0);
            } else {
                areas.setStatus(1);
            }
            areas.setUpdatedBy(event.userId);
            areasRepository.save(areas);
            return event.createResponse(null, 200, "OK");
        }
        return event.createResponse(Arrays.asList("id"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public Event processCreate(Event event) {
        Areas areasReq = (Areas) event.payload;
        if (checkPermissionAreas(event, areasReq)) {
            // Check if name already exists
            if (areasRepository.countByName(areasReq.getName()) > 0) {
                return event.createResponse("name", 409, "Areas name already exists");
            }
            areasReq.setCreatedBy(event.userId);
            areasReq.setCreatedAt(new java.util.Date());
            Areas areas = areasRepository.save(areasReq);
            return event.createResponse(areas, 201, "Created");
        }
        return event.createResponse(Arrays.asList("permission"), ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public Event processSearch(Event event) {
        AreasSearchDTO searchDTO = (AreasSearchDTO) event.payload;
        BaseController.ListRequest listRequest = new BaseController.ListRequest(
            searchDTO.getSize(), searchDTO.getPage(), searchDTO.getSortBy());

        List<Long> userIdList = new ArrayList<>();
        userIdList.add(-1L); // avoid null
        if (event.userType == UserType.ENTERPRISE) {
            userIdList = getListChildOfUser(event.userId);
            userIdList.add(event.userId);
        }
        
        List<Integer> typesList = new ArrayList<>();
        if(searchDTO.getType()!= -3 && searchDTO.getType()!= -1) {
            typesList.add(searchDTO.getType());
        } else if(searchDTO.getType() == -1){
            typesList.add(0);
            typesList.add(1);
            typesList.add(2);
            typesList.add(3);
        }else {
            typesList.add(2);
            typesList.add(3);
        }

        // Use custom search method
        Page<AreasSearch> areasPage = areasRepository.searchAreas(
            searchDTO.getName(),
            searchDTO.getParentId(),
            searchDTO.getStatus() != null ? searchDTO.getStatus() : -1,
        typesList,
        listRequest.getPageable());

        PageInfo pageInfo = new PageInfo();
        pageInfo.setTotalCount(areasPage.getTotalElements());
        pageInfo.setData(ObjectMapperUtil.toJsonString(areasPage.getContent()));
        return event.createResponse(pageInfo, 200, null);
    }

    public Event processDelete(Event event) {
        Long areasId = (Long) event.payload;
        Optional<Areas> areasOptional = areasRepository.findById(areasId);
        if (!areasOptional.isPresent()) {
            return event.createResponse("id", ResponseCode.NOT_FOUND, "Not Found");
        }
        Areas areas = areasOptional.get();
        if (areas.getCreatedBy() == 0) {
            return event.createResponse("id", ResponseCode.BAD_REQUEST, "Cannot delete default areas");
        }
        if (checkPermissionAreas(event, areas)) {
            areasRepository.deleteById(areasId);
            return event.createResponse(null, ResponseCode.OK, "Deleted");
        }
        return event.createResponse("id", ResponseCode.FORBIDDEN, MessageKeyConstant.FORBIDDEN);
    }

    public Event processGetAll(Event event) {
        List<Areas> areasList = areasRepository.findAll();
        return event.createResponse(areasList, ResponseCode.OK, null);
    }

    public Event processCheckExits(Event event) {
        String name = (String) event.payload;
        long count = areasRepository.countByName(name);
        return event.createResponse(count > 0, ResponseCode.OK, null);
    }

    boolean checkPermissionAreas(Event event, Areas areas) {
        if (event.userType == UserType.ADMIN) {
            return true;
        } else if (event.userType == UserType.ENTERPRISE) {
            // Enterprise users can manage areas they created or their subordinates created
            return isChildOfUser(areas.getCreatedBy(), event.userId) || areas.getCreatedBy().equals(event.userId);
        } else if (event.userType == UserType.CUSTOMER) {
            // Customers can only manage areas they created
            return areas.getCreatedBy().equals(event.userId);
        }
        return false;
    }

    private boolean isChildOfUser(Long childUserId, Long parentUserId) {
        List<Long> childUsers = getListChildOfUser(parentUserId);
        return childUsers.contains(childUserId);
    }

    private List<Long> getListChildOfUser(Long userId) {
        List<Long> listFinal = new ArrayList<>();
        listFinal.addAll(userRepository.findAllByUserManageOrUser(userId)
            .stream()
            .map(user -> user.getId())
            .collect(Collectors.toList()));
        return listFinal;
    }

    public Event getAreaHierarchy(Event event) {
        Long areaId = (Long) event.payload;
        List<Areas> areas = areasRepository.getAreaHierarchy(areaId);
        return event.createResponse(areas, 200, "OK");
    }
}
