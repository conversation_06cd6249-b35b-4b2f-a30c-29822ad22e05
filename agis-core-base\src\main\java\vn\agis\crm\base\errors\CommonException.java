package vn.agis.crm.base.errors;

import org.zalando.problem.AbstractThrowableProblem;
import org.zalando.problem.Status;
import vn.agis.crm.base.constants.ResponseCode;

public class CommonException extends AbstractThrowableProblem {

    public CommonException(int statusCode, String message) {
        super(null, message!=null?message: ResponseCode.getResponseStatusMessage(statusCode), Status.valueOf(statusCode));
    }
}
