package vn.agis.crm.base.jpa.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerSearchDto {
    private String fullName;    // search in fullName (LIKE)
    private String phone;       // search in phone (LIKE)
    private String email;       // search in email (LIKE)
    private String cccd;        // search in cccd (LIKE)
    private String address;     // search in both address_permanent and address_contact (LIKE)
    private String sourceType;  // Data | Leads | Event | Refer (optional)
    private String sourceDetail; // search in source_detail (LIKE)
    private String businessField; // search in business_field (LIKE)
    private String interests;   // search in interests JSON array (LIKE)
    private String relativeName; // search in customer_relatives.full_name (LIKE)
    private Long projectId;     // filter by transacted project (optional)
    private Long purchasedProjectId; // filter by customers who purchased properties in specific project
    private String purchasedProjectName; // filter by customers who purchased properties in specific project
    private Long activeOfferProjectId; // filter by customers with active offers for specific project
    private String activeOfferProjectName; // filter by customers with active offers for specific project
    private String propertyType; // filter by property/unit type purchased
    private String birthDateFrom; // birth date range start (yyyy-MM-dd)
    private String birthDateTo;   // birth date range end (yyyy-MM-dd)
    private String birthdayDayMonth; // exact birthday search by day/month (DD/MM format, ignoring year)
    private Long employeeId;    // filter by responsible employee (optional)
    private Integer page = 0;
    private Integer size = 10;
    private String sortBy = "createdAt,asc";

    public CustomerSearchDto() {}

    public CustomerSearchDto(String fullName, String phone, String email, String cccd, String address, String sourceType,
                             String sourceDetail, String businessField, String interests, String relativeName,
                             Long projectId, Long purchasedProjectId, Long activeOfferProjectId, String propertyType,
                             String birthDateFrom, String birthDateTo, String birthdayDayMonth, Long employeeId,
                             Integer page, Integer size, String sortBy) {
        this.fullName = fullName;
        this.phone = phone;
        this.email = email;
        this.cccd = cccd;
        this.address = address;
        this.sourceType = sourceType;
        this.sourceDetail = sourceDetail;
        this.businessField = businessField;
        this.interests = interests;
        this.relativeName = relativeName;
        this.projectId = projectId;
        this.purchasedProjectId = purchasedProjectId;
        this.activeOfferProjectId = activeOfferProjectId;
        this.propertyType = propertyType;
        this.birthDateFrom = birthDateFrom;
        this.birthDateTo = birthDateTo;
        this.birthdayDayMonth = birthdayDayMonth;
        this.employeeId = employeeId;
        this.page = page;
        this.size = size;
        this.sortBy = sortBy;
    }
}

