package vn.agis.crm.base.jpa.dto.resp;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class AlertFullNameResponse {
    private Long id;
    private String name;
    private String deviceTypeName;
    private String modelCode;
    private Integer eventType;

    private Integer status;
    private Integer severity;

    private Long createdBy;

    private String createdByName;
    private String createdDate;
}
