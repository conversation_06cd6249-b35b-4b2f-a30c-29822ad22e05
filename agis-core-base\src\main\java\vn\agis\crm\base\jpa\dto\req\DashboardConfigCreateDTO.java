package vn.agis.crm.base.jpa.dto.req;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class DashboardConfigCreateDTO {
    private Long Id;
    private Long chartId;
    private Integer status;
    private String threshold;
    private String positionConfig;
    private String lastFilter;
}
