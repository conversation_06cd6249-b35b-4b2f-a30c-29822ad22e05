package vn.agis.crm.constant.sql;

public class SQLUserManage {
    public static final String UPDATE_USER_MANAGE_BY_USER_ID_AND_USER_MANAGE_ID = """
        update user_manage set user_manage_id = :newUserManageId where (-1 in :listUserId or user_id in (:listUserId)) and user_manage_id = :oldUserManageId
        """;
    public static final String FIND_USER_BY_USERMANAGEID_AND_NO_ONE_MANAGE = """
            select count(*) from users u
            where (u.id in (SELECT user_id FROM user_manage where user_manage_id =  :userManageId ) OR u.id not in (	select distinct um.user_id from user_manage um)) and u.id = :userId
            """;
}
