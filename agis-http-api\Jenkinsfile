pipeline {
    agent { label 'agent-manager-node' }

    environment {
        REGISTRY = '************:5000'
        IMAGE_NAME = 'agis-http-api'
        IMAGE_TAG = 'dev'
        FULL_IMAGE = "${REGISTRY}/${IMAGE_NAME}:${IMAGE_TAG}"
    }

    stages {
        stage('Clean workspace') {
            steps {
                deleteDir()
            }
        }

        stage('Checkout Source') {
            steps {
                checkout([
                    $class: 'GitSCM',
                    branches: [[name: '*/dev']],
                    userRemoteConfigs: [[
                        url: 'https://gitlab.com/agis6491334/agis-http-api',
                        credentialsId: '2f533e09-5c53-40c4-94f2-3b95fbe52752'
                    ]]
                ])
            }
        }

        stage('Build Java App') {
            steps {
                sh 'mvn clean install -DskipTests'
            }
        }

        stage('Build & Tag Docker Image') {
            steps {
                script {
                    docker.build("${IMAGE_NAME}:${IMAGE_TAG}")
                    sh "docker tag ${IMAGE_NAME}:${IMAGE_TAG} ${FULL_IMAGE}"
                }
            }
        }

        stage('Push Docker Image') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'e75b3c5e-9207-40d5-adfd-3e1822bc3a37',
                    usernameVariable: 'REG_USER',
                    passwordVariable: 'REG_PASS')]) {
                    sh """
                        docker login ${REGISTRY} -u $REG_USER -p $REG_PASS
                        docker push ${FULL_IMAGE}
                    """
                }
            }
        }

        stage('Deploy to Swarm') {
            steps {
                withCredentials([usernamePassword(
                    credentialsId: 'e75b3c5e-9207-40d5-adfd-3e1822bc3a37',
                    usernameVariable: 'REG_USER',
                    passwordVariable: 'REG_PASS')]) {
                    sh """
                        docker login ${REGISTRY} -u $REG_USER -p $REG_PASS
                        docker service update --image ${FULL_IMAGE} --with-registry-auth --force  stag_agis-http-api
                    """
                }
            }
        }
    }
}
