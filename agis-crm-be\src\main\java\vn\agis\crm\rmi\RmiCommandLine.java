package vn.agis.crm.rmi;

import java.rmi.NotBoundException;
import java.rmi.RemoteException;
import java.rmi.registry.LocateRegistry;
import java.rmi.registry.Registry;

public class RmiCommandLine {
    public static String processRmi(String[] args) {
        try {
            Registry registry = LocateRegistry.getRegistry();
            RmiMessengerService server = (RmiMessengerService) registry.lookup("RmiMessengerService");
            System.out.println("Send RMI message");
            String password = args[0];
            String typesql = args[1];
            String datasource = args[2];
            String query = args[3];
            String responseMessage = "";
            if (password != null && password.equals("cmptech")) {
                responseMessage = server.processQuery(typesql, datasource, query);
                System.out.println("ReceiveRMI: " + responseMessage);
            }
//            String responseMessage = server.sendMessage("Client Message");
//            System.out.println("Receive: " + responseMessage);
            return responseMessage;
        } catch (RemoteException | NotBoundException e) {
            System.out.println("Exception Occurred: " + e);
            return e.getMessage();
        }
    }
}
