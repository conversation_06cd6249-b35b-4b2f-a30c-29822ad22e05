package vn.agis.crm.base.jpa.dto.res;

import lombok.Data;
import vn.agis.crm.base.jpa.dto.EmployeeSummaryDto;
import vn.agis.crm.base.jpa.entity.LeadRule.ConflictPolicy;

@Data
public class LeadRuleResDto {
    private Long id;
    private String name;
    private Integer priority;
    private String conditions; // JSON string
    private ConflictPolicy conflictPolicy;
    private Boolean isActive;

    private EmployeeSummaryDto manager;
    private EmployeeSummaryDto staff;
}

