package vn.agis.crm.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.jpa.entity.QuerySuggest;
import vn.agis.crm.repository.QuerySuggestRepository;

import java.util.List;

@Service
@Transactional
public class QuerySuggestService {
    @Autowired
    private QuerySuggestRepository querySuggestRepository;
    public Event process(Event event) {
        switch (event.method){
            case Constants.Method.SEARCH:
                return search(event);
        }
        return event;
    }

    private Event search(Event event) {
        List<QuerySuggest> querySuggests = querySuggestRepository.findAll();
        return Event.createResponse(event, querySuggests,  200, null);
    }
}
