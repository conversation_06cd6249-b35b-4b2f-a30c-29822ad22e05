package vn.agis.crm.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import vn.agis.crm.base.jpa.dto.req.PermissionDto;
import vn.agis.crm.base.jpa.entity.Permissions;
import vn.agis.crm.service.PermissionApiService;

import java.util.List;

@RestController
@RequestMapping("/permissions")
public class PermissionController extends CrudController<Permissions, Long> {

    private final PermissionApiService permissionService;

    @Autowired
    public PermissionController(PermissionApiService service) {
        super(service);
        this.permissionService = service;
        this.baseUrl = "/permissions";
    }

    @GetMapping
    public ResponseEntity<List<Permissions>> getAllPermissions(
            @RequestParam(name = "description", required = false) String description) {
        List<Permissions> permissions;

        if (description != null && !description.trim().isEmpty()) {
            // Use description-based filtering
            permissions = permissionService.getAllWithDescriptionFilter(description.trim());
        } else {
            // Return all permissions (maintain backward compatibility)
            permissions = permissionService.getAll();
        }

        return ResponseEntity.ok(permissions);
    }

    @PostMapping
    public ResponseEntity<Permissions> createPermission(@RequestBody PermissionDto dto) {
        Permissions created = permissionService.createPermission(dto);
        return ResponseEntity.status(201).body(created);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Permissions> updatePermission(@PathVariable Long id, @RequestBody PermissionDto dto) {
        dto.setId(id);
        Permissions updated = permissionService.updatePermission(dto);
        return ResponseEntity.ok(updated);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePermission(@PathVariable Long id) {
        permissionService.deleteById(id);
        return ResponseEntity.noContent().build();
    }

    @GetMapping("/checkExistName")
    public ResponseEntity<Boolean> checkExistName(@RequestParam("name") String name) {
        return ResponseEntity.ok(permissionService.checkExistName(name));
    }
}
