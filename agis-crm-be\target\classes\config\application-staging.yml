server:
  port: 8080
  servlet:
    context-path: /cmp
spring:
  datasource:
    url: ***************************************
    username: m2m
    password: m2m
    driver-class-name: oracle.jdbc.driver.OracleDriver
    hikari:
      data-source-properties:
        stringtype: unspecified
  jpa:
    database-platform: org.hibernate.dialect.Oracle12cDialect
    use-new-id-generator-mappings: false
    show-sql: true
    hibernate:
      # Drop n create table, good for testing, comment this in production
      ddl-auto: none

