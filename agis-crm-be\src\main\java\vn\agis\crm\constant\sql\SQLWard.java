package vn.agis.crm.constant.sql;

public class SQLWard {
    public static final String SQL_SEARCH_WARD =
            """
            SELECT w.code, w.name, w.full_name, w.province_code
            FROM wards w
            WHERE (
            :#{#search.keySearch} = ' ' OR UPPER(name) LIKE CONCAT('%',UPPER(:#{#search.keySearch}), '%') COLLATE utf8mb4_general_ci OR UPPER(full_name) LIKE CONCAT('%',UPPER(:#{#search.keySearch}), '%') COLLATE utf8mb4_general_ci)
            AND (:#{#search.provinceCode} = -1 OR province_code = :#{#search.provinceCode})
            """;
}
