package vn.agis.crm.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import vn.agis.crm.base.constants.AMQPConstants;
import vn.agis.crm.base.constants.Constants;
import vn.agis.crm.base.constants.ResponseCode;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.exception.BadRequestException;
import vn.agis.crm.base.exception.DuplicateException;
import vn.agis.crm.base.exception.ForbiddenException;
import vn.agis.crm.base.exception.NotFoundException;
import vn.agis.crm.base.jpa.dto.InteractionPrimaryDto;
import vn.agis.crm.base.jpa.dto.InteractionPrimarySearchDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryCreateDto;
import vn.agis.crm.base.jpa.dto.req.InteractionPrimaryUpdateDto;
import vn.agis.crm.base.jpa.entity.InteractionsPrimary;
import vn.agis.crm.base.utils.ObjectMapperUtil;
import vn.agis.crm.base.utils.RequestUtils;
import vn.agis.crm.constants.MessageKeyConstant;

import java.util.List;

/**
 * API Service layer for InteractionsPrimary
 * Handles HTTP API requests and communicates with backend service via AMQP
 */
@Service
public class InteractionsPrimaryApiService extends CrudService<InteractionsPrimary, Long> {

    private static final Logger logger = LoggerFactory.getLogger(InteractionsPrimaryApiService.class);

    public InteractionsPrimaryApiService() {
        super(InteractionsPrimary.class);
        this.routingKey = AMQPConstants.RoutingKey.ROUTING_KEY_CORE_MANAGEMENT;
        this.category = Constants.Category.INTERACTIONS_PRIMARY;
    }

    /**
     * Create new primary interaction
     */
    public InteractionPrimaryDto createInteraction(InteractionPrimaryCreateDto createDto) {
        InteractionPrimaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.CREATE, category, createDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK || event.respStatusCode == ResponseCode.CREATED) {
                response = (InteractionPrimaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.Validation.INVALID_INPUT);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error creating primary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(createDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Update existing primary interaction
     */
    public InteractionPrimaryDto updateInteraction(InteractionPrimaryUpdateDto updateDto) {
        InteractionPrimaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.UPDATE, category, updateDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (InteractionPrimaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.BAD_REQUEST) {
                throw new BadRequestException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.Validation.INVALID_INPUT);
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error updating primary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(updateDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Get primary interaction by ID
     */
    public InteractionPrimaryDto getInteractionById(Long id) {
        InteractionPrimaryDto response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.FIND_BY_ID, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (InteractionPrimaryDto) event.payload;
                return response;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error getting primary interaction by ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(id), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Delete primary interaction by ID
     */
    public void deleteInteraction(Long id) {
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.DELETE, category, id, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                return;
            } else if (event.respStatusCode == ResponseCode.NOT_FOUND) {
                throw new NotFoundException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.NOT_FOUND);
            } else if (event.respStatusCode == ResponseCode.FORBIDDEN) {
                throw new ForbiddenException(event.respErrorDesc, category, (String) event.payload, 
                    MessageKeyConstant.FORBIDDEN);
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error deleting primary interaction: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(id), null, event);
        }
    }

    /**
     * Search primary interactions with filters and pagination
     */
    @SuppressWarnings("unchecked")
    public Page<InteractionPrimaryDto> searchInteractions(InteractionPrimarySearchDto searchDto) {
        Page<InteractionPrimaryDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp(Constants.Method.SEARCH, category, searchDto, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Page<InteractionPrimaryDto>) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error searching primary interactions: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(searchDto), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Get primary interactions by customer offer ID
     */
    @SuppressWarnings("unchecked")
    public List<InteractionPrimaryDto> getInteractionsByCustomerOfferId(Long customerOfferId) {
        List<InteractionPrimaryDto> response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("FIND_BY_CUSTOMER_OFFER_ID", category, customerOfferId, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (List<InteractionPrimaryDto>) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error getting primary interactions by customer offer ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(customerOfferId), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }

    /**
     * Count primary interactions by customer offer ID
     */
    public Long countInteractionsByCustomerOfferId(Long customerOfferId) {
        Long response = null;
        Long timeRequest = System.currentTimeMillis(), timeResponse = null;
        Event event = null;
        try {
            event = RequestUtils.amqp("COUNT_BY_CUSTOMER_OFFER_ID", category, customerOfferId, routingKey);
            timeResponse = System.currentTimeMillis();
            
            if (event.respStatusCode == ResponseCode.OK) {
                response = (Long) event.payload;
                return response;
            } else {
                throw new RuntimeException("Unexpected response code: " + event.respStatusCode);
            }
        } catch (Exception e) {
            logger.error("Error counting primary interactions by customer offer ID: {}", e.getMessage(), e);
            throw e;
        } finally {
            writeLog(timeRequest, timeResponse, ObjectMapperUtil.toJsonString(customerOfferId), 
                ObjectMapperUtil.toJsonString(response), event);
        }
    }
}
