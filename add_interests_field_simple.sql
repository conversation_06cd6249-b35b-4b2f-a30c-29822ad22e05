-- Add interests field to customers table
-- Simple ALTER TABLE statement for adding JSON field to store customer interests

-- Add the interests field
ALTER TABLE customers 
ADD COLUMN interests JSON NULL 
COMMENT 'JSON array storing customer interests and preferences (e.g., ["Real Estate Investment", "Luxury Properties"])';

-- Create functional index for efficient JSON searching
-- This supports the search query: JSON_SEARCH(c.interests, 'one', CONCAT('%', :interests, '%'))
CREATE INDEX idx_customers_interests 
ON customers ((CAST(interests AS CHAR(1000) ARRAY)));

-- Verify the field was added successfully
DESCRIBE customers;

-- Example usage (commented out):
/*
-- Insert example data
UPDATE customers 
SET interests = JSON_ARRAY('Real Estate Investment', 'Luxury Properties', 'Commercial Spaces')
WHERE id = 1;

-- Search example
SELECT * FROM customers 
WHERE JSON_SEARCH(interests, 'one', '%Investment%') IS NOT NULL;
*/
