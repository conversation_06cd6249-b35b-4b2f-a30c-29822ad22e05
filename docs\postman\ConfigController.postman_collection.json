{"info": {"_postman_id": "e2f0b5d8-44a2-46ae-8e26-9ee1a1b3d9f1", "name": "AGIS HTTP API - ConfigController", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "Postman collection for ConfigController endpoints"}, "variable": [{"key": "base_url", "value": "http://localhost:8080"}, {"key": "token", "value": ""}, {"key": "config<PERSON><PERSON>", "value": ""}, {"key": "configType", "value": "-1"}, {"key": "page", "value": "0"}, {"key": "size", "value": "10"}, {"key": "sort", "value": "createdAt,desc"}, {"key": "id", "value": "1"}], "item": [{"name": "GET /configs/search", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/configs/search?configKey={{configKey}}&configType={{configType}}&page={{page}}&size={{size}}&sort={{sort}}", "host": ["{{base_url}}"], "path": ["configs", "search"], "query": [{"key": "config<PERSON><PERSON>", "value": "{{config<PERSON><PERSON>}}"}, {"key": "configType", "value": "{{configType}}"}, {"key": "page", "value": "{{page}}"}, {"key": "size", "value": "{{size}}"}, {"key": "sort", "value": "{{sort}}"}]}, "description": "Search configs by key/type with pagination"}}, {"name": "GET /configs/by-key/{configKey}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/configs/by-key/{{config<PERSON><PERSON>}}", "host": ["{{base_url}}"], "path": ["configs", "by-key", "{{config<PERSON><PERSON>}}"]}, "description": "Get config by config<PERSON><PERSON>"}}, {"name": "POST /configs", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/configs", "host": ["{{base_url}}"], "path": ["configs"]}, "body": {"mode": "raw", "raw": "{\n  \"configKey\": \"APP_THEME\",\n  \"configType\": 1,\n  \"configValue\": \"dark\",\n  \"description\": \"Giao diện mặc định\"\n}"}, "description": "Create new config"}}, {"name": "PUT /configs/{id}", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{base_url}}/configs/{{id}}", "host": ["{{base_url}}"], "path": ["configs", "{{id}}"]}, "body": {"mode": "raw", "raw": "{\n  \"configKey\": \"APP_THEME\",\n  \"configType\": 1,\n  \"configValue\": \"light\",\n  \"description\": \"<PERSON><PERSON><PERSON> di<PERSON>n mặc định (sửa)\"\n}"}, "description": "Update config by id"}}, {"name": "GET /configs/{id}", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/configs/{{id}}", "host": ["{{base_url}}"], "path": ["configs", "{{id}}"]}, "description": "Get config by id"}}, {"name": "DELETE /configs/{id}", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{token}}", "type": "text"}], "url": {"raw": "{{base_url}}/configs/{{id}}", "host": ["{{base_url}}"], "path": ["configs", "{{id}}"]}, "description": "Delete config by id"}}]}