package vn.agis.crm.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import vn.agis.crm.base.jpa.dto.ImportErrorDto;
import vn.agis.crm.base.jpa.dto.ValidationResultDto;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Pattern;

import static vn.agis.crm.util.ImportErrorType.*;
import static vn.agis.crm.util.ImportSeverity.*;

/**
 * Enhanced utility class for validating import data with comprehensive validation rules
 * Supports improved error reporting and Vietnamese localization
 */
public class ImportDataValidator {

    private static final Logger logger = LoggerFactory.getLogger(ImportDataValidator.class);

    // Required fields as per technical specification
    private static final Set<String> REQUIRED_FIELDS = Set.of(
        "HỌ VÀ TÊN KHÁCH HÀNG",
        "PHONE"
    );

    // Enhanced phone validation pattern for Vietnamese phone numbers
    private static final Pattern VIETNAM_PHONE_PATTERN = Pattern.compile("^(\\+84|84|0)(3[2-9]|5[689]|7[06-9]|8[1-689]|9[0-46-9])[0-9]{7}$");

    // Enhanced email validation pattern
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");

    // Date formats to try parsing (enhanced with dd/MM/yyyy priority)
    private static final List<String> DATE_FORMATS = Arrays.asList(
        "dd/MM/yyyy",  // Primary Vietnamese format
        "d/M/yyyy",
        "dd-MM-yyyy",
        "d-M-yyyy",
        "yyyy-MM-dd",
        "yyyy/MM/dd",
        "dd/MM/yy",
        "d/M/yy",
        "d-MMM-yy",
        "EEEE, MMMM d, yyyy",
        "MM/dd/yyyy"
    );

    // Valid marital status values (Vietnamese and English)
    private static final Set<String> VALID_MARITAL_STATUS = Set.of(
        "ĐỘC THÂN", "ĐÃ LẬP GIA ĐÌNH", "LY THÂN", "GÓA", "KHÁC",
        "single", "married", "divorced", "widowed", "other"
    );

    // Marital status mapping from Vietnamese to English
    private static final Map<String, String> MARITAL_STATUS_MAPPING = Map.of(
        "ĐỘC THÂN", "single",
        "ĐÃ LẬP GIA ĐÌNH", "married",
        "LY THÂN", "divorced",
        "GÓA", "widowed",
        "KHÁC", "other"
    );

    // Number pattern for parsing Vietnamese formatted numbers
    private static final Pattern NUMBER_PATTERN = Pattern.compile("^[0-9,\\.\\s]+$");

    /**
     * Enhanced validation for a single row of import data with comprehensive rules
     */
    public static ValidationResultDto validateRow(LinkedHashMap<String, String> rowData, Long importJobId,
                                                 Set<String> existingPhones, Set<String> filePhones) {
        Integer rowNumber = Integer.parseInt(rowData.getOrDefault("__ROW_NUMBER__", "0"));
        ValidationResultDto result = new ValidationResultDto(rowNumber);
        result.setOriginalRowData(rowData);

        // Check if row is completely empty
        if (isEmptyRow(rowData)) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, null, null,
                    EMPTY_ROW.getCode(), "Dòng trống hoặc không chứa dữ liệu hợp lệ",
                ImportSeverity.WARNING.getCode()));
            return result;
        }

        // Validate required fields (full_name and phone)
        validateRequiredFields(rowData, importJobId, rowNumber, result);

        // Enhanced phone validation with Vietnamese format and duplicate checking
        validatePhoneEnhanced(rowData, importJobId, rowNumber, result, existingPhones, filePhones);

        // Enhanced email validation (optional field)
        validateEmailEnhanced(rowData, importJobId, rowNumber, result);

        // Enhanced birth date validation with logical checks
        validateBirthDate(rowData, importJobId, rowNumber, result);

        // Marital status enum validation
        validateMaritalStatus(rowData, importJobId, rowNumber, result);

        // Validate numbers (existing logic)
        validateNumbers(rowData, importJobId, rowNumber, result);

        // Set customer info for easier identification
        result.setCustomerPhone(rowData.get("PHONE"));
        result.setCustomerName(rowData.get("HỌ VÀ TÊN KHÁCH HÀNG"));

        return result;
    }
    
    /**
     * Validate required fields
     */
    private static void validateRequiredFields(Map<String, String> rowData, Long importJobId,
                                             Integer rowNumber, ValidationResultDto result) {
        for (String requiredField : REQUIRED_FIELDS) {
            String value = rowData.get(requiredField);
            if (value == null || value.trim().isEmpty()) {
                // Create Vietnamese error message based on field type
                String vietnameseFieldName = getVietnameseFieldName(requiredField);
                String errorMessage = "Trường bắt buộc '" + vietnameseFieldName + "' bị thiếu hoặc trống";

                result.addError(new ImportErrorDto(importJobId, rowNumber, requiredField, value,
                    ImportErrorType.MISSING_REQUIRED_FIELD.getCode(),
                    errorMessage,
                    ImportSeverity.ERROR.getCode()));
            }
        }
    }

    /**
     * Get Vietnamese field name for error messages
     */
    private static String getVietnameseFieldName(String fieldName) {
        switch (fieldName) {
            case "HỌ VÀ TÊN KHÁCH HÀNG":
                return "Họ và tên khách hàng";
            case "PHONE":
                return "Số điện thoại";
            case "EMAIL":
                return "Email";
            case "NGÀY SINH":
                return "Ngày sinh";
            case "TÌNH TRẠNG HÔN NHÂN":
                return "Tình trạng hôn nhân";
            default:
                return fieldName;
        }
    }
    
    /**
     * Enhanced phone validation with Vietnamese format and comprehensive duplicate checking
     */
    private static void validatePhoneEnhanced(Map<String, String> rowData, Long importJobId, Integer rowNumber,
                                            ValidationResultDto result, Set<String> existingPhones, Set<String> filePhones) {
        String phone = rowData.get("PHONE");
        if (phone == null || phone.trim().isEmpty()) {
            return; // Already handled by required field validation
        }

        // Remove Excel formatting artifacts (leading single quotes)
        String cleanPhone = removeExcelFormattingArtifacts(phone.trim());

        // Enhanced format validation for Vietnamese phone numbers
        if (!isValidVietnamesePhone(cleanPhone)) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "phone", cleanPhone,
                INVALID_PHONE_FORMAT.getCode(),
                "Số điện thoại không đúng định dạng Việt Nam",
                ERROR.getCode()));
            return;
        }

        // Normalize phone for duplicate checking
        String normalizedPhone = normalizeVietnamesePhone(cleanPhone);

        // Check for duplicates in existing system
        if (existingPhones != null && existingPhones.contains(normalizedPhone)) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "phone", cleanPhone,
                    DUPLICATE_IN_SYSTEM.getCode(),
                "Số điện thoại đã tồn tại trong hệ thống",
                WARNING.getCode()));
        }

        // Check for duplicates in current file
        if (filePhones != null) {
            if (filePhones.contains(normalizedPhone)) {
                result.addError(new ImportErrorDto(importJobId, rowNumber, "phone", cleanPhone,
                    DUPLICATE_IN_FILE.getCode(),
                    "Số điện thoại bị trùng lặp trong file",
                    ERROR.getCode()));
            } else {
                filePhones.add(normalizedPhone);
            }
        }
    }

    /**
     * Remove Excel formatting artifacts from phone numbers
     */
    private static String removeExcelFormattingArtifacts(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return phone;
        }

        String cleaned = phone.trim();

        // Remove leading single quotes that Excel adds to force text interpretation
        if (cleaned.startsWith("'")) {
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    }

    /**
     * Validate Vietnamese phone number format
     */
    private static boolean isValidVietnamesePhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return false;
        }

        // Remove Excel formatting artifacts first
        String cleanPhone = removeExcelFormattingArtifacts(phone);

        // Remove spaces, dashes, parentheses
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");

        // Check against Vietnamese phone pattern
        return VIETNAM_PHONE_PATTERN.matcher(cleanPhone).matches();
    }

    /**
     * Normalize Vietnamese phone number to E.164 format
     */
    private static String normalizeVietnamesePhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return null;
        }

        // Remove Excel formatting artifacts first
        String cleanPhone = removeExcelFormattingArtifacts(phone);

        // Remove spaces, dashes, parentheses
        cleanPhone = cleanPhone.replaceAll("[\\s\\-\\(\\)]", "");

        // Convert to +84 format
        if (cleanPhone.startsWith("0")) {
            cleanPhone = "+84" + cleanPhone.substring(1);
        } else if (cleanPhone.startsWith("84") && !cleanPhone.startsWith("+84")) {
            cleanPhone = "+" + cleanPhone;
        } else if (!cleanPhone.startsWith("+")) {
            cleanPhone = "+84" + cleanPhone;
        }

        return cleanPhone;
    }
    
    /**
     * Enhanced email validation (optional field with proper format checking)
     */
    private static void validateEmailEnhanced(Map<String, String> rowData, Long importJobId,
                                            Integer rowNumber, ValidationResultDto result) {
        String email = rowData.get("EMAIL");

        // Email is optional, so empty is valid
        if (email == null || email.trim().isEmpty()) {
            return;
        }

        String cleanEmail = email.trim();

        // Validate email format
        if (!EMAIL_PATTERN.matcher(cleanEmail).matches()) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "email", cleanEmail,
                INVALID_EMAIL_FORMAT.getCode(),
                "Email không đúng định dạng",
                ERROR.getCode()));
        }
    }

    /**
     * Enhanced birth date validation with logical checks
     */
    private static void validateBirthDate(Map<String, String> rowData, Long importJobId,
                                        Integer rowNumber, ValidationResultDto result) {
        String birthDateStr = rowData.get("NGÀY SINH");
        if (birthDateStr == null) {
            birthDateStr = rowData.get("birth_date");
        }

        // Birth date is optional
        if (birthDateStr == null || birthDateStr.trim().isEmpty()) {
            return;
        }

        String cleanBirthDate = birthDateStr.trim();
        Date parsedDate = null;

        // Try to parse with multiple date formats
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format);
                sdf.setLenient(false); // Strict parsing
                parsedDate = sdf.parse(cleanBirthDate);
                break;
            } catch (ParseException e) {
                // Try next format
            }
        }

        if (parsedDate == null) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "birth_date", cleanBirthDate,
                INVALID_DATE_FORMAT.getCode(),
                "Ngày sinh không đúng định dạng (dd/MM/yyyy)",
                ERROR.getCode()));
            return;
        }

        // Logical validation: birth date cannot be in the future
        Date today = new Date();
        if (parsedDate.after(today)) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "birth_date", cleanBirthDate,
                INVALID_FORMAT.getCode(),
                "Ngày sinh không thể là ngày tương lai",
                ERROR.getCode()));
            return;
        }

        // Logical validation: reasonable age (not older than 150 years)
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.YEAR, -150);
        Date minBirthDate = cal.getTime();

        if (parsedDate.before(minBirthDate)) {
            result.addError(new ImportErrorDto(importJobId, rowNumber, "birth_date", cleanBirthDate,
                INVALID_FORMAT.getCode(),
                "Ngày sinh không hợp lý (quá 150 tuổi)",
                WARNING.getCode()));
        }
    }

    /**
     * Validate marital status enum values
     */
    private static void validateMaritalStatus(Map<String, String> rowData, Long importJobId,
                                            Integer rowNumber, ValidationResultDto result) {
        String maritalStatus = rowData.get("TÌNH TRẠNG HÔN NHÂN");
        if (maritalStatus == null) {
            maritalStatus = rowData.get("marital_status");
        }

        // Marital status is optional
        if (maritalStatus == null || maritalStatus.trim().isEmpty()) {
            return;
        }

        String cleanMaritalStatus = maritalStatus.trim().toUpperCase();

        // Check if value is in valid set
        if (!VALID_MARITAL_STATUS.contains(cleanMaritalStatus)) {
            // Try to find a close match for better error message
            String suggestion = findClosestMaritalStatus(cleanMaritalStatus);
            String errorMessage = suggestion != null ?
                String.format("Tình trạng hôn nhân không hợp lệ. Có thể bạn muốn nhập: %s", suggestion) :
                "Tình trạng hôn nhân không hợp lệ. Các giá trị hợp lệ: ĐỘC THÂN, ĐÃ LẬP GIA ĐÌNH, LY THÂN, GÓA, KHÁC";

            result.addError(new ImportErrorDto(importJobId, rowNumber, "marital_status", maritalStatus,
                INVALID_FORMAT.getCode(),
                errorMessage,
                ERROR.getCode()));
        }
    }

    /**
     * Find closest marital status match for better error messages
     */
    private static String findClosestMaritalStatus(String input) {
        if (input == null || input.trim().isEmpty()) {
            return null;
        }

        String cleanInput = input.trim().toUpperCase();

        // Common variations and typos
        Map<String, String> commonVariations = Map.ofEntries(
            Map.entry("DOC THAN", "ĐỘC THÂN"),
            Map.entry("ĐỘC THÂN", "ĐỘC THÂN"),
            Map.entry("SINGLE", "ĐỘC THÂN"),
            Map.entry("DA LAP GIA DINH", "ĐÃ LẬP GIA ĐÌNH"),
            Map.entry("ĐÃ LẬP GIA ĐÌNH", "ĐÃ LẬP GIA ĐÌNH"),
            Map.entry("MARRIED", "ĐÃ LẬP GIA ĐÌNH"),
            Map.entry("LY THAN", "LY THÂN"),
            Map.entry("DIVORCED", "LY THÂN"),
            Map.entry("GOA", "GÓA"),
            Map.entry("WIDOWED", "GÓA"),
            Map.entry("KHAC", "KHÁC"),
            Map.entry("OTHER", "KHÁC")
        );

        return commonVariations.get(cleanInput);
    }
    
    /**
     * Validate date fields
     */
    private static void validateDates(Map<String, String> rowData, Long importJobId, 
                                    Integer rowNumber, ValidationResultDto result) {
        String[] dateFields = {
            "NGÀY THÁNG NĂM SINH",
            "GIAO DỊCH NGÀY",
            "THỜI GIAN NHẬN ĐƯỢC LEAD ĐỂ CHĂM"
        };
        
        for (String dateField : dateFields) {
            String dateValue = rowData.get(dateField);
            if (dateValue != null && !dateValue.trim().isEmpty()) {
                if (!isValidDate(dateValue)) {
                    result.addWarning(new ImportErrorDto(importJobId, rowNumber, dateField, dateValue,
                        ImportErrorType.INVALID_DATE_FORMAT.getCode(),
                        "Date '" + dateValue + "' is not in a recognized format",
                        ImportSeverity.WARNING.getCode()));
                }
            }
        }
    }
    
    /**
     * Validate number fields
     */
    private static void validateNumbers(Map<String, String> rowData, Long importJobId, 
                                      Integer rowNumber, ValidationResultDto result) {
        String[] numberFields = {
            "GIÁ GỐC TRÊN HỢP ĐỒNG",
            "GÍA BÁN KỲ VỌNG",
            "GIÁ CHO THUÊ KỲ VỌNG"
        };
        
        for (String numberField : numberFields) {
            String numberValue = rowData.get(numberField);
            if (numberValue != null && !numberValue.trim().isEmpty()) {
                if (!isValidNumber(numberValue)) {
                    result.addWarning(new ImportErrorDto(importJobId, rowNumber, numberField, numberValue,
                        ImportErrorType.INVALID_NUMBER_FORMAT.getCode(),
                        "Number '" + numberValue + "' is not in a valid format",
                        ImportSeverity.WARNING.getCode()));
                }
            }
        }
    }
    
    /**
     * Check if row is empty
     */
    private static boolean isEmptyRow(Map<String, String> rowData) {
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            if (!entry.getKey().startsWith("__") && entry.getValue() != null && !entry.getValue().trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Validate date format
     */
    private static boolean isValidDate(String dateStr) {
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
                sdf.setLenient(false);
                sdf.parse(dateStr.trim());
                return true;
            } catch (ParseException e) {
                // Try next format
            }
        }
        
        // Try with DateTimeFormatter for additional formats
        try {
            LocalDate.parse(dateStr.trim(), DateTimeFormatter.ISO_LOCAL_DATE);
            return true;
        } catch (DateTimeParseException e) {
            // Not a valid date
        }
        
        return false;
    }
    
    /**
     * Validate number format (Vietnamese style with commas)
     */
    private static boolean isValidNumber(String numberStr) {
        if (numberStr == null || numberStr.trim().isEmpty()) {
            return true;
        }
        
        String cleanNumber = numberStr.trim().replaceAll("[,\\s]", "");
        
        try {
            Double.parseDouble(cleanNumber);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * Parse number from Vietnamese format
     */
    public static Double parseNumber(String numberStr) {
        if (numberStr == null || numberStr.trim().isEmpty()) {
            return null;
        }
        
        String cleanNumber = numberStr.trim().replaceAll("[,\\s]", "");
        
        try {
            return Double.parseDouble(cleanNumber);
        } catch (NumberFormatException e) {
            return null;
        }
    }
    
    /**
     * Parse date from multiple formats
     */
    public static Date parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        
        for (String format : DATE_FORMATS) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.ENGLISH);
                sdf.setLenient(false);
                return sdf.parse(dateStr.trim());
            } catch (ParseException e) {
                // Try next format
            }
        }
        
        return null;
    }
}
