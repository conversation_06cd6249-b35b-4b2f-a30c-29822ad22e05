package vn.agis.crm.base.jpa.dto;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class UserAuth2Info {
    private Integer userType;
    private String tax;
    private String identity;
    private String citizenId;
    private String name;
    private String email;
    private String mobile;
    private String birthdate;
    private String TechID;
    private String loAs;
    private Long exp;
    private Long nbf;
    private String sub;
    private String tenantId;
    private String userName;

    public Map<String, Object> toMap() {
        Map<String, Object> data = new HashMap();
        data.put("userType", this.userType);
        data.put("tax", this.tax);
        data.put("identity", this.identity);
        data.put("citizenId", this.citizenId);
        data.put("name", this.name);
        data.put("email", this.email);
        data.put("mobile", this.mobile);
        data.put("birthdate", this.birthdate);
        data.put("techId", this.TechID);
        data.put("loAs", this.loAs);
        data.put("exp", this.exp);
        data.put("nbf", this.nbf);
        data.put("sub", this.sub);
        data.put("tenantId", this.tenantId);
        data.put("userName", this.userName);
        return data;
    }
}
