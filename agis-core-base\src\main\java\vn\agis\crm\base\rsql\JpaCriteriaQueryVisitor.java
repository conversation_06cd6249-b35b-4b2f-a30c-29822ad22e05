package vn.agis.crm.base.rsql;

import com.github.tennaito.rsql.jpa.AbstractJpaVisitor;
import com.github.tennaito.rsql.jpa.JpaPredicateVisitor;
import cz.jirutka.rsql.parser.ast.AndNode;
import cz.jirutka.rsql.parser.ast.ComparisonNode;
import cz.jirutka.rsql.parser.ast.OrNode;
import cz.jirutka.rsql.parser.ast.RSQLVisitor;

import javax.persistence.EntityManager;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.From;
import java.util.logging.Level;
import java.util.logging.Logger;

/**
 * Author: kiendt
 * Date: 3/16/2021
 * Contact: <EMAIL>
 */
public class JpaCriteriaQueryVisitor<T> extends AbstractJpaVisitor<CriteriaQuery<T>, T> implements RSQLVisitor<CriteriaQuery<T>, EntityManager> {
    private static final Logger LOG = Logger.getLogger(com.github.tennaito.rsql.jpa.JpaCriteriaQueryVisitor.class.getName());
    private final JpaPredicateVisitor<T> predicateVisitor;

    public JpaCriteriaQueryVisitor(Class<T> clazz, T... t) {
        super(t);
        this.setEntityClass(clazz);
        this.predicateVisitor = new JpaPredicateVisitor(clazz);
    }

    public JpaCriteriaQueryVisitor(T... t) {
        super(t);
        this.predicateVisitor = new JpaPredicateVisitor();
    }

    protected JpaPredicateVisitor<T> getPredicateVisitor() {
        this.predicateVisitor.setBuilderTools(this.getBuilderTools());
        return this.predicateVisitor;
    }

    public CriteriaQuery<T> visit(AndNode node, EntityManager entityManager) {
        LOG.log(Level.INFO, "Creating CriteriaQuery for AndNode: {0}", node);
        CriteriaQuery<T> criteria = entityManager.getCriteriaBuilder().createQuery(this.entityClass);
        From root = criteria.from(this.entityClass);
        return criteria.where(this.getPredicateVisitor().defineRoot(root).visit(node, entityManager));
    }

    public CriteriaQuery<T> visit(OrNode node, EntityManager entityManager) {
        LOG.log(Level.INFO, "Creating CriteriaQuery for OrNode: {0}", node);
        CriteriaQuery<T> criteria = entityManager.getCriteriaBuilder().createQuery(this.entityClass);
        From root = criteria.from(this.entityClass);
        return criteria.where(this.getPredicateVisitor().defineRoot(root).visit(node, entityManager));
    }

    public CriteriaQuery<T> visit(ComparisonNode node, EntityManager entityManager) {
        LOG.log(Level.INFO, "Creating CriteriaQuery for ComparisonNode: {0}", node);
        CriteriaQuery<T> criteria = entityManager.getCriteriaBuilder().createQuery(this.entityClass);
        From root = criteria.from(this.entityClass);
        return criteria.where(this.getPredicateVisitor().defineRoot(root).visit(node, entityManager));
    }
}
