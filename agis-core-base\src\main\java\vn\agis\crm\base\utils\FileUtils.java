package vn.agis.crm.base.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashSet;
import java.util.Set;

public class FileUtils {
    private final static Logger logger = LoggerFactory.getLogger(FileUtils.class);

    public static Set<File> listFileTree(File dir) {
        Set<File> fileTree = new HashSet<File>();
        if (dir == null || dir.listFiles() == null) {
            return fileTree;
        }
        for (File entry : dir.listFiles()) {
            if (entry.isFile()) fileTree.add(entry);
            else fileTree.addAll(listFileTree(entry));
        }
        return fileTree;
    }

    public static void move(String from, String to) {
        Path sourcePath = Paths.get(from);

        // Đường dẫn đến thư mục đích (nơi bạn muốn di chuyển tệp đến)
        Path destinationDirectory = Paths.get(to);

        try {
            // Di chuyển tệp từ nguồn đến thư mục đích
            Files.move(sourcePath, destinationDirectory.resolve(sourcePath.getFileName()), StandardCopyOption.REPLACE_EXISTING);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void delete(String filePath) {
        // Tạo đối tượng File với đường dẫn của tệp tin
        File fileToDelete = new File(filePath);

        // Kiểm tra xem tệp tin tồn tại
        if (fileToDelete.exists()) {
            // Xóa tệp tin
            fileToDelete.delete();
        } else {
            logger.info("Tệp tin không tồn tại: " + filePath);
        }

    }

    public static void copyFile(String sourcePath, String destinationPath) {
        try (FileInputStream fis = new FileInputStream(sourcePath);
             FileOutputStream fos = new FileOutputStream(destinationPath)) {

            byte[] buffer = new byte[1024];
            int bytesRead;

            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static void renameFile(String oldFileName, String newFileName) {
        File oldFile = new File(oldFileName);
        File newFile = new File(newFileName);

        if (oldFile.exists()) {
            oldFile.renameTo(newFile);
        } else {
            System.out.println("File does not exist.");
        }
    }

    public static class Extension {

        private Extension() {
        }

        public static class Excel {

            public static final String XLSX = "XLSX";
            public static final String XLS = "XLS";

            private Excel() {
            }
        }

        public static class Word {

            public static final String DOCX = "DOCX";
            public static final String DOC = "DOC";

            private Word() {
            }
        }

        public static class PortableDocumentFormat {

            public static final String PDF = "PDF";

            private PortableDocumentFormat() {
            }
        }

        public static class PowerPoint {

            public static final String PPT = "PPT";
            public static final String PPTX = "PPTX";

            private PowerPoint() {
            }
        }

        public static class Image {

            public static final String PNG = "PNG";
            public static final String GIF = "GIF";
            public static final String JPEG = "JPEG";
            public static final String JPG = "JPG";
            public static final String TIFF = "TIFF";
            public static final String JFIF = "JFIF";
            public static final String ICO = "ICO";
            public static final String WEBP = "WEBP";

            private Image() {
            }
        }

        public static class Video {

            public static final String MKV = "MKV";
            public static final String FLV = "FLV";
            public static final String AVI = "AVI";
            public static final String MP4 = "MP4";
            public static final String MOV = "MOV";
            public static final String WMV = "WMV";
            public static final String VOB = "VOB";

            private Video() {
            }

        }


    }

    public static final String[] exts = new String[]{
            Extension.Image.JPG,
            Extension.Image.JPEG,
            Extension.Image.PNG,
            Extension.Image.WEBP,
            Extension.Image.ICO,
            Extension.Image.JPG,
            Extension.Image.TIFF,
            Extension.Image.JFIF,
            Extension.Video.MP4,
            Extension.Video.AVI,
            Extension.Video.FLV,
            Extension.Video.MKV,
            Extension.Video.MOV,
            Extension.Video.WMV,
            Extension.Video.VOB,
            Extension.Excel.XLS,
            Extension.Excel.XLSX,
            Extension.PortableDocumentFormat.PDF,
            Extension.Word.DOCX,
            Extension.Word.DOC,
            Extension.PowerPoint.PPT,
            Extension.PowerPoint.PPTX
    };

}
