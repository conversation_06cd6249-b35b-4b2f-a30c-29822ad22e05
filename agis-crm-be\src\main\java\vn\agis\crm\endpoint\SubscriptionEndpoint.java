package vn.agis.crm.endpoint;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import vn.agis.crm.base.event.Event;
import vn.agis.crm.base.event.EventBus;
import vn.agis.crm.service.SubscriptionService;

@Component
public class SubscriptionEndpoint {
    private static Logger logger = LoggerFactory.getLogger(SubscriptionEndpoint.class);
    private SubscriptionService subscriptionService;
    protected EventBus eventBus;
    public SubscriptionEndpoint(SubscriptionService subscriptionService, EventBus eventBus) {
        this.subscriptionService = subscriptionService;
        this.eventBus = eventBus;
    }
    public Event process(Event event) {
        return this.subscriptionService.process(event);
    }
}
